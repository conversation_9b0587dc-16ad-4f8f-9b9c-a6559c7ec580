<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dbj</groupId>
        <artifactId>dbj-classpal-books-bus</artifactId>
        <version>${revision}</version>
    </parent>


    <artifactId>dbj-classpal-books-client</artifactId>
    <name>dbj-classpal-books-client</name>
    <description>Generated Project by dbj-framework-codegen.</description>

    <properties>
    </properties>

    <dependencies>
<!--        <dependency>-->
<!--            <groupId>com.dbj</groupId>-->
<!--            <artifactId>dbj-classpal-books-common</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.dbj</groupId>
            <artifactId>dbj-classpal-commons-starter</artifactId>
        </dependency>
    </dependencies>

    <build>
    </build>


</project>