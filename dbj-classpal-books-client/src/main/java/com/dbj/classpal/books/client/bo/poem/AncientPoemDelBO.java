package com.dbj.classpal.books.client.bo.poem;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 古诗文删除DelBO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Tag(name="古诗文删除DelBO", description="古诗文删除DelBO")
public class AncientPoemDelBO implements Serializable {


    @NotBlank
    @Schema(description = "广告分类", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer classifyId;

    @Schema(description = "主键ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "主键ID列表不能为空")
    private List<Integer> ids;

}
