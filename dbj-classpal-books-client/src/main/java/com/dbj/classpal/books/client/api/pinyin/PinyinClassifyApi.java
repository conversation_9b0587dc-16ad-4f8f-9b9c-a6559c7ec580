package com.dbj.classpal.books.client.api.pinyin;

import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinClassifyBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinClassifyUpsertBO;
import com.dbj.classpal.books.client.dto.pinyin.PinyinClassifyDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <p>
 * 汉语拼音分类 远程Api
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface PinyinClassifyApi {
    /**----------------------------------- admin调用Api -----------------------------------------*/

    /**
     * 获取所有拼音分类
     * @param bo 拼音分类查询条件
     * @return RestResponse对象
     */
    @PostMapping("/pinyinClassify/getPinyinClassifyAll")
    RestResponse<List<PinyinClassifyDTO>> getPinyinClassifyAll(@RequestBody @Validated PinyinClassifyBO bo);
    /**
     * 保存拼音分类
     * @param bo 拼音分类UpsertBO
     * @return RestResponse对象
     * @throws BusinessException 业务异常
     */
    @PostMapping(value = "/pinyinClassify/savePinyinClassify")
    RestResponse<Boolean> savePinyinClassify(@RequestBody @Validated PinyinClassifyUpsertBO bo) throws BusinessException;
    /**
     * 更新拼音分类
     * @param bo 拼音分类UpsertBO
     * @return RestResponse对象
     * @throws BusinessException 业务异常
     */
    @PostMapping(value = "/pinyinClassify/updatePinyinClassify")
    RestResponse<Boolean> updatePinyinClassify(@RequestBody @Validated PinyinClassifyUpsertBO bo) throws BusinessException;
    /**
     * 删除拼音分类
     * @param bo 包含id的请求对象
     * @return RestResponse对象
     * @throws BusinessException 业务异常
     */
    @PostMapping(value = "/pinyinClassify/deletePinyinClassify")
    RestResponse<Boolean> deletePinyinClassify(@RequestBody @Validated CommonIdApiBO bo) throws BusinessException;

    /**
     * 重新排序拼音分类
     * @param bo 包含广告id的请求对象
     * @return RestResponse对象
     * @throws BusinessException 业务异常
     */
    @PostMapping(value = "/pinyinClassify/sortPinyinClassify")
    RestResponse<Boolean> sortPinyinClassify(@RequestBody @Validated CommonIdsApiBO bo);



    /**----------------------------------- App调用Api -----------------------------------------*/
    /**
     * 获取拼音分类
     * @param bo 包含拼音分类信息的请求对象
     * @return 包含用户拼音分类的RestResponse对象
     */
    @PostMapping("/pinyin/getAppPinyinClassify")
    RestResponse<List<PinyinClassifyDTO>> getAppPinyinClassify(@RequestBody @Validated PinyinClassifyBO bo);
}
