package com.dbj.classpal.books.client.dto.audio;

import com.dbj.classpal.framework.utils.util.TreeVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2025/6/27 17:22
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AudioClassifyDTO extends TreeVO<Integer, AudioClassifyDTO> {

    @Schema(description = "是否默认分类 0否 1是")
    private Integer defaultType;

    @Schema(description = "音频分类名称")
    private String classifyName;

    @Schema(description = "数量")
    private Integer count;
}
