package com.dbj.classpal.books.client.api.books;

import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsQuestionDetailBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsQuestionSavaBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsQuestionUpdateBO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AdminBooksRankInCodeContentsQuestionApi {

    @PostMapping("/books/rank/in/code/contents/question/details")
    RestResponse<BooksRankInCodesContentsQuestionDetailBO> details(@RequestParam Integer inCodesContentsId);

    @PostMapping("/books/rank/in/code/contents/question/save")
    RestResponse<Boolean> save(@RequestBody BooksRankInCodesContentsQuestionSavaBO saveBO) throws BusinessException;

    @PostMapping("/books/rank/in/code/contents/question/update")
    RestResponse<Boolean> update(@RequestBody BooksRankInCodesContentsQuestionUpdateBO saveBO) throws BusinessException;
}
