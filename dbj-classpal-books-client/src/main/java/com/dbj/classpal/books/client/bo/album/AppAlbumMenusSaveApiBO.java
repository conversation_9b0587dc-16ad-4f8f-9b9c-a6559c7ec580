package com.dbj.classpal.books.client.bo.album;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumSaveApiBO
 * Date:     2025-04-10 09:37:13
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
@Tag(name = "专辑分类新增BO")
public class AppAlbumMenusSaveApiBO implements Serializable {

    @Schema(description = "父节点ID",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "父节点ID不能为空")
    private Integer parentId;

    @Schema(description = "专辑分类名称",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "专辑分类名称不能为空")
    @Size(min = 1, max = 100,message = "专辑分类名称范围在1~100字符内")
    private String albumMenuName;

    @Schema(description = "专辑分类类型 1音频 2视频",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "专辑类型不能为空")
    private Integer albumType;

    @Schema(description = "排序")
    private Integer orderNum;
}
