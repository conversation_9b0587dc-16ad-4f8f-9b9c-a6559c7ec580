package com.dbj.classpal.books.client.bo.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;

/**
 * app广告查询参数BO
 * <AUTHOR> <PERSON>
 * @since 2025-04-21 16:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
@Tag(name="app广告查询参数BO", description="app广告查询参数BO")
public class AdvertisementAppBO implements Serializable {


    /** 广告类型字典项 */
    @Schema(description = "广告类型字典项", requiredMode = Schema.RequiredMode.REQUIRED)
    private String advertisementType;
//    /** 广告类型字典项集合 */
//    @Schema(description = "广告类型字典项集合")
//    private Set<String> advertisementTypes;
    /** 年级id */
    @Schema(description = "年级id")
    private String gradeId;
//    /** 性别 */
//    @Schema(description = "性别")
//    private String gender;
    /** 关系类型 */
    @Schema(description = "关系类型")
    private Integer relationType;
    /** ip省属地 */
    @Schema(description = "ip省属地")
    private String provinceName;
    /** ip市属地 */
    @Schema(description = "ip市属地")
    private String cityAreaId;
    /** 城市名称 */
    @Schema(description = "城市名称")
    private String cityName;
    @Schema(description = "app用户id")
    private Integer appUserId;
}
