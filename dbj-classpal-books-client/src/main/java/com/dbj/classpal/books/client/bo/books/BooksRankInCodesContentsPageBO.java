package com.dbj.classpal.books.client.bo.books;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 图书书内码分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="BooksRankInCodesContents对象", description="图书书内码分类表")
public class BooksRankInCodesContentsPageBO implements Serializable {



    @Schema(description = "图书id")
    private Integer booksId;

    @Schema(description = "书内码名称")
    private String contentName;

    @Schema(description = "册书名称")
    private String productItemName;
}
