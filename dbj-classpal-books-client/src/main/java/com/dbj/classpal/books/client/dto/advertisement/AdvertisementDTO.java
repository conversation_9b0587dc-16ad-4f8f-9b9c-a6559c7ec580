
package com.dbj.classpal.books.client.dto.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 广告DTO
 * <AUTHOR>
 * @since 2025-04-21 16:52
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Tag(name="广告DTO", description="广告DTO")
public class AdvertisementDTO implements Serializable  {

	/**
	 * 广告id
	 */
	@Schema(description = "广告id")
	private Integer id;
	/**
	 * 广告类型字典项的值
	 */
	@Schema(description = "广告类型字典项的值 首页弹窗-homePopup 瓷片列表-ceramicList 瓷片区右下-ceramicBottomRight 瓷片区右上-ceramicTopRight 瓷片区Banner-ceramicBanner")
	private String type;
	/**
	 * 描述
	 */
	@Schema(description = "描述")
	private String description;
	/**
	 * 跳转类型 0-无跳转 1-链接 2-小程序 3-基础图文
	 */
	@Schema(description = "跳转类型 0-无跳转 1-链接 2-小程序 3-基础图文")
	private Integer redirectType;
//	/**
//	 * 跳转标识
//	 */
//	@Schema(description = "跳转标识")
//	private String redirectFlag;
	/**
	 * 跳转地址
	 */
	@Schema(description = "跳转地址")
	private String redirectUri;
	/**
	 * 外部小程序appid
	 */
	@Schema(description = "外部小程序appid")
	private String redirectAppId;
//	/**
//	 * 落地页面id
//	 */
//	@Schema(description = "落地页面id")
//	private String advertisingLandingId;
	/**
	 * 标题
	 */
	@Schema(description = "标题")
	private String title;
	/**
	 * 富文本内容
	 */
	@Schema(description = "富文本内容")
	private String content;
	/**
	 * 展示次数 1-仅一次 2-每次 3-每天一次
	 */
	@Schema(description = "展示次数 1-仅一次 2-每次 3-每天一次")
	private Integer showType;
	/**
	 * 条件个数
	 */
	@Schema(description = "条件个数")
	private Long conditionCount;
	/**
	 * 排序（越大越靠前）
	 */
	@Schema(description = "排序（越大越靠前）")
	private Integer sort;
	/**
	 * 是否启用 1-是 0-否
	 */
	@Schema(description = "是否启用 true-是 false-否")
	private Boolean status;
	/**
	 * 封面路径
	 */
	@Schema(description = "封面路径")
	private String coverUrl;
	/**
	 * 启用时间
	 */
	@Schema(description = "启用时间")
	private LocalDateTime activationTime;
	/**
	 * 展示条件列表
	 */
	@Schema(description = "展示条件")
	private List<AdvertisementLevelConditionDTO> advertisementLevelConditionList;
}
