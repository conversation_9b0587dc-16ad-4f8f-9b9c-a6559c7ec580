package com.dbj.classpal.books.client.bo.evaluation;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppUserPaperEvaluationQueryBO
 * Date:     2025-05-19 15:19:02
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class AdminUserPaperEvaluationQueryApiBO implements Serializable {

    @Schema(description = "评测表id")
    private Integer id;

    @Schema(description = "学员")
    private Integer userId;

    @Schema(description = "学员UID")
    private String uid;

    @Schema(description = "学员年级ids")
    private List<String> gradeIds;

    @Schema(description = "地区")
    private String region;

}
