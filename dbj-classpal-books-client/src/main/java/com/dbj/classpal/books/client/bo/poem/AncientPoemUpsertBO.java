package com.dbj.classpal.books.client.bo.poem;


import com.dbj.classpal.books.client.bo.material.AppCommonMediaBO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 古诗文UpsertBO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Tag(name="古诗文UpsertBO", description="古诗文UpsertBO")
public class AncientPoemUpsertBO implements Serializable {

    @Schema(description = "ID")
    private Integer id;

    @NotNull
    @Schema(description = "古诗文分类", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer classifyId;

    @Schema(description = "封面图名称")
    private String coverName;

    @Schema(description = "封面图路径")
    private String coverUrl;

    @Schema(description = "背景图名称")
    private String backgroundName;

    @Schema(description = "背景图路径")
    private String backgroundUrl;

    @NotBlank
    @Size(max = 32)
    @Schema(description = "标题", requiredMode = Schema.RequiredMode.REQUIRED)
    private String title;

    @NotBlank
    @Size(max = 64)
    @Schema(description = "标题拼音", requiredMode = Schema.RequiredMode.REQUIRED)
    private String titlePinyin;

    @Schema(description = "作者")
    private String author;

    @Schema(description = "所属朝代")
    private String dynasty;

    @NotBlank
    @Schema(description = "年级", requiredMode = Schema.RequiredMode.REQUIRED)
    private String grade;

    @NotNull
    @Schema(description = "是否课外内容 0-课内 1-课外", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer isOut;

    @Schema(description = "古诗文简介")
    private String introduction;

    @NotBlank
    @Size(max = 256)
    @Schema(description = "古诗文释义",  requiredMode = Schema.RequiredMode.REQUIRED)
    private String interpretation;

    @NotBlank
    @Size(max = 256)
    @Schema(description = "古诗文赏析",  requiredMode = Schema.RequiredMode.REQUIRED)
    private String appreciation;

    @NotBlank
    @Size(max = 1024)
    @Schema(description = "校验文本", requiredMode = Schema.RequiredMode.REQUIRED)
    private String verifyText;

    @Max(99999)
    @Schema(description = "排序权重")
    private Integer sort;

    @NotEmpty
    @NotNull
    @Valid
    @Schema(description = "原文音频文件", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<AppCommonMediaBO> originalAudioMedias;

    @Valid
    @Schema(description = "解析音频文件", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<AppCommonMediaBO> explanationAudioMedias;
}
