package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Data;

/**
 * 点读书更新API BO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Schema(name = "PointReadingBookUpdateApiBO", description = "点读书更新API BO")
public class PointReadingBookUpdateApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @NotNull(message = "主键ID不能为空")
    private Integer id;

    @Schema(description = "点读书名称")
    @NotBlank(message = "点读书名称不能为空")
    private String name;

    @Schema(description = "所属分类ID")
    @NotNull(message = "所属分类ID不能为空")
    private Integer categoryId;

    @Schema(description = "封面文件ID")
    private String coverId;

    @Schema(description = "封面URL")
    private String coverUrl;

    @Schema(description = "封面文件名")
    private String coverName;

    @Schema(description = "原始文件ID")
    private String originalFileId;

    @Schema(description = "原始文件URL")
    private String originalUrl;

    @Schema(description = "原始文件名")
    private String originalName;

    @Schema(description = "原始文件MD5")
    private String originalMd5;

    @Schema(description = "启用状态：0-未启用 1-已启用")
    private Integer launchStatus;

    @Schema(description = "排序")
    private Integer sortNum;

}
