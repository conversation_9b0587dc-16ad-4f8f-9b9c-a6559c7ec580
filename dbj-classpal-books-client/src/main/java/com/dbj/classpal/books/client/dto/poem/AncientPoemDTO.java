
package com.dbj.classpal.books.client.dto.poem;

import com.dbj.classpal.books.client.dto.material.AppCommonMediaDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 古诗文DTO
 * <AUTHOR> <PERSON>
 * @since 2025-05-23
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Tag(name="古诗文DTO", description="古诗文DTO")
public class AncientPoemDTO implements Serializable  {


	@Schema(description = "ID")
	private Integer id;

	@Schema(description = "古诗文分类")
	private Integer classifyId;

	@Schema(description = "封面图名称")
	private String coverName;

	@Schema(description = "封面图路径")
	private String coverUrl;

	@Schema(description = "背景图名称")
	private String backgroundName;

	@Schema(description = "背景图路径")
	private String backgroundUrl;

	@Schema(description = "标题")
	private String title;

	@Schema(description = "标题拼音")
	private String titlePinyin;

	@Schema(description = "作者")
	private String author;

	@Schema(description = "所属朝代")
	private String dynasty;

	@Schema(description = "年级")
	private String grade;

	@Schema(description = "是否课外内容 0-课内 1-课外")
	private Integer isOut;

	@Schema(description = "古诗文简介")
	private String introduction;

	@Schema(description = "古诗文释义")
	private String interpretation;

	@Schema(description = "古诗文赏析")
	private String appreciation;

	@Schema(description = "校验文本")
	private String verifyText;

	@Schema(description = "排序权重")
	private Integer sort;

	@Schema(description = "原文音频")
	List<AppCommonMediaDTO> originalAudioMedias;

	@Schema(description = "讲解音频")
	List<AppCommonMediaDTO> explanationAudioMedias;

	@Schema(description = "是否被引用")
	private Boolean isRef;

}
