package com.dbj.classpal.books.client.bo.poem;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className AncientPoemBusinessRefPageBO
 * @description
 * @date 2025-05-27 09:36
 **/
@Data
public class AncientPoemBusinessRefSaveBO implements Serializable {


    @Schema(description = "业务id")
    @NotNull(message = "业务id不能为空")
    private Integer businessId;

    @Schema(description = "古诗文id")
    @NotNull(message = "古诗文id不能为空")
    private List<Integer> ancientPoemIds;

    @Schema(description = "业务类型 1-古诗背诵")
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;
}
