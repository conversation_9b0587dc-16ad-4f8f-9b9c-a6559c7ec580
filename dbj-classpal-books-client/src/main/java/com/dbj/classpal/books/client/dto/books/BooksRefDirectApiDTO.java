package com.dbj.classpal.books.client.dto.books;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialBusinessRefDTO
 * Date:     2025-04-14 08:54:29
 * Description: 表名： ,描述： 表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@Tag(name = "专辑引用素材跳转DTO")
public class BooksRefDirectApiDTO implements Serializable {
    @Schema(description = "图书id")
    private Integer booksId;

    @Schema(description = "册数id")
    private Integer rankId;

    @Schema(description = "册数功能分类id")
    private Integer rankClassifyId;

    @Schema(description = "书内码id")
    private Integer businessId;

    @Schema(description = "图书名称")
    private String bookName;
}
