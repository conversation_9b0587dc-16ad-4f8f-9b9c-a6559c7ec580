package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 单书图片资源分页查询参数
 */
@Data
@Schema(description = "单书图片资源分页查询参数")
public class AppEBookPageImagesQueryApiBO implements Serializable {
    
    @NotNull(message = "单书ID不能为空")
    @Schema(description = "单书ID")
    private List<Integer> bookIds;
} 