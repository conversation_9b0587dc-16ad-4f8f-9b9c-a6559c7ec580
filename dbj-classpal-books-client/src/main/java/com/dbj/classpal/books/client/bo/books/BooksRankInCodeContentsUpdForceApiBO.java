package com.dbj.classpal.books.client.bo.books;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 图书卷册赠册表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="BooksRankInfo对象", description="图书卷册赠册表")
public class BooksRankInCodeContentsUpdForceApiBO implements Serializable {



    @Schema(description =  "书内码内容id")
    private Integer id;

    @Schema(description =  "强跳链接")
    private String forcePromotionUrl;

}
