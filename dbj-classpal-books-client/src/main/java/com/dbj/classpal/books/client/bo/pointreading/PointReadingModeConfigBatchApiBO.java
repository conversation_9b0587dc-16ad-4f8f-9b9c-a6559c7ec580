package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 点读书模式配置批量操作API BO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingModeConfigBatchApiBO", description = "点读书模式配置批量操作API BO")
public class PointReadingModeConfigBatchApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "配置ID列表")
    @NotEmpty(message = "配置ID列表不能为空")
    private List<Integer> ids;
}
