package com.dbj.classpal.books.client.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 题目分类业务关联 BO
 */
@Data
@Schema(description = "题目分类业务关联")
public class QuestionCategoryBusinessRefApiBO implements Serializable {

    /**
     * 题目分类ID
     */
    @Schema(description = "题目分类ID")
    private Integer questionCategoryId;
    /**
     * 题目分类名称
     */
    @Schema(description = "题目分类名称")
    private String questionCategoryName;
    /**
     * 题目数量
     */
    @Schema(description = "题目数量")
    private Integer questionNum;

    /**
     * 业务ID
     */
    @Schema(description = "业务ID")
    private Integer businessId;

    /**
     * 业务类型（1-音频专辑 2-视频专辑 3-图书）
     */
    @Schema(description = "业务类型（1-音频专辑 2-视频专辑 3-图书）")
    private Integer businessType;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sortNum;
} 