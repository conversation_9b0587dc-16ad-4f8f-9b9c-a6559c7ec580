package com.dbj.classpal.books.client.bo.material;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialBusinessRefSaveApiBO
 * Date:     2025-04-17 09:49:46
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class AppMaterialBusinessRefSaveApiBO implements Serializable {

    @Schema(description = "业务id")
    private Integer businessId;

    @Schema(description = "业务类型 1内容管理-音频专辑 2内容管理-视频专辑 3图书管理-图书资源 4图书管理-题库 5图书管理-音频专辑 6图书管理-视频专辑")
    private Integer businessType;

    @Schema(description = "素材id列表")
    private List<Integer>appMaterialIds;
}
