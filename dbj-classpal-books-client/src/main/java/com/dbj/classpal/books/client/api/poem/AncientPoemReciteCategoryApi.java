package com.dbj.classpal.books.client.api.poem;

import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCategorySaveBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCategorySortBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCategoryUpdateBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemReciteCategoryDTO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemReciteCategoryDetailDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className AncientPoemReciteCategoryApi
 * @description
 * @date 2025-05-26 09:09
 **/
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AncientPoemReciteCategoryApi {

    @PostMapping("/ancientPoemReciteCategory/listAncientPoemReciteCategory")
    RestResponse<List<AncientPoemReciteCategoryDTO>> listAncientPoemReciteCategory();

    @GetMapping("/ancientPoemReciteCategory/getAncientPoemReciteCategory")
    RestResponse<AncientPoemReciteCategoryDetailDTO> getAncientPoemReciteCategory(@RequestParam Integer id) throws BusinessException;
    @PostMapping("/ancientPoemReciteCategory/save")
    RestResponse<Boolean> save(@RequestBody AncientPoemReciteCategorySaveBO ancientPoemReciteCategorySaveBO);

    @PostMapping("/ancientPoemReciteCategory/update")
    RestResponse<Boolean> update(@RequestBody AncientPoemReciteCategoryUpdateBO ancientPoemReciteCategoryUpdateBO) throws BusinessException;

    @PostMapping("/ancientPoemReciteCategory/delete")
    RestResponse<Boolean> delete(@RequestParam Integer id) throws BusinessException;

    @PostMapping("/ancientPoemReciteCategory/sort")
    RestResponse<Boolean> sort(@RequestBody List<AncientPoemReciteCategorySortBO> ancientPoemReciteCategorySortBOs) throws BusinessException;
}
