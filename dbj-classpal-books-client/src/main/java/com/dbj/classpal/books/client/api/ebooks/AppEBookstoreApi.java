package com.dbj.classpal.books.client.api.ebooks;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.ebooks.*;
import com.dbj.classpal.books.client.dto.ebooks.AppEBookstoreApiDTO;
import com.dbj.classpal.books.client.dto.ebooks.AppEBookstoreH5ListApiDTO;
import com.dbj.classpal.books.client.dto.ebooks.AppEBookstoreStatisticsApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 书城API接口
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}", path = "/api/classpal/books/${spring.application.version}")
public interface AppEBookstoreApi {

    /**
     * 分页查询书城列表
     *
     * @param pageRequest 分页查询参数
     * @return 书城分页数据
     */
    @PostMapping("/ebook/bookstore/page")
    RestResponse<Page<AppEBookstoreApiDTO>> page(@RequestBody PageInfo<AppEBookstoreQueryApiBO> pageRequest) throws BusinessException;

    /**
     * 分页查询书城集
     * 支持两种查询模式：
     * 1. 默认查询已启用的所有书城集
     * 2. 根据书城ID查询特定的分享书城
     *
     * @param pageRequest 分页查询参数
     * @return 书城集分页数据
     */
    @PostMapping("/ebook/bookstore/collections/page")
    RestResponse<Page<AppEBookstoreH5ListApiDTO>> pageBookstoreCollections(@RequestBody PageInfo<AppEBookstoreH5QueryApiBO> pageRequest) throws BusinessException;

    /**
     * 查询书城详情
     *
     * @param idBO 书城ID参数
     * @return 书城详情数据
     */
    @PostMapping("/ebook/bookstore/detail")
    RestResponse<AppEBookstoreApiDTO> detail(@RequestBody AppEBookstoreIdApiBO idBO) throws BusinessException;

    /**
     * 新增书城
     *
     * @param saveBO 书城保存参数
     * @return 保存结果
     */
    @PostMapping("/ebook/bookstore/save")
    RestResponse<Integer> save(@RequestBody AppEBookstoreSaveApiBO saveBO) throws BusinessException;

    /**
     * 更新书城
     *
     * @param updateBO 书城更新参数
     * @return 更新结果
     */
    @PostMapping("/ebook/bookstore/update")
    RestResponse<Boolean> update(@RequestBody AppEBookstoreUpdateApiBO updateBO) throws BusinessException;

    /**
     * 删除书城
     *
     * @param idBO 书城ID参数
     * @return 删除结果
     */
    @PostMapping("/ebook/bookstore/delete")
    RestResponse<Boolean> delete(@RequestBody AppEBookstoreIdApiBO idBO) throws BusinessException;

    /**
     * 批量删除书城
     *
     * @param idsBO ID列表参数
     * @return 批量删除结果
     */
    @PostMapping("/ebook/bookstore/delete/batch")
    RestResponse<Boolean> deleteBatch(@RequestBody AppEBookstoreIdsApiBO idsBO) throws BusinessException;

    /**
     * 批量启用书城
     *
     * @param idsBO ID列表参数
     * @return 批量启用结果
     */
    @PostMapping("/ebook/bookstore/enable/batch")
    RestResponse<Boolean> enableBatch(@RequestBody AppEBookstoreIdsApiBO idsBO) throws BusinessException;

    /**
     * 批量禁用书城
     *
     * @param idsBO ID列表参数
     * @return 批量禁用结果
     */
    @PostMapping("/ebook/bookstore/disable/batch")
    RestResponse<Boolean> disableBatch(@RequestBody AppEBookstoreIdsApiBO idsBO) throws BusinessException;
    /**
     * 批量允许下载
     *
     * @param idsBO ID列表参数
     * @return 批量允许下载结果
     */
    @PostMapping("/ebook/bookstore/allow-download/batch")
    RestResponse<Boolean> allowDownloadBatch(@RequestBody AppEBookstoreIdsApiBO idsBO) throws BusinessException;

    /**
     * 批量关闭下载
     *
     * @param idsBO ID列表参数
     * @return 批量关闭下载结果
     */
    @PostMapping("/ebook/bookstore/disable-download/batch")
    RestResponse<Boolean> disableDownloadBatch(@RequestBody AppEBookstoreIdsApiBO idsBO) throws BusinessException;

    /**
     * 获取书城统计信息
     *
     * @param idBO 书城ID参数
     * @return 统计信息
     */
    @PostMapping("/ebook/bookstore/statistics")
    RestResponse<AppEBookstoreStatisticsApiDTO> getStatistics(@RequestBody AppEBookstoreIdApiBO idBO) throws BusinessException;

}