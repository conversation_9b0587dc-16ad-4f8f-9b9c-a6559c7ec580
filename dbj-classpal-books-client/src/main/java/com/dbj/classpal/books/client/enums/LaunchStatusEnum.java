package com.dbj.classpal.books.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 点读书启用状态枚举
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Getter
@AllArgsConstructor
public enum LaunchStatusEnum {

    /**
     * 未启用
     */
    DISABLED(0, "未启用"),

    /**
     * 已启用
     */
    ENABLED(1, "已启用");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举值
     */
    public static LaunchStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (LaunchStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据状态码获取描述
     *
     * @param code 状态码
     * @return 状态描述
     */
    public static String getDescByCode(Integer code) {
        LaunchStatusEnum status = getByCode(code);
        return status != null ? status.getDesc() : "未知状态";
    }
}
