package com.dbj.classpal.books.client.api.question;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.question.*;
import com.dbj.classpal.books.client.dto.question.QuestionApiDTO;
import com.dbj.classpal.books.client.dto.question.QuestionBlankContentApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface QuestionApi {

    /**
     * 分页查询题目列表
     */
    @PostMapping("/question/page")
    RestResponse<Page<QuestionApiDTO>> pageList(@RequestBody @Valid PageInfo<QuestionPageApiBO> pageApiBO);

    /**
     * 获取题目详情
     */
    @PostMapping("/question/detail")
    RestResponse<QuestionApiDTO> getQuestion(@RequestBody @Valid  QuestionIdApiBO idApiBO) throws BusinessException;

    /**
     * 根据分类ID获取题目列表
     */
    @PostMapping("/question/category/list")
    RestResponse<List<QuestionApiDTO>> getQuestionList(@RequestBody @Valid QuestionCategoryIdQueryApiBO idApiBO);

    /**
     * 创建题目
     */
    @PostMapping("/question/create")
    RestResponse<Integer> createQuestion(@RequestBody @Valid QuestionSaveApiBO question) throws BusinessException;

    /**
     * 更新题目
     */
    @PostMapping("/question/edit")
    RestResponse<Void> updateQuestion( @RequestBody @Valid  QuestionEditApiBO question) throws BusinessException;

    /**
     * 删除题目
     */
    @PostMapping("/question/batch/delete")
    RestResponse<Void> batchDeleteQuestion(@RequestBody @Valid  QuestionIdsApiBO idsApiBO) throws BusinessException;

    /**
     * 获取题目正确答案
     */
    @PostMapping("/question/answer/get")
    RestResponse<List<String>> getQuestionAnswer(@RequestBody @Valid  QuestionIdApiBO idApiBO);

    /**
     * 获取完形填空题内容
     */
    @PostMapping("/question/blank-content/get")
    RestResponse<QuestionBlankContentApiDTO> getQuestionBlankContent(@RequestBody @Valid  QuestionIdApiBO idApiBO);

    /**
     * 批量复制题目
     */
    @PostMapping("/question/batch/copy")
    RestResponse<Void> batchCopyQuestion(@RequestBody @Valid QuestionCopyApiBO copyApiBO) throws BusinessException;

    /**
     * 批量移动题目
     */
    @PostMapping("/question/batch/move")
    RestResponse<Void> batchMoveQuestion(@RequestBody @Valid  QuestionMoveApiBO moveApiBO);
} 