package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 点读书ID API BO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Schema(name = "PointReadingBookIdApiBO", description = "点读书ID API BO")
public class PointReadingBookIdApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "点读书ID")
    @NotNull(message = "点读书ID不能为空")
    private Integer bookId;
}
