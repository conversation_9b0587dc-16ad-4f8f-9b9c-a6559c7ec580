package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/8/1 16:28
 */
@Data
public class PointReadingBooksBusinessRefQueryBO {

    @NotNull
    @Schema(description = "业务id", requiredMode = RequiredMode.REQUIRED)
    private Integer businessId;

    @NotNull
    @Schema(description = "业务类型", requiredMode = RequiredMode.REQUIRED)
    private String businessType;
}
