package com.dbj.classpal.books.client.api.shorts;

import com.dbj.classpal.books.client.dto.shorts.ShortUrlInfoDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface ShortUrlInfoApi {

    /**
     * 分页查询题目列表
     */
    @PostMapping("/short/getLongUrl")
    RestResponse<ShortUrlInfoDTO> getLongUrl(@RequestParam String shortUrl) throws BusinessException;

}