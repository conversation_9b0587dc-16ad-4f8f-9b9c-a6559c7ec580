package com.dbj.classpal.books.client.dto.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 试卷结果DTO
 */
@Data
@Schema(description = "试卷结果DTO")
public class PaperResultApiDTO implements Serializable {

    @Schema(description = "题目结果列表")
    private List<QuestionResultApiDTO> questionResults;

    @Schema(description = "正确题目数量")
    private Integer correctCount;

    @Schema(description = "错误题目数量")
    private Integer wrongCount;
} 