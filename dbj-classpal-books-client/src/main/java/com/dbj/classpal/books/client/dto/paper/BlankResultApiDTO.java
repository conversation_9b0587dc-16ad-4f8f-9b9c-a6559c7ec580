package com.dbj.classpal.books.client.dto.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 填空题结果DTO
 */
@Data
@Schema(description = "填空题结果DTO")
public class BlankResultApiDTO implements Serializable {

    @Schema(description = "区域ID")
    private Integer blankAreaId;

    @Schema(description = "题目ID")
    private Integer questionId;

    @Schema(description = "空位索引")
    private Integer blankIndex;

    /**
     * 用户答案
     */
    @Schema(description = "用户答案")
    private String userAnswer;

    /**
     * 用户选择的答案ID
     */
    @Schema(description = "用户选择的答案ID")
    private String userAnswerIds;

    /**
     * 正确答案
     */
    @Schema(description = "正确答案")
    private String correctAnswer;

    /**
     * 正确答案ID
     */
    @Schema(description = "正确答案ID")
    private String correctAnswerIds;

    @Schema(description = "结果：1-正确，0-错误")
    private Integer result;

    @Schema(description = "答案选项列表")
    private List<QuestionAnswerApiDTO> options;
} 