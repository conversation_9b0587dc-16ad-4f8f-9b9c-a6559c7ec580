package com.dbj.classpal.books.client.dto.books;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 图书卷册赠册表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="BooksRankInfo对象", description="图书卷册赠册表")
public class BooksRankInfoDetailDTO implements Serializable {





    @Schema(description = "名称")
    private Integer id;

    @Schema(description =  "卷册名称")
    private String productItemName;

    @Schema(description = "新印码")
    private String newPrintCodeUrl;

    @Schema(description = "H5链接")
    private String h5PageUrl;

    @Schema(description = "强跳链接")
    private String forcePromotionUrl;


}
