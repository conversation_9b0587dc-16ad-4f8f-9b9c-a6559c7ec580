package com.dbj.classpal.books.client.dto.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 预置提示音表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AudioGlobalConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "音频简介id")
    private Integer audioIntroId;

    @Schema(description = "全局配置类型：1 背景音 2 特效")
    private Integer type;

    @Schema(description = "背景音类型：1 预置 2 自定义")
    private Integer audioType;

    @Schema(description = "全局背景音id")
    private Integer audioBackgroundId;

    @Schema(description = "全局特效字典code")
    private String dictEffectsCode;

    @Schema(description = "音量")
    private Integer volume;

    @Schema(description = "模式：1 循环播放 2 播放一次")
    private Integer model;

}
