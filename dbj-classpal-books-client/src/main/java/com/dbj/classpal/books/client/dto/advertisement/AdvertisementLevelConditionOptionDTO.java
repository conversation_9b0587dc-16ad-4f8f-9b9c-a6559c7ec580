
package com.dbj.classpal.books.client.dto.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;

/**
 * 广告层级条件选项DTO
 * <AUTHOR> <PERSON>
 * @since 2025-04-21 16:52
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Tag(name="广告层级条件选项DTO", description="广告层级条件选项DTO")
public class AdvertisementLevelConditionOptionDTO implements Serializable  {

	/**
	 * 广告层级条件选项关联表id
	 */
	@Schema(description = "广告层级条件选项关联表id")
	private Integer id;
	/**
	 * 广告id
	 */
	@Schema(description = "广告id")
	private Integer advertisementId;
	/**
	 * 广告条件id
	 */
	@Schema(description = "广告条件id")
	private Integer advertisementLevelConditionId;
	/**
	 * 选项类型 1-年级 3-关系 4-图书类别  6-指定图书  11-属地省级 12-属地市级 13-属地分级标签
	 */
	@Schema(description = "选项类型 1-年级 3-关系 4-图书类别 6-指定图书 11-属地省级 12-属地市级 13-属地分级标签")
	private Integer type;
	/**
	 * 选项值
	 */
	@Schema(description = "选项值")
	private String optionValue;
	/**
	 * 选项名
	 */
	@Schema(description = "选项名")
	private String optionName;
	/**
	 * 最小数量
	 */
	@Schema(description = "最小数量")
	private Integer minQty;
	/**
	 * 最大数量
	 */
	@Schema(description = "最大数量")
	private Integer maxQty;

}
