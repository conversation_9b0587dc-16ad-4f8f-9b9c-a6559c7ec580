package com.dbj.classpal.books.client.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;

import java.io.Serializable;

/**
 * 题目分页查询参数
 */
@Data
@Schema(description = "题目分页查询参数")
public class QuestionPageApiBO  implements Serializable {

    @Schema(description = "学科ID")
    private Integer subjectId;

    @Schema(description = "分类ID")
    @NotNull(message = "分类ID不能为空")
    private Integer categoryId;

    @Schema(description = "题目类型")
    private Integer type;

    @Schema(description = "题目标题")
    private String title;
} 