package com.dbj.classpal.books.client.bo.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 预置提示音表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AudioHintMusicUpdBO implements Serializable {

    @NotNull(message = "主键id不能为空")
    @Schema(description = "主键id", requiredMode = RequiredMode.REQUIRED)
    private Integer id;

    @NotEmpty(message = "提示音名称不能为空")
    @Schema(description = "提示音名称", requiredMode = RequiredMode.REQUIRED)
    private String name;

    @NotNull(message = "提示音类型不能为空")
    @Schema(description = "提示音类型：1 预置提示音 2 预置背景音", requiredMode = RequiredMode.REQUIRED)
    private Integer type;

    @NotNull(message = "素材id不能为空")
    @Schema(description = "素材id", requiredMode = RequiredMode.REQUIRED)
    private Integer originMaterialId;

    @NotEmpty(message = "素材不能为空")
    @Schema(description = "素材文件", requiredMode = RequiredMode.REQUIRED)
    private String materialUrl;

    @Schema(description = "素材名称")
    private String materialName;

    @Schema(description = "素材icon")
    private String materialIcon;

    @Schema(description = "资源时长")
    private String materialDuration;

    @Schema(description = "排序权重")
    private Integer weight;

}
