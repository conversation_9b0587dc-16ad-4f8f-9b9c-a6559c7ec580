package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 点读书目录查询API BO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingMenuQueryApiBO", description = "点读书目录查询API BO")
public class PointReadingMenuQueryApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "目录名称")
    private String name;

    @Schema(description = "所属点读书ID")
    private Integer bookId;

    @Schema(description = "父级目录ID")
    private Integer parentId;

    @Schema(description = "目录层级")
    private Integer level;

    @Schema(description = "状态：0-停用 1-正常")
    private Integer status;
}
