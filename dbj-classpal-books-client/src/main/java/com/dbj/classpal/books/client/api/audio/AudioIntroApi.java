package com.dbj.classpal.books.client.api.audio;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.audio.*;
import com.dbj.classpal.books.client.dto.audio.AudioClassifyDTO;
import com.dbj.classpal.books.client.dto.audio.AudioContextInfoListDTO;
import com.dbj.classpal.books.client.dto.audio.AudioIntroDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/30 10:55
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}", path = "/api/classpal/books/${spring.application.version}")
public interface AudioIntroApi {

    @PostMapping("/audio/intro/save")
    RestResponse<Boolean> save(@RequestBody AudioIntroSaveBO bo) throws BusinessException;

    @PostMapping("/audio/intro/list")
    RestResponse<Page<AudioIntroDTO>> page(@RequestBody PageInfo<AudioIntroQueryBO> page) throws BusinessException;

    @PostMapping("/audio/intro/remove")
    RestResponse<Boolean> remove(@RequestBody AudioIntroDelBO bo) throws BusinessException;

    @PostMapping("/audio/intro/move")
    RestResponse<Boolean> move(@RequestBody AudioIntroMoveBO bo) throws BusinessException;

    @PostMapping("/audio/intro/copy")
    RestResponse<Boolean> copy(@RequestBody AudioIntroCopyBO bo) throws BusinessException;
    /**
     * 查询音频简介详情
     * @param bo
     * @return
     * @throws BusinessException
     */
    @PostMapping("/audio/intro/getDetails")
    RestResponse<AudioIntroDTO> getDetails(@RequestBody AudioIntroIdBO bo) throws BusinessException;
}
