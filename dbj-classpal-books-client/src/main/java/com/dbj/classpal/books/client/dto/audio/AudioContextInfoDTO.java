package com.dbj.classpal.books.client.dto.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
/**
 * 音频文本详情
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AudioContextInfoDTO {

    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "音频简介id")
    private Integer audioIntroId;

    @Schema(description = "tts格式音频文本")
    private String text;

    @Schema(description = "html格式音频文本")
    private String htmlText;

    @Schema(description = "发音人id")
    private Integer audioSpeakerId;

    @Schema(description = "发音人")
    private AudioSpeakerDTO speakerDTO;

    @Schema(description = "情感：neutral（中性）、happy（开心）、angry（生气）、sad（悲伤）、fear（害怕）、hate（憎恨）、surprise（惊讶）、arousal（激动）、serious（严肃）、disgust（厌恶）、jealousy（嫉妒）、embarrassed（尴尬）、frustrated（沮丧）、affectionate（深情）、gentle（温柔）、newscast（播报）、customer-service（客服）、story（小说）、living（直播）")
    private String emotion;

    @Schema(description = "音量，取值范围：0~100")
    @NotNull(message = "音量不能为空")
    private Integer volume;

    @Schema(description = "语速，取值范围：-500~500")
    @NotNull(message = "语速不能为空")
    private Integer speechRate;

    @Schema(description = "语调，取值范围：-500~500")
    @NotNull(message = "语调不能为空")
    private Integer pitchRate;

    @Schema(description = "情绪强度，数值范围 [0.01,2.0]")
    private BigDecimal intensity;

    @Schema(description = "阿里云语音合成的任务id")
    private String taskId;

    @Schema(description = "版本号")
    private Integer version;

    @Schema(description = "排序")
    private Integer orderNum;

    @Schema(description = "预置提示音id，多个用英文逗号隔开")
    private String audioHintMusicIds;
}
