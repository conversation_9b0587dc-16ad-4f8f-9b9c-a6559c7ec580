package com.dbj.classpal.books.client.bo.pinyin;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 汉语拼音DelBO
 * <AUTHOR> <PERSON>
 * @since 2025-05-20 09:00
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Tag(name="汉语拼音DelBO", description="汉语拼音DelBO")
public class PinyinDelBO implements Serializable {

    /** 拼音分类id */
    @NotNull
    @Schema(description = "拼音分类id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer pinyinClassifyId;

    @Schema(description = "主键ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "主键ID列表不能为空")
    private List<Integer> ids;

}
