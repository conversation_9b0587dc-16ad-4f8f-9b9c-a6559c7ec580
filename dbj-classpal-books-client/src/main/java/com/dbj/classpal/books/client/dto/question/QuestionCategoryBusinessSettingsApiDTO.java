package com.dbj.classpal.books.client.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 题库业务设置API DTO
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "题库业务设置API DTO")
public class QuestionCategoryBusinessSettingsApiDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 业务ID
     */
    @Schema(description = "业务ID")
    private Integer businessId;

    /**
     * 业务类型（1-学习模块答题 2-图书书内码答题 3-点读答题）
     */
    @Schema(description = "业务类型（1-学习模块答题 2-图书书内码答题 3-点读答题）")
    private Integer businessType;

    /**
     * 业务类型描述
     */
    @Schema(description = "业务类型描述")
    private String businessTypeDesc;

    /**
     * 关联的题库分类列表
     */
    @Schema(description = "关联的题库分类列表")
    private List<QuestionCategoryBusinessRefApiDTO> questionCategories;

    /**
     * 出题方式
     */
    @Schema(description = "出题方式")
    private Integer questionMethod;

    /**
     * 题目数量
     */
    @Schema(description = "题目数量")
    private Integer questionNum;

    /**
     * 状态（1-启用 0-禁用）
     */
    @Schema(description = "状态（1-启用 0-禁用）")
    private Integer status;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
