package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 章节热点区域批量保存API BO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingPageHotspotSaveApiBO", description = "章节热点区域批量保存API BO")
public class PointReadingChapterHotspotSaveApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "所属章节ID")
    @NotNull(message = "所属章节ID不能为空")
    private Integer chapterId;

    @Schema(description = "热点区域列表")
    @NotEmpty(message = "热点区域列表不能为空")
    @Valid
    private List<PointReadingHotspotSaveApiBO> hotspots;
}
