package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Data;

/**
 * 点读书热点区域排序API BO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingHotspotSortApiBO", description = "点读书热点区域排序API BO")
public class PointReadingHotspotSortApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "热点ID")
    @NotNull(message = "热点ID不能为空")
    private Integer id;

    @Schema(description = "排序号")
    @NotNull(message = "排序号不能为空")
    private Integer sortNum;
}
