package com.dbj.classpal.books.client.dto.material;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import org.apache.poi.hpsf.Decimal;

import java.io.Serial;
import java.io.Serializable;

@Tag(name = "素材中心媒体文件DTO")
@Data
public class AppCommonMediaDTO implements Serializable {

    /**
     * 业务id
     */
    @Schema(description = "业务id")
    private Integer businessId;
    /**
     * 素材库ID
     */
    @Schema(description = "素材库ID")
    private Integer materialId;
    /**
     * 素材库地址
     */
    @Schema(description = "素材库地址")
    private String materialPath;
    /**
     * 素材名称
     */
    @Schema(description = "素材名称")
    private String materialName;
    /**
     * 资源类型 1文件夹 2图片  3音频 4视频 5文档 6压缩包
     */
    @Schema(description = "资源类型 1文件夹 2图片  3音频 4视频 5文档 6压缩包")
    private Integer materialType;
    /**
     * 后缀名
     */
    @Schema(description = "后缀名")
    private String materialExtension;
    /**
     * 资源大小(KB)
     */
    @Schema(description = "资源大小(KB)")
    private Decimal materialSize;
    /**
     * 资源时长(s）
     */
    @Schema(description = "资源时长(s）")
    private Integer materialDuration;
    /**
     * 原始资源oss路径
     */
    @Schema(description = "原始资源oss路径")
    private String materialOriginUrl;
    /**
     * 字幕
     */
    @Schema(description = "字幕")
    private String materialCaption;
    /**
     * 资源图标
     */
    @Schema(description = "资源图标")
    private String materialIcon;

    @Schema(description = "排序")
    private Integer orderNum;
}
