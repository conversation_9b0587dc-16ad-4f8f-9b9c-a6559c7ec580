package com.dbj.classpal.books.client.bo.album;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: CommonIdBO
 * Date:     2025-07-10 10:43:02
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class AppAlbumElementsIdsApiBO implements Serializable {
    @Schema(description = "主键ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "主键ID列表不能为空")
    private Set<Integer> ids;
}
