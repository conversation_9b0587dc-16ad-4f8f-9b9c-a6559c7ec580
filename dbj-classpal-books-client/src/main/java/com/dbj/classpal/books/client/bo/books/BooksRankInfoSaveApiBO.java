package com.dbj.classpal.books.client.bo.books;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 图书卷册赠册表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="册书赠册保存对象", description="图书卷册赠册表")
public class BooksRankInfoSaveApiBO implements Serializable {



    @Schema(description =  "封面格式 : 横板 vertical 竖版 vertical")
    private String coverType;

    @Schema(description =  "产品id")
    private Integer bookId;

    @Schema(description =  "是否赠册 1-卷册 2-赠册")
    private Integer type;

    @Schema(description =  "序号")
    @NotNull(message = "序号不能为空")
    private Integer serialNo;

    @Schema(description =  "卷册名称")
    @NotEmpty(message = "序号不能为空")
    private String productItemName;

    @Schema(description =  "封面url")
    private String picUrl;

    @Schema(description =  "物流编码")
    private String logisticsCode;

    @Schema(description =  "唯一编码")
    private String code;

    @Schema(description =  "配套类容是否启用 1-是 0-否")
    private Integer status;


}
