package com.dbj.classpal.books.client.dto.album;

import com.dbj.classpal.books.client.dto.material.AppMaterialBusinessRefMaterialQueryApiDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumElementsBusinessRefQueryApiDTO
 * Date:     2025-04-10 09:31:28
 * Description: 表名： ,描述： 表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@Tag(name = "专辑关联业务查询查询DTO")
public class AppAlbumElementsBusinessRefMaterialQueryApiDTO implements Serializable {
    
    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "专辑类型 1音频 2视频")
    private Integer albumType;

    @Schema(description = "专辑封面")
    private String albumCover;

    @Schema(description = "专辑标题")
    private String albumTitle;

    @Schema(description = "专辑简介")
    private String albumRemark;

    @Schema(description = "资源总时长")
    private Integer materialDurations;

    @Schema(description = "专辑关联资源列表")
    private List<AppMaterialBusinessRefMaterialQueryApiDTO>refBusinessList;
}
