package com.dbj.classpal.books.client.api.album;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.album.*;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.dto.album.AppAlbumElementsQueryApiDTO;
import com.dbj.classpal.books.client.dto.album.AppAlbumMenusTreeApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumApi
 * Date:     2025-04-15 11:13:46
 * Description: 表名： ,描述： 表
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AppAlbumMenusApi {

    /**
     * 查询专辑分类结构树
     * @param bo
     * @return
     */
    @PostMapping("/albumMenus/getAllAlbumMenusTree")
    RestResponse<AppAlbumMenusTreeApiDTO> getAllAlbumMenusTree(@RequestBody AppAlbumMenusQueryApiBO bo);

    /**
     * 重命名分类
     * @return
     */
    @PostMapping("/albumMenus/reNameAlbumMenus")
    RestResponse<Boolean> reNameAlbumMenus(@RequestBody @Valid AppAlbumMenusReNameApiBO bo) throws BusinessException;

    /**
     * 新增专辑分类
     * @return
     */
    @PostMapping("/albumMenus/saveAlbumMenus")
    RestResponse<Boolean> saveAlbumMenus(@RequestBody @Valid AppAlbumMenusSaveApiBO bo) throws BusinessException;


    /**
     * 删除专辑分类
     * @return
     */
    @PostMapping("/albumMenus/deleteAlbumMenus")
    RestResponse<Boolean> deleteAlbumMenus(@RequestBody @Valid AppAlbumMenusDeleteApiBO bo) throws BusinessException;


    /**
     * 重置专辑分类排序
     * @return
     */
    @PostMapping("/albumMenus/resetAlbumMenusOrderNum")
    RestResponse<Boolean> resetAlbumMenusOrderNum(@RequestBody @Valid AppAlbumMenusBatchMoveApiBO bo) throws BusinessException;
}
