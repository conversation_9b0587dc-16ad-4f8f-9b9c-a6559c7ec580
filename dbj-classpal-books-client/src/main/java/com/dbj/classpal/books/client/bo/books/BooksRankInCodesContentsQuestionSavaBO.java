package com.dbj.classpal.books.client.bo.books;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.books.client.bo.question.QuestionCategoryBusinessRefSaveApiBO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 图书书内码题库表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("books_rank_in_codes_contents_question")
@Tag(name = "BooksRankInCodesContentsQuestion对象", description="图书书内码题库表")
public class BooksRankInCodesContentsQuestionSavaBO implements Serializable {




    @Schema(description = "书内码内容目录id")
    @NotEmpty(message = "书内码内容目录id不能为空")
    private Integer inCodesContentsId;

    @Schema(description = "封面url")
    private String url;

    @Schema(description = "出题方式")
    @NotEmpty(message = "出题方式不能为空")
    private String questionMethod;

    @Schema(description = "题目数量")
    @NotEmpty(message = "题目数量不能为空")
    private Integer questionNum;

    @Schema(description = "关联题库")
    @NotEmpty(message = "关联题库不能为空")
    List<QuestionCategoryBusinessRefSaveApiBO> questionCategoryBusinessRefApiBOList;

}
