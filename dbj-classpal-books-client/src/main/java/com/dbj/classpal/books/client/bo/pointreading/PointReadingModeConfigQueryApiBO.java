package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 点读书模式配置查询API BO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingModeConfigQueryApiBO", description = "点读书模式配置查询API BO")
public class PointReadingModeConfigQueryApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "显示名称")
    private String displayName;

    @Schema(description = "点读书ID")
    private Integer bookId;

    @Schema(description = "模式配置字典ID")
    private Integer configDictId;

    @Schema(description = "处理状态：10-处理中 20-处理完成 30-处理失败")
    private Integer processStatus;

    @Schema(description = "状态：0-停用 1-正常")
    private Integer status;
}
