package com.dbj.classpal.books.client.bo.evaluation;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationQueryBO
 * Date:     2025-04-15 13:35:46
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class AdminEvaluationNodeSortApiBO implements Serializable {

    @Schema(description = "评测表id",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "评测表id不能为空")
    private Integer id;

    @Schema(description = "评测项id",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "评测项id不能为空")
    private Integer evaluationNodeId;

    @Schema(description = "目标评测项id",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "目标评测项id不能为空")
    private Integer aimEvaluationNodeId;

    @Schema(description = "目标评测项位置 -1前面 1后面")
    @NotNull(message = "目标评测项位置不能为空")
    private Integer order;

}
