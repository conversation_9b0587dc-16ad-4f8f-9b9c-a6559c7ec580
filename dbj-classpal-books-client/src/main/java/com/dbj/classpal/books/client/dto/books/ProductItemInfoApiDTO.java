
package com.dbj.classpal.books.client.dto.books;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;

/**
* Copyright (C), 2017-2020, com.dbj
* FileName: ProductItemInfo
* Date:     2024-2-3 16:39:14
* Description: 描述：产品卷册赠册表
* <AUTHOR>
*/
@Schema(description = "Copyright (C), 2017-2020, com.dbj FileName: ProductItemInfo Date:     2024-2-3 16:39:14 Description: 描述：产品卷册赠册表")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class ProductItemInfoApiDTO implements Serializable  {

	/**
	 * 册书id
	 */
	@Schema(description = "册书id")
	private Integer id;
	/**
	 * 产品id
	 */
	@Schema(description = "产品id")
	private Integer productInfoId;
	/**
	 * 册书类型 1-卷册 2-赠册
	 */
	@Schema(description = "册书类型 1-卷册 2-赠册")
	private Integer type;
	/**
	 * 序号
	 */
	@Schema(description = "序号")
	private Integer serialNo;
	/**
	 * 卷册名称
	 */
	@Schema(description = "卷册名称")
	private String productItemName;
	/**
	 * 封面url
	 */
	@Schema(description = "封面url")
	private String picUrl;
	/**
	 * 物流编码
	 */
	@Schema(description = "物流编码")
	private String logisticsCode;
	/**
	 * 唯一编码
	 */
	@Schema(description = "唯一编码")
	private String code;

}
