package com.dbj.classpal.books.client.api.studycenter;

import com.dbj.classpal.books.client.bo.studycenter.*;
import com.dbj.classpal.books.client.dto.studycenter.*;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.Operation;

import java.util.List;

@FeignClient(name = "dbj-classpal-books-api-${spring.application.version}", path = "/api/classpal/books/${spring.application.version}")
public interface StudyCenterApi{

    @PostMapping("/studyCenter/module/create")
    RestResponse<Boolean> createModule(@Validated @RequestBody AppStudyModuleCreateApiBO bo) throws BusinessException;

    @PostMapping("/studyCenter/module/update")
    RestResponse<Boolean> updateModule(@Validated @RequestBody AppStudyModuleUpdateApiBO bo) throws BusinessException;

    @PostMapping("/studyCenter/module/delete")
    RestResponse<Boolean> deleteModule(@Validated @RequestBody AppStudyModuleIdsApiBO idsApiBO) throws BusinessException;

    @PostMapping("/studyCenter/module/page")
    RestResponse<Page<AppStudyModuleListApiDTO>> pageModule(@Validated @RequestBody PageInfo<AppStudyModuleQueryPageApiBO> pageInfo) throws BusinessException;

    @PostMapping("/studyCenter/module/detail")
    RestResponse<AppStudyModuleDetailApiDTO> getModuleDetail(@Validated @RequestBody AppStudyModuleIdApiBO idsApiBO) throws BusinessException;

    @PostMapping("/studyCenter/module/home")
    RestResponse<List<StudyCenterCategoryApiDTO>> listHome(@Validated @RequestBody StudyCenterModuleListQueryApiBO queryBO) throws BusinessException;

    @PostMapping("/studyCenter/module/recent")
    RestResponse<List<AppStudyModuleProgressListApiDTO>> listRecentStudy(@Validated @RequestBody StudyCenterModuleListQueryApiBO queryBO) throws BusinessException;

    @PostMapping("/studyCenter/module/recent/record")
    RestResponse<Boolean> recordStudy(@Validated @RequestBody StudyCenterModuleStudyApiBO queryBO) throws BusinessException;

    @PostMapping("/studyCenter/module/recent/delete")
    RestResponse<Boolean> deleteStudyRecord(@Validated @RequestBody StudyCenterModuleStudyApiBO queryBO) throws BusinessException;


    @PostMapping("/studyCenter/module/batch-publish")
    RestResponse<Boolean> batchPublish(@Validated @RequestBody AppStudyModuleIdsApiBO idsApiBO) throws BusinessException;

    @PostMapping("/studyCenter/module/batch-unpublish")
    RestResponse<Boolean> batchUnpublish(@Validated @RequestBody AppStudyModuleIdsApiBO idsApiBO) throws BusinessException;

    @PostMapping("/studyCenter/module/batch-show")
    RestResponse<Boolean> batchShow(@Validated @RequestBody AppStudyModuleIdsApiBO idsApiBO) throws BusinessException;

    @PostMapping("/studyCenter/module/batch-hide")
    RestResponse<Boolean> batchHide(@Validated @RequestBody AppStudyModuleIdsApiBO idsApiBO) throws BusinessException;
}