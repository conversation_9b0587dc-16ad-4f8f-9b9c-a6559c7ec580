package com.dbj.classpal.books.client.bo.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/6/30 10:55
 */
@Data
public class AudioIntroQueryBO {

    @Schema(description = "音频名称")
    private String name;

    @NotNull(message = "分类id不能为空")
    @Schema(description = "分类id", requiredMode = RequiredMode.REQUIRED)
    private Integer audioClassifyId;
}
