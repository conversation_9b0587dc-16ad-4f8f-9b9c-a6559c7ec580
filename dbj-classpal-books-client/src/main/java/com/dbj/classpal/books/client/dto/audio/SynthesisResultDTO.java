package com.dbj.classpal.books.client.dto.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SynthesisResultDTO {

    @Schema(description = "音频简介id")
    private Integer audioIntroId;

    @Schema(description = "合成状态：0 待合成 1 合成中 2 已合成（成功）3 合成失败 4 取消")
    private Integer status;




}
