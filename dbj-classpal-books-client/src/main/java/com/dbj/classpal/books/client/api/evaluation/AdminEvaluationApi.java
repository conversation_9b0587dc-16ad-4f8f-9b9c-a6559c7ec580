package com.dbj.classpal.books.client.api.evaluation;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.evaluation.*;
import com.dbj.classpal.books.client.dto.evaluation.AdminEvaluationDetailQueryApiDTO;
import com.dbj.classpal.books.client.dto.evaluation.AdminEvaluationQueryApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AdminEvaluationApi {

    /**
     * 分页查询评测表列表
     * @param pageRequest
     * @return
     */
    @PostMapping("/evaluation/pageInfo")
    RestResponse<Page<AdminEvaluationQueryApiDTO>> pageInfo(@RequestBody PageInfo<AdminEvaluationQueryApiBO> pageRequest);

    /**
     * 获取评测表列表
     * @return
     * @throws BusinessException
     */
    @PostMapping("/evaluation/list")
    RestResponse<List<AdminEvaluationQueryApiDTO>> list() throws BusinessException;

    /**
     * 新增评测表
     * @param bo
     * @return
     */
    @PostMapping("/evaluation/saveAppEvaluation")
    RestResponse<Boolean> saveAppEvaluation(@RequestBody @Valid AdminEvaluationSaveApiBO bo);


    /**
     * 删除评测表
     * @param bo
     * @return
     */
    @PostMapping("/evaluation/deleteAppEvaluation")
    RestResponse<Boolean> deleteAppEvaluation(@RequestBody @Valid CommonIdsApiBO bo) throws BusinessException;


    /**
     * 查询评测表详情
     * @param bo
     * @return
     */
    @PostMapping("/evaluation/getDetail")
    RestResponse<AdminEvaluationDetailQueryApiDTO> getDetail(@RequestBody CommonIdApiBO bo);


    /**
     * 修改评测表名称
     * @param bo
     * @return
     */
    @PostMapping("/evaluation/reName")
    RestResponse<Boolean> reName(@RequestBody @Valid AdminEvaluationReNameApiBO bo);


    /**
     * 修改评测表封面
     * @param bo
     * @return
     */
    @PostMapping("/evaluation/reCover")
    RestResponse<Boolean> reCover(@RequestBody @Valid AdminEvaluationReCoverApiBO bo);

    /**
     * 修改评测表简介
     * @param bo
     * @return
     */
    @PostMapping("/evaluation/reRemark")
    RestResponse<Boolean> reRemark(@RequestBody @Valid AdminEvaluationReRemarkApiBO bo);



    /**
     * 批量修改上下架状态
     * @param bo
     * @return
     */
    @PostMapping("/evaluation/updateStatus")
    RestResponse<Boolean> updateStatus(@RequestBody @Valid AdminEvaluationUpdateStatusApiBO bo);


    /**
     * 批量修改隐藏状态
     * @param bo
     * @return
     */
    @PostMapping("/evaluation/updateVisible")
    RestResponse<Boolean> updateVisible(@RequestBody @Valid AdminEvaluationUpdateVisibleApiBO bo);

    /**
     * 批量启用/禁用评测表
     * @param bo
     * @return
     */
    @PostMapping("/evaluation/updateOpen")
    RestResponse<Boolean> updateOpen(@RequestBody @Valid AdminEvaluationUpdateOpenApiBO bo) throws BusinessException;
}
