package com.dbj.classpal.books.client.api.audio;


import com.dbj.classpal.books.client.bo.audio.*;
import com.dbj.classpal.books.client.dto.audio.AudioBackgroundDTO;
import com.dbj.classpal.books.client.dto.audio.AudioGlobalConfigDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 全局配置
 * <AUTHOR>
 * @since 2025-06-27
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}", path = "/api/classpal/books/${spring.application.version}")
public interface AudioGlobalConfigApi {

    /**
     * 查询全局配置
     * @param bo
     * @return
     * @throws BusinessException
     */
    @PostMapping("/globalConfig/getGlobalConfig")
    RestResponse<List<AudioGlobalConfigDTO>> getGlobalConfig(AudioIntroIdBO bo) throws BusinessException;
    /**
     * 保存全局配置
     * @param bo
     * @return
     * @throws BusinessException
     */
    @PostMapping("/globalConfig/saveGlobalConfig")
    RestResponse<Integer> saveGlobalConfig(List<AudioGlobalConfigAddBO> bo) throws BusinessException;

}
