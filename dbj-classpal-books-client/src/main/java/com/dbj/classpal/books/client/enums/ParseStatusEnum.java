package com.dbj.classpal.books.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 点读书解析状态枚举
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Getter
@AllArgsConstructor
public enum ParseStatusEnum {

    /**
     * 待解析
     */
    PENDING(10, "待解析"),

    /**
     * 解析中
     */
    PARSING(20, "解析中"),

    /**
     * 已解析
     */
    PARSED(30, "已解析"),

    /**
     * 解析失败
     */
    FAILED(40, "解析失败");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举值
     */
    public static ParseStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ParseStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据状态码获取描述
     *
     * @param code 状态码
     * @return 状态描述
     */
    public static String getDescByCode(Integer code) {
        ParseStatusEnum status = getByCode(code);
        return status != null ? status.getDesc() : "未知状态";
    }
}
