package com.dbj.classpal.books.client.dto.studycenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "答题设置-详情DTO")
public class AppStudyModuleQuestionExtDetailApiDTO {
    @Schema(description = "主键ID")
    private Integer questionExtId;

    @Schema(description = "模块ID")
    private Integer moduleId;

    @Schema(description = "出题方式")
    private Integer questionMethod;

    @Schema(description = "题目数量")
    private Integer questionNum;

    @Schema(description = "是否启用 1-是 0-否")
    private Integer status;
    @Schema(description = "关联题库数据")
    List<AppStudyModuleQuestionRefApiDTO> question;

} 