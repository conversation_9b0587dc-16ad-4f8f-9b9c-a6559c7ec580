package com.dbj.classpal.books.client.dto.poem.app;

import com.baomidou.mybatisplus.annotation.TableField;
import com.dbj.classpal.books.client.dto.material.AppCommonMediaDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className AncientPoemReciteAppUserAssessmentScorePageDTO
 * @description
 * @date 2025-05-28 11:13
 **/
@Data
public class AncientPoemReciteAppUserAssessmentScoreDetailDTO implements Serializable {

    @Schema(description = "ID")
    private Integer id;

    @Schema(description = "古诗文id")
    private Integer ancientPoemId;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "标题拼音")
    private String titlePinyin;

    @Schema(description = "作者")
    private String author;

    @Schema(description = "所属朝代")
    private String dynasty;

    @Schema(description = "年级")
    private String grade;

    @Schema(description = "是否课外内容 0-课内 1-课外")
    private Integer isOut;
    @Schema(description = "校验文本")
    private String verifyText;

    @Schema(description = "古诗文释义")
    private String interpretation;

    @Schema(description = "古诗文赏析")
    private String appreciation;

    @Schema(description = "原文音频")
    AppCommonMediaDTO originalAudioMedia;

    @Schema(description = "讲解音频")
    AppCommonMediaDTO explanationAudioMedia;

    @Schema(description = "总分（0-100）")
    private BigDecimal totalScore;

    @Schema(description = "流利度得分（0-20）")
    @TableField("fluency_score")
    private BigDecimal fluencyScore;

    @Schema(description = "完整度得分（0-20）")
    @TableField("completeness_score")
    private BigDecimal completenessScore;

    @Schema(description = "韵律得分（0-20）")
    @TableField("rhythm_score")
    private BigDecimal rhythmScore;

    @Schema(description = "声调得分（0-20）")
    @TableField("tone_score")
    private BigDecimal toneScore;

    @Schema(description = "发音得分（0-20）")
    @TableField("pronunciation_score")
    private BigDecimal pronunciationScore;

    @Schema(description = "评测时间 0 否 1是")
    private Integer isAssessment;

     @Schema(description = "评语")
     private String comment;

    @Schema(description = "内容")
    private String content;
    @Schema(description = "古诗文简介")
    private String introduction;
    @Schema(description = "背景图路径")
    private String backgroundUrl;
}
