package com.dbj.classpal.books.client.api.pointreading;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.pointreading.*;
import com.dbj.classpal.books.client.dto.pointreading.PointReadingBookApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 点读书API接口
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}", path = "/api/classpal/books/${spring.application.version}")
public interface PointReadingBookApi {

    /**
     * 分页查询点读书列表
     *
     * @param pageRequest 分页查询参数
     * @return 点读书分页数据
     */
    @PostMapping("/point-reading/book/page")
    RestResponse<Page<PointReadingBookApiDTO>> page(@RequestBody @Validated PageInfo<PointReadingBookQueryApiBO> pageRequest) throws BusinessException;

    /**
     * 查询点读书详情
     *
     * @param idBO 点读书ID参数
     * @return 点读书详情数据
     */
    @PostMapping("/point-reading/book/detail")
    RestResponse<PointReadingBookApiDTO> detail(@RequestBody @Validated PointReadingBookIdApiBO idBO) throws BusinessException;

    /**
     * 新增点读书
     *
     * @param saveBO 点读书保存参数
     * @return 新增点读书ID
     */
    @PostMapping("/point-reading/book/save")
    RestResponse<Integer> save(@RequestBody @Validated PointReadingBookSaveApiBO saveBO) throws BusinessException;

    /**
     * 更新点读书
     *
     * @param updateBO 点读书更新参数
     * @return 更新结果
     */
    @PostMapping("/point-reading/book/update")
    RestResponse<Boolean> update(@RequestBody @Validated PointReadingBookUpdateApiBO updateBO) throws BusinessException;

    /**
     * 删除点读书
     *
     * @param idBO 点读书ID参数
     * @return 删除结果
     */
    @PostMapping("/point-reading/book/delete")
    RestResponse<Boolean> delete(@RequestBody @Validated PointReadingBookIdApiBO idBO) throws BusinessException;

    /**
     * 批量删除点读书
     *
     * @param batchBO 批量操作参数
     * @return 批量删除结果
     */
    @PostMapping("/point-reading/book/delete/batch")
    RestResponse<Boolean> deleteBatch(@RequestBody @Validated PointReadingBookBatchApiBO batchBO) throws BusinessException;

    /**
     * 批量启用点读书
     *
     * @param batchBO 批量操作参数
     * @return 批量启用结果
     */
    @PostMapping("/point-reading/book/enable/batch")
    RestResponse<Boolean> enableBatch(@RequestBody @Validated PointReadingBookBatchApiBO batchBO) throws BusinessException;

    /**
     * 批量禁用点读书
     *
     * @param batchBO 批量操作参数
     * @return 批量禁用结果
     */
    @PostMapping("/point-reading/book/disable/batch")
    RestResponse<Boolean> disableBatch(@RequestBody @Validated PointReadingBookBatchApiBO batchBO) throws BusinessException;

    /**
     * 批量上线点读书
     *
     * @param batchBO 批量操作参数
     * @return 批量上线结果
     */
    @PostMapping("/point-reading/book/launch/batch")
    RestResponse<Boolean> launchBatch(@RequestBody @Validated PointReadingBookBatchApiBO batchBO) throws BusinessException;

    /**
     * 批量下线点读书
     *
     * @param batchBO 批量操作参数
     * @return 批量下线结果
     */
    @PostMapping("/point-reading/book/offline/batch")
    RestResponse<Boolean> offlineBatch(@RequestBody @Validated PointReadingBookBatchApiBO batchBO) throws BusinessException;

    /**
     * 根据MD5查询点读书
     *
     * @param md5BO MD5查询参数
     * @return 点读书信息
     */
    @PostMapping("/point-reading/book/get-by-md5")
    RestResponse<PointReadingBookApiDTO> getByMd5(@RequestBody @Validated PointReadingBookMd5ApiBO md5BO) throws BusinessException;

    /**
     * 更新解析状态
     *
     * @param parseStatusBO 解析状态更新参数
     * @return 更新结果
     */
    @PostMapping("/point-reading/book/update-parse-status")
    RestResponse<Boolean> updateParseStatus(@RequestBody @Validated PointReadingBookParseStatusApiBO parseStatusBO) throws BusinessException;

    /**
     * 批量复制点读书
     *
     * @param copyBO 批量复制参数
     * @return 复制结果
     */
    @PostMapping("/point-reading/book/copy/batch")
    RestResponse<Boolean> copyBatch(@RequestBody @Validated PointReadingBookCopyApiBO copyBO) throws BusinessException;

    /**
     * 批量移动点读书
     *
     * @param moveBO 批量移动参数
     * @return 移动结果
     */
    @PostMapping("/point-reading/book/move/batch")
    RestResponse<Boolean> moveBatch(@RequestBody @Validated PointReadingBookMoveApiBO moveBO) throws BusinessException;
    //todo 查看引用
}
