package com.dbj.classpal.books.client.dto.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 点读书分类API DTO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Schema(name = "PointReadingCategoryApiDTO", description = "点读书分类API DTO")
public class PointReadingCategoryApiDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "分类名称")
    private String name;

    @Schema(description = "父级分类ID")
    private Integer parentId;

    @Schema(description = "是否默认分类：0-否 1-是")
    private Integer isDefault;

    @Schema(description = "排序")
    private Integer sortNum;

    @Schema(description = "状态：0-停用 1-正常")
    private Integer status;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新者")
    private String updateBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "子分类列表")
    private List<PointReadingCategoryApiDTO> children;

    @Schema(description = "子分类数量")
    private Integer childrenCount;
}
