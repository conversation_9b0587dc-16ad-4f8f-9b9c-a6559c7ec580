package com.dbj.classpal.books.client.dto.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 发音人
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AudioSpeakerDTO {

    @Schema(description = "发音人id")
    private Integer id;

    @Schema(description = "发音人名称")
    private String name;

    @Schema(description = "（阿里云）音色值")
    private String voice;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "音频模板url")
    private String audioModelUrl;

    @Schema(description = "音色类型：1 男声 2 女声 3 童声 4 多情感")
    private Integer voiceType;

    @Schema(description = "语言类型：1 中文 2 英文 3 粤语")
    private Integer language;

    @Schema(description = "类型名称")
    private String typeName;

    @Schema(description = "适用场景")
    private String usageScenarios;

    @Schema(description = "支持语言")
    private String supportedLanguages;

    @Schema(description = "是否支持儿化音, 0 否 1 是")
    private String isRhotic;

    @Schema(description = "多情感列表")
    private List<AudioEmotionClassifyConfigDTO> emotionList;
    
}
