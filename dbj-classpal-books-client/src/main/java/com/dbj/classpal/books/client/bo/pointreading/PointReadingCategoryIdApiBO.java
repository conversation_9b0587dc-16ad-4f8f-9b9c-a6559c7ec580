package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 点读书分类ID API BO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Schema(name = "PointReadingCategoryIdApiBO", description = "点读书分类ID API BO")
public class PointReadingCategoryIdApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "分类ID")
    @NotNull(message = "分类ID不能为空")
    private Integer id;
}
