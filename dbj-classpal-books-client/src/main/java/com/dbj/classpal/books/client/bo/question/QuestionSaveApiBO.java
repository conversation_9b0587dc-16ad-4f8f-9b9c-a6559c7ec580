package com.dbj.classpal.books.client.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema
@Data
public class QuestionSaveApiBO implements Serializable {

    /**
     * 分类id
     */
    @Schema(description = "分类id")
    @NotNull(message = "分类id不能为空")
    private Integer questionCategoryId;

    /**
     * 题目
     */
    @Schema(description = "题目")
    @NotNull(message = "题目不能为空")
    private String title;

    @Schema(description = "学科")
    @NotNull(message = "学科id不能为空")
    private Integer subjectId;

    /**
     * 媒体文件类型 1-图片 2-音频 3-视频
     * * @see com.dbj.classpal.books.common.enums.question.OptionTypeEnum
     */
    @Schema(description = "媒体文件类型 1-文本 2-图片,3-音频,4-视频")
    @NotNull(message = "媒体文件类型不能为空")
    private Integer mediaType;

    /**
     * 媒体文件url
     */
    @Schema(description = "媒体文件url")
    private List<QuestionMediaApiBO> mediaUrl;

    @Schema(description = "辅助识图")
    private List<QuestionRecognitionApiBO> aidedRecognitionUrl;

    /**
     * 题目类型 0-单选题 1-多选题 2-判断题 3-填空题 4-排序题，5-完形填空
     */
    @Schema(description = "题目类型 0-单选题 1-多选题 2-判断题 3-填空题 4-排序题，5-完形填空")
    @NotNull(message = "题目类型不能为空")
    private Integer type;

    /**
     * 选项类型 1-文本 2-图片,3-音频,4-视频
     */
    @Schema(description = "选项类型 1-文本 2-图片,3-音频,4-视频")
    private Integer optionType;

    /**
     * 权重
     */
    @Schema(description = "权重")
    private Integer weight;

    /**
     * 选择题答案id（多个英文逗号隔开）
     */
    @Schema(description = "选择题答案id（多个英文逗号隔开）",hidden = true)
    private String answer;

    /**
     * 解析
     */
    @Schema(description = "解析")
    private String analyzes;


    /**
     * 答案列表
     */
    @Schema(description = "答案列表")
    private List<QuestionAnswerSaveApiBO> answers;

    /**
     * 完形填空区域列表
     */
    @Schema(description = "完形填空区域列表")
    private List<QuestionBlankAreaSaveApiBO> blankAreas;
} 