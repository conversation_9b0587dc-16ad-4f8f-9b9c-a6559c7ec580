package com.dbj.classpal.books.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 点读书状态枚举
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Getter
@AllArgsConstructor
public enum StatusEnum {

    /**
     * 停用
     */
    DISABLED(0, "停用"),

    /**
     * 正常
     */
    NORMAL(1, "正常");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举值
     */
    public static StatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (StatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据状态码获取描述
     *
     * @param code 状态码
     * @return 状态描述
     */
    public static String getDescByCode(Integer code) {
        StatusEnum status = getByCode(code);
        return status != null ? status.getDesc() : "未知状态";
    }
}
