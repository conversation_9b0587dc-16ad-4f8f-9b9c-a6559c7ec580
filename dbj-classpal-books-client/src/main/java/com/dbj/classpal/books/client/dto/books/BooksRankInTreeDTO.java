package com.dbj.classpal.books.client.dto.books;

import com.dbj.classpal.books.client.dto.books.app.BooksRankInCodesContentsTreeAppDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 图书书内码分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="BooksRankInCodesContents对象", description="图书书内码分类表")
public class BooksRankInTreeDTO implements Serializable {



    @Schema(description =  "序号")
    private Integer serialNo;

    @Schema(description =  "卷册名称")
    private String productItemName;

    @Schema(description = "新印码")
    private String newPrintCodeUrl;

    @Schema(description = "H5链接")
    private String h5PageUrl;

    @Schema(description = "目录树")
    private List<BooksRankInCodesContentsTreeAppDTO> booksRankInCodesContentsTreeDTOList;

}
