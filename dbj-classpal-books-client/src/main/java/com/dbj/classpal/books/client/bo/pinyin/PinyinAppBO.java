package com.dbj.classpal.books.client.bo.pinyin;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 拼音信息AppBO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Tag(name="拼音信息AppBO", description="拼音信息AppBO")
public class PinyinAppBO implements Serializable {


    @NotNull
    @Schema(description = "拼音分类id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer classifyId;
}
