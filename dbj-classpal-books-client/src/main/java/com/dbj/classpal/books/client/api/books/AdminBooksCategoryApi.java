package com.dbj.classpal.books.client.api.books;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.books.BooksCategoryApiBO;
import com.dbj.classpal.books.client.bo.books.BooksInfoPageBO;
import com.dbj.classpal.books.client.dto.books.BooksCategoryApiDTO;
import com.dbj.classpal.books.client.dto.books.BooksInfoPageApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AdminBooksCategoryApi {

    @PostMapping("/books/category/list")
    RestResponse<List<BooksCategoryApiDTO>> list(@RequestBody BooksCategoryApiBO bookCategoryApiBO) throws BusinessException;

    @PostMapping("/books/category/sync")
    RestResponse<Boolean> sync() throws BusinessException;
}
