package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 书架ID业务参数对象
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
@Data
@Schema(description = "书架ID参数BO")
public class AppEBookshelfIdApiBO implements Serializable {

    @Schema(description = "书架ID")
    @NotNull(message = "书架ID不能为空")
    private Integer id;
} 