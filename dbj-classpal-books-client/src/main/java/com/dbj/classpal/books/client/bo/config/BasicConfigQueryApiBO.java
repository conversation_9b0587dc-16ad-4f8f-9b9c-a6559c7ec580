package com.dbj.classpal.books.client.bo.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Schema
@Data
public class BasicConfigQueryApiBO implements Serializable {


    /**
     * 配置名称
     */
    @Schema(description = "配置名称")
    private String name;
    /**
     * 配置类型
     */
    @Schema(description = "业务类型，题库question，学习模块 study-center 学科 subject")
    private String bizType;

}