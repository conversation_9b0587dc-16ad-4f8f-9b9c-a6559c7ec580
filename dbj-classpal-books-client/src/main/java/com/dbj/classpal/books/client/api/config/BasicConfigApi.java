package com.dbj.classpal.books.client.api.config;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.config.*;
import com.dbj.classpal.books.client.dto.config.BasicConfigApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface BasicConfigApi {

    /**
     * 创建配置
     */
    @PostMapping("/config/subject/create")
    RestResponse<Integer> create(@RequestBody BasicConfigApiBO apiBO) throws BusinessException;

    /**
     * 更新配置
     */
    @PostMapping("/config/config/update")
    RestResponse<Void> update(@RequestBody BasicConfigApiBO apiBO) throws BusinessException;

    /**
     * 删除配置
     */
    @PostMapping("/config/config/batch/delete")
    RestResponse<Void> batchDelete(@RequestBody BasicConfigIdsApiBO idsApiBO) throws BusinessException;

    /**
     * 获取配置详情
     */
    @GetMapping("/config/config/detail")
    RestResponse<BasicConfigApiDTO> detail(@RequestBody BasicConfigIdApiBO idApiBO);

    /**
     * 获取配置列表
     */
    @PostMapping("/config/config/page")
    RestResponse<Page<BasicConfigApiDTO>> pageList(@RequestBody PageInfo<BasicConfigQueryApiBO> queryApiBO);
    /**
     * 获取配置列表
     */
    @PostMapping("/config/config/list")
    RestResponse<List<BasicConfigApiDTO>> list(@RequestBody BasicConfigQueryApiBO queryApiBO);

    /**
     * 更新排序
     */
    @PostMapping("/config/config/update-sort")
    RestResponse<Void> updateSort(@RequestBody BasicConfigSortApiBO sortApiBO);
} 