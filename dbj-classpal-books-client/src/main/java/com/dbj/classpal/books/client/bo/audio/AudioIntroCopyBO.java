package com.dbj.classpal.books.client.bo.audio;

import cn.hutool.core.bean.copier.CopyOptions;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/6/30 14:33
 */
@Data
public class AudioIntroCopyBO {

    @NotEmpty
    @Schema(description = "id", requiredMode = RequiredMode.REQUIRED)
    private Set<Integer> ids;

    @NotNull
    @Schema(description = "复制到分类id", requiredMode = RequiredMode.REQUIRED)
    private Integer audioClassifyId;

    public static final CopyOptions COPY_OPTIONS = CopyOptions.create()
            .setIgnoreProperties("id", "createBy", "createTime", "updateBy", "updateTime", "tenantId", "isDeleted");
}
