package com.dbj.classpal.books.client.dto.books;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 图书书内码分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="BooksRankInCodesContents对象", description="图书书内码分类表")
public class BooksRankInCodesContentsPageDTO implements Serializable {




    @Schema(description = "页面id")
    private Integer contentId;
    @Schema(description = "页面名称")
    private String contentName;
    @Schema(description = "册数id")
    private Integer rankId;
    @Schema(description = "册数名称")
    private String productItemName;
    @Schema(description =  "H5链接")
    private String h5PageUrl;

    @Schema(description =  "新印码")
    private String newPrintCodeUrl;

}
