package com.dbj.classpal.books.client.bo.books.app;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 产品分类配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="BooksUserRef对象", description="产品分类配置表")
public class BooksUserLastStudyBO implements Serializable {




    @Schema(description ="app用户id")
    @TableField("app_user_id")
    private Integer appUserId;

    @Schema(description ="图书id")
    @TableField("books_id")
    private Integer booksId;

    @Schema(description ="最近学习")
    @TableField("is_last_study")
    private Boolean isLastStudy;

    @Schema(description ="是否启用 1-是 0-否")
    @TableField("status")
    private Boolean status;


}
