package com.dbj.classpal.books.client.bo.pinyin;

import com.dbj.classpal.books.client.bo.material.AppCommonMediaBO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 汉语拼音信息UpsertBO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Tag(name="汉语拼音信息UpsertBO", description="汉语拼音信息UpsertBO")
public class PinyinUpsertBO implements Serializable {


    @Schema(description = "ID")
    private Integer id;

    @NotNull
    @Schema(description = "拼音分类id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer classifyId;

    @NotBlank
    @Size(max = 16)
    @Schema(description = "标题", requiredMode = Schema.RequiredMode.REQUIRED)
    private String title;

    @NotEmpty
    @Valid
    @Schema(description = "口型动画媒体文件", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<AppCommonMediaBO> oralAnimationMedias;

    @NotEmpty
    @Valid
    @Schema(description = "发音媒体文件", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<AppCommonMediaBO> pronounceMedias;

    @NotEmpty
    @Valid
    @Schema(description = "四声发音媒体文件", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<AppCommonMediaBO> fourTonePronounceMedias;

    @NotNull
    @Schema(description = "启用状态 0-否 1-是", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer status;

    @Max(99999)
    @Schema(description = "排序权重")
    private Integer sort;
}
