package com.dbj.classpal.books.client.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Schema
@Data
public class QuestionBlankContentApiBO implements Serializable {
    /**
     * 题目id
     */
    @Schema(description = "题目id")
    private Integer questionId;

    /**
     * 空位答案映射 Map<空位序号, 答案>
     */
    @Schema(description = "空位答案映射 Map<空位序号, 答案>")
    private Map<String, String> blankAnswerMap;

    /**
     * 空位选项映射 Map<空位序号, 选项列表>
     */
    @Schema(description = "空位选项映射 Map<空位序号, 选项列表>")
    private Map<String, List<String>> blankOptionsMap;

    /**
     * 空位内容列表
     */
    @Schema(description = "空位内容列表")
    private List<QuestionBlankAreaApiBO> blankAreaList;
} 