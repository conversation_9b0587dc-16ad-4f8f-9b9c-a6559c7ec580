package com.dbj.classpal.books.client.dto.pinyin;

import com.dbj.classpal.books.client.dto.material.AppCommonMediaDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 拼音信息DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Tag(name="拼音信息DTO", description="拼音信息DTO")
public class PinyinDTO implements Serializable {

    @Schema(description = "ID")
    private Integer id;

    @Schema(description = "拼音分类id")
    private Integer classifyId;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "启用状态 0-否 1-是")
    private Integer status;

    @Schema(description = "排序权重")
    private Integer sort;

    @Schema(description = "口型动画媒体文件")
    private List<AppCommonMediaDTO> oralAnimationMedias;

    @Schema(description = "发音媒体文件")
    private List<AppCommonMediaDTO> pronounceMedias;

    @Schema(description = "四声发音媒体文件")
    private List<AppCommonMediaDTO> fourTonePronounceMedias;
}
