package com.dbj.classpal.books.client.dto.poem.app;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className AncientPoemBusinessRefPageDTO
 * @description
 * @date 2025-05-27 09:30
 **/
@Data
public class AncientPoemBusinessRefPageDTO implements Serializable {


    @Schema(description = "ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "古诗文id")
    private Integer ancientPoemId;

    @Schema(description = "业务id")
    private Integer businessId;

    @Schema(description = "业务名称")
    private String businessName;

    @Schema(description = "业务类型 1-古诗背诵")
    private Integer businessType;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "标题拼音")
    private String titlePinyin;

    @Schema(description = "作者")
    private String author;

    @Schema(description = "所属朝代")
    private String dynasty;

    @Schema(description = "年级")
    private String grade;

    @Schema(description = "是否课外内容 0-课内 1-课外")
    private Integer isOut;

    @Schema(description = "总分（0-100）")
    private BigDecimal totalScore;

    @Schema(description = "校验文本")
    private String verifyText;

    @Schema(description = "古诗文简介")
    private String introduction;
    @Schema(description = "背景图路径")
    private String backgroundUrl;

    @Schema(description = "封面图路径")
    private String coverUrl;




}
