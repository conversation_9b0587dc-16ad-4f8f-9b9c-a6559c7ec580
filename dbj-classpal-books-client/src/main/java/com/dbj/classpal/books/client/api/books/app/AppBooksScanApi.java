package com.dbj.classpal.books.client.api.books.app;

import com.dbj.classpal.books.client.bo.books.app.BooksScanInfoBO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className BooksScanApi
 * @description
 * @date 2025-04-25 08:35
 **/
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AppBooksScanApi {

    /**
     * <AUTHOR>
     * @Description  我的书架
     * @Date 2025/4/21 10:29
     * @param
     * @return
     **/
    @PostMapping("/app/books/user/scan/save")
    RestResponse<Boolean> save(@RequestBody BooksScanInfoBO bo) throws BusinessException;
}
