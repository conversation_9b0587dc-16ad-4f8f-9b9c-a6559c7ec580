package com.dbj.classpal.books.client.bo.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;

/**
 * 广告状态更新BO
 * <AUTHOR> @since 2025-04-21 16:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
@Tag(name="广告状态更新BO", description="广告状态更新BO")
public class AdvertisementStatusUpdateBO implements Serializable {

    @NotNull
    @Schema(description = "广告id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer id;

    @NotNull
    @Schema(description = "是否启用 true-是 false-否", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean status;
}
