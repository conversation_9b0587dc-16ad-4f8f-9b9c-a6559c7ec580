package com.dbj.classpal.books.client.dto.poem;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 古诗引用RelateDTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Tag(name="古诗文RelateDTO", description="古诗文RelateDTO")
@Accessors(chain = true)
public class AncientPoemRelateDTO implements Serializable {


//    @Schema(description = "ID")
//    private Integer id;
//
    @Schema(description = "古诗文id")
    private Integer ancientPoemId;

    @Schema(description = "古诗文标题")
    private String ancientPoemTitle;

    @Schema(description = "业务id")
    private Integer businessId;

    @Schema(description = "业务名称")
    private String businessName;

    @Schema(description = "业务类型 1-古诗背诵")
    private Integer businessType;

    @Schema(description = "业务类型描述")
    private String businessTypeDesc;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "引用对象名称")
    private String sourceName;

    @Schema(description = "额外信息")
    private Map<String, Object> other;
}
