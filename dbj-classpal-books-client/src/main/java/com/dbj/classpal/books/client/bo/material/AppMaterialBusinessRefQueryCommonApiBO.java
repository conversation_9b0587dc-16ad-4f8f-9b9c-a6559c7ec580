package com.dbj.classpal.books.client.bo.material;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialCommonIdBO
 * Date:     2025-04-14 10:22:40
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class AppMaterialBusinessRefQueryCommonApiBO implements Serializable {

    @Schema(description = "素材id")
    private Integer appMaterialId;

    @Schema(description = "业务id")
    private Integer businessId;

    @Schema(description = "业务类型 1内容管理-音频专辑 2内容管理-视频专辑 3图书管理-图书资源 4图书管理-题库 5图书管理-音频专辑 6图书管理-视频专辑")
    private Integer businessType;

    @Schema(description = "业务名称")
    private String businessName;

    @Schema(description = "资源类型 1文件夹 2图片  3音频 4视频 5文档 6压缩包")
    private Integer appMaterialType;

}
