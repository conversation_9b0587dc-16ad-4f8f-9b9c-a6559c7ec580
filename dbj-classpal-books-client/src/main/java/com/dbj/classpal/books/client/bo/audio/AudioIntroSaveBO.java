package com.dbj.classpal.books.client.bo.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/6/30 10:55
 */
@Data
public class AudioIntroSaveBO {

    private Integer id;

    @NotEmpty
    @Size(max = 128)
    @Schema(description = "音频名称", requiredMode = RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "分类id")
    private Integer audioClassifyId;

}
