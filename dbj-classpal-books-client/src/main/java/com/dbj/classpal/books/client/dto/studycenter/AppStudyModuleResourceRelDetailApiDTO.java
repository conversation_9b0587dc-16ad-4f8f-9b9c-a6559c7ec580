package com.dbj.classpal.books.client.dto.studycenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "学习模块资源关联-详情ApiDTO")
public class AppStudyModuleResourceRelDetailApiDTO {
    @Schema(description = "主键ID")
    private Integer id;
    @Schema(description = "学习模块ID")
    private Integer moduleId;
    @Schema(description = "资源ID")
    private Integer resourceId;
    @Schema(description = "资源类型")
    private String resourceType;
    @Schema(description = "资源名称")
    private String resourceName;
    @Schema(description = "资源icon")
    private String resourceIcon;
    @Schema(description = "排序权重")
    private Integer sortNum;
    @Schema(description = "状态 0-禁用 1-启用")
    private Integer status;
} 