package com.dbj.classpal.books.client.dto.books;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-02-04 14:53
 */
@Schema(description = "")
@Data
public class ProductInfoApiDTO {

    /**
     * id
     */
    @Schema(description = "id",hidden = true)
    private Integer id;

    @Schema(description ="编码")
    private String code;
    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Integer productId;
    /**
     * 产品名称
     */
    @Schema(description = "产品名称")
    private String productName;
    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private Integer productType;
    /**
     * 封面url
     */
    @Schema(description = "封面url")
    private String picUrl;
    /**
     * 卷册数
     */
    @Schema(description = "卷册数")
    private Integer volumeNum;
    /**
     * 赠册数
     */
    @Schema(description = "赠册数")
    private Integer giftVolumeNum;
    /**
     * 分类
     */
    @Schema(description = "分类")
    private String categoryNames;
}
