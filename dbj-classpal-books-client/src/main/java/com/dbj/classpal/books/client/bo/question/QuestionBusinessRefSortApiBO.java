package com.dbj.classpal.books.client.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationQueryBO
 * Date:     2025-04-15 13:35:46
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class QuestionBusinessRefSortApiBO implements Serializable {

    @Schema(description = "评测项id",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "评测项id不能为空")
    private Integer appEvaluationNodeId;


    @Schema(description = "主键id",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键id不能为空")
    private Integer id;

    @Schema(description = "目标id",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "目标id不能为空")
    private Integer aimId;

    @Schema(description = "相对目标位置 -1前面 1后面")
    @NotNull(message = "相对目标位置不能为空")
    private Integer order;

}
