package com.dbj.classpal.books.client.bo.share;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 删除分享链接API请求参数BO
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@Schema(description = "删除分享链接请求参数")
public class RemoveShareUrlApiBO implements Serializable {

    @Schema(description = "业务类型：b5-单书，b6-书架，b7-书城")
    @NotEmpty(message = "业务类型不能为空")
    private String businessType;

    @Schema(description = "业务ID")
    @NotNull(message = "业务ID不能为空")
    private Integer businessId;
}
