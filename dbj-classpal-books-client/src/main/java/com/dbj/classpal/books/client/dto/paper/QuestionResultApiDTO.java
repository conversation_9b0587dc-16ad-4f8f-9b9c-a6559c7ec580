package com.dbj.classpal.books.client.dto.paper;

import com.dbj.classpal.books.client.dto.question.QuestionMediaApiDTO;
import com.dbj.classpal.books.client.dto.question.QuestionRecognitionApiDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 题目结果DTO
 */
@Data
@Schema(description = "题目结果DTO")
public class QuestionResultApiDTO implements Serializable {

    @Schema(description = "题目ID")
    private Integer questionId;

    @Schema(description = "题目标题")
    private String title;

    @Schema(description = "题目类型")
    private Integer type;
    /**
     * 媒体类型
     */
    @Schema(description = "媒体文件类型 1-文本 2-图片,3-音频,4-视频")
    private Integer mediaType;

    /**
     * 选项类型
     */
    @Schema(description = "选项类型 1-文本 2-图片,3-音频,4-视频")
    private Integer optionType;

    @Schema(description = "用户答案")
    private String userAnswer;

    /**
     * 用户选择的答案ID
     */
    @Schema(description = "用户选择的答案ID")
    private String userAnswerIds;

    @Schema(description = "正确答案")
    private String correctAnswer;
    /**
     * 正确答案ID
     */
    @Schema(description = "正确答案ID")
    private String correctAnswerIds;

    /**
     * 媒体文件url
     */
    @Schema(description = "媒体文件url")
    private List<QuestionMediaApiDTO> mediaUrl;

    @Schema(description = "辅助识图")
    private List<QuestionRecognitionApiDTO> aidedRecognitionUrl;

    @Schema(description = "题目解析")
    private String analyzes;

    @Schema(description = "结果：1-正确，0-错误")
    private Integer result;

    @Schema(description = "答案选项列表")
    private List<QuestionAnswerApiDTO> options;

    @Schema(description = "填空题结果列表")
    private List<BlankResultApiDTO> blankResults;
} 