package com.dbj.classpal.books.client.api.advertisement;

import com.dbj.classpal.books.client.bo.advertisement.AdvertisementResourceBO;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementResourceDTO;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <p>
 * 广告页面 远程Api
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AdvertisementResourceApi {

    /**
     * 获取广告页面列表
     *
     * @param bo
     * @return
     */
    @PostMapping("/advertisement/getAdvertisementResourceList")
    RestResponse<List<AdvertisementResourceDTO>> getAdvertisementResourceList(@RequestBody @Validated AdvertisementResourceBO bo);
}
