package com.dbj.classpal.books.client.api.ebooks;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigWatermarkTemplateEditApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigWatermarkTemplateQueryApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigWatermarkTemplateSaveApiBO;
import com.dbj.classpal.books.client.dto.ebooks.AppEbooksConfigWatermarkTemplateQueryApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AppEbooksConfigWatermarkTemplateApi {
    /**
     * 分页查询水印模板列表
     * @param pageRequest
     * @return
     */
    @PostMapping("/ebooksConfig/watermarkTemplate/pageInfo")
    RestResponse<Page<AppEbooksConfigWatermarkTemplateQueryApiDTO>> pageInfo(@RequestBody PageInfo<AppEbooksConfigWatermarkTemplateQueryApiBO> pageRequest) throws BusinessException;

    /**
     * 查询所有水印模板列表
     * @return
     */
    @PostMapping("/ebooksConfig/watermarkTemplate/getAll")
    RestResponse<List<AppEbooksConfigWatermarkTemplateQueryApiDTO>> getAll();

    /**
     * 添加水印模板
     * @param bo
     * @return
     */
    @PostMapping("/ebooksConfig/watermarkTemplate/saveEbooksConfigWatermarkTemplate")
    RestResponse<Boolean>saveEbooksConfigWatermarkTemplate(@RequestBody AppEbooksConfigWatermarkTemplateSaveApiBO bo) throws BusinessException;

    /**
     * 修改水印模板
     * @param bo
     * @return
     */
    @PostMapping("/ebooksConfig/watermarkTemplate/updateEbooksConfigWatermarkTemplate")
    RestResponse<Boolean>updateEbooksConfigWatermarkTemplate(@RequestBody AppEbooksConfigWatermarkTemplateEditApiBO bo) throws BusinessException;

    /**
     * 删除水印模板
     * @param bo
     * @return
     */
    @PostMapping("/ebooksConfig/watermarkTemplate/deleteEbooksConfigWatermarkTemplate")
    RestResponse<Boolean>deleteEbooksConfigWatermarkTemplate(@RequestBody CommonIdsApiBO bo) throws BusinessException;
}
