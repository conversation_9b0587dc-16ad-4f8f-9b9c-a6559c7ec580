package com.dbj.classpal.books.client.enums.material;

import com.dbj.classpal.books.client.enums.ScanCodeTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum MaterialTransCodeEnum {
    TRANS_CODE_SUCCESS(200, "转码成功"),
    TRANS_CODE_FAIL(201, "转码失败"),
    TRANS_CODE_ERROR(500,"业务异常");

    private Integer code;
    private String msg;

    public static MaterialTransCodeEnum getByCode(Integer code) {
        for (MaterialTransCodeEnum value : MaterialTransCodeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
