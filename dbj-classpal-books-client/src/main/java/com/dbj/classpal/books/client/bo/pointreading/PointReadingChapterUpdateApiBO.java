package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Data;

/**
 * 点读书章节更新API BO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingPageUpdateApiBO", description = "点读书章节更新API BO")
public class PointReadingChapterUpdateApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "章节ID")
    @NotNull(message = "章节ID不能为空")
    private Integer id;

    @Schema(description = "所属目录ID")
    @NotNull(message = "所属目录ID不能为空")
    private Integer menuId;

    @Schema(description = "章节名称")
    @NotBlank(message = "章节名称不能为空")
    private String name;

    @Schema(description = "章节图片URL")
    private String imageUrl;

    @Schema(description = "章节图片名称")
    private String imageName;

    @Schema(description = "章节音频URL")
    private String audioUrl;

    @Schema(description = "章节音频名称")
    private String audioName;

    @Schema(description = "章节内容")
    private String content;

    @Schema(description = "排序")
    private Integer sortNum;

}
