package com.dbj.classpal.books.client.dto.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 试听音频合成任务错误信息
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AudioTaskErrorDTO {

    @Schema(description = "阿里云错误状态码")
    private int status;
    @Schema(description = "阿里云错误信息")
    private String errorMessage;
    @Schema(description = "原因")
    private String reason;
    @Schema(description = "解决方案")
    private String solution;
}
