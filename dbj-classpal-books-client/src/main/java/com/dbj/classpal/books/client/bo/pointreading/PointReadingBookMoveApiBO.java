package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 点读书批量移动API BO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingBookMoveApiBO", description = "点读书批量移动API BO")
public class PointReadingBookMoveApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "点读书ID列表")
    @NotEmpty(message = "点读书ID列表不能为空")
    private List<Integer> ids;

    @Schema(description = "目标分类ID")
    @NotNull(message = "目标分类ID不能为空")
    private Integer targetCategoryId;
}
