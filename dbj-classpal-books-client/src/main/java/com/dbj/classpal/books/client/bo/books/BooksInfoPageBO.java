package com.dbj.classpal.books.client.bo.books;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 图书表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="图书分页查询对象", description="图书表")
public class BooksInfoPageBO implements Serializable {

    @Schema(description = "产品名称")
    private String bookName;

    @Schema(description = "分类id")
    private List<Integer> categoryIds;

    @Schema(description = "是否隐藏")
    private Integer isHide;

    @Schema(description = "上下架状态  上架 下架")
    private Integer launchStatus;

    @Schema(description = "启用状态")
    private Boolean status;

}
