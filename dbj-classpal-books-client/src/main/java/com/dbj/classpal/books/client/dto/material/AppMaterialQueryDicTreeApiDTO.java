package com.dbj.classpal.books.client.dto.material;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialQueryDTO
 * Date:     2025-04-10 09:31:28
 * Description: 表名： ,描述： 表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@Tag(name = "素材中心查询DTO")
public class AppMaterialQueryDicTreeApiDTO implements Serializable {

    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "是否根节点 0否 1是")
    private Integer isRoot;

    @Schema(description = "父节点ID")
    private Integer parentId;

    @Schema(description = "文件夹名称")
    private String materialName;

    @Schema(description = "资源类型 1文件夹 2图片  3音频 4视频 5文档 6压缩包")
    private Integer materialType;

    @Schema(description = "资源路径")
    private String materialPath;

    @Schema(description = "资源大小 kb")
    private Double materialSize;

    @Schema(description = "排序")
    private Integer orderNum;

    @Schema(description = "子节点")
    private List<AppMaterialQueryDicTreeApiDTO>children;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
