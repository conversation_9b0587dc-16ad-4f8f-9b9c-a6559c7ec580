package com.dbj.classpal.books.client.dto.studycenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Schema
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class StudyCenterCategoryApiDTO {
    /**
     * 所属分类ID
     */
    @Schema(description = "所属分类ID")
    private Integer beLongCategoryId;
    
    /**
     * 所属分类名称
     */
    @Schema(description = "所属分类名称")
    private String beLongCategoryName;

    /**
     * 所属分类icon
     */
    @Schema(description = "所属分类icon")
    private String beLongCategoryIcon;

    /**
     * 所属分类背景图片
     */
    @Schema(description = "所属分类背景图片")
    private String beLongCategoryBackGround;

    /**
     * 所属分类背景图片
     */
    @Schema(description = "所属分类背景图片name")
    private String beLongCategoryBackGroundName;

    /**
     * 模块列表
     */
    @Schema(description = "模块列表")
    private List<StudyCenterModuleListApiDTO> moduleList;
    
}