
package com.dbj.classpal.books.client.dto.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 广告层级条件DTO
 * <AUTHOR> <PERSON>
 * @since 2025-04-21 16:52
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Tag(name="广告层级条件DTO", description="广告层级条件DTO")
public class AdvertisementLevelConditionDTO implements Serializable  {

	/**
	 * 广告层级条件关联表id
	 */
	@Schema(description = "广告层级条件关联表id")
	private Integer id;
	/**
	 * 广告id
	 */
	@Schema(description = "广告id")
	private Integer advertisementId;

	/**
	 * 逻辑类型 1-且 2-或 3-非
	 */
	@Schema(description = "逻辑类型 1-且 2-或 3-非")
	private Integer logicType;
	/**
	 * 父级id
	 */
	@Schema(description = "父级id")
	private Integer parentId;
	/**
	 * 层级
	 */
	@Schema(description = "层级")
	private Integer level;
	/**
	 * 广告条件类型
	 */
	@Schema(description = "广告条件类型")
	private String conditionType;
	/**
	 * 广告条件类型字典名
	 */
	@Schema(description = "广告条件类型字典名")
	private String name;
	/**
	 * 是否符合条件
	 */
	@Schema(description = "是否符合条件")
	private Boolean isEligible;
	/**
	 * 子集
	 */
	@Schema(description = "子集")
	private List<AdvertisementLevelConditionDTO> childrenList;
	/**
	 * 条件选项
	 */
	@Schema(description = "条件选项")
	private List<AdvertisementLevelConditionOptionDTO> advertisementLevelConditionOptionList;

}
