package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 点读书热点区域批量操作API BO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingHotspotBatchApiBO", description = "点读书热点区域批量操作API BO")
public class PointReadingHotspotBatchApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "热点ID列表")
    @NotEmpty(message = "热点ID列表不能为空")
    private List<Integer> ids;
}
