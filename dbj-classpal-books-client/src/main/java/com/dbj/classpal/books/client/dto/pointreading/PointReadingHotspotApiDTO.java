package com.dbj.classpal.books.client.dto.pointreading;

import com.dbj.classpal.books.client.dto.question.QuestionCategoryBusinessSettingsApiDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 点读书热点区域API DTO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingHotspotApiDTO", description = "点读书热点区域API DTO")
public class PointReadingHotspotApiDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "热点ID")
    private Integer id;

    @Schema(description = "所属章节ID")
    private Integer chapterId;

    @Schema(description = "点读书ID")
    private Integer bookId;

    @Schema(description = "章节名称")
    private String chapterName;

    @Schema(description = "热点名称")
    private String name;

    @Schema(description = "前端节点ID（前端定义唯一性）")
    private String nodeId;

    @Schema(description = "父节点ID（支持层级结构）")
    private String parentNodeId;

    @Schema(description = "节点名称")
    private String nodeName;

    @Schema(description = "节点层级（1-根节点，2-二级节点...）")
    private Integer nodeLevel;

    @Schema(description = "区域类型：10-热点区域 20-标记点")
    private Integer areaType;

    @Schema(description = "区域类型描述")
    private String areaTypeDesc;

    @Schema(description = "事件类型：10-点读 20-答题 30-跟读")
    private Integer eventType;

    @Schema(description = "事件类型描述")
    private String eventTypeDesc;

    @Schema(description = "X坐标")
    private BigDecimal coordinateX;

    @Schema(description = "Y坐标")
    private BigDecimal coordinateY;

    @Schema(description = "宽度")
    private BigDecimal width;

    @Schema(description = "高度")
    private BigDecimal height;

    @Schema(description = "是否支持跟读：0-否 1-是")
    private Integer followRead;

    @Schema(description = "是否支持跟读描述")
    private String followReadDesc;

    @Schema(description = "跟读校验文本")
    private String verifyText;

    @Schema(description = "填空文本内容（富文本格式，当模式为填空时有值）")
    private String fillBlankContent;

    @Schema(description = "模式配置字典ID（冗余存储的历史数据）")
    private Integer configDictId;

    @Schema(description = "模式显示名称（冗余存储的历史数据）")
    private String displayName;

    @Schema(description = "当前模式配置ID（查询时动态获取最新配置）")
    private Integer currentModeId;

    @Schema(description = "当前模式名称（查询时动态获取最新配置）")
    private String currentModeName;

    @Schema(description = "当前模式是否启用（查询时动态获取最新配置）")
    private Boolean currentModeEnabled;

    @Schema(description = "排序")
    private Integer sortNum;

    @Schema(description = "状态：0-停用 1-正常")
    private Integer status;

    @Schema(description = "状态描述")
    private String statusDesc;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "答题设置，仅事件类型为答题时有值")
    private QuestionCategoryBusinessSettingsApiDTO questionSettings;

    @Schema(description = "媒体文件列表")
    private List<PointReadingHotspotMediaApiDTO> mediaList;
}
