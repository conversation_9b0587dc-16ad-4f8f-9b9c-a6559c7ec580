package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 书城集查询业务对象
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
@Data
@Schema(description = "书城集查询参数BO")
public class AppEBookstoreH5QueryApiBO implements Serializable {

    @Schema(description = "书城ID（用于查询特定分享的书城，为空时查询所有已启用的书城集）")
    private Integer storeId;

}