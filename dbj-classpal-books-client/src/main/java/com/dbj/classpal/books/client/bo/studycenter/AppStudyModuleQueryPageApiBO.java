package com.dbj.classpal.books.client.bo.studycenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "学习模块-分页查询ApiBO")
public class AppStudyModuleQueryPageApiBO {
    @Schema(description = "模块类型1-评测，2-音频专辑 3 -视频专辑 4-功能 5- 题库")
    private Integer moduleType;
    @Schema(description = "标题关键字")
    private String title;
    @Schema(description = "上架状态")
    private Integer publishStatus;
    @Schema(description = "是否显示")
    private Integer isVisible;
    @Schema(description = "所属分类")
    private Integer belongCategoryId;
    @Schema(description = "适用年级，ALL表示全部，部分年级用逗号分隔")
    private String applicableGrades;
} 