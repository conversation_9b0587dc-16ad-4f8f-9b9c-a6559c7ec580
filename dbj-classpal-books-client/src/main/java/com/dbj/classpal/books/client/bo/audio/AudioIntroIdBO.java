package com.dbj.classpal.books.client.bo.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 预置提示音表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AudioIntroIdBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "音频简介ID")
    @NotNull(message = "音频简介ID不能为空")
    private Integer audioIntroId;


}
