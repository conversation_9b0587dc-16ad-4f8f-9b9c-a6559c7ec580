package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 书架更新业务参数对象
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
@Data
@Schema(description = "书架更新参数BO")
public class AppEBookshelfUpdateApiBO extends AppEBookshelfSaveApiBO implements Serializable {

    @Schema(description = "书架ID")
    @NotNull(message = "书架ID不能为空")
    private Integer id;

} 