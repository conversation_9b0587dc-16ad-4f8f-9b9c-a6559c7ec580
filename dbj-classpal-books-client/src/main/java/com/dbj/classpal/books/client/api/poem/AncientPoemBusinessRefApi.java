package com.dbj.classpal.books.client.api.poem;

import com.dbj.classpal.books.client.bo.poem.AncientPoemBusinessRefListBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemBusinessRefSaveBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemBusinessRefSortBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemBusinessRefListDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className IAncientPoemBusinessRefApi
 * @description
 * @date 2025-05-27 09:29
 **/
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AncientPoemBusinessRefApi {

    @PostMapping("/ancientPoemBusinessRef/listAncientPoemBusinessRef")
    RestResponse<List<AncientPoemBusinessRefListDTO>> listAncientPoemBusinessRef(@RequestBody AncientPoemBusinessRefListBO anAncientPoemBusinessRefList);

    /**
     * 保存
     */
    @PostMapping("/ancientPoemBusinessRef/batchSave")
    RestResponse<Boolean> batchSave(@RequestBody AncientPoemBusinessRefSaveBO anotherPoemBusinessRefSaveBO) throws BusinessException;
    /**
     * 删除
     */
    @PostMapping("/ancientPoemBusinessRef/batchDelete")
    RestResponse<Boolean> batchDelete(@RequestParam List<Integer> ids);
    /**
     * 排序
     */
    @PostMapping("/ancientPoemBusinessRef/batchSort")
    RestResponse<Boolean> batchSort(@RequestBody List<AncientPoemBusinessRefSortBO> anotherPoemBusinessRefSortList);
}

