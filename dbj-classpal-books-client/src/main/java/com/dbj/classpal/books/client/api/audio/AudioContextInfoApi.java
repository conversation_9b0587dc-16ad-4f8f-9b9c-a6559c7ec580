package com.dbj.classpal.books.client.api.audio;

import com.dbj.classpal.books.client.bo.audio.*;
import com.dbj.classpal.books.client.dto.audio.AudioContextInfoListDTO;
import com.dbj.classpal.books.client.dto.audio.AudioSynthesizerTaskInfoDTO;
import com.dbj.classpal.books.client.dto.audio.AudioTrialUseDTO;
import com.dbj.classpal.books.client.dto.audio.SynthesisResultDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 音频文本提示音
 * <AUTHOR>
 * @since 2025-06-25
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}", path = "/api/classpal/books/${spring.application.version}")
public interface AudioContextInfoApi {

    /**
     * 保存音频文本
     * @param bo
     * @return
     * @throws BusinessException
     */
    @PostMapping("/contextInfo/save")
    RestResponse<Integer> save(@RequestBody AudioContextInfoBO bo) throws BusinessException;
    /**
     * 查询音频文本详情
     * @param bo
     * @return
     * @throws BusinessException
     */
    @PostMapping("/contextInfo/getContextInfoList")
    RestResponse<AudioContextInfoListDTO> getContextInfoList(@RequestBody AudioIntroIdBO bo) throws BusinessException;
    /**
     * 取消合成任务
     * @param bo
     * @return
     * @throws BusinessException
     */
    @PostMapping("/contextInfo/cancel")
    RestResponse<Integer> cancel(@RequestBody AudioIntroIdBO bo) throws BusinessException;
    /**
     * 合成音频
     * @param bo
     * @return
     * @throws BusinessException
     */
    @PostMapping("/contextInfo/synthesis")
    RestResponse<Integer> synthesis(@RequestBody AudioContextInfoBO bo) throws BusinessException;
    /**
     * 重新合成/立即合成
     * @param bo
     * @return
     * @throws BusinessException
     */
    @PostMapping("/contextInfo/resynthesis")
    RestResponse<Integer> resynthesis(@RequestBody AudioIntroIdBO bo) throws BusinessException;
    /**
     * 试听音频
     * @param bo
     * @return
     * @throws BusinessException
     */
    @PostMapping("/contextInfo/submitSynthesis")
    RestResponse<AudioSynthesizerTaskInfoDTO> submitSynthesis(@RequestBody AudioTrialUseBO bo) throws BusinessException;
    /**
     * 查询试听任务结果
     */
    @PostMapping("/contextInfo/getTaskInfo")
    RestResponse<AudioSynthesizerTaskInfoDTO> getTaskInfo(@RequestBody AudioTaskBO bo) throws BusinessException;
    /**
     * 查询合成状态
     */
    @PostMapping("/contextInfo/getSynthesizeStatus")
    RestResponse<SynthesisResultDTO> getSynthesizeStatus(@RequestBody AudioIntroIdBO bo);

}
