package com.dbj.classpal.books.client.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema
@Data
public class QuestionCategoryIdApiBO implements Serializable {

    /**
     * 主键
     */
    @Schema(description = "主键")
    @NotNull(message = "主键不能为空")
    private Integer id;
    /**
     * 父节点
     */
    @Schema(description = "父节点")
    @NotNull(message = "父节点不能为空")
    private Integer fatherId;
} 