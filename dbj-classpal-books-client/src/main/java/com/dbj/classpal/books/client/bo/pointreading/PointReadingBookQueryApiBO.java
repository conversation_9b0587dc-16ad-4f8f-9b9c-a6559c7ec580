package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 点读书查询API BO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Schema(name = "PointReadingBookQueryApiBO", description = "点读书查询API BO")
public class PointReadingBookQueryApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "点读书名称")
    private String name;

    @Schema(description = "所属分类ID")
    private Integer categoryId;

    @Schema(description = "解析状态：10-待解析 20-解析中 30-已解析 40-解析失败")
    private Integer parseStatus;

    @Schema(description = "启用状态：0-未启用 1-已启用")
    private Integer launchStatus;

    @Schema(description = "状态：0-停用 1-正常")
    private Integer status;

    @Schema(description = "原始文件MD5")
    private String originalMd5;
}
