package com.dbj.classpal.books.client.dto.evaluation.app;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationQueryBO
 * Date:     2025-04-15 13:35:46
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
@Tag(name = "APP客户端查询评测表",description = "APP客户端查询评测表")
public class AppEvaluationQueryApiDTO implements Serializable {

    @Schema(description = "评测表主键id")
    private Integer id;

    @Schema(description = "评测名称")
    private String evaluationName;

    @Schema(description = "评测介绍")
    private String evaluationRemark;

    @Schema(description = "评测报告id")
    private Integer appUserPaperEvaluationId;

    @Schema(description = "是否已生成报告 0未生成 1已生成 2生成失败")
    private Integer isGenerated;

    @Schema(description = "评测项列表")
    private List<AppEvaluationNodeQueryApiDTO>nodeQueryApiDTOList;

}
