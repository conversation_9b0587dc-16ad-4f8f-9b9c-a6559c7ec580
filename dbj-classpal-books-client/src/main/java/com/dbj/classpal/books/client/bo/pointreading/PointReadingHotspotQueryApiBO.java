package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 点读书热点区域查询API BO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingHotspotQueryApiBO", description = "点读书热点区域查询API BO")
public class PointReadingHotspotQueryApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "热点名称")
    private String name;

    @Schema(description = "所属章节ID")
    private Integer chapterId;

    @Schema(description = "前端节点ID")
    private String nodeId;

    @Schema(description = "父节点ID")
    private String parentNodeId;

    @Schema(description = "节点层级")
    private Integer nodeLevel;

    @Schema(description = "区域类型：10-热点区域 20-标记点")
    private Integer areaType;

    @Schema(description = "事件类型：10-点读 20-答题 30-跟读")
    private Integer eventType;

    @Schema(description = "媒体类型：10-音频 20-视频")
    private Integer mediaType;

    @Schema(description = "状态：0-停用 1-正常")
    private Integer status;
}
