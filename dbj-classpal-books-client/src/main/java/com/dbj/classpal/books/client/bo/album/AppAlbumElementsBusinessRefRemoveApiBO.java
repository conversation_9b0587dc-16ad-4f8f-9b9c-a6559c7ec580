package com.dbj.classpal.books.client.bo.album;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Set;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumElementsBusinessRefRemoveBO
 * Date:     2025-07-09 13:35:46
 * Description: 专辑RemoveBO
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
@Tag(name = "专辑RemoveBO")
public class AppAlbumElementsBusinessRefRemoveApiBO {

    @Schema(description = "业务id集合")
    private Set<Integer> businessIds;

    @NotNull
    @Schema(description = "业务类型")
    private Integer businessType;
}
