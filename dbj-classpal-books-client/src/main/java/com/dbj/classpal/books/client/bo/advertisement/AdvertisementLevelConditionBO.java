
package com.dbj.classpal.books.client.bo.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 广告条件BO
 * <AUTHOR>
 * @since 2025-04-21 16:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
@Tag(name="广告条件BO", description="广告条件BO")
public class AdvertisementLevelConditionBO implements Serializable  {


	/**
	 * 逻辑类型 1-且 2-或 3-非
	 */
	@Schema(description = "逻辑类型 1-且 2-或 3-非")
	private Integer logicType;
	/**
	 * 层级
	 */
	@Schema(description = "层级")
	private Integer level;
	/**
	 * 广告条件类型字典项
	 */
	@Schema(description = "广告条件类型字典项")
	private String conditionType;
	/**
	 * 广告条件类型字典名
	 */
	@Schema(description = "广告条件类型字典名")
	private String name;
	/**
	 * 子集
	 */
	@Schema(description = "子集")
	private List<AdvertisementLevelConditionBO> childrenList;

	/**
	 * 条件选项
	 */
	@Valid
	@Schema(description = "条件选项")
	private List<AdvertisementLevelConditionOptionBO> advertisementLevelConditionOptionList;

}
