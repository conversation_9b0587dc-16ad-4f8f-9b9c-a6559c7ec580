package com.dbj.classpal.books.client.bo.poem;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;

/**
 * 古诗文关联关系BO
 * <AUTHOR>
 * @since 2025-05-23
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Tag(name="古诗文关联关系BO", description="古诗文关联关系BO")
public class AncientPoemBusinessRefBO implements Serializable {

    /**
     * 古诗文id
     */
    @NotNull
    @Schema(description = "古诗文id")
    private Integer ancientPoemId;

    @Schema(description = "排序")
    private Integer sort;
}
