package com.dbj.classpal.books.client.bo.audio;

import feign.Client.Default;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/6/27 17:25
 */
@Data
public class AudioClassifyDelBO {

    @NotEmpty(message = "id不能为空")
    @Schema(description = "分类id", requiredMode = RequiredMode.REQUIRED)
    private Set<Integer> ids;
}
