package com.dbj.classpal.books.client.dto.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 书城统计信息API数据传输对象
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
@Data
@Schema(description = "书城统计信息API DTO")
public class AppEBookstoreStatisticsApiDTO implements Serializable {
    @Schema(description = "书城ID")
    private Integer storeId;

    @Schema(description = "书城名称")
    private String storeTitle;

    @Schema(description = "书架总数")
    private Integer totalShelfCount;

    @Schema(description = "书籍总数")
    private Integer totalBookCount;
    
    @Schema(description = "各书架的书籍数量映射，key为书架ID，value为书籍数量")
    private Map<Integer, Integer> shelfBookCountMap;
    
    @Schema(description = "书架详情列表")
    private List<ShelfStatisticsApiDTO> shelfStatistics;
    
    /**
     * 书架统计内部类
     */
    @Data
    @Schema(description = "书架统计信息")
    public static class ShelfStatisticsApiDTO implements Serializable {

        @Schema(description = "书架ID")
        private Integer shelfId;
        
        @Schema(description = "书架名称")
        private String shelfTitle;
        
        @Schema(description = "书籍数量")
        private Integer bookCount;

        @Schema(description = "单书列表")
        private List<AppEBookApiDTO> books;
    }
} 