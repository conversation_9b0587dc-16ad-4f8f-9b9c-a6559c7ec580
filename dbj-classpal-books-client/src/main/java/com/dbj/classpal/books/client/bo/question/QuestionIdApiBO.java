package com.dbj.classpal.books.client.bo.question;

import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema
@Data
public class QuestionIdApiBO implements Serializable {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Integer id;


} 