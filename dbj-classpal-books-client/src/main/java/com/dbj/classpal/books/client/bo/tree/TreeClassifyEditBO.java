package com.dbj.classpal.books.client.bo.tree;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 树形分类EditBO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
public class TreeClassifyEditBO implements Serializable {


    @NotNull
    @Schema(description = "id")
    private Integer id;

    @NotBlank
    @Size(max = 32)
    @Schema(description = "分类名称")
    private String name;
}
