package com.dbj.classpal.books.client.api.question;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.question.QuestionBusinessRefPageApiBO;
import com.dbj.classpal.books.client.bo.question.QuestionBusinessRefSaveApiBO;
import com.dbj.classpal.books.client.bo.question.QuestionBusinessRefSortApiBO;
import com.dbj.classpal.books.client.dto.question.QuestionBusinessRefApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface QuestionBusinessRefApi {

    /**
     * 分页查询题目列表
     */
    @PostMapping("/questionBusinessRef/page")
    RestResponse<Page<QuestionBusinessRefApiDTO>> pageList(@RequestBody @Valid PageInfo<QuestionBusinessRefPageApiBO> pageApiBO);

    /**
     * 新增题目业务关联关系
     */
    @PostMapping("/questionBusinessRef/saveQuestionBusinessRef")
    RestResponse<Boolean> saveQuestionBusinessRef(@RequestBody @Valid QuestionBusinessRefSaveApiBO bo) throws BusinessException;


    /**
     * 删除题目业务关联关系
     */
    @PostMapping("/questionBusinessRef/deleteQuestionBusinessRef")
    RestResponse<Boolean> deleteQuestionBusinessRef(@RequestBody @Valid CommonIdsApiBO bo);


    /**
     * 题目业务关联关系排序
     */
    @PostMapping("/questionBusinessRef/sortQuestionBusinessRef")
    RestResponse<Boolean> sortQuestionBusinessRef(@RequestBody @Valid QuestionBusinessRefSortApiBO bo) throws BusinessException;

} 