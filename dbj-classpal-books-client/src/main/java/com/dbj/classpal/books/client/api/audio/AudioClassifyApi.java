package com.dbj.classpal.books.client.api.audio;

import com.dbj.classpal.books.client.bo.audio.AudioClassifyAddBO;
import com.dbj.classpal.books.client.bo.audio.AudioClassifyDelBO;
import com.dbj.classpal.books.client.bo.audio.AudioClassifyMoveBO;
import com.dbj.classpal.books.client.bo.audio.AudioClassifyQueryBO;
import com.dbj.classpal.books.client.dto.audio.AudioClassifyDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/27 17:13
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}", path = "/api/classpal/books/${spring.application.version}")
public interface AudioClassifyApi {

    @PostMapping("/audio/classify/save")
    RestResponse<Boolean> save(@RequestBody AudioClassifyAddBO bo) throws BusinessException;

    @PostMapping("/audio/classify/list")
    RestResponse<List<AudioClassifyDTO>> list(@RequestBody AudioClassifyQueryBO bo) throws BusinessException;

    @PostMapping("/audio/classify/remove")
    RestResponse<Boolean> remove(@RequestBody AudioClassifyDelBO bo) throws BusinessException;

    @PostMapping("/audio/classify/move")
    RestResponse<Boolean> move(@RequestBody AudioClassifyMoveBO bo) throws BusinessException;
}
