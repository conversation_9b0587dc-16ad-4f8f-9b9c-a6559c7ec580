package com.dbj.classpal.books.client.dto.books;

import com.dbj.classpal.books.client.dto.books.app.BooksRankInCodesContentsTreeAppDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 图书书内码分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="BooksRankInCodesContents对象", description="图书书内码分类表")
public class BooksTreeDTO implements Serializable {



    @Schema(description = "图书名称")
    private String bookName;

    @Schema(description = "册书集合")
    private List<BooksRankInTreeDTO> booksRankInTreeDTOList;

}
