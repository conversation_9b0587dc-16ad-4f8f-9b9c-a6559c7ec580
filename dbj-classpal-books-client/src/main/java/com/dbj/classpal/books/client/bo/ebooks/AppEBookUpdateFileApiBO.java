package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * 单书更新文件业务对象
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "单书更新文件业务对象")
public class AppEBookUpdateFileApiBO implements Serializable {

    @Schema(description = "单书ID")
    @NotNull(message = "单书ID不能为空")
    private Integer id;

    @Schema(description = "文件Id")
    private Integer fileId;

    @Schema(description = "文件URL")
    @NotNull(message = "文件URL不能为空")
    private String fileUrl;

    @Schema(description = "文件名称")
    @NotNull(message = "文件名称不能为空")
    private String fileName;

    @Schema(description = "文件md5")
    private String fileMd5;

    @Schema(description = "文件大小")
    @NotNull(message = "文件大小不能为空")
    private Double fileSize;
}