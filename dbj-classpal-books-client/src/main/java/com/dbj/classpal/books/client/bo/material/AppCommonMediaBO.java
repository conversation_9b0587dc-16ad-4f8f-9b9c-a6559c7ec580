package com.dbj.classpal.books.client.bo.material;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Schema
@Data
public class AppCommonMediaBO implements Serializable {

    /**
     * 素材库ID
     */
    @NotNull
    @Schema(description = "素材库ID")
    private Integer materialId;
    /**
     * 素材库地址
     */
    @NotBlank
    @Schema(description = "素材库地址")
    private String materialPath;

    @Max(99999)
    @Schema(description = "排序")
    private Integer orderNum;
}
