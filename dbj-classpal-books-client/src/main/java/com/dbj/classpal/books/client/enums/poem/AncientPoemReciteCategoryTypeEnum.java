package com.dbj.classpal.books.client.enums.poem;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@Schema(description = "古诗背诵分类")
public enum AncientPoemReciteCategoryTypeEnum {
    SYSTEM("system", "系统"),
    OTHER("other", "其他");

    private final String code;
    private final String desc;

    public static AncientPoemReciteCategoryTypeEnum fromCode(String code) {
        for (AncientPoemReciteCategoryTypeEnum type : values()) {
            if (type.getCode().equalsIgnoreCase(code)) {
                return type;
            }
        }
        return null;
    }
} 