package com.dbj.classpal.books.client.dto.books;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 产品分类配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("books_category")
@Tag(name="BooksCategory对象", description="产品分类配置表")
public class BooksCategoryNameTreeApiDTO implements Serializable {



    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "名称")
    private String nameTree;

}
