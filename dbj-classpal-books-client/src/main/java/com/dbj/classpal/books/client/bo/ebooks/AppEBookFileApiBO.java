package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * 单书更新文件业务对象
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "单书解析文件业务对象")
public class AppEBookFileApiBO implements Serializable {

    @Schema(description = "文件Id")
    @NotEmpty(message = "文件Id不能为空")
    private Integer fileId;

    @Schema(description = "文件URL")
    @NotEmpty(message = "文件URL不能为空")
    private String fileUrl;

    @Schema(description = "文件名称")
    @NotEmpty(message = "文件名称不能为空")
    private String fileName;

    @Schema(description = "文件大小")
    @NotNull(message = "文件大小不能为空")
    private Double fileSize;

    @Schema(description = "文件md5")
    @NotEmpty(message = "文件md5不能为空")
    private String fileMd5;
}