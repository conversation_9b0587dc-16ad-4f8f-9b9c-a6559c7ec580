package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 点读书解析状态更新API BO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Schema(name = "PointReadingBookParseStatusApiBO", description = "点读书解析状态更新API BO")
public class PointReadingBookParseStatusApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "点读书ID")
    @NotNull(message = "点读书ID不能为空")
    private Integer id;

    @Schema(description = "解析状态：10-待解析 20-解析中 30-已解析 40-解析失败")
    @NotNull(message = "解析状态不能为空")
    private Integer parseStatus;
}
