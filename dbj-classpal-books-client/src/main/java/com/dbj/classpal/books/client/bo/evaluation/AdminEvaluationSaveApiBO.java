package com.dbj.classpal.books.client.bo.evaluation;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationSaveApiBO
 * Date:     2025-04-15 13:35:46
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class AdminEvaluationSaveApiBO implements Serializable {

    @Schema(description = "评测名称")
    @NotEmpty(message = "评测名称不能为空")
    private String evaluationName;

    @Schema(description = "评测封面")
    private String evaluationCover;

    @Schema(description = "评测介绍")
    private String evaluationRemark;

    @Schema(description = "上下架状态 0下架 1上架")
    private Integer evaluationStatus;

    @Schema(description = "是否隐藏 0隐藏 1显示")
    private Integer evaluationVisible;

}
