package com.dbj.classpal.books.client.bo.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 预置提示音表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AudioSpeakerBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "音色类型：1 男声 2 女声 3 童声 4 多情感")
    private List<Integer> voiceType;

    @Schema(description = "音色名称")
    private String voiceName;

    @Schema(description = "语言类型：1 中文 2 英文 3 粤语")
    private List<Integer> language;


}
