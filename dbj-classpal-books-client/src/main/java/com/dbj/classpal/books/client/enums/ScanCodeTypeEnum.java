package com.dbj.classpal.books.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: BusinessTypeEnum
 * Date:     2025-04-14 08:48:01
 * Description: 表名： ,描述： 表
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ScanCodeTypeEnum {

    b1("b1","册数H5码"),
    b2("b2","册数新印码"),
    b3("b3","书内码H5码"),
    b4("b4","书内码新印码");

    private String code;
    private String name;

    public static ScanCodeTypeEnum getByCode(String code) {
        for (ScanCodeTypeEnum value : ScanCodeTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
