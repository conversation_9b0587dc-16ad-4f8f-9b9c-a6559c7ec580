package com.dbj.classpal.books.client.bo.album;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumElementsUpdateStatusApiBO
 * Date:     2025-04-15 13:35:46
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
@Tag(name = "修改专辑简介BO")
public class AppAlbumElementsUpdateStatusApiBO implements Serializable {
    @Schema(description = "主键ID",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键ID不能为空")
    private Integer id;

    @Schema(description = "专辑隐藏状态  0隐藏 1显示")
    @NotNull(message = "专辑隐藏状态不能为空")
    private Integer albumStatus;
}
