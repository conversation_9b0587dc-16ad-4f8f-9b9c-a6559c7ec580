package com.dbj.classpal.books.client.bo.books.app;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 图书卷册赠册用户学习时长表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="BooksRankStudyTimeLog对象", description="图书卷册赠册用户学习时长表")
public class BooksRankStudyTimeLogBO implements Serializable {




    @Schema(description ="书内码内容目录id")
    private Integer inCodesContentsId;

    @Schema(description ="时常 按秒存")
    private Integer studyTime;
}
