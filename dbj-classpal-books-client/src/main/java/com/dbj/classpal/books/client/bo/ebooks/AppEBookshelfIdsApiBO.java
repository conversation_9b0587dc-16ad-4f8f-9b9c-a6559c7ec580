package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 书架ID列表业务参数对象
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
@Data
@Schema(description = "书架ID列表参数BO")
public class AppEBookshelfIdsApiBO implements Serializable {

    @Schema(description = "书架ID列表")
    @NotEmpty(message = "书架ID列表不能为空")
    private List<Integer> ids;
} 