package com.dbj.classpal.books.client.dto.pinyin;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 拼音分类DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Tag(name="拼音分类DTO", description="拼音分类DTO")
public class PinyinClassifyDTO implements Serializable {

    @Schema(description = "ID")
    private Integer id;

    @Schema(description = "拼音")
    private String pinyin;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "排序权重")
    private Integer sort;
}
