package com.dbj.classpal.books.client.bo.material;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialReNameApiBO
 * Date:     2025-04-11 16:46:18
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class AppMaterialReNameApiBO implements Serializable {

    @Schema(description = "主键id",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键id不能为空")
    private Integer id;

    @Schema(description = "资源名称",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "资源名称不能为空")
    @Size(min = 1, max = 100,message = "资源名称名称范围在1~100字符内")
    private String materialName;
}
