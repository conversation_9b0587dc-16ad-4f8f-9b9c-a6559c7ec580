package com.dbj.classpal.books.client.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Schema
@Data
public class QuestionQueryApiBO implements Serializable {

    /**
     * 分类ID
     */
    @Schema(description = "分类ID")
    private Integer categoryId;

    /**
     * 题目类型 0-单选题 1-多选题 2-判断题 3-填空题 4-排序题，5-完形填空
     */
    @Schema(description = "题目类型")
    private Integer type;

    /**
     * 题目标题关键字
     */
    @Schema(description = "题目标题关键字")
    private String titleKeyword;

    /**
     * 状态 0-禁用 1-启用
     */
    @Schema(description = "状态")
    private Integer status;
} 