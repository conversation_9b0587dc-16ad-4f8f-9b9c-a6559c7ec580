package com.dbj.classpal.books.client.api.evaluation.app;

import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.evaluation.app.AppEvaluationQueryApiBO;
import com.dbj.classpal.books.client.bo.evaluation.app.AppUserPaperEvaluationSaveApiBO;
import com.dbj.classpal.books.client.dto.evaluation.app.AppEvaluationQueryApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AppEvaluationApi {

    /**
     * app客户端查询评测及评测项页面
     * @param bo
     * @return
     */
    @PostMapping("/app/evaluation/getEvaluation")
    RestResponse<AppEvaluationQueryApiDTO> getEvaluation(@RequestBody @Valid AppEvaluationQueryApiBO bo) throws BusinessException;
}
