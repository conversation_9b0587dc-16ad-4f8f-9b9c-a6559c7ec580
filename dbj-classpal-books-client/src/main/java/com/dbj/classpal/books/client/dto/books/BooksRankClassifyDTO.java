package com.dbj.classpal.books.client.dto.books;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 图书配置-图书内容分类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="BooksRankClassify对象", description="图书配置-图书内容分类")
public class BooksRankClassifyDTO implements Serializable {


    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "类型名称")
    private String name;

    @Schema(description = "书内码 bookCodes")
    private String type;

    @Schema(description = "册数id")
    private Integer rankId;

    @Schema(description = "权重用于排序")
    private Integer weight;


}
