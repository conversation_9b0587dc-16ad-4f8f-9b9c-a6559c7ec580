package com.dbj.classpal.books.client.bo.studycenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "学习进度-新增ApiBO")
public class AppStudyModuleProgressCreateApiBO {
    @Schema(description = "用户ID")
    private Integer userId;
    @Schema(description = "学习模块ID")
    private Integer moduleId;

    @Schema(description = "最后学习时间")
    private Date lastLearnTime;
} 