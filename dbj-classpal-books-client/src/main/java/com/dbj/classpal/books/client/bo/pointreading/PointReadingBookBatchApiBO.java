package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 点读书批量操作API BO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Schema(name = "PointReadingBookBatchApiBO", description = "点读书批量操作API BO")
public class PointReadingBookBatchApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "点读书ID列表")
    @NotEmpty(message = "点读书ID列表不能为空")
    private List<Integer> ids;
}
