package com.dbj.classpal.books.client.bo.studycenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "学习进度-编辑ApiBO")
public class AppStudyModuleProgressUpdateApiBO extends AppStudyModuleProgressCreateApiBO {
    @Schema(description = "主键ID")
    private Integer id;
}
 