package com.dbj.classpal.books.client.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema
@Data
public class QuestionAnswerSaveApiBO implements Serializable {

    /**
     * 媒体文件url
     */
    @Schema(description = "媒体文件url")
    private List<QuestionMediaApiBO> mediaUrl;


    /**
     * 序号
     */
    @Schema(description = "序号")
    private Integer serialNo;

    /**
     * 选项名称
     */
    @Schema(description = "选项名称")
    @Size(min = 1, max = 8, message = "选项名称长度必须在 {min} 到 {max} 个字符之间")
    private String optionName;

    /**
     * 选项内容
     */
    @Schema(description = "选项内容")
    @Size(min = 1, max = 128, message = "选项名称长度必须在 {min} 到 {max} 个字符之间")
    private String optionContent;

    /**
     * 是否为答案 0-否 1-是
     */
    @Schema(description = "是否为答案 0-否 1-是")
    private Integer isAnswer;
} 