package com.dbj.classpal.books.client.bo.studycenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "学习模块资源关联-分页查询ApiBO")
public class AppStudyModuleResourceRelQueryPageApiBO {
    @Schema(description = "学习模块ID")
    private Integer moduleId;
    @Schema(description = "资源类型")
    private String resourceType;
    @Schema(description = "资源ID")
    private Integer resourceId;
} 