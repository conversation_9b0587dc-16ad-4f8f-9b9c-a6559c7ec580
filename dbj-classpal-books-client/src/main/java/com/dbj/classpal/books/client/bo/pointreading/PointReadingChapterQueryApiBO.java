package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 点读书章节查询API BO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingPageQueryApiBO", description = "点读书章节查询API BO")
public class PointReadingChapterQueryApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "章节名称")
    private String name;

    @Schema(description = "所属目录ID")
    private Integer menuId;

    @Schema(description = "状态：0-停用 1-正常")
    private Integer status;
}
