package com.dbj.classpal.books.client.bo.material;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialQueryFilterBO
 * Date:     2025-04-24 15:27:55
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class AppMaterialQueryFilterApiBO implements Serializable {

    @Schema(description = "资源类型 不传参数-全部文件 2图片  3音频 4视频 5文档 6压缩包")
    private Integer materialType;

    @Schema(description = "资源类型后缀名")
    private List<String> materialExtensions;
}
