package com.dbj.classpal.books.client.api.poem;

import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionSaveBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionSortBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionUpdateBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionUpdateCoverUrlBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionUpdateDescBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionUpdateStatusBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionUpdateTitleBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemReciteCollectionDetailDTO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemReciteCollectionDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className AncientPoemReciteCategoryApi
 * @description
 * @date 2025-05-26 09:09
 **/
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AncientPoemReciteCollectionApi {

    /**
     * 分页查询
     */
    @PostMapping("/ancientPoemReciteCollection/listAncientPoemReciteCollection")
    RestResponse<List<AncientPoemReciteCollectionDTO>> listAncientPoemReciteCollection(@RequestBody AncientPoemReciteCollectionBO ancientPoemReciteCollectionBO);

    /**
     * 获取详情
     */
    @PostMapping("/ancientPoemReciteCollection/get")
    RestResponse<AncientPoemReciteCollectionDetailDTO> getAncientPoemReciteCollection(@RequestParam Integer id) throws BusinessException;

    /**
     * 删除
     */
    @PostMapping("/ancientPoemReciteCollection/batchDelete")
    RestResponse<Boolean> deleteAncientPoemReciteCollection(@RequestParam List<Integer> ids) throws BusinessException;
    /**
     * 保存
     */
    @PostMapping("/ancientPoemReciteCollection/save")
    RestResponse<Boolean> saveAncientPoemReciteCollection(@RequestBody AncientPoemReciteCollectionSaveBO ancientPoemReciteCollectionSaveBO) throws BusinessException;
    @PostMapping("/ancientPoemReciteCollection/updateTitle")
    RestResponse<Boolean> updateTitle(@RequestBody AncientPoemReciteCollectionUpdateTitleBO ancientPoemReciteCollectionUpdateTitleBO) throws BusinessException;
    @PostMapping("/ancientPoemReciteCollection/updateDescription")
    RestResponse<Boolean> updateDescription(@RequestBody AncientPoemReciteCollectionUpdateDescBO ancientPoemReciteCollectionUpdateDescBO) throws BusinessException;
    @PostMapping("/ancientPoemReciteCollection/updateStatus")
    RestResponse<Boolean> updateStatus(@RequestBody AncientPoemReciteCollectionUpdateStatusBO ancientPoemReciteCollectionUpdateStatusBO) throws BusinessException;
    @PostMapping("/ancientPoemReciteCollection/updateCoverUrl")
    RestResponse<Boolean> updateCoverUrl(@RequestBody AncientPoemReciteCollectionUpdateCoverUrlBO anotherCollectionUpdateCoverUrlBO) throws BusinessException;

    @PostMapping("/ancientPoemReciteCollection/update")
    RestResponse<Boolean> updateAncientPoemReciteCollection(@RequestBody AncientPoemReciteCollectionUpdateBO ancientPoemReciteCollectionUpdateBO) throws BusinessException;


    @PostMapping("/ancientPoemReciteCollection/sort")
    RestResponse<Boolean> sort(@RequestBody List<AncientPoemReciteCollectionSortBO> sortBOList) throws BusinessException;


}
