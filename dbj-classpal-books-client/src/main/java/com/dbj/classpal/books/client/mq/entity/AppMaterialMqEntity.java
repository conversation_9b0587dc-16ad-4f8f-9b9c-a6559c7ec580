package com.dbj.classpal.books.client.mq.entity;
import com.dbj.classpal.framework.mq.entity.RabbitmqEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AppMaterialMqEntity extends RabbitmqEntity implements Serializable {

    @Schema(description = "状态码")
    private Integer code;

    @Schema(description = "错误消息")
    private String msg;

    @Schema(description = "数据内容")
    private String paramJson;

    @Schema(description = "转码后素材Id")
    private Integer finalMaterialId;
}
