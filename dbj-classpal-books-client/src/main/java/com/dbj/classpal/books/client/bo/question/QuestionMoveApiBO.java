package com.dbj.classpal.books.client.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema
@Data
public class QuestionMoveApiBO implements Serializable {

    /**
     * 源题目ID列表
     */
    @Schema(description = "源题目ID列表")
    @NotNull(message = "源题目ID列表不能为空")
    private List<Integer> questionIds;

    /**
     * 目标分类ID
     */
    @Schema(description = "目标分类ID")
    @NotNull(message = "目标分类ID不能为空")
    private Integer targetCategoryId;
} 