package com.dbj.classpal.books.client.bo.tree;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 树形分类菜单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
public class TreeClassifyListBO implements Serializable {

    @NotNull
    @Schema(description = "业务类型 1-古诗词分类", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer type;

    @Schema(description = "分类名称")
    private String name;
}
