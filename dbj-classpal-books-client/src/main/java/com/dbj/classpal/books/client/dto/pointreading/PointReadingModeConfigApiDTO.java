package com.dbj.classpal.books.client.dto.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 点读书模式配置API DTO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingModeConfigApiDTO", description = "点读书模式配置API DTO")
public class PointReadingModeConfigApiDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "配置ID")
    private Integer id;

    @Schema(description = "模式配置字典ID")
    private Integer configDictId;

    @Schema(description = "配置字典名称")
    private String configDictName;

    @Schema(description = "点读书ID")
    private Integer bookId;

    @Schema(description = "点读书名称")
    private String bookName;

    @Schema(description = "显示名称")
    private String displayName;

    @Schema(description = "处理状态：10-处理中 20-处理完成 30-处理失败")
    private Integer processStatus;

    @Schema(description = "处理状态描述")
    private String processStatusDesc;

    @Schema(description = "原始文件ID")
    private String originalFileId;

    @Schema(description = "原始文件URL")
    private String originalUrl;

    @Schema(description = "原始文件名")
    private String originalName;

    @Schema(description = "原始文件MD5")
    private String originalMd5;

    @Schema(description = "排序")
    private Integer sortNum;

    @Schema(description = "状态：0-停用 1-正常")
    private Integer status;

    @Schema(description = "状态描述")
    private String statusDesc;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
