package com.dbj.classpal.books.client.bo.poem;


import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.util.Set;

/**
 * <p>
 * 古诗文MoveBO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Tag(name="古诗文MoveBO", description="古诗文MoveBO")
public class AncientPoemMoveBO implements Serializable {
    @NotNull
    @NotEmpty
    @Schema(description = "古诗文id集合", requiredMode = Schema.RequiredMode.REQUIRED)
    private Set<Integer> ids;

    @NotNull
    @Schema(description = "新古诗文分类", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer newClassifyId;
}
