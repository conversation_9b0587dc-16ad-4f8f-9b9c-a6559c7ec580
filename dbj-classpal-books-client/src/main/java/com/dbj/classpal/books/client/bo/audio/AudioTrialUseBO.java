package com.dbj.classpal.books.client.bo.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 预置提示音表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AudioTrialUseBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "音频简介id")
    @NotNull(message = "音频简介id不能为空")
    private Integer audioIntroId;

    @Schema(description = "tts格式音频文本")
    @NotEmpty(message = "tts格式音频文本不能为空")
    private String text;

    @Schema(description = "发音人")
    private String voice;

    @Schema(description = "音量")
    private Integer volume;

    @Schema(description = "语速，取值范围：-500~500")
    private Integer speechRate;

    @Schema(description = "语调，取值范围：-500~500")
    private Integer pitchRate;

//    @Schema(description = "全局配置")
    private AudioGlobalConfigAddBO audioGlobalConfig;


}
