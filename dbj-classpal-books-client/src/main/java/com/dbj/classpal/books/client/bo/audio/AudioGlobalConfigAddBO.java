package com.dbj.classpal.books.client.bo.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 预置提示音表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AudioGlobalConfigAddBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "音频简介ID")
    @NotNull(message = "音频简介ID不能为空")
    private Integer audioIntroId;

    @Schema(description = "全局配置类型：1 背景音 2 特效")
    @NotNull(message = "全局配置类型不能为空")
    private Integer type;

    @Schema(description = "背景音类型：1 预置 2 自定义")
    @NotNull(message = "背景音类型不能为空")
    private Integer audioType;

    @Schema(description = "全局背景音id(预置背景音id or 自定义背景音id)")
    @NotNull(message = "全局背景音id不能为空")
    private Integer audioBackgroundId;

    @Schema(description = "全局特效字典code")
    private String dictEffectsCode;

    @Schema(description = "（全局背景音）音量")
    @NotNull(message = "（全局背景音）音量不能为空")
    private Integer volume;

    @Schema(description = "（全局背景音）模式：1 循环播放 2 播放一次")
    @NotNull(message = "（全局背景音）模式不能为空")
    private Integer model;

}
