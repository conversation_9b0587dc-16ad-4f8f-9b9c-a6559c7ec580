package com.dbj.classpal.books.client.api.poem.app;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionSaveBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionSortBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionUpdateCoverUrlBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionUpdateDescBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionUpdateStatusBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionUpdateTitleBO;
import com.dbj.classpal.books.client.bo.poem.app.AppAncientPoemReciteCollectionPageBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemReciteCollectionDTO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemReciteCollectionDetailDTO;
import com.dbj.classpal.books.client.dto.poem.app.AppAncientPoemReciteCollectionPageDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className AncientPoemReciteCategoryApi
 * @description
 * @date 2025-05-26 09:09
 **/
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AppAncientPoemReciteCollectionApi {

    /**
     * 分页查询
     */
    @PostMapping("/ancientPoemReciteCollection/pageAncientPoemReciteCollection")
    RestResponse<Page<AppAncientPoemReciteCollectionPageDTO>> pageAncientPoemReciteCollection(@RequestBody PageInfo<AppAncientPoemReciteCollectionPageBO> pageInfo);


}
