package com.dbj.classpal.books.client.dto.poem;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className AncientPoemReciteCategoryPageDTO
 * @description
 * @date 2025-05-26 09:10
 **/
@Data
public class AncientPoemReciteCategoryDTO  implements Serializable {

    @Schema(description = "ID")
    private Integer id;

    @Schema(description = "类型 system 系统 other 其他 系统类型不让更改")
    @TableField("type")
    private String type;

    @Schema(description = "分类名称")
    @TableField("name")
    private String name;

    @Schema(description = "排序权重")
    @TableField("sort")
    private Integer sort;
}
