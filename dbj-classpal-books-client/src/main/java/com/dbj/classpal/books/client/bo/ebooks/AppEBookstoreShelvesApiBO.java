package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 书城书架管理业务参数对象
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
@Data
@Schema(description = "书城书架管理参数BO")
public class AppEBookstoreShelvesApiBO implements Serializable {

    @Schema(description = "书城ID")
    private Integer storeId;
    
    @Schema(description = "书架ID列表")
    private List<Integer> shelfIds;
    
    @Schema(description = "书架排序映射，key为书架ID，value为排序序号",hidden = true)
    private Map<Integer, Integer> shelfSortMap;
} 