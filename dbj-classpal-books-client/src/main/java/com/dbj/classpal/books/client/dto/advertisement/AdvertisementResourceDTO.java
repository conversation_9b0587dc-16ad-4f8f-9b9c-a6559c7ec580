package com.dbj.classpal.books.client.dto.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 广告页面DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Tag(name="广告页面DTO", description="广告页面DTO")
public class AdvertisementResourceDTO implements Serializable {


    /**
     * 广告页面id
     */
    @Schema(description = "广告id")
    private Integer id;

    @Schema(description = "页面名称")
    private String name;

    @Schema(description = "排序（越大越靠前）")
    private Integer sort;

    @Schema(description = "广告页面区域")
    private List<AdvertisementResourceAreaDTO> advertisementResourceAreas;
}
