package com.dbj.classpal.books.client.bo.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 预置提示音表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AudioHintMusicDelBO implements Serializable {

    @NotNull(message = "主键id不能为空")
    @Schema(description = "主键id", requiredMode = RequiredMode.REQUIRED)
    private List<Integer> ids;

    @NotNull(message = "提示音类型不能为空")
    @Schema(description = "提示音类型：1 预置提示音 2 预置背景音", requiredMode = RequiredMode.REQUIRED)
    private Integer type;


}
