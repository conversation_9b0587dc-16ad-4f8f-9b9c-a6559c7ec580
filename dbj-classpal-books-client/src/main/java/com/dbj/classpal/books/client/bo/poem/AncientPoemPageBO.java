package com.dbj.classpal.books.client.bo.poem;


import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 古诗文分页查询PageBO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Tag(name="古诗文分页查询PageBO", description="古诗文分页查询PageBO")
public class AncientPoemPageBO implements Serializable {

    @Schema(description = "分类id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer classifyId;

    @Schema(description = "封面路径")
    private String coverUrl;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "作者")
    private String author;

    @Schema(description = "所属朝代")
    private String dynasty;

    @Schema(description = "年级集合")
    private Set<String> grades;

    @Schema(description = "是否课外内容 0-课内 1-课外")
    private Integer isOut;

    @Schema(description = "节点及其子节点集合", hidden = true)
    List<Integer> subTreeIds;
}
