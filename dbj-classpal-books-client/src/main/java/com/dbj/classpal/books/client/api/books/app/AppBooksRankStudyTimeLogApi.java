package com.dbj.classpal.books.client.api.books.app;

import com.dbj.classpal.books.client.bo.books.app.BooksRankStudyTimeLogBO;
import com.dbj.classpal.books.client.bo.books.app.BooksUserShelfBO;
import com.dbj.classpal.books.client.dto.books.app.BooksUserLastStudyDTO;
import com.dbj.classpal.books.client.dto.books.app.BooksUserShelfDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AppBooksRankStudyTimeLogApi {

    /**
     * <AUTHOR>
     * @Description  我的书架
     * @Date 2025/4/21 10:29
     * @param
     * @return
     **/
    @PostMapping("/app/books/rank/studytime/report")
    RestResponse<Boolean> report(@RequestBody List<BooksRankStudyTimeLogBO> booksUserShelfBO ) throws BusinessException;

}
