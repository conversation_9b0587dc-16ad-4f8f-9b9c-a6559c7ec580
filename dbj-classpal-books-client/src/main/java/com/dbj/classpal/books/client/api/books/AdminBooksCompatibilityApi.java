package com.dbj.classpal.books.client.api.books;

import com.dbj.classpal.books.client.bo.books.BooksCompatibilityBO;
import com.dbj.classpal.books.client.bo.books.BooksCompatibilitySaveBO;
import com.dbj.classpal.books.client.bo.books.BooksCompatibilityUpdBO;
import com.dbj.classpal.books.client.dto.books.BooksCompatibilityApiDTO;
import com.dbj.classpal.books.client.dto.books.app.BooksCompatibilityApiDetailDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AdminBooksCompatibilityApi {

    @PostMapping("/books/compatibility/save")
    RestResponse<Boolean> save(@RequestBody BooksCompatibilitySaveBO booksCompatibilitySaveBO) throws BusinessException;

    @PostMapping("/books/compatibility/detail")
    RestResponse<BooksCompatibilityApiDetailDTO> detail(@RequestParam String internalCode) throws BusinessException;

    @PostMapping("/books/compatibility/list")
    RestResponse<List<BooksCompatibilityApiDTO>> list(@RequestBody BooksCompatibilityBO booksCompatibilityBO) throws BusinessException;

    @PostMapping("/books/compatibility/update")
    RestResponse<Boolean> update(@RequestBody BooksCompatibilityUpdBO booksCompatibilityUpdBO) throws BusinessException;


    @PostMapping("/books/compatibility/delete")
    RestResponse<Boolean> delete(@RequestParam Integer id) throws BusinessException;


}
