package com.dbj.classpal.books.client.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema
@Data
public class QuestionIdsApiBO implements Serializable {

    /**
     * 主键
     */
    @Schema(description = "题目ID")
    @NotNull(message = "题目ID不能为空")
    private List<Integer> ids;


} 