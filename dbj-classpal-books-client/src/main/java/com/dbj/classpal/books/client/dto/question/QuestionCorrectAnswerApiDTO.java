package com.dbj.classpal.books.client.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "题目正确答案DTO")
public class QuestionCorrectAnswerApiDTO implements Serializable {
    @Schema(description = "题目ID")
    private Integer questionId;

    @Schema(description = "题目标题")
    private String title;

    @Schema(description = "题目类型")
    private Integer type;

    /**
     * 学科名称
     */
    @Schema(description = "学科名称")
    private String subjectName;

    @Schema(description = "正确答案ID，逗号分隔")
    private String correctAnswerIds;

    @Schema(description = "正确答案内容，填空题为文本，其他题型为选项内容，逗号分隔")
    private String correctAnswer;
    
    @Schema(description = "完形填空题的空位正确答案列表")
    private List<BlankCorrectAnswerApiDTO> blankResults;
} 