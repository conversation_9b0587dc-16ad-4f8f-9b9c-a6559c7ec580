package com.dbj.classpal.books.client.dto.poem.app;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 古诗背诵合集表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AppAncientPoemReciteCollectionPageDTO implements Serializable {


    @Schema(description =  "合集ID")
    private Integer id;

    @Schema(description =  "所属分类ID")
    private Integer categoryId;

    @Schema(description =  "合集标题")
    private String title;

    @Schema(description =  "合集描述")
    private String description;

    @Schema(description =  "封面URL")
    private String coverUrl;

    @Schema(description =  "状态（0下架，1上架）")
    private Integer launchStatus;
    @Schema(description =  "古诗数量）")
    private Integer poemNum;

    @Schema(description =  "是否启用 0 否 1是")
    @TableField("status")
    private Integer status;



}
