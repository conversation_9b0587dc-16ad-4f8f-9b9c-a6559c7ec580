package com.dbj.classpal.books.client.api.poem;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemCopyBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemMoveBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemPageBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemUpsertBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemDTO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemRelateDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <p>
 * 古诗文信息表 远程Api
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AncientPoemApi {
    /**----------------------------------- admin调用Api -----------------------------------------*/
    /**
     * 分页查询古诗文信息
     * @param pageInfo 包含分页信息的请求对象
     * @return 包含分页古诗文信息的RestResponse对象
     */
    @PostMapping("/ancientPoem/getAncientPoemPage")
    RestResponse<Page<AncientPoemDTO>> getAncientPoemPage(@RequestBody @Validated PageInfo<AncientPoemPageBO> pageInfo);
    /**
     * 根据古诗文ID获取古诗文详细信息
     * @param bo 包含古诗文ID的请求对象
     * @return 包含古诗文信息的RestResponse对象
     * @throws BusinessException 业务异常
     */
    @PostMapping(value = "/ancientPoem/getAncientPoemInfo")
    RestResponse<AncientPoemDTO> getAncientPoemInfo(@RequestBody @Validated CommonIdApiBO bo) throws BusinessException;
    /**
     * 保存古诗文信息
     * @param bo 包含古诗文信息的请求对象
     * @return 包含保存古诗文信息的RestResponse对象
     * @throws BusinessException 业务异常
     */
    @PostMapping(value = "/ancientPoem/saveAncientPoem")
    RestResponse<Boolean> saveAncientPoem(@RequestBody @Validated AncientPoemUpsertBO bo) throws BusinessException;
    /**
     * 更新古诗文
     * @param bo 包含古诗文信息的请求对象
     * @return 包含更新古诗文信息的RestResponse对象
     * @throws BusinessException 业务异常
     */
    @PostMapping(value = "/ancientPoem/updateAncientPoem")
    RestResponse<Boolean> updateAncientPoem(@RequestBody @Validated AncientPoemUpsertBO bo) throws BusinessException;
    /**
     * 删除古诗文
     * @param bo 包含古诗文ID的请求对象
     * @return 包含删除古诗文信息的RestResponse对象
     * @throws BusinessException 业务异常
     */
    @PostMapping(value = "/ancientPoem/deleteAncientPoem")
    RestResponse<Boolean> deleteAncientPoem(@RequestBody @Validated CommonIdsApiBO bo) throws BusinessException;

    /**
     * 复制古诗文
     * @param bo 包含古诗文ID的请求对象
     * @return 包含删除古诗文信息的RestResponse对象
     * @throws BusinessException 业务异常
     */
    @PostMapping(value = "/ancientPoem/copyAncientPoem")
    RestResponse<Boolean> copyAncientPoem(@RequestBody @Validated AncientPoemCopyBO bo) throws BusinessException;

    /**
     * 移动古诗文
     * @param bo 包含古诗文ID的请求对象
     * @return 包含删除古诗文信息的RestResponse对象
     * @throws BusinessException 业务异常
     */
    @PostMapping(value = "/ancientPoem/moveAncientPoem")
    RestResponse<Boolean> moveAncientPoem(@RequestBody @Validated AncientPoemMoveBO bo) throws BusinessException;

    /**
     * 古诗文引用
     * @param bo 包含古诗文ID的请求对象
     * @return 包含删除古诗文信息的RestResponse对象
     * @throws BusinessException 业务异常
     */
    @PostMapping(value = "/ancientPoem/getAncientPoemRelate")
    RestResponse<List<AncientPoemRelateDTO>> getAncientPoemRelate(@RequestBody @Validated CommonIdApiBO bo) throws BusinessException;

    /**----------------------------------- App调用Api -----------------------------------------*/
}
