package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 点读书章节批量操作API BO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingPageBatchApiBO", description = "点读书章节批量操作API BO")
public class PointReadingChapterBatchApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "章节ID列表")
    @NotEmpty(message = "章节ID列表不能为空")
    private List<Integer> ids;
}
