package com.dbj.classpal.books.client.dto.poem.app;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className AncientPoemReciteAppUserAssessmentScorePageDTO
 * @description
 * @date 2025-05-28 11:13
 **/
@Data
public class AncientPoemReciteAppUserCountDTO implements Serializable {

    @Schema(description = "背诵")
    private Long totalNum;

    @Schema(description = "评语")
    private String comment;

}
