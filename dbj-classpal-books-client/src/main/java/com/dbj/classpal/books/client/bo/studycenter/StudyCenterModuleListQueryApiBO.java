package com.dbj.classpal.books.client.bo.studycenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "学习中心模块-列表查询ApiBO")
public class StudyCenterModuleListQueryApiBO {
    @Schema(description = "用户ID",hidden = true)
    private Integer userId;

    @Schema(description = "模块类型1-评测，2-音频专辑 3 -视频专辑 4-功能 5- 题库")
    private String moduleType;

    @Schema(description = "适用年级，ALL表示全部，部分年级用逗号分隔")
    private String applicableGrades;

    @Schema(description = "限制返回条数")
    private Integer limit = 10;
} 