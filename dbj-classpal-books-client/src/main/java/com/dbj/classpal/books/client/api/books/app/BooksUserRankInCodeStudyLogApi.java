package com.dbj.classpal.books.client.api.books.app;

import com.dbj.classpal.books.client.bo.books.app.BooksUserRankInCodeStudyLogBO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className BooksUserRankInCodeStudyLogApi
 * @description
 * @date 2025-04-27 10:15
 **/
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface BooksUserRankInCodeStudyLogApi {

    @PostMapping("/app/books/user/rank/in/code/study/log/save")
    RestResponse<Boolean> save(@RequestBody BooksUserRankInCodeStudyLogBO bo) throws BusinessException;
}
