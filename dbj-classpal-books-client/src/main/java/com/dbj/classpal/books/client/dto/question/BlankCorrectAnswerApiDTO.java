package com.dbj.classpal.books.client.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "完形填空空位正确答案DTO")
public class BlankCorrectAnswerApiDTO implements Serializable {
    @Schema(description = "空位区域ID")
    private Integer blankAreaId;

    @Schema(description = "空位序号")
    private Integer blankIndex;

    @Schema(description = "正确答案内容")
    private String correctAnswer;

    @Schema(description = "正确答案ID")
    private String correctAnswerIds;
} 