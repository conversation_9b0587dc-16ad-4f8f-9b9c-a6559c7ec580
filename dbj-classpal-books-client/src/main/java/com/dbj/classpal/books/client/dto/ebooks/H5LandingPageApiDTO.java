package com.dbj.classpal.books.client.dto.ebooks;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * H5落地页API响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Data
@Schema(description = "H5落地页API响应数据")
public class H5LandingPageApiDTO {

    @Schema(description = "分享类型")
    private String type;

    @Schema(description = "单书信息（b5类型有值）")
    private AppEBookLandingApiDTO ebook;

    @Schema(description = "书架信息（b6类型有值，或在书城中查看具体书架时有值）")
    private AppEBookshelfLandingApiDTO bookshelf;

    @Schema(description = "书城信息（b7类型有值）")
    private AppEBookstoreLandingApiDTO bookstore;

    @Schema(description = "书架分页列表（b7类型有值）")
    private Page<AppEBookshelfLandingApiDTO> bookshelves;

    @Schema(description = "单书分页列表（b6类型有值）")
    private Page<AppEBookLandingApiDTO> ebooks;

    @Schema(description = "上下文信息",hidden = true)
    private ContextInfo context;

    @Schema(description = "页面标题")
    private String pageTitle;

    @Schema(description = "是否有效")
    private Boolean valid;

    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 上下文信息类
     */
    @Data
    @Schema(description = "上下文信息")
    public static class ContextInfo {

        @Schema(description = "当前书城ID（如果在书城上下文中）")
        private Integer currentStoreId;

        @Schema(description = "当前书城标题")
        private String currentStoreTitle;

        @Schema(description = "当前书架ID（如果在书架上下文中）")
        private Integer currentShelfId;

        @Schema(description = "当前书架标题")
        private String currentShelfTitle;

        @Schema(description = "面包屑导航")
        private String breadcrumb;
    }
}
