package com.dbj.classpal.books.client.dto.books;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * <p>
 * 产品分类配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="BooksUserRef对象", description="产品分类配置表")
public class BooksScanCountDTO implements Serializable {


    @Schema(description ="图书id")
    private Integer booksId;

    @Schema(description =  "用户添加数量")
    private Long scanCount;
}
