package com.dbj.classpal.books.client.dto.poem.app;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className AncientPoemReciteAppUserAssessmentScorePageDTO
 * @description
 * @date 2025-05-28 11:13
 **/
@Data
public class AncientPoemReciteAppUserAssessmentScorePageDTO implements Serializable {

    @Schema(description = "ID")
    private Integer id;

    @Schema(description = "古诗文id")
    private Integer ancientPoemId;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "标题拼音")
    private String titlePinyin;

    @Schema(description = "古诗文简介")
    private String introduction;

    @Schema(description = "封面路径")
    private String coverUrl;

    @Schema(description = "作者")
    private String author;

    @Schema(description = "所属朝代")
    private String dynasty;

    @Schema(description = "年级")
    private String grade;

    @Schema(description = "是否课外内容 0-课内 1-课外")
    private Integer isOut;

    @Schema(description = "总分（0-100）")
    private BigDecimal totalScore;


}
