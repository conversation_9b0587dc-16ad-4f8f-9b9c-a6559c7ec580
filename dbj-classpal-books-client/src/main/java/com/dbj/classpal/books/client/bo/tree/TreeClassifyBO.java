package com.dbj.classpal.books.client.bo.tree;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 树形分类菜单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
public class TreeClassifyBO implements Serializable {


    @Schema(description = "id")
    private Integer id;

    @Schema(description = "分类名称")
    private String name;

    @Schema(description = "父分类id, null表示顶级分类")
    private Integer parentId;

    @Schema(description = "分类级别，0为顶级分类")
    private Integer level;

    @Schema(description = "业务类型")
    private String type;

    @Schema(description = "排序权重")
    private Integer sort;
}
