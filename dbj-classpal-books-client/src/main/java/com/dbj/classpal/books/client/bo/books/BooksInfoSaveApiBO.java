package com.dbj.classpal.books.client.bo.books;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 图书表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="图书保存对象", description="图书表")
public class BooksInfoSaveApiBO implements Serializable {



    @Schema(description = "产品名称")
    @NotEmpty(message = "产品名称不能为空")
    private String bookName;
    @Schema(description = "简介")
    private String blurb;

    @Schema(description = "编者")
    private String editorName;

    @Schema(description = "ISBN")
    private String isbn;

    @Schema(description = "分类id")
    @NotEmpty(message = "分类id不能为空")
    private List<Integer> categoryIds;

    @Schema(description = "出版单位")
    private String publisher;

    @Schema(description = "物流编码")
    private String logisticsCode;

    @Schema(description = "唯一编码")
    private String code;

    @Schema(description = "卷册数")
    private Integer volumeNum;

    @Schema(description = "是否隐藏")
    @NotNull(message = "是否隐藏不能为空")
    private Integer isHide;

    @Schema(description = "上下架状态  上架 下架")
    @NotNull(message = "上下架状态不能为空")
    private Integer launchStatus;

    @Schema(description = "是否启用 1-是 0-否")
    private Integer status;

    @Schema(description = "印匠图书id")
    @NotNull(message = "关联图书不能为空")
    private Integer printBooksId;

    @Schema(description = "卷册/赠册")
    private List<BooksRankInfoSaveApiBO> booksRankInfoApiBOList;




}
