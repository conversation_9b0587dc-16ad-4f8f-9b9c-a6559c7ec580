package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * 点读书MD5查询API BO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Schema(name = "PointReadingBookMd5ApiBO", description = "点读书MD5查询API BO")
public class PointReadingBookMd5ApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "文件MD5")
    @NotBlank(message = "文件MD5不能为空")
    private String md5;
}
