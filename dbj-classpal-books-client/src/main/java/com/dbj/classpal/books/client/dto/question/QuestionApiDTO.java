package com.dbj.classpal.books.client.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "题目DTO")
public class QuestionApiDTO implements Serializable {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Integer id;

    /**
     * 分类id
     */
    @Schema(description = "分类id")
    private Integer questionCategoryId;

    /**
     * 题目
     */
    @Schema(description = "题目")
    private String title;

    /**
     * 媒体文件类型 1-图片 2-音频 3-视频
     */
    @Schema(description = "媒体文件类型 1-文本 2-图片,3-音频,4-视频")
    private Integer mediaType;

    /**
     * 媒体文件url
     */
    @Schema(description = "媒体文件url")
    private List<QuestionMediaApiDTO> mediaUrl;

    @Schema(description = "辅助识图")
    private List<QuestionRecognitionApiDTO> aidedRecognitionUrl;


    /**
     * 题目类型 0-单选题 1-多选题 2-判断题 3-填空题 4-排序题，5-完形填空
     */
    @Schema(description = "题目类型 0-单选题 1-多选题 2-判断题 3-填空题 4-排序题，5-完形填空")
    private Integer type;

    /**
     * 题目类型名称
     */
    @Schema(description = "题目类型名称")
    private String typeName;

    /**
     * 选项类型 1-文本 2-图片,3-音频,4-视频
     */
    @Schema(description = "选项类型 1-文本 2-图片,3-音频,4-视频")
    private Integer optionType;

    /**
     * 权重
     */
    @Schema(description = "权重")
    private Integer weight;

    /**
     * 选择题答案id（多个英文逗号隔开）
     */
    @Schema(description = "选择题答案id（多个英文逗号隔开）")
    private String answer;

    /**
     * 解析
     */
    @Schema(description = "解析")
    private String analyzes;

    /**
     * 是否启用 1-是 0-否
     */
    @Schema(description = "是否启用 1-是 0-否")
    private Boolean status;

    /**
     * 答案列表
     */
    @Schema(description = "答案列表")
    private List<QuestionAnswerApiDTO> answers;

    /**
     * 完形填空区域列表
     */
    @Schema(description = "完形填空区域列表")
    private List<QuestionBlankAreaApiDTO> blankAreas;

    /**
     * 学科ID
     */
    @Schema(description = "学科ID")
    private Integer subjectId;

    /**
     * 学科名称
     */
    @Schema(description = "学科名称")
    private String subjectName;

    /**
     * 引用数量
     */
    @Schema(description = "引用数量")
    private Integer referenceCount;

    /**
     * 缩略图URL
     */
    @Schema(description = "缩略图URL")
    private String thumbnailUrl;
}