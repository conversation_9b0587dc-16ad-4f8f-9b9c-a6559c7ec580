package com.dbj.classpal.books.client.dto.evaluation.app;

import com.dbj.classpal.books.client.dto.evaluation.AdminUserPaperEvaluationAnalysisQueryApiDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationReportQueryDTO
 * Date:     2025-05-20 08:55:36
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
@Tag(name = "评测报告(详情)",description = "评测报告(详情)")
public class AppEvaluationReportQueryApiDTO implements Serializable {
    @Schema(description = "评测报告主键id")
    private Integer id;

    @Schema(description = "学员")
    private Integer userId;

    @Schema(description = "学员名称")
    private String userName;

    @Schema(description = "学员UID")
    private String uid;

    @Schema(description = "年级")
    private String gradeName;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "评测名称")
    private String evaluationName;

    @Schema(description = "评测时间")
    private LocalDateTime generatedTime;

    @Schema(description = "综合评价")
    private String evaluation;

    @Schema(description = "各模块答题情况")
    List<AdminUserPaperEvaluationAnalysisQueryApiDTO>analysisQueryApiDTOList;

}
