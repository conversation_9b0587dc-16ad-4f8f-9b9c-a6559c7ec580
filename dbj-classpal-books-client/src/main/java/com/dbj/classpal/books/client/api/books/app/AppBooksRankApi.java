package com.dbj.classpal.books.client.api.books.app;

import com.dbj.classpal.books.client.bo.books.BooksRankInfoApiBO;
import com.dbj.classpal.books.client.dto.books.BooksRankInfoApiDTO;
import com.dbj.classpal.books.client.dto.material.AppMaterialBusinessRefMaterialQueryApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AppBooksRankApi {

    
    /**
     * <AUTHOR>
     * @Description  app根据图书id查询册数目录
     * @Date 2025/4/16 14:40 
     * @param 
     * @return 
     **/
    @PostMapping("/app/books/rank/list")
    RestResponse<List<BooksRankInfoApiDTO>> list(@RequestBody BooksRankInfoApiBO bookRankInfoApiBO) throws BusinessException;


    /**
     * 查询引用列表
     * @return
     */
    @PostMapping("/app/materialBusinessRef/refBusinessListByRankId")
    RestResponse<List<AppMaterialBusinessRefMaterialQueryApiDTO>> refBusinessListByRankId(@RequestParam Integer rankId) throws BusinessException;

}
