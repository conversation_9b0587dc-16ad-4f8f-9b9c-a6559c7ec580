package com.dbj.classpal.books.client.dto.material;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialBusinessRefDTO
 * Date:     2025-04-14 08:54:29
 * Description: 表名： ,描述： 表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@Tag(name = "文件已使用大小查询DTO")
public class AppMaterialStatisticsSizeApiDTO implements Serializable {
    @Schema(description = "查询文件已使用大小（kb）")
    private Double size;

}
