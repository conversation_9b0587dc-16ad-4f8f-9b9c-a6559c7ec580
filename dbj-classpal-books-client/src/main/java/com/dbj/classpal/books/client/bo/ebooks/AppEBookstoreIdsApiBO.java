package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 书城ID列表业务参数对象
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
@Data
@Schema(description = "书城ID列表参数BO")
public class AppEBookstoreIdsApiBO implements Serializable {

    @Schema(description = "书城ID列表")
    @NotEmpty(message = "书城ID列表不能为空")
    private List<Integer> ids;
} 