package com.dbj.classpal.books.client.dto.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 分享链接结果API响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@Schema(description = "分享链接结果")
public class ShareUrlResultApiDTO implements Serializable {

    @Schema(description = "业务类型")
    private String businessType;

    @Schema(description = "业务ID")
    private Integer businessId;

    @Schema(description = "业务名称（书名/书架名/书城名）")
    private String businessName;

    @Schema(description = "分享链接")
    private String shareUrl;

    @Schema(description = "分享二维码")
    private String shareQrCode;

    @Schema(description = "分享状态：0-未分享，1-已分享")
    private Integer shareStatus;

    @Schema(description = "分享时间")
    private String shareTime;
}
