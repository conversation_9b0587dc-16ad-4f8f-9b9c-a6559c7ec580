package com.dbj.classpal.books.client.api.tree;

import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.tree.TreeClassifyAddBO;
import com.dbj.classpal.books.client.bo.tree.TreeClassifyEditBO;
import com.dbj.classpal.books.client.bo.tree.TreeClassifyListBO;
import com.dbj.classpal.books.client.bo.tree.TreeClassifyMoveBO;
import com.dbj.classpal.books.client.dto.tree.TreeClassifyDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <p>
 * 公共树 远程Api
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface TreeClassifyApi {
    /**----------------------------------- admin调用Api -----------------------------------------*/
    /**
     * 获取树
     * @param bo 树参数对象
     * @return 树列表
     * @throws BusinessException 业务异常
     */
    @PostMapping("/tree/getTree")
    RestResponse<List<TreeClassifyDTO>> getTree(@Validated @RequestBody TreeClassifyListBO bo);

    /**
     * 添加树节点
     * @param bo 节点信息
     * @return 添加结果
     * @throws BusinessException 业务异常
     */
    @PostMapping(value = "/tree/addNode")
    RestResponse<Boolean> addNode(@Validated @RequestBody TreeClassifyAddBO bo) throws BusinessException;

    /**
     * 重命名树节点
     * @param bo 节点信息
     * @return 重命名结果
     * @throws BusinessException 业务异常
     */
    @PostMapping(value = "/tree/renameNode")
    RestResponse<Boolean> renameNode(@Validated @RequestBody TreeClassifyEditBO bo) throws BusinessException;

    /**
     * 删除树节点
     * @param bo 节点信息
     * @return 删除结果
     * @throws BusinessException 业务异常
     */
    @PostMapping("/tree/deleteNode")
    RestResponse<Boolean> deleteNode(@Validated @RequestBody CommonIdApiBO bo) throws BusinessException;
    /**
     * 移动树节点
     * @param node 移动树节点
     * @return 移动结果
     * @throws BusinessException 业务异常
     */
    @PutMapping(value = "/tree/moveNode")
    RestResponse<Boolean> moveNode(@Validated @RequestBody TreeClassifyMoveBO node) throws BusinessException;

}
