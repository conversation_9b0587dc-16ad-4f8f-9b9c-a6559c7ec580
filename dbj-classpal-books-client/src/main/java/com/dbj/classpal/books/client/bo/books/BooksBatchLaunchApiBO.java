package com.dbj.classpal.books.client.bo.books;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 图书表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="图书批量上下架对象", description="图书表")
public class BooksBatchLaunchApiBO implements Serializable {

    @Schema(description = "上下架状态  上架 下架")
    private Integer launchStatus;

    @Schema(description = "唯一编码")
    private List<Integer> ids;
}
