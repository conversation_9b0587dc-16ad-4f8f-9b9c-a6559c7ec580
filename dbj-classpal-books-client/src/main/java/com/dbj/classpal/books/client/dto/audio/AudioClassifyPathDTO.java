package com.dbj.classpal.books.client.dto.audio;

import com.dbj.classpal.framework.utils.util.TreeVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2025/6/30 13:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AudioClassifyPathDTO extends TreeVO<Integer, AudioClassifyPathDTO> {

    @Schema(description = "分类名称")
    private String classifyName;

    @Schema(description = "音频id")
    private Integer audioIntroId;
}
