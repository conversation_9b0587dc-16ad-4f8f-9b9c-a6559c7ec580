package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 书架保存业务参数对象
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
@Data
@Schema(description = "书架保存参数BO")
public class AppEBookshelfSaveApiBO implements Serializable {

    @Schema(description = "书架名称")
    @NotEmpty(message = "书架名称不能为空")
    private String shelfTitle;

    @Schema(description = "封面URL")
    @NotEmpty(message = "封面不能为空")
    private String coverUrl;

    @Schema(description = "封面名称")
    @NotEmpty(message = "封面名称为空")
    private String coverUrlName;

    @Schema(description = "简介")
    private String blurb;

    @Schema(description = "启用状态：0-禁用，1-启用")
    @NotNull(message = "启用状态不能为空")
    private Integer launchStatus;

    @Schema(description = "允许下载：0-否，1-是")
    @NotNull(message = "允许下载不能为空")
    private Integer allowDownload;

    @Schema(description = "排序序号")
    private Integer sortNum;

    @Schema(description = "单书ID列表")
    private List<Integer> bookIds;
} 