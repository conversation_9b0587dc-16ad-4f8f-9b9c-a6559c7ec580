package com.dbj.classpal.books.client.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 题目分类排序ApiBO
 */
@Data
@Schema(description = "题目分类排序ApiBO")
public class QuestionCategorySortApiBO implements Serializable {

    @Schema(description = "主键ID",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键ID不能为空")
    private Integer id;

    @Schema(description = "目标父节点ID",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "目标父节点ID不能为空")
    private Integer fatherId;

    @Schema(description = "目标子节点id(如果没有则传-1)")
    private Integer aimId;

    @Schema(description = "相对目标子节点位置 -1前面 1后面")
    private Integer order;
}