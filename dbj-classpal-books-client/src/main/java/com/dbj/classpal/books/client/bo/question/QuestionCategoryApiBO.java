package com.dbj.classpal.books.client.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Schema
@Data
public class QuestionCategoryApiBO implements Serializable {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Integer id;

    /**
     * 类型名称
     */
    @Schema(description = "类型名称")
    @NotNull(message = "类型名称不能为空")
    @Size(min = 1, max = 64, message = "类型名称长度必须在 {min} 到 {max} 个字符之间")
    private String name;

    /**
     * 父级id
     */
    @Schema(description = "父级id")
    private Integer fatherId;
}