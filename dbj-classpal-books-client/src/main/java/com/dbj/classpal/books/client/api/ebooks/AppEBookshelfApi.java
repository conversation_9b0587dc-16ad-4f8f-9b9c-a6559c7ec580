package com.dbj.classpal.books.client.api.ebooks;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.ebooks.*;
import com.dbj.classpal.books.client.dto.ebooks.AppEBookshelfApiDTO;
import com.dbj.classpal.books.client.dto.ebooks.AppEBookshelfLandingApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 书架API接口
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}", path = "/api/classpal/books/${spring.application.version}")
public interface AppEBookshelfApi {

    /**
     * 分页查询书架列表
     *
     * @param pageRequest 分页查询参数
     * @return 书架分页数据
     */
    @PostMapping("/ebook/bookshelf/page")
    RestResponse<Page<AppEBookshelfApiDTO>> page(@RequestBody PageInfo<AppEBookshelfQueryApiBO> pageRequest) throws BusinessException;

    /**
     * H5分页查询书架列表（支持书城关联查询）
     *
     * @param pageRequest 分页查询参数
     * @return 书架分页数据（H5优化版本，不包含书籍列表）
     */
    @PostMapping("/ebook/bookshelf/h5/page")
    RestResponse<Page<AppEBookshelfLandingApiDTO>> pageForH5(@RequestBody @Validated PageInfo<AppEBookshelfH5QueryApiBO> pageRequest) throws BusinessException;

    /**
     * 查询书架详情
     *
     * @param idBO 书架ID参数
     * @return 书架详情数据
     */
    @PostMapping("/ebook/bookshelf/detail")
    RestResponse<AppEBookshelfApiDTO> detail(@RequestBody AppEBookshelfIdApiBO idBO) throws BusinessException;

    /**
     * 新增书架
     *
     * @param saveBO 书架保存参数
     * @return 保存结果
     */
    @PostMapping("/ebook/bookshelf/save")
    RestResponse<Integer> save(@RequestBody AppEBookshelfSaveApiBO saveBO) throws BusinessException;

    /**
     * 更新书架
     *
     * @param updateBO 书架更新参数
     * @return 更新结果
     */
    @PostMapping("/ebook/bookshelf/update")
    RestResponse<Boolean> update(@RequestBody AppEBookshelfUpdateApiBO updateBO) throws BusinessException;

    /**
     * 删除书架
     *
     * @param idBO 书架ID参数
     * @return 删除结果
     */
    @PostMapping("/ebook/bookshelf/delete")
    RestResponse<Boolean> delete(@RequestBody AppEBookshelfIdApiBO idBO) throws BusinessException;

    /**
     * 批量删除书架
     *
     * @param idsBO ID列表参数
     * @return 批量删除结果
     */
    @PostMapping("/ebook/bookshelf/delete/batch")
    RestResponse<Boolean> deleteBatch(@RequestBody AppEBookshelfIdsApiBO idsBO) throws BusinessException;

    /**
     * 批量启用书架
     *
     * @param idsBO ID列表参数
     * @return 批量启用结果
     */
    @PostMapping("/ebook/bookshelf/enable/batch")
    RestResponse<Boolean> enableBatch(@RequestBody AppEBookshelfIdsApiBO idsBO) throws BusinessException;

    /**
     * 批量禁用书架
     *
     * @param idsBO ID列表参数
     * @return 批量禁用结果
     */
    @PostMapping("/ebook/bookshelf/disable/batch")
    RestResponse<Boolean> disableBatch(@RequestBody AppEBookshelfIdsApiBO idsBO) throws BusinessException;

    /**
     * 批量允许下载
     *
     * @param idsBO ID列表参数
     * @return 批量允许下载结果
     */
    @PostMapping("/ebook/bookshelf/allow-download/batch")
    RestResponse<Boolean> allowDownloadBatch(@RequestBody AppEBookshelfIdsApiBO idsBO) throws BusinessException;

    /**
     * 批量关闭下载
     *
     * @param idsBO ID列表参数
     * @return 批量关闭下载结果
     */
    @PostMapping("/ebook/bookshelf/disable-download/batch")
    RestResponse<Boolean> disableDownloadBatch(@RequestBody AppEBookshelfIdsApiBO idsBO) throws BusinessException;

} 