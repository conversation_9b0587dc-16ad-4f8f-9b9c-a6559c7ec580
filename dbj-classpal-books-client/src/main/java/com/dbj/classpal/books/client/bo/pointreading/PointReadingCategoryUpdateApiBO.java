package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Data;

/**
 * 点读书分类更新API BO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Schema(name = "PointReadingCategoryUpdateApiBO", description = "点读书分类更新API BO")
public class PointReadingCategoryUpdateApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @NotNull(message = "主键ID不能为空")
    private Integer id;

    @Schema(description = "分类名称")
    @NotBlank(message = "分类名称不能为空")
    private String name;

    @Schema(description = "父级分类ID")
    private Integer parentId;

    @Schema(description = "是否默认分类：0-否 1-是")
    private Integer isDefault;

    @Schema(description = "排序")
    private Integer sortNum;

}
