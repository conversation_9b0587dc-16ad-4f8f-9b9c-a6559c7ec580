package com.dbj.classpal.books.client.bo.poem.app;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className AncientPoemReciteAppUserAssessmentScoreSaveBO
 * @description
 * @date 2025-05-28 11:15
 **/
@Data
public class AncientPoemReciteAppUserAssessmentScorePageBO implements Serializable {



    @Schema(description = "用户id",hidden = true)
    private Integer appUserId;


}
