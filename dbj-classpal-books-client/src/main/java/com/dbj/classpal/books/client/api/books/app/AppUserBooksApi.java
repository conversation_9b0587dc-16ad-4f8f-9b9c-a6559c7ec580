package com.dbj.classpal.books.client.api.books.app;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.books.BooksInfoPageBO;
import com.dbj.classpal.books.client.bo.books.app.BooksUserShelfBO;
import com.dbj.classpal.books.client.dto.books.BooksInfoPageApiDTO;
import com.dbj.classpal.books.client.dto.books.app.BooksUserLastStudyDTO;
import com.dbj.classpal.books.client.dto.books.app.BooksUserShelfDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AppUserBooksApi {

    /**
     * <AUTHOR>
     * @Description  我的书架
     * @Date 2025/4/21 10:29
     * @param
     * @return
     **/
    @PostMapping("/app/books/user/bookshelf/pageInfo")
    RestResponse<List<BooksUserShelfDTO>> booksUserShellist(@RequestBody BooksUserShelfBO bo) throws BusinessException;


    /**
     * 首页图书模块
     * @return
     * @throws BusinessException
     */
    @PostMapping("/app/books/user/last/study")
    RestResponse<BooksUserLastStudyDTO> lastStudy() throws BusinessException;


    /**
     * <AUTHOR>
     * @Description  删除书架图书信息
     * @Date 2025/4/21 10:31
     * @param
     * @return
     **/
    @PostMapping("/app/books/user/bookshelf/del")
    RestResponse<Boolean> del(@RequestParam Integer bookId) throws BusinessException;

    /**
     * <AUTHOR>
     * @Description  批量添加图书
     * @Date 2025/4/21 10:32
     * @param
     * @return
     **/
    @PostMapping("/app/books/user/bookshelf/batchSave")
    RestResponse<Boolean> batchSave(@RequestBody List<Integer> bookIds) throws BusinessException;



}
