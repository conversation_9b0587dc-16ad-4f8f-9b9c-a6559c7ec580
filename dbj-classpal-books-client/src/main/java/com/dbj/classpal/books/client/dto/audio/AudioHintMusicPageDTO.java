package com.dbj.classpal.books.client.dto.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 预置提示音表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@Accessors(chain = true)
public class AudioHintMusicPageDTO implements Serializable {

    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "提示音名称")
    private String name;

    @Schema(description = "提示音类型：1 预置提示音 2 预置背景音")
    private Integer type;

    @Schema(description = "素材id")
    private Integer originMaterialId;

    @Schema(description = "资源路径")
    private String materialUrl;

    @Schema(description = "资源名称")
    private String materialName;

    @Schema(description = "资源icon")
    private String materialIcon;

    @Schema(description = "资源时长（单位：秒）")
    private String materialDuration;

    @Schema(description = "排序权重")
    private Integer weight;

    @Schema(description = "关联数")
    private Integer refNum;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;


}
