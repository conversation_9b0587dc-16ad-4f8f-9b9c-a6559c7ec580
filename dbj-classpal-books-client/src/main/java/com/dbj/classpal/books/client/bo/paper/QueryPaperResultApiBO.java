package com.dbj.classpal.books.client.bo.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 查询试卷结果参数
 */
@Data
@Schema(description = "查询试卷结果参数")
public class QueryPaperResultApiBO implements Serializable {
    @Schema(description = "业务类型 1内容管理-音频专辑 2内容管理-视频专辑 3图书管理-图书资源 4图书管理-题库 5图书管理-音频专辑 6图书管理-视频专辑 10内容管理-评测",requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer businessType;
    @Schema(description = "业务类型ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "业务类型ID不能为空")
    private Integer businessId;

    @Schema(description = "试卷ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer paperId;
    
    @Schema(description = "用户ID",hidden = true, requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer appUserId;
} 