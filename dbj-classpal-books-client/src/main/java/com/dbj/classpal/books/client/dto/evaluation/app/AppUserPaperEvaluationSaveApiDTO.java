package com.dbj.classpal.books.client.dto.evaluation.app;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppUserPaperEvaluationDTO
 * Date:     2025-05-19 14:26:00
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
@Tag(name = "评测报告",description = "评测报告")
public class AppUserPaperEvaluationSaveApiDTO implements Serializable {
    @Schema(description = "评测报告id")
    private Integer id;

    @Schema(description = "学员")
    private Integer userId;

    @Schema(description = "学员名称")
    private String userName;

    @Schema(description = "学员UID")
    private String uid;

    @Schema(description = "学员年级")
    private String gradeName;

    @Schema(description = "地区")
    private String ipCity;

    @Schema(description = "评测时间")
    private LocalDateTime createTime;

}
