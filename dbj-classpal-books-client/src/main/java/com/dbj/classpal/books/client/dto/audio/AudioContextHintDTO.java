package com.dbj.classpal.books.client.dto.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 预置提示音表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AudioContextHintDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "类型：1 预置背景音 2 自定义")
    private Integer type;

    @Schema(description = "资源名称")
    private String name;

    @Schema(description = "资源名称")
    private String materialName;

    @Schema(description = "资源路径")
    private String materialPath;

    @Schema(description = "资源大小")
    private Double materialSize;

    @Schema(description = "资源时长(s)")
    private Integer materialDuration;

    @Schema(description = "排序")
    private Integer orderNum;

}
