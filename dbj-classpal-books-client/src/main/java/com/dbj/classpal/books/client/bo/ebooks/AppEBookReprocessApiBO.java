package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 单书重新处理API请求参数BO
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@Schema(description = "单书重新处理请求参数")
public class AppEBookReprocessApiBO implements Serializable {

    @Schema(description = "单书ID")
    @NotNull(message = "单书ID不能为空")
    private Integer bookId;
}
