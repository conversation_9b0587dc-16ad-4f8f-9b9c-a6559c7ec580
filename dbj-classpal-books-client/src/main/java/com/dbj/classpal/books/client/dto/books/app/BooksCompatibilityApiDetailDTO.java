package com.dbj.classpal.books.client.dto.books.app;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className BooksCompatibilityApiDetailDTO
 * @description
 * @date 2025-04-24 08:55
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="兼容连接返回", description="兼容连接返回")
public class BooksCompatibilityApiDetailDTO {

    @Schema(description = "主键id")
    private Integer id;
    @Schema(description = "册数id或者书内码页id")
    private Integer internalId;

    @Schema(description = "兼容连接")
    private String internalCode;

    @Schema(description = "连接类型（rank=按册数关联，contents=按书内码关联）")
    private String connectionType;

    @Schema(description = "册数参数")
    private Rank rank;

    @Schema(description = "书内码参数")
    private InCodesContents inCodesContents;


    @Data
    public static class Rank{
        @Schema(description = "图书id")
        private Integer booksId;
        @Schema(description =  "册数id")
        private Integer rankId;
    }

    @Data
    public static class InCodesContents{
        @Schema(description = "图书id")
        private Integer booksId;
        @Schema(description =  "册数id")
        private Integer rankId;
        @Schema(description =  "分类id")
        private Integer rankClassifyId;
        @Schema(description =  "内容id")
        private Integer contentsId;
    }



}
