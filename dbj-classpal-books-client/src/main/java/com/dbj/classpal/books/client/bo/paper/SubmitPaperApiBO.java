package com.dbj.classpal.books.client.bo.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 提交试卷参数
 */
@Data
@Schema(description = "提交试卷参数")
public class SubmitPaperApiBO implements Serializable {
    
    @Schema(description = "用户ID",hidden = true)
    private Integer appUserId;
    
    @Schema(description = "业务ID（评测业务为评测项ID）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "业务ID不能为空")
    private Integer businessId;

    @Schema(description = "业务类型 1内容管理-音频专辑 2内容管理-视频专辑 3图书管理-图书资源 4图书管理-题库 5图书管理-音频专辑 6图书管理-视频专辑  10内容管理-评测",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;

    @Schema(description = "评测表ID,如果是评测必填")
    private Integer appEvaluationId;
    
    @Schema(description = "题目作答结果")
    private List<SubmitQuestionResultApiBO> questionResults;
    
    @Schema(description = "填空题作答结果")
    private List<SubmitBlankResultApiBO> blankResults;
} 