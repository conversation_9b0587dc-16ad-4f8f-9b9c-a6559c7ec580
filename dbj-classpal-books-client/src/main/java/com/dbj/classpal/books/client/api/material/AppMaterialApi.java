package com.dbj.classpal.books.client.api.material;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.material.*;
import com.dbj.classpal.books.client.dto.material.AppMaterialQueryApiDTO;
import com.dbj.classpal.books.client.dto.material.AppMaterialQueryDicTreeApiDTO;
import com.dbj.classpal.books.client.dto.material.AppMaterialQueryRootApiDTO;
import com.dbj.classpal.books.client.dto.material.AppMaterialStatisticsSizeApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AppMaterialApi {

    /**
     * 分页查询素材中心列表
     * @param pageRequest
     * @return
     */
    @PostMapping("/material/pageInfo")
    RestResponse<Page<AppMaterialQueryApiDTO>> pageInfo(@RequestBody PageInfo<AppMaterialQueryApiBO> pageRequest);

    /**
     * 查询根节点
     * @return
     */
    @PostMapping("/material/getRoot")
    RestResponse<AppMaterialQueryRootApiDTO> getRoot();

    /**
     * 查询素材中心资源是否上传过
     * @param bo
     * @return
     */
    @PostMapping("/material/materialExist")
    RestResponse<Boolean> materialExist(@RequestBody AppMaterialExistQueryApiBO bo) throws BusinessException;


    /**
     * 新建文件夹
     * @param saveMkdirBO
     * @return
     */
    @PostMapping("/material/materialMkdir")
    RestResponse<Boolean> materialMkdir(@RequestBody AppMaterialSaveMkdirApiBO saveMkdirBO) throws BusinessException;


    /**
     * 重命名文件
     * @return
     */
    @PostMapping("/material/renameMaterial")
    RestResponse<Boolean> renameMaterial(@RequestBody AppMaterialReNameApiBO bo) throws BusinessException;

    /**
     * 移动文件资源
     * @param ioBo
     * @return
     */
    @PostMapping("/material/moveMaterial")
    RestResponse<Boolean> moveMaterial(@RequestBody AppMaterialIOApiBO ioBo) throws BusinessException;


    /**
     * 批量移动文件资源
     * @param ioBo
     * @return
     */
    @PostMapping("/material/batchMoveMaterial")
    RestResponse<Boolean> batchMoveMaterial(@RequestBody AppMaterialBatchIOApiBO ioBo) throws BusinessException;


    /**
     * 复制文件资源
     * @param ioBo
     * @return
     */
    @PostMapping("/material/copyMaterial")
    RestResponse<Boolean> copyMaterial(@RequestBody AppMaterialIOApiBO ioBo) throws BusinessException;

    /**
     * 批量复制文件资源
     * @param ioBo
     * @return
     */
    @PostMapping("/material/batchCopyMaterial")
    RestResponse<Boolean> batchCopyMaterial(@RequestBody AppMaterialBatchIOApiBO ioBo) throws BusinessException;

    /**
     * 获取所有文件夹结构树
     * @return
     */
    @PostMapping("/material/getAllDirectsTree")
    RestResponse<AppMaterialQueryDicTreeApiDTO> getAllDirectsTree();

    /**
     * 查询该文件夹父节点列表
     * @return
     */
    @PostMapping("/material/getMaterialParentsPath")
    RestResponse<List<AppMaterialQueryApiDTO>> getMaterialParentsPath(@RequestBody AppMaterialQueryApiBO bo) throws BusinessException;


    /**
     * 编辑字幕
     * @return
     */
    @PostMapping("/material/editCaption")
    RestResponse<Boolean> editCaption(@RequestBody AppMaterialEditCaptionApiBO bo) throws BusinessException;


    /**
     * 删除素材
     * @return
     */
    @PostMapping("/material/deleteMaterial")
    RestResponse<Boolean> deleteMaterial(@RequestBody CommonIdApiBO bo) throws BusinessException;

    /**
     * 批量删除素材
     * @return
     */
    @PostMapping("/material/batchDeleteMaterial")
    RestResponse<Boolean> batchDeleteMaterial(@RequestBody AppMaterialBatchCommonIdApiBO bo) throws BusinessException;

    /**
     * 查询文件已使用大小（kb）
     * @return
     */
    @PostMapping("/material/usedSize")
    RestResponse<AppMaterialStatisticsSizeApiDTO> usedSize() throws BusinessException;
}
