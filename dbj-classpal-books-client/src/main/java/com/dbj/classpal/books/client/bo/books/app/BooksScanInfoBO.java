package com.dbj.classpal.books.client.bo.books.app;

import com.dbj.classpal.books.client.enums.ScanCodeTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 图书配置-图书内容分类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="BooksScanInfo对象", description="图书配置-图书内容分类")
public class BooksScanInfoBO implements Serializable {




    @Schema(description ="图书id")
    private Integer booksId;

    @Schema(description ="册数id")
    private Integer rankId;

    @Schema(description ="册数功能分类id")
    private Integer rankClassifyId;

    @Schema(description ="书内码内容目录id")
    private Integer inCodesContentsId;

    @Schema(description ="二维码类型 册数H5码 b1 册数新印码 b2 H书内码5码 b3 书内码新印码 b4 ")
    private ScanCodeTypeEnum scanCodeType;




}
