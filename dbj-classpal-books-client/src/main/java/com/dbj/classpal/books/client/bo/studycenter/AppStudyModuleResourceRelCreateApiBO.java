package com.dbj.classpal.books.client.bo.studycenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "学习模块资源关联-新增ApiBO")
public class AppStudyModuleResourceRelCreateApiBO {
    @Schema(description = "学习模块ID")
    private Integer moduleId;
    @Schema(description = "资源ID")
    private Integer resourceId;
    @Schema(description = "资源类型")
    private String resourceType;
    @Schema(description = "排序权重")
    private Integer sortNum;
} 