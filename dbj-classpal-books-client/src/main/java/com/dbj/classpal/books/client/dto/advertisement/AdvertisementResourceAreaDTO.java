package com.dbj.classpal.books.client.dto.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 广告页面区域DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Tag(name="广告页面区域DTO", description="广告页面区域DTO")
public class AdvertisementResourceAreaDTO implements Serializable {

    @Schema(description = "id")
    private Integer id;

    @Schema(description = "页面id")
    private Integer resourceId;

    @Schema(description = "广告类型")
    private String type;

    @Schema(description = "广告类型名称")
    private String name;

    @Schema(description = "排序（越大越靠前）")
    private Integer sort;
}
