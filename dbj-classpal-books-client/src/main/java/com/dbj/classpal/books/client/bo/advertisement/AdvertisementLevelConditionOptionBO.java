
package com.dbj.classpal.books.client.bo.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;

/**
 * 广告展示条件选项BO
 * <AUTHOR>
 * @since 2025-04-21 16:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
@Tag(name="广告展示条件选项BO", description="广告展示条件选项BO")
public class AdvertisementLevelConditionOptionBO implements Serializable  {


	/**
	 * 选项类型 {@link com.dbj.classpal.books.common.enums.advertisement.AdvertiseOptionTypeEnum}
	 */
	@NotNull
	@Schema(description = "选项类型 1-年级 3-关系 4-图书类别 6-指定图书 11-属地省级 12-属地市级 13-属地分级标签")
	private Integer type;
	/**
	 * 选项值(多个值使用逗号拼接)
	 */
	@Schema(description = "选项值(多个值使用逗号拼接)")
	private String optionValue;
	/**
	 * 选项名称(多个名称使用、拼接)
	 */
	@Schema(description = "选项名称(多个名称使用、拼接)")
	private String optionName;
	/**
	 * 最小数量
	 */
	@Schema(description = "最小数量")
	private Integer minQty;
	/**
	 * 最大数量
	 */
	@Schema(description = "最大数量")
	private Integer maxQty;

}
