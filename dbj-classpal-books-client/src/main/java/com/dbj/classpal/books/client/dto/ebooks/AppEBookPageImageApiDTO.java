package com.dbj.classpal.books.client.dto.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 单书图片资源DTO
 */
@Data
@Schema(description = "单书图片资源DTO")
public class AppEBookPageImageApiDTO implements Serializable {
    
    @Schema(description = "资源ID")
    private Integer id;
    
    @Schema(description = "单书ID")
    private Integer resourceId;
    
    @Schema(description = "页码")
    private Integer pageNum;
    
    @Schema(description = "资源URL")
    private String resourceUrl;
    
    @Schema(description = "文件名称")
    private String fileName;
    
    @Schema(description = "上一页页码，如果不存在则为null")
    private Integer prevPageNum;
    
    @Schema(description = "下一页页码，如果不存在则为null")
    private Integer nextPageNum;
    
    @Schema(description = "总页数")
    private Integer totalPages;
} 