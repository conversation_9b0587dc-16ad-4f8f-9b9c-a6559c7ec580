package com.dbj.classpal.books.client.bo.tree;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 树形分类EditBO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
public class TreeClassifyMoveBO implements Serializable {


    @NotNull
    @Schema(description = "id")
    private Integer id;

    @NotNull
    @Schema(description = "目标节点id")
    private Integer targetId;

    @Schema(description = "是否移动到目标前 true-移动到目标前 false-移动到目标后 null-变为子节点")
    private Boolean moveToBefore;
}
