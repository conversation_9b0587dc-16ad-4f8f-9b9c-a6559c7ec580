package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 单书查询业务对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "单书查询参数ApiBO")
public class AppEBookQueryApiBO implements Serializable {

    @Schema(description = "样书标题")
    private String bookTitle;

    @Schema(description = "分类名称")
    private String categoryName;

    @Schema(description = "适用年级")
    private List<Integer> applicableGrades;

    @Schema(description = "学科ID")
    private List<Integer> subjectIds;

    @Schema(description = "阶段ID")
    private List<Integer> stageIds;

    @Schema(description = "允许下载：0-否，1-是")
    private Integer allowDownload;

    @Schema(description = "启用状态：0-禁用，1-启用")
    private Integer launchStatus;

}