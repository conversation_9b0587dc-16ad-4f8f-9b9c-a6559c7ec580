package com.dbj.classpal.books.client.bo.poem;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className AncientPoemBusinessRefPageBO
 * @description
 * @date 2025-05-27 09:36
 **/
@Data
public class AncientPoemBusinessRefSortBO implements Serializable {

    @Schema(description = "ID")
    private Integer id;

    @Schema(description = "排序")
    private Integer sort;
}
