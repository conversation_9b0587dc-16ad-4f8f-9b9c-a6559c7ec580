package com.dbj.classpal.books.client.bo.pinyin;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;

/**
 * 汉语拼音分页查询BO
 * <AUTHOR> <PERSON>
 * @since 2025-05-20 09:00
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Tag(name="汉语拼音分页查询BO", description="汉语拼音分页查询BO")
public class PinyinPageBO implements Serializable {


    @NotNull
    @Schema(description = "拼音分类id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer classifyId;

    @Size(max = 64)
    @Schema(description = "标题")
    private String title;

    @Schema(description = "启用状态 0-否 1-是")
    private Integer status;
}
