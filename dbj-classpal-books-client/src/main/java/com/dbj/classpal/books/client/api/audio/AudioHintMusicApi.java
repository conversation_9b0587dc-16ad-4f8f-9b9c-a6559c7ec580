package com.dbj.classpal.books.client.api.audio;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.audio.AudioHintMusicAddBO;
import com.dbj.classpal.books.client.bo.audio.AudioHintMusicDelBO;
import com.dbj.classpal.books.client.bo.audio.AudioHintMusicPageBO;
import com.dbj.classpal.books.client.bo.audio.AudioHintMusicUpdBO;
import com.dbj.classpal.books.client.dto.audio.AudioHintMusicPageDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 音频制作配置
 * <AUTHOR>
 * @since 2025-06-25
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}", path = "/api/classpal/books/${spring.application.version}")
public interface AudioHintMusicApi {

    @PostMapping("/preset/music/save")
    RestResponse<Boolean> save(@RequestBody AudioHintMusicAddBO saveBO) throws BusinessException;

    @PostMapping("/preset/music/update")
    RestResponse<Boolean> update(@RequestBody AudioHintMusicUpdBO saveBO) throws BusinessException;

    @PostMapping("/preset/music/pageInfo")
    RestResponse<Page<AudioHintMusicPageDTO>> pageInfo(@RequestBody PageInfo<AudioHintMusicPageBO> pageInfo) throws BusinessException;

    @PostMapping("/preset/music/delete")
    RestResponse<Boolean> delete(@RequestBody AudioHintMusicDelBO bo) throws BusinessException;

}
