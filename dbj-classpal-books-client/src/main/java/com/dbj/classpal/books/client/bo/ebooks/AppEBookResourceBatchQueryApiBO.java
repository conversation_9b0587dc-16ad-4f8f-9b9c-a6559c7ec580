package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 单书资源批量查询API业务对象
 */
@Data
@Schema(description = "单书资源批量查询参数ApiBO")
public class AppEBookResourceBatchQueryApiBO implements Serializable {

    @Schema(description = "书籍ID列表")
    private List<Integer> bookIds;

    @Schema(description = "书架ID列表")
    private List<Integer> shelfIds;

    @Schema(description = "书城ID")
    private Integer storeId;

    @Schema(description = "资源类型：1-加水印PDF, 2-切图图片")
    private Integer resourceType;

    @Schema(description = "处理类型：1-仅添加水印, 2-仅切图, 3-加水印并切图")
    private Integer businessType;

    @Schema(description = "是否按书籍分组返回")
    private Boolean groupByBook = true;
}
