package com.dbj.classpal.books.client.bo.poem;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className AncientPoemReciteCategoryPageBO
 * @description
 * @date 2025-05-26 09:10
 **/
@Data
public class AncientPoemReciteCategorySortBO implements Serializable {

    @Schema(description = "ID")
    @NotNull(message = "主键ID不能为空")
    private Integer id;

    @Schema(description = "排序权重")
    @NotNull(message = "排序权重不能为空")
    private Integer sort;
}
