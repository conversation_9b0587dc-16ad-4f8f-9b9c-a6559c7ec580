package com.dbj.classpal.books.client.bo.books;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/7/31 15:06
 */
@Data
public class BooksInfoToggleBO {

    @NotEmpty
    @Schema(description = "id", requiredMode = RequiredMode.REQUIRED)
    private Set<Integer> ids;

    @NotNull
    @Schema(description = "状态", requiredMode = RequiredMode.REQUIRED)
    private Boolean status;
}
