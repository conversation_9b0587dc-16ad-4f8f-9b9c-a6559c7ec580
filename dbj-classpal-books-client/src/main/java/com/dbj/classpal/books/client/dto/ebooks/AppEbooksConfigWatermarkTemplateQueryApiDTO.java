package com.dbj.classpal.books.client.dto.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="电子样书-样书配置-水印模板-查询BO", description="电子样书-样书配置-水印模板-查询BO")
public class AppEbooksConfigWatermarkTemplateQueryApiDTO implements Serializable {
    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "水印图片")
    private String watermark;

    @Schema(name = "水印图片名称")
    private String watermarkName;

    @Schema(description = "模板名称")
    private String templateName;

    @Schema(description = "水印大小（倍率0.1~1）")
    private Float scale;

    @Schema(description = "透明度（倍率0.1~1）")
    private Float transparency;

    @Schema(description = "旋转角度（0°~360°)")
    private Float rotationAngle;

    @Schema(description = "横向间距（倍率0.1~2）")
    private Float horizontalSpacing;

    @Schema(description = "纵向间距（倍率0.1~2）")
    private Float verticalSpacing;

    @Schema(description = "权重")
    private Integer sort;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;


}
