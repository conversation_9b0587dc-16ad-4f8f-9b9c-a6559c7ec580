package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 点读书模式配置排序API BO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingModeConfigSortApiBO", description = "点读书模式配置排序API BO")
public class PointReadingModeConfigSortApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "配置ID")
    @NotNull(message = "配置ID不能为空")
    private Integer id;

    @Schema(description = "排序号")
    @NotNull(message = "排序号不能为空")
    private Integer sortNum;
}
