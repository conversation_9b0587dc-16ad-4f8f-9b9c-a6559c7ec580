package com.dbj.classpal.books.client.api.question;

import com.dbj.classpal.books.client.bo.question.QueryWrongQuestionsApiBO;
import com.dbj.classpal.books.client.bo.question.WrongQuestionIdsApiBO;
import com.dbj.classpal.books.client.bo.question.WrongQuestionPkIdsApiBO;
import com.dbj.classpal.books.client.bo.question.WrongQuestionSubjectApiBO;
import com.dbj.classpal.books.client.dto.question.QuestionCorrectAnswerApiDTO;
import com.dbj.classpal.books.client.dto.question.WrongQuestionResultApiDTO;
import com.dbj.classpal.books.client.dto.question.WrongQuestionSubjectApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface WrongQuestionApi {
    /**
     * 删除错题
     *
     * @param idsApiBO 删除错题参数
     * @return 是否成功
     * @throws BusinessException 业务异常
     */
    @PostMapping("/wrong/question/remove")
    RestResponse<Boolean> removeWrongQuestion(@RequestBody WrongQuestionPkIdsApiBO idsApiBO) throws BusinessException;

    /**
     * 查询学科
     *
     * @param subjectApiBO 查询学科
     * @return 是否成功
     * @throws BusinessException 业务异常
     */
    @PostMapping("/wrong/question/subject")
    RestResponse<List<WrongQuestionSubjectApiDTO>> listSubject(@RequestBody WrongQuestionSubjectApiBO subjectApiBO) throws BusinessException;


    /**
     * 查询用户错题列表（统一接口，返回普通题和完形填空题）
     *
     * @param queryBO 查询参数
     * @return 错题列表（包含普通题和完形填空题）
     * @throws BusinessException 业务异常
     */
    @PostMapping("/wrong/question/list")
    RestResponse<List<WrongQuestionResultApiDTO>> listWrongQuestions(@RequestBody QueryWrongQuestionsApiBO queryBO) throws BusinessException;

    /**
     * 查询错题本中题目的正确答案
     *
     * @param idsApiBO 题目ID集合，为空则查询全部
     * @return 正确答案列表
     * @throws BusinessException 业务异常
     */
    @PostMapping("/wrong/question/correct")
    RestResponse<List<QuestionCorrectAnswerApiDTO>> getWrongQuestionsCorrectAnswers(@RequestBody WrongQuestionIdsApiBO idsApiBO)
            throws BusinessException;
}
