package com.dbj.classpal.books.client.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Set;

@Schema(description = "查询错题列表参数")
@Data
public class QueryWrongQuestionsApiBO implements Serializable {

    /**
     * 用户id
     */
    @Schema(description = "用户id",hidden = true)
    private Integer appUserId;

    /**
     * 学科ID（可选）
     */
    @Schema(description = "学科ID（可选）")
    private Integer subjectId;
    
    /**
     * 题目ID集合（可选，为空则查询全部）
     */
    @Schema(description = "题目ID集合（可选，为空则查询全部）")
    private Set<Integer> questionIds;
    
    /**
     * 是否包含答案信息
     */
    @Schema(description = "是否包含答案信息")
    private Boolean includeAnswers;
    
    /**
     * 题目类型（可选，为空则查询全部类型）
     */
    @Schema(description = "题目类型（可选，为空则查询全部类型）")
    private Integer type;
    
    /**
     * 来源类型（可选，为空则查询全部来源）
     */
    @Schema(description = "来源类型 1:用户答题 2:用户评测")
    private Integer sourceType;
    
    /**
     * 来源ID（可选）
     */
    @Schema(description = "来源ID")
    private Integer sourceId;
}