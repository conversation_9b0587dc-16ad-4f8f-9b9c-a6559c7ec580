package com.dbj.classpal.books.client.dto.tree;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 树形分类菜单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
public class TreeClassifyDTO implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "分类名称")
    private String name;

    @Schema(description = "父分类id, null表示顶级分类")
    private Integer parentId;

    @Schema(description = "分类级别，0为顶级分类")
    private Integer level;

    @Schema(description = "业务类型")
    private Integer type;

    @Schema(description = "排序权重")
    private Integer sort;

    @Schema(description = "关联数量")
    private Long refNum;

    @Schema(description = "子节点")
    List<TreeClassifyDTO> children;

    @Schema(description = "完整路径")
    private String fullPath;
}
