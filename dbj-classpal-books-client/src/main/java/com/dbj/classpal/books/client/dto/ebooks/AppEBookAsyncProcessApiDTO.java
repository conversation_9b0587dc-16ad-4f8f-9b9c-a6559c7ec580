package com.dbj.classpal.books.client.dto.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 单书异步处理API响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@Schema(description = "单书异步处理响应数据")
public class AppEBookAsyncProcessApiDTO implements Serializable {

    @Schema(description = "任务ID，用于后续查询处理状态")
    private String taskId;

    @Schema(description = "业务ID")
    private Integer businessId;

    @Schema(description = "任务创建时间")
    private String createTime;

    @Schema(description = "预计处理时长（秒）")
    private Integer estimatedDuration;
}
