package com.dbj.classpal.books.client.bo.material;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialQueryBO
 * Date:     2025-04-10 09:37:13
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class AppMaterialExistQueryApiBO implements Serializable {
    @Schema(description = "文件名称",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "文件名称不能为空")
    private String materialName;

    @Schema(description = "所在文件夹id",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "所在文件夹id不能为空")
    private Integer parentId;

    @Schema(description = "资源md5值",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "资源md5值不能为空")
    private String materialMd5;

    @Schema(description = "文件夹路径（上传文件不填，上传文件夹时必填）")
    private String dirPath;
}
