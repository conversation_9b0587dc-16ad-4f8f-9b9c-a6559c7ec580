package com.dbj.classpal.books.client.dto.evaluation.app;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppUserPaperEvaluationDTO
 * Date:     2025-05-19 14:26:00
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
@Tag(name = "评测报告评测项分析",description = "评测报告评测项分析")
public class AppUserPaperEvaluationAnalysisAiParamsApiDTO implements Serializable {

    @Schema(description = "评测报告评测项分析id")
    private Integer id;

    @Schema(description = "评测项名称")
    private String name;

    @Schema(description = "答题正确数量")
    private Integer correctCount;

    @Schema(description = "答题错误数量")
    private Integer incorrectCount;

}
