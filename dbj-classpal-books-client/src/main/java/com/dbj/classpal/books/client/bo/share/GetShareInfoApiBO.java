package com.dbj.classpal.books.client.bo.share;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 获取分享信息API请求参数BO
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@Schema(description = "获取分享信息请求参数")
public class GetShareInfoApiBO implements Serializable {

    @Schema(description = "业务类型：b5-单书，b6-书架，b7-书城")
    @NotEmpty(message = "业务类型不能为空")
    private String businessType;

    @Schema(description = "业务ID")
    @NotNull(message = "业务ID不能为空")
    private Integer businessId;

    @Schema(description = "是否强制重新生成（覆盖已有的分享链接）",hidden = true)
    private Boolean forceRegenerate = false;
}
