package com.dbj.classpal.books.client.dto.books;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 图书兼容链接表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="BooksCompatibility对象", description="图书兼容链接表")
public class BooksCompatibilityApiDTO implements Serializable {



    @Schema(description = "主键id")
    private Integer id;
    @Schema(description = "册数id或者书内码页id")
    private Integer internalId;

    @Schema(description = "兼容连接")
    private String internalCode;

    @Schema(description = "连接类型（rank=按册数关联，contents=按书内码关联）")
    private String connectionType;




}
