package com.dbj.classpal.books.client.api.ebooks;

import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigCategoryQueryApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigCategorySaveApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigCategoryUpdateApiBO;
import com.dbj.classpal.books.client.dto.ebooks.AppEbooksConfigCategoryQueryApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AppEbooksConfigCategoryApi {

    /**
     * 查询所有图书分类
     * @param bo
     * @return
     */
    @PostMapping("/ebooksConfig/category/getAllCategory")
    RestResponse<List<AppEbooksConfigCategoryQueryApiDTO>>getAllCategory(@RequestBody AppEbooksConfigCategoryQueryApiBO bo) throws BusinessException;


    /**
     * 新增图书分类
     * @param bo
     * @return
     */
    @PostMapping("/ebooksConfig/category/saveCategory")
    RestResponse<Boolean>saveCategory(@RequestBody @Valid AppEbooksConfigCategorySaveApiBO bo) throws BusinessException;


    /**
     * 修改图书分类
     * @param bo
     * @return
     */
    @PostMapping("/ebooksConfig/category/updateCategory")
    RestResponse<Boolean>updateCategory(@RequestBody @Valid AppEbooksConfigCategoryUpdateApiBO bo) throws BusinessException;

    /**
     * 删除图书分类
     * @param bo
     * @return
     */
    @PostMapping("/ebooksConfig/category/deleteCategory")
    RestResponse<Boolean>deleteCategory(@RequestBody @Valid CommonIdsApiBO bo) throws BusinessException;
}
