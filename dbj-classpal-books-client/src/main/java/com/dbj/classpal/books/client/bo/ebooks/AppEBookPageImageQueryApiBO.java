package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 单书图片资源查询参数
 */
@Data
@Schema(description = "单书图片资源查询参数")
public class AppEBookPageImageQueryApiBO implements Serializable {
    
    @NotNull(message = "单书ID不能为空")
    @Schema(description = "单书ID")
    private Integer bookId;
    
    @Schema(description = "当前页码，如果为空则从第一页开始查询")
    private Integer pageNum;
    
    @Schema(description = "是否需要查询上一页和下一页信息", defaultValue = "false")
    private Boolean needPrevNext = false;
} 