package com.dbj.classpal.books.client.bo.material;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialQueryBO
 * Date:     2025-04-10 09:37:13
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class AppMaterialQueryApiBO implements Serializable {
    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "父节点ID")
    private Integer parentId;

    @Schema(description = "资源名称")
    private String materialName;

    @Schema(description = "资源类型 不传参数-全部文件 2图片  3音频 4视频 5文档 6压缩包")
    private Integer materialType;

    @Schema(description = "资源过滤条件")
    private List<AppMaterialQueryFilterApiBO> filters;

    @Schema(description = "排序")
    private List<AppMaterialQueryOrderApiBO> orders;
}
