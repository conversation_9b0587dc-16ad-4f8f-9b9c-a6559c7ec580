package com.dbj.classpal.books.client.bo.evaluation.app;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppUserPaperEvaluationSaveApiBO
 * Date:     2025-05-19 15:19:02
 * Description: 表名： ,描述： 表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Tag(name = "重新评测", description = "重新评测")
public class AppUserPaperEvaluationGenerateApiBO implements Serializable {

    @Schema(description = "评测报告id")
    @NotNull(message = "评测报告id不能为空")
    private Integer id;

    @Schema(description = "ai评测结果")
    private String data;
}
