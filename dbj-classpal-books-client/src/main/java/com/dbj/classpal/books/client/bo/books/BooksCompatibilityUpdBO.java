package com.dbj.classpal.books.client.bo.books;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 图书兼容链接表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="BooksCompatibility对象", description="图书兼容链接表")
public class BooksCompatibilityUpdBO implements Serializable {



    @Schema(description = "主键id")
    private Integer id;


    @Schema(description = "兼容连接")
    private String internalCode;

}
