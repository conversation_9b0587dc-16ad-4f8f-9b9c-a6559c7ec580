package com.dbj.classpal.books.client.api.evaluation.app;

import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.evaluation.app.AppUserPaperEvaluationGenerateApiBO;
import com.dbj.classpal.books.client.bo.evaluation.app.AppUserPaperEvaluationSaveApiBO;
import com.dbj.classpal.books.client.dto.evaluation.app.AppEvaluationReportQueryApiDTO;
import com.dbj.classpal.books.client.dto.evaluation.app.AppUserPaperEvaluationAiParamsApiDTO;
import com.dbj.classpal.books.client.dto.evaluation.app.AppUserPaperEvaluationAnalysisAiParamsApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AppUserPaperEvaluationApi {

    /**
     * 重新评测
     * @param bo
     * @return
     */
    @PostMapping("/app/userPaperEvaluation/resetEvaluation")
    RestResponse<Boolean> resetEvaluation(@RequestBody @Valid AppUserPaperEvaluationSaveApiBO bo) throws BusinessException;


    /**
     * 获取ai需要传入的参数
     * @param bo
     * @return
     */
    @PostMapping("/app/userPaperEvaluation/getAiParams")
    RestResponse<AppUserPaperEvaluationAiParamsApiDTO>getAiParams(@RequestBody @Valid CommonIdApiBO bo) throws BusinessException;

    /**
     * 检查是否满足生成评测报告的条件
     * @param bo
     * @return
     * @throws BusinessException
     */
    @PostMapping("/app/userPaperEvaluation/checkSubmitEvaluationReport")
    RestResponse<Boolean> checkSubmitEvaluationReport(@RequestBody @Valid CommonIdApiBO bo) throws BusinessException;

    /**
     * 生成/获取评测报告
     * @param bo
     * @return
     */
    @PostMapping("/app/userPaperEvaluation/genEvaluationReport")
    RestResponse<AppEvaluationReportQueryApiDTO> genEvaluationReport(@RequestBody @Valid AppUserPaperEvaluationGenerateApiBO bo) throws BusinessException;

}
