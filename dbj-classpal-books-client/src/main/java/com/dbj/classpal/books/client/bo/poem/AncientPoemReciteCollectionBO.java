package com.dbj.classpal.books.client.bo.poem;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 古诗背诵合集表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ancient_poem_recite_collection")
public class AncientPoemReciteCollectionBO implements Serializable {

    @Schema(description =  "所属分类ID")
    @NotNull(message = "所属分类ID不能为空")
    private Integer categoryId;

    @Schema(description =  "合集标题")
    private String title;

    @Schema(description =  "状态（0下架，1上架）")
    private Boolean launchStatus;

    @Schema(description =  "是否启用 0 否 1是")
    private Integer status;



}
