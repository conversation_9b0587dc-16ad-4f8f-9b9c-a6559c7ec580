package com.dbj.classpal.books.client.api.pointreading;

import com.dbj.classpal.books.client.bo.pointreading.PointReadingBooksBusinessRefQueryBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingBooksBusinessRefSaveBO;
import com.dbj.classpal.books.client.dto.pointreading.PointReadingMenuApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 点读书API接口
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}", path = "/api/classpal/books/${spring.application.version}")
public interface PointReadingBookBusinessRefApi {

    @PostMapping("/point/reading/books/business/ref/save")
    RestResponse<Boolean> save(@RequestBody PointReadingBooksBusinessRefSaveBO bo) throws BusinessException;

    @PostMapping("/point/reading/books/business/ref/menuList")
    RestResponse<List<PointReadingMenuApiDTO>> menuList(@RequestBody PointReadingBooksBusinessRefQueryBO bo) throws BusinessException;

}
