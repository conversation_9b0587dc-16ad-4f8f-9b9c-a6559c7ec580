package com.dbj.classpal.books.client.dto.paper;

import com.dbj.classpal.books.client.dto.question.*;
import com.dbj.classpal.books.client.dto.question.QuestionAnswerApiDTO;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 试卷题目DTO
 */
@Data
@Schema(description = "试卷题目DTO")
public class PaperQuestionApiDTO implements Serializable {

    @Schema(description = "题目ID")
    private Integer id;

    @Schema(description = "题目标题")
    private String title;

    @Schema(description = "题目类型")
    private Integer type;

    @Schema(description = "题目类型名称")
    private String typeName;

    @Schema(description = "媒体文件类型 1-文本 2-图片,3-音频,4-视频")
    private Integer mediaType;

    /**
     * 媒体文件url
     */
    @Schema(description = "媒体文件url")
    private List<QuestionMediaApiDTO> mediaUrl;

    @Schema(description = "辅助识图")
    private List<QuestionRecognitionApiDTO> aidedRecognitionUrl;


    @Schema(description = "选项类型 1-文本 2-图片,3-音频,4-视频")
    private Integer optionType;

    @Schema(description = "权重")
    private Integer weight;

    @Schema(description = "解析")
    private String analyzes;

    @Schema(description = "排序号")
    private Integer sortNum;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "该题型题目总数")
    private Integer typeCount;

    @Schema(description = "该题型内序号")
    private Integer typeIndex;

    @Schema(description = "答案选项列表")
    private List<QuestionAnswerAppApiDTO> answers;

    @Schema(description = "填空区域列表")
    private List<QuestionBlankAreaApiDTO> blankAreas;
} 