package com.dbj.classpal.books.client.bo.evaluation;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationQueryBO
 * Date:     2025-04-15 13:35:46
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class AdminEvaluationQueryApiBO implements Serializable {

    @Schema(description = "评测名称")
    private String evaluationName;

    @Schema(description = "上架状态  0下架 1上架")
    private Integer evaluationStatus;

    @Schema(description = "是否隐藏  0隐藏 1显示 ")
    private Integer evaluationVisible;

    @Schema(description = "是否启用 0否 1是")
    private Integer evaluationOpen;
}
