package com.dbj.classpal.books.client.bo.share;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * H5落地页API请求参数BO
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Data
@Schema(description = "H5落地页API请求参数")
public class H5LandingPageApiBO {

    @Schema(description = "分享类型：BOOK-单书，BOOKSHELF-书架，BOOKSTORE-书城")
    @NotEmpty(message = "分享类型不能为空")
    private String type;

    @Schema(description = "单书ID（BOOK类型需要，或在书架/书城中查看具体书籍时需要）")
    private Integer ebookId;

    @Schema(description = "书架ID（BOOKSHELF类型需要，或在书城中查看具体书架时需要）")
    private Integer shelfId;

    @Schema(description = "书城ID（BOOKSTORE类型需要）")
    private Integer storeId;

    @Schema(description = "页码（分页查询时使用，默认为1）")
    private Integer pageNum = 1;

    @Schema(description = "每页大小（分页查询时使用，默认为10）")
    private Integer pageSize = 10;
}
