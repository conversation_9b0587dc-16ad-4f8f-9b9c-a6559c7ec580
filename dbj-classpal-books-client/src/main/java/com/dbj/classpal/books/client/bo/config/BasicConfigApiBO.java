package com.dbj.classpal.books.client.bo.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Schema
@Data
public class BasicConfigApiBO implements Serializable {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 配置类型
     */
    @Schema(description = "业务类型，题库question，学习模块 study-center 学科 subject")
    private String bizType;

    /**
     * 配置名称
     */
    @Schema(description = "配置名称")
    private String name;

    /**
     * 封面
     */
    @Schema(description = "封面")
    private String icon;

    /**
     * 封面名称
     */
    @Schema(description = "封面名称")
    private String iconName;


    /**
     * 背景图片
     */
    @Schema(description = "背景图片")
    private String background;

    /**
     * 背景图片名称
     */
    @Schema(description = "背景图片名称")
    private String backgroundName;

    /**
     * 排序权重
     */
    @Schema(description = "排序权重")
    private Integer sortNum;
} 