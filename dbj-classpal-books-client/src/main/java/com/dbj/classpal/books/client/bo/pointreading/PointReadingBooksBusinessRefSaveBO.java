package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/7/31 16:14
 */
@Data
public class PointReadingBooksBusinessRefSaveBO {

    @NotNull
    @Schema(description = "点读书id", requiredMode = RequiredMode.REQUIRED)
    private Integer bookId;

    @NotNull
    @Schema(description = "业务id", requiredMode = RequiredMode.REQUIRED)
    private Integer businessId;

    @NotEmpty
    @Schema(description = "业务类型 BOOK_IN_CODES-图书资源 BOOK_RANK_CLASSIFY-点读", requiredMode = RequiredMode.REQUIRED)
    private String businessType;
}
