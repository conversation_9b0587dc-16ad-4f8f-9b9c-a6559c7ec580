package com.dbj.classpal.books.client.dto.books;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 图书书内码题库表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("books_rank_in_codes_contents_question")
@Tag(name = "BooksRankInCodesContentsQuestion对象", description="图书书内码题库表")
public class BooksRankInCodesContentsQuestionDetailDTO implements Serializable {




    @Schema(description = "图书id")
    private Integer booksId;

    @Schema(description = "册书id")
    private Integer rankId;

    @Schema(description = "册数功能分类id")
    private Integer rankClassifyId;

    @Schema(description = "书内码内容目录id")
    private Integer inCodesContentsId;

    @Schema(description = "封面url")
    private String url;

    @Schema(description = "出题方式 ")
    private String questionMethod;

    @Schema(description = "题目数量")
    private Integer questionNum;

    @Schema(description = "是否启用 1-是 0-否")
    private Integer status;

}
