package com.dbj.classpal.books.client.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema
@Data
public class QuestionBlankAreaApiDTO implements Serializable {
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Integer id;

    /**
     * 题目id
     */
    @Schema(description = "题目id")
    private Integer questionId;

    /**
     * 空位序号
     */
    @Schema(description = "空位序号")
    private Integer blankIndex;

    /**
     * 正确答案ID（多个英文逗号隔开）
     */
//    @Schema(description = "正确答案ID（多个英文逗号隔开）")
//    private String answerIds;

    /**
     * 答案列表
     */
    @Schema(description = "答案列表")
    private List<QuestionAnswerAppApiDTO> answers;

} 