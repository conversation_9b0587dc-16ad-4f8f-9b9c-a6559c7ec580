package com.dbj.classpal.books.client.dto.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 书城统计信息API数据传输对象
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
@Data
@Schema(description = "书城H5DTO")
public class AppEBookstoreH5ListApiDTO implements Serializable {
    @Schema(description = "书城ID")
    private Integer storeId;

    @Schema(description = "书城名称")
    private String storeTitle;

    @Schema(description = "封面URL")
    private String coverUrl;

    @Schema(description = "封面URL")
    private String coverUrlName;

    @Schema(description = "简介")
    private String blurb;

    @Schema(description = "分享链接")
    private String shareUrl;

    @Schema(description = "分享二维码")
    private String shareQrCode;

    @Schema(description = "启用状态：0-禁用，1-启用")
    private Integer launchStatus;

    @Schema(description = "允许下载：0-否，1-是")
    private Integer allowDownload;

    @Schema(description = "书架总数")
    private Integer totalShelfCount;

    @Schema(description = "书籍总数")
    private Integer totalBookCount;

    @Schema(description = "书架详情列表")
    private List<ShelfH5ApiDTO> shelfStatistics;
    
    /**
     * 书架统计内部类（H5分页查询专用 - 不包含具体书籍列表）
     */
    @Data
    @Schema(description = "书架统计信息")
    public static class ShelfH5ApiDTO implements Serializable {

        @Schema(description = "书架ID")
        private Integer shelfId;

        @Schema(description = "书架名称")
        private String shelfTitle;

        @Schema(description = "封面URL")
        private String coverUrl;

        @Schema(description = "封面URL")
        private String coverUrlName;

        @Schema(description = "简介")
        private String blurb;

        @Schema(description = "分享链接")
        private String shareUrl;

        @Schema(description = "分享二维码")
        private String shareQrCode;

        @Schema(description = "书籍数量")
        private Integer bookCount;

        @Schema(description = "允许下载：0-否，1-是")
        private Integer allowDownload;

    }
} 