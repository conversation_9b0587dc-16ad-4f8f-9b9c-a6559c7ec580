package com.dbj.classpal.books.client.bo.album;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: CommonIdBO
 * Date:     2025-07-10 10:43:02
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class AppAlbumElementsBusinessApiBO implements Serializable {

    @Schema(description = "业务id集合")
    private Set<Integer> businessIds;

    @Schema(description = "业务类型")
    private Integer businessType;


}
