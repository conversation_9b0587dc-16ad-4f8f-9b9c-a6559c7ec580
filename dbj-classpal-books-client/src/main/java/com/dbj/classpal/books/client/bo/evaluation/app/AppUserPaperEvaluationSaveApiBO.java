package com.dbj.classpal.books.client.bo.evaluation.app;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppUserPaperEvaluationSaveApiBO
 * Date:     2025-05-19 15:19:02
 * Description: 表名： ,描述： 表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Tag(name = "重新评测", description = "重新评测")
public class AppUserPaperEvaluationSaveApiBO implements Serializable {

    @Schema(description = "评测表id")
    @NotNull(message = "评测表id不能为空")
    private Integer appEvaluationId;

}
