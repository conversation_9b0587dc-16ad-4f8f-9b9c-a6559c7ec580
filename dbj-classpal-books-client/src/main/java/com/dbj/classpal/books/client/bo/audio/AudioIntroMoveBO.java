package com.dbj.classpal.books.client.bo.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/6/30 10:55
 */
@Data
public class AudioIntroMoveBO {

    @NotEmpty
    @Schema(description = "id", requiredMode = RequiredMode.REQUIRED)
    private Set<Integer> ids;

    @NotNull
    @Schema(description = "分类id", requiredMode = RequiredMode.REQUIRED)
    private Integer audioClassifyId;
}
