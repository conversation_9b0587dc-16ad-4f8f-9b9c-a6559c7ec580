package com.dbj.classpal.books.client.bo.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;

/**
 * 广告分页查询BO
 * <AUTHOR> Yi
 * @since 2025-04-21 15:08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
@Tag(name="广告分页查询BO", description="广告分页查询BO")
public class AdvertisementPageBO implements Serializable {

    @NotBlank
    @Schema(description = "广告类型字典项的值 首页弹窗-homePopup 瓷片列表-ceramicList 瓷片区右下-ceramicBottomRight 瓷片区右上-ceramicTopRight 瓷片区Banner-ceramicBanner", requiredMode = Schema.RequiredMode.REQUIRED)
    private String type;

    @Size(max = 64)
    @Schema(description = "标题")
    private String title;
}
