package com.dbj.classpal.books.client.api.album;

import com.dbj.classpal.books.client.bo.album.*;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.dto.album.AppAlbumElementsBusinessRefMaterialQueryApiDTO;
import com.dbj.classpal.books.client.dto.album.AppAlbumElementsBusinessRefQueryApiDTO;
import com.dbj.classpal.books.client.dto.books.BooksRefDirectApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumApi
 * Date:     2025-04-15 11:13:46
 * Description: 表名： ,描述： 表
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AppAlbumElementsBusinessRefApi {

    /**
     * 查询专辑被引用列表数据
     * @param bo
     * @return
     */
    @PostMapping("/albumElementsBusinessRef/getElementsBusinessRef")
    RestResponse<List<AppAlbumElementsBusinessRefQueryApiDTO>> getElementsBusinessRef(@RequestBody AppAlbumElementsBusinessRefQueryCommonApiBO bo);


    /**
     * 查询专辑关联业务数据
     * @param bo
     * @return
     */
    @PostMapping("/albumElementsBusinessRef/getElementsBusinessRefMaterialRef")
    RestResponse<AppAlbumElementsBusinessRefMaterialQueryApiDTO> getElementsBusinessRefMaterialRef(@RequestBody AppAlbumElementsBusinessRefQueryApiBO bo);

    /**
     * 查询专辑关联业务数据
     * @param bo
     * @return
     */
    @PostMapping("/albumElementsBusinessRef/saveOrUpdateElementsBusinessRefMaterialRef")
    RestResponse<Boolean> saveOrUpdateElementsBusinessRefMaterialRef(@RequestBody AppAlbumElementsBusinessRefSaveApiBO bo) throws BusinessException;


    /**
     * 素材关联图书管理-图书资源引用跳转页面所需参数
     */
    @PostMapping("/albumElementsBusinessRef/getAlbumElementsBusinessRefBooks")
    RestResponse<BooksRefDirectApiDTO> getAlbumElementsBusinessRefBooks(@RequestBody @Valid CommonIdApiBO bo) throws BusinessException;

}
