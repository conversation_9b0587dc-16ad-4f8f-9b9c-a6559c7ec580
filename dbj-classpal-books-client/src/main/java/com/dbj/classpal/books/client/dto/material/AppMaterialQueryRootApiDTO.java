package com.dbj.classpal.books.client.dto.material;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialQueryDTO
 * Date:     2025-04-10 09:31:28
 * Description: 表名： ,描述： 表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@Tag(name = "素材中心查询根节点DTO")
public class AppMaterialQueryRootApiDTO implements Serializable {

    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "是否根节点 0否 1是")
    private Integer isRoot;

    @Schema(description = "资源名称")
    private String materialName;
}
