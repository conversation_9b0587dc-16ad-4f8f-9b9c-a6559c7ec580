package com.dbj.classpal.books.client.dto.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 音频文本详情
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AudioTrialUseDTO {

    @Schema(description = "音频地址")
    private String audioUrl;

}
