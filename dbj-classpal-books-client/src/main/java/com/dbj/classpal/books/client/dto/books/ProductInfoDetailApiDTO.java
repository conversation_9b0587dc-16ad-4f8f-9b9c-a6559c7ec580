package com.dbj.classpal.books.client.dto.books;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-02-04 14:53
 */
@Schema(description = "")
@Data
public class ProductInfoDetailApiDTO {
    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Integer id;
    /**
     * 产品名称
     */
    @Schema(description = "产品名称")
    private String productName;
    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private Integer productType;

    @Schema(description = "产品名称")
    private String bookName;
    /**
     * 封面url
     */
    @Schema(description = "封面url")
    private String picUrl;

    /**
     * 出版单位
     */
    @Schema(description = "出版单位")
    private String publisherName;

    /**
     * 编者
     */
    @Schema(description = "编者")
    private String editorName;

    /**
     * ISBN
     */
    @Schema(description = "ISBN")
    private String isbn;

    /**
     * 物流编码
     */
    @Schema(description = "物流编码")
    private String logisticsCode;
    /**
     * 唯一编码
     */
    @Schema(description = "唯一编码")
    private String code;
    /**
     * 卷册数
     */
    @Schema(description = "卷册数")
    private Integer volumeNum;
    /**
     * 卷册信息
     */
    @Schema(description = "卷册信息")
    private List<ProductItemInfoApiDTO> productItemInfoList;

}
