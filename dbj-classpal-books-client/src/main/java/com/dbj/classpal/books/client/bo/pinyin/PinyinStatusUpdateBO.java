package com.dbj.classpal.books.client.bo.pinyin;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;

/**
 * 汉语拼音状态更新BO
 * <AUTHOR>
 * @since 2025-05-20 16:52
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Tag(name="汉语拼音状态更新BO", description="汉语拼音状态更新BO")
public class PinyinStatusUpdateBO implements Serializable {


    @NotNull
    @Schema(description = "拼音id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer id;

    @NotNull
    @Schema(description = "启用状态 0-否 1-是", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer status;
}
