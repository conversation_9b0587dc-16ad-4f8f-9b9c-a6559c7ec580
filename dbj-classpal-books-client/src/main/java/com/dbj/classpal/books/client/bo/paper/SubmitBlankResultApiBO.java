package com.dbj.classpal.books.client.bo.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 提交填空题结果参数
 */
@Data
@Schema(description = "提交填空题结果参数")
public class SubmitBlankResultApiBO implements Serializable {

    @Schema(description = "题目ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "题目ID不能为空")
    private Integer questionId;
    
    @Schema(description = "空位索引", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "空位索引不能为空")
    private Integer blankIndex;

    @Schema(description = "空位id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "空位id")
    private Integer blankAreaId;

    @Schema(description = "答案ID列表，逗号分隔")
    private String answerIds;
} 