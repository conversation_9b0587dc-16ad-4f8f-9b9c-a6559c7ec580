package com.dbj.classpal.books.client.bo.poem.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className AncientPoemBusinessRefPageBO
 * @description
 * @date 2025-05-27 09:36
 **/
@Data
public class AncientPoemBusinessRefPageBO implements Serializable {


    @Schema(description = "业务id")
    private Integer businessId;

    @Schema(description = "业务类型 1-古诗背诵")
    private Integer businessType;

    @Schema(description = "古诗标题")
    private String title;

    @Schema(description = "作者")
    private String author;

    @Schema(description = "所属朝代")
    private String dynasty;

    @Schema(description = "年级")
    private List<String> grade;

    @Schema(description = "是否课外内容 0-课内 1-课外")
    private Integer isOut;

}
