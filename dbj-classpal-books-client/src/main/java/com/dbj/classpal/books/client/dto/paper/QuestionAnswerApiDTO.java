package com.dbj.classpal.books.client.dto.paper;

import com.dbj.classpal.books.client.dto.question.QuestionMediaApiDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 答案选项DTO
 */
@Data
@Schema(description = "答案选项DTO")
public class QuestionAnswerApiDTO implements Serializable {

    @Schema(description = "选项ID")
    private Integer id;

    @Schema(description = "区域ID")
    private Integer blankAreaId;

    @Schema(description = "序号")
    private Integer serialNo;

    /**
     * 选项名称
     */
    @Schema(description = "选项名称")
    private String optionName;

    @Schema(description = "选项内容")
    private String optionContent;

    @Schema(description = "是否为正确答案")
    private Integer isAnswer;
    /**
     * 媒体文件url
     */
    @Schema(description = "媒体文件url")
    private List<QuestionMediaApiDTO> mediaUrl;
} 