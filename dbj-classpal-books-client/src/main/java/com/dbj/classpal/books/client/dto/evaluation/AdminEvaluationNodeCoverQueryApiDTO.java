package com.dbj.classpal.books.client.dto.evaluation;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationQueryBO
 * Date:     2025-04-15 13:35:46
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
@Tag(name = "评测项",description = "评测项")
public class AdminEvaluationNodeCoverQueryApiDTO implements Serializable {
    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "评测项封面")
    private String appEvaluationNodeCover;

    @Schema(description = "评测项封面名称")
    private String appEvaluationNodeCoverName;
}
