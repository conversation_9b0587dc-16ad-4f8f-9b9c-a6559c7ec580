package com.dbj.classpal.books.client.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 题目分类业务关联 BO
 */
@Data
@Schema(description = "题目分类业务关联")
public class QuestionCategoryBusinessRefSaveApiBO implements Serializable {

    /**
     * 题目分类ID
     */
    @Schema(description = "题目分类ID")
    private Integer questionCategoryId;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sortNum;
} 