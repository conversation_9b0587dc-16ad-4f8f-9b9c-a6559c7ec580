package com.dbj.classpal.books.client.bo.studycenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "学习进度-分页查询ApiBO")
public class AppStudyModuleProgressQueryPageApiBO {
    @Schema(description = "用户ID")
    private Integer userId;
    @Schema(description = "学习模块ID")
    private Integer moduleId;
} 