package com.dbj.classpal.books.client.bo.material;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class AppMaterialQueryOrderApiBO implements Serializable {

    private String column;
    /**
     * 是否正序排列，默认 true
     */
    private boolean asc = true;
}
