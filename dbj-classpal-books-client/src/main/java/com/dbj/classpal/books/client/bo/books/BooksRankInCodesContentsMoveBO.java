package com.dbj.classpal.books.client.bo.books;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 图书书内码分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="BooksRankInCodesContents对象", description="图书书内码分类表")
public class BooksRankInCodesContentsMoveBO implements Serializable {




    @NotNull(message = "移动后的父id不能为空")
    @Schema(description = "移动后的父级id")
    private Integer moveFatherId;

    @NotNull(message = "id不能为空")
    @Schema(description = "id")
    private Integer id;

    @Schema(description = "排序序号")
    @NotNull(message = "排序不能为空")
    private List<BooksRankInCodesContentsSortBO> sortList;



}
