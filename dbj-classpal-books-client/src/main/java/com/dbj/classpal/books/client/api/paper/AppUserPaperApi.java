package com.dbj.classpal.books.client.api.paper;

import com.dbj.classpal.books.client.bo.paper.*;
import com.dbj.classpal.books.client.dto.paper.PaperQuestionApiDTO;
import com.dbj.classpal.books.client.dto.paper.PaperResultApiDTO;
import com.dbj.classpal.books.client.dto.paper.UserPaperCheckSubmitApiDTO;
import com.dbj.classpal.books.client.dto.question.QuestionCorrectAnswerApiDTO;
import com.dbj.classpal.books.client.dto.paper.UserPaperApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 用户试卷服务接口
 */
@FeignClient(name = "dbj-classpal-books-api-${spring.application.version}", path = "/api/classpal/books/${spring.application.version}")
public interface AppUserPaperApi {

    /**
     * 查看试卷题目列表
     *
     * @param queryBO 查询参数
     * @return 题目列表
     */
    @PostMapping("/paper/questions")
    RestResponse<List<PaperQuestionApiDTO>> getPaperQuestions(@RequestBody @Valid QueryPaperQuestionsApiBO queryBO) throws BusinessException;

    /**
     * 提交试卷
     *
     * @param submitBO 提交试卷参数
     * @return 是否成功
     */
    @PostMapping("/paper/submit")
    RestResponse<UserPaperApiDTO> submitPaper(@RequestBody @Valid SubmitPaperApiBO submitBO) throws BusinessException;

    @PostMapping("/paper/checkSubmit")
    RestResponse<UserPaperCheckSubmitApiDTO> checkSubmit(@RequestBody @Valid CheckSubmitApiBO checkBo) throws BusinessException;
    /**
     * 查看题目作答结果列表
     *
     * @param queryBO 查询题目结果参数
     * @return 题目结果列表
     */
    @PostMapping("/paper/result")
    RestResponse<PaperResultApiDTO> getPaperResult(@RequestBody @Valid QueryPaperResultApiBO queryBO) throws BusinessException;

    /**
     * 重新考试（删除原有答题记录，重新获取试卷）
     *
     * @param retakePaperBO 重新考试参数
     * @throws BusinessException 业务异常
     */
    @PostMapping("/paper/retake")
    RestResponse<Void> retakePaper(@RequestBody @Valid RetakePaperApiBO retakePaperBO) throws BusinessException;

    @PostMapping("/paper/correctAnswers")
    RestResponse<List<QuestionCorrectAnswerApiDTO>> getAllQuestionsCorrectAnswers(@RequestBody @Valid QueryQuestionsCorrectAnswerApiBO bo) throws BusinessException;

} 