package com.dbj.classpal.books.client.bo.poem;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className AncientPoemReciteCategoryPageBO
 * @description
 * @date 2025-05-26 09:10
 **/
@Data
public class AncientPoemReciteCategorySaveBO  implements Serializable {

    @Schema(description = "类型 system 系统 other 其他 系统类型不让更改")
    private String type;

    @Schema(description = "分类名称")
    @NotEmpty(message = "分类名称不能为空")
    private String name;

    @Schema(description = "排序权重")
    private Integer sort;
}
