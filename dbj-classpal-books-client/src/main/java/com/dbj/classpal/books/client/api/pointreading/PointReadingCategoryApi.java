package com.dbj.classpal.books.client.api.pointreading;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.pointreading.*;
import com.dbj.classpal.books.client.dto.pointreading.PointReadingCategoryApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 点读书分类API接口
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}", path = "/api/classpal/books/${spring.application.version}")
public interface PointReadingCategoryApi {

    /**
     * 分页查询点读书分类列表
     *
     * @param pageRequest 分页查询参数
     * @return 分类分页数据
     */
    @PostMapping("/point-reading/category/page")
    RestResponse<Page<PointReadingCategoryApiDTO>> page(@RequestBody @Validated PageInfo<PointReadingCategoryQueryApiBO> pageRequest) throws BusinessException;

    /**
     * 查询分类树形结构
     *
     * @param queryBO 查询条件
     * @return 树形分类列表
     */
    @PostMapping("/point-reading/category/tree")
    RestResponse<List<PointReadingCategoryApiDTO>> tree(@RequestBody PointReadingCategoryQueryApiBO queryBO) throws BusinessException;

    /**
     * 查询分类详情
     *
     * @param idBO 分类ID参数
     * @return 分类详情数据
     */
    @PostMapping("/point-reading/category/detail")
    RestResponse<PointReadingCategoryApiDTO> detail(@RequestBody @Validated PointReadingCategoryIdApiBO idBO) throws BusinessException;

    /**
     * 新增分类
     *
     * @param saveBO 分类保存参数
     * @return 新增分类ID
     */
    @PostMapping("/point-reading/category/save")
    RestResponse<Integer> save(@RequestBody @Validated PointReadingCategorySaveApiBO saveBO) throws BusinessException;

    /**
     * 更新分类
     *
     * @param updateBO 分类更新参数
     * @return 更新结果
     */
    @PostMapping("/point-reading/category/update")
    RestResponse<Boolean> update(@RequestBody @Validated PointReadingCategoryUpdateApiBO updateBO) throws BusinessException;

    /**
     * 删除分类
     *
     * @param idBO 分类ID参数
     * @return 删除结果
     */
    @PostMapping("/point-reading/category/delete")
    RestResponse<Boolean> delete(@RequestBody @Validated PointReadingCategoryIdApiBO idBO) throws BusinessException;

    /**
     * 批量删除分类
     *
     * @param batchBO 批量操作参数
     * @return 批量删除结果
     */
    @PostMapping("/point-reading/category/delete/batch")
    RestResponse<Boolean> deleteBatch(@RequestBody @Validated PointReadingCategoryBatchApiBO batchBO) throws BusinessException;

    /**
     * 批量启用分类
     *
     * @param batchBO 批量操作参数
     * @return 批量启用结果
     */
    @PostMapping("/point-reading/category/enable/batch")
    RestResponse<Boolean> enableBatch(@RequestBody @Validated PointReadingCategoryBatchApiBO batchBO) throws BusinessException;

    /**
     * 批量禁用分类
     *
     * @param batchBO 批量操作参数
     * @return 批量禁用结果
     */
    @PostMapping("/point-reading/category/disable/batch")
    RestResponse<Boolean> disableBatch(@RequestBody @Validated PointReadingCategoryBatchApiBO batchBO) throws BusinessException;
}
