package com.dbj.classpal.books.client.dto.evaluation.app;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationQueryBO
 * Date:     2025-04-15 13:35:46
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
@Tag(name = "APP客户端查询评测表下评测项及报告分析记录",description = "APP客户端查询评测表下评测项及报告分析记录")
public class AppEvaluationNodeQueryApiDTO implements Serializable {

    @Schema(description = "评测项主键id")
    private Integer id;

    @Schema(description = "评测项名称")
    private String appEvaluationNodeName;

    @Schema(description = "评测项封面")
    private String appEvaluationNodeCover;

    @Schema(description = "排序")
    private Integer appEvaluationOrder;

    @Schema(description = "是否已提交试卷 0否 1是")
    private Integer isGenerated;

    @Schema(description = "评测项分析结果主键id")
    private Integer appUserPaperEvaluationAnalysisId;

    @Schema(description = "试卷id")
    private Integer appUserPaperInfoId;

}
