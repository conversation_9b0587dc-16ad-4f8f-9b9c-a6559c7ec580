package com.dbj.classpal.books.client.dto.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 点读书API DTO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Schema(name = "PointReadingBookApiDTO", description = "点读书API DTO")
public class PointReadingBookApiDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "点读书名称")
    private String name;

    @Schema(description = "所属分类ID")
    private Integer categoryId;

    @Schema(description = "分类名称")
    private String categoryName;

    @Schema(description = "封面文件ID")
    private String coverId;

    @Schema(description = "封面URL")
    private String coverUrl;

    @Schema(description = "封面文件名")
    private String coverName;

    @Schema(description = "原始文件ID")
    private String originalFileId;

    @Schema(description = "原始文件URL")
    private String originalUrl;

    @Schema(description = "原始文件名")
    private String originalName;

    @Schema(description = "原始文件MD5")
    private String originalMd5;

    @Schema(description = "解析状态：10-待解析 20-解析中 30-已解析 40-解析失败")
    private Integer parseStatus;

    @Schema(description = "解析状态描述")
    private String parseStatusDesc;

    @Schema(description = "启用状态：0-未启用 1-已启用")
    private Integer launchStatus;

    @Schema(description = "启用状态描述")
    private String launchStatusDesc;

    @Schema(description = "排序")
    private Integer sortNum;

    @Schema(description = "状态：0-停用 1-正常")
    private Integer status;

    @Schema(description = "状态描述")
    private String statusDesc;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新者")
    private String updateBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "页面数量")
    private Integer pageCount;

    @Schema(description = "热点数量")
    private Integer hotspotCount;
}
