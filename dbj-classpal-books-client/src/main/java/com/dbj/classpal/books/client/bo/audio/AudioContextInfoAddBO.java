package com.dbj.classpal.books.client.bo.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 预置提示音表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AudioContextInfoAddBO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @Schema(description = "音频简介id")
    @NotNull(message = "音频简介id不能为空")
    private Integer audioIntroId;

    @Schema(description = "tts格式音频文本")
    @NotEmpty(message = "tts格式音频文本不能为空")
    private String text;

    @Schema(description = "html格式音频文本")
    @NotEmpty(message = "html格式音频文本不能为空")
    private String htmlText;

    @Schema(description = "发音人id")
    private Integer audioSpeakerId;

    @Schema(description = "情感：neutral（中性）、happy（开心）、angry（生气）、sad（悲伤）、fear（害怕）、hate（憎恨）、surprise（惊讶）、arousal（激动）、serious（严肃）、disgust（厌恶）、jealousy（嫉妒）、embarrassed（尴尬）、frustrated（沮丧）、affectionate（深情）、gentle（温柔）、newscast（播报）、customer-service（客服）、story（小说）、living（直播）")
    private String emotion;

    @Schema(description = "音量，取值范围：0~100")
    private Integer volume;

    @Schema(description = "语速，取值范围：-500~500")
    private Integer speechRate;

    @Schema(description = "语调，取值范围：-500~500")
    private Integer pitchRate;

    @Schema(description = "情绪强度，数值范围 [0.01,2.0]")
    private BigDecimal intensity;

    @Schema(description = "排序")
    private Integer orderNum;

    @Schema(description = "提示音id，多个用英文逗号隔开")
    private String audioHintMusicIds;

}
