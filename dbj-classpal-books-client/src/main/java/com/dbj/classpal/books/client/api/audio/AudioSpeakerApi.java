package com.dbj.classpal.books.client.api.audio;


import com.dbj.classpal.books.client.bo.audio.AudioSpeakerBO;
import com.dbj.classpal.books.client.dto.audio.AudioSpeakerDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 发音人
 * <AUTHOR>
 * @since 2025-06-25
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}", path = "/api/classpal/books/${spring.application.version}")
public interface AudioSpeakerApi {

    /**
     * 获取发音人列表
     * @param bo
     * @return
     * @throws BusinessException
     */
    @PostMapping("/speaker/template/list")
    RestResponse<List<AudioSpeakerDTO>> list(@RequestBody AudioSpeakerBO bo) throws BusinessException;

}
