package com.dbj.classpal.books.client.dto.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 点读书目录API DTO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingMenuApiDTO", description = "点读书目录API DTO")
public class PointReadingMenuApiDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "目录ID")
    private Integer id;

    @Schema(description = "所属点读书ID")
    private Integer bookId;

    @Schema(description = "点读书名称")
    private String bookName;

    @Schema(description = "目录名称")
    private String name;

    @Schema(description = "父级目录ID")
    private Integer parentId;

    @Schema(description = "父级目录名称")
    private String parentName;

    @Schema(description = "目录层级")
    private Integer level;

    @Schema(description = "排序")
    private Integer sortNum;

    @Schema(description = "状态：0-停用 1-正常")
    private Integer status;

    @Schema(description = "状态描述")
    private String statusDesc;

    @Schema(description = "子目录列表")
    private List<PointReadingMenuApiDTO> children;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
