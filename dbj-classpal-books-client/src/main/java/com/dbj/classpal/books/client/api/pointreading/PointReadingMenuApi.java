package com.dbj.classpal.books.client.api.pointreading;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingMenuBatchApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingMenuQueryApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingMenuSaveApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingMenuSortApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingMenuUpdateApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingIdApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingBookIdApiBO;
import com.dbj.classpal.books.client.bo.pointreading.PointReadingParentIdApiBO;
import com.dbj.classpal.books.client.dto.pointreading.PointReadingMenuApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 点读书目录 API接口
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}", path = "/api/classpal/books/${spring.application.version}")
public interface PointReadingMenuApi {

    /**
     * 分页查询点读书目录
     *
     * @param pageInfo 分页查询参数
     * @return 分页结果
     */
    @Operation(summary = "分页查询点读书目录")
    @PostMapping("/point-reading/menu/page")
    RestResponse<Page<PointReadingMenuApiDTO>> pageMenu(@RequestBody @Validated PageInfo<PointReadingMenuQueryApiBO> pageInfo) throws BusinessException;

    /**
     * 查询目录详情
     *
     * @param idBO 目录ID参数
     * @return 目录详情
     */
    @Operation(summary = "查询目录详情")
    @PostMapping("/point-reading/menu/detail")
    RestResponse<PointReadingMenuApiDTO> detail(@RequestBody @Validated PointReadingIdApiBO idBO) throws BusinessException;

    /**
     * 保存点读书目录
     *
     * @param saveBO 保存参数
     * @return 目录ID
     */
    @Operation(summary = "保存点读书目录")
    @PostMapping("/point-reading/menu/save")
    RestResponse<Integer> save(@RequestBody @Validated PointReadingMenuSaveApiBO saveBO) throws BusinessException;

    /**
     * 更新点读书目录
     *
     * @param updateBO 更新参数
     * @return 更新结果
     */
    @Operation(summary = "更新点读书目录")
    @PostMapping("/point-reading/menu/update")
    RestResponse<Boolean> update(@RequestBody @Validated PointReadingMenuUpdateApiBO updateBO) throws BusinessException;

    /**
     * 删除点读书目录
     *
     * @param idBO 目录ID参数
     * @return 删除结果
     */
    @Operation(summary = "删除点读书目录")
    @PostMapping("/point-reading/menu/delete")
    RestResponse<Boolean> delete(@RequestBody @Validated PointReadingIdApiBO idBO) throws BusinessException;

    /**
     * 批量删除点读书目录
     *
     * @param batchBO 批量删除参数
     * @return 删除结果
     */
    @Operation(summary = "批量删除点读书目录")
    @PostMapping("/point-reading/menu/delete/batch")
    RestResponse<Boolean> deleteBatch(@RequestBody @Validated PointReadingMenuBatchApiBO batchBO) throws BusinessException;

    /**
     * 获取目录树形结构
     *
     * @param bookIdBO 点读书ID参数
     * @return 目录树
     */
    @Operation(summary = "获取目录树形结构")
    @PostMapping("/point-reading/menu/tree")
    RestResponse<List<PointReadingMenuApiDTO>> getMenuTree(@RequestBody @Validated PointReadingBookIdApiBO bookIdBO) throws BusinessException;

    /**
     * 获取子目录列表
     *
     * @param parentIdBO 父级目录ID参数
     * @return 子目录列表
     */
    @Operation(summary = "获取子目录列表")
    @PostMapping("/point-reading/menu/children")
    RestResponse<List<PointReadingMenuApiDTO>> getChildren(@RequestBody @Validated PointReadingParentIdApiBO parentIdBO) throws BusinessException;

    /**
     * 更新排序
     *
     * @param sortBO 排序参数
     * @return 更新结果
     */
    @Operation(summary = "更新排序")
    @PostMapping("/point-reading/menu/sort")
    RestResponse<Boolean> updateSort(@RequestBody @Validated PointReadingMenuSortApiBO sortBO) throws BusinessException;
}
