package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 单书ID列表业务对象
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "单书ID列表业务对象")
public class AppEBookIdsApiBO implements Serializable {

    @Schema(description = "单书ID列表")
    @NotEmpty(message = "单书ID列表不能为空")
    private List<Integer> ids;
} 