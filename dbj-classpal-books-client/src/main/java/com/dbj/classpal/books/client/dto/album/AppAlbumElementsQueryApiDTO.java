package com.dbj.classpal.books.client.dto.album;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumElementsQueryApiDTO
 * Date:     2025-04-10 09:31:28
 * Description: 表名： ,描述： 表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@Tag(name = "专辑查询DTO")
public class AppAlbumElementsQueryApiDTO implements Serializable {
    
    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "专辑菜单ID")
    private Integer appAlbumMenuId;

    @Schema(description = "专辑类型 1音频 2视频")
    private Integer albumType;

    @Schema(description = "专辑封面")
    private String albumCover;

    @Schema(description = "专辑标题")
    private String albumTitle;

    @Schema(description = "关联文件数量")
    private Integer refCount;

    @Schema(description = "专辑简介")
    private String albumRemark;

    @Schema(description = "是否隐藏 0显示 1隐藏")
    private Integer albumVisible;

    @Schema(description = "是否隐藏文本")
    private String albumVisibleStr;

    @Schema(description = "上架状态 0下架 1上架")
    private Integer albumStatus;

    @Schema(description = "上架状态文本")
    private String albumStatusStr;

    @Schema(description = "排序")
    private Integer orderNum;

    @Schema(description = "是否被引用")
    private Boolean isRef;

    @Schema(description = "试学体验个数")
    private Integer trialCount;

    @Schema(description = "售卖方式开关")
    private Boolean salesModeStatus;

    @Schema(description = "销售模式：0-免费 1-收费")
    private Boolean salesMode;

    @Schema(description = "销售价格(元)")
    private BigDecimal salesPrice;

    @Schema(description = "原价(元)")
    private BigDecimal originalPrice;

}
