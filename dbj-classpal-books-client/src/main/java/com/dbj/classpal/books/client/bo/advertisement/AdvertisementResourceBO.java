package com.dbj.classpal.books.client.bo.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 资源位查询BO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
@Tag(name="资源位查询BO", description="资源位查询BO")
public class AdvertisementResourceBO implements Serializable {


    @Schema(description = "资源位名称")
    private String areaName;
}
