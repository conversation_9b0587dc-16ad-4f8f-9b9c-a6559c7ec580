package com.dbj.classpal.books.client.api.books;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodeContentsUpdForceApiBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsMoveBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsPageBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsSaveBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsTreeBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsUpdBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsUpdTypeBO;
import com.dbj.classpal.books.client.dto.books.BooksRankInCodesContentsDetailDTO;
import com.dbj.classpal.books.client.dto.books.BooksRankInCodesContentsPageDTO;
import com.dbj.classpal.books.client.dto.books.BooksRankInCodesContentsTreeDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AdminBooksRankInCodeContentsApi {

    @PostMapping("/books/rank/in/code/contents/list")
    RestResponse<List<BooksRankInCodesContentsTreeDTO>> list(@RequestBody BooksRankInCodesContentsTreeBO boardBooksRankClassifyBO) throws BusinessException;
    @PostMapping("/books/rank/in/code/contents/page")
    RestResponse<Page<BooksRankInCodesContentsPageDTO>> page(@RequestBody PageInfo<BooksRankInCodesContentsPageBO> pageInfo) throws BusinessException;
    @PostMapping("/books/rank/in/code/contents/save")
    RestResponse<Integer> save(@RequestBody BooksRankInCodesContentsSaveBO booksRankInCodesContentsSaveBO) throws BusinessException;
    @PostMapping("/books/rank/in/code/contents/update")
    RestResponse<Boolean> update(@RequestBody BooksRankInCodesContentsUpdBO booksRankInCodesContentsUpdBO) throws BusinessException;
    @PostMapping("/books/rank/in/code/contents/delete")
    RestResponse<Boolean> delete(@RequestParam Integer id) throws BusinessException;
    @PostMapping("/books/rank/in/code/contents/updateType")
    RestResponse<Boolean> updateType(@RequestBody BooksRankInCodesContentsUpdTypeBO bo) throws BusinessException;
    @PostMapping("/books/rank/in/code/contents/forcePromotionUrl")
    RestResponse<Boolean> forcePromotionUrl(@RequestBody BooksRankInCodeContentsUpdForceApiBO bo) throws BusinessException;
    @PostMapping("/books/rank/in/code/contents/detail")
    RestResponse<BooksRankInCodesContentsDetailDTO> detail(@RequestParam Integer id) throws BusinessException;
    @PostMapping("/books/rank/in/code/contents/move")
    RestResponse<Boolean> move(@RequestBody BooksRankInCodesContentsMoveBO bo) throws BusinessException;




}
