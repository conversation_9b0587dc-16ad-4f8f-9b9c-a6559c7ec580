package com.dbj.classpal.books.client.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "错题查询结果")
@Data
public class WrongQuestionResultApiDTO implements Serializable {


    /**
     * ID
     */
    @Schema(description = "ID")
    private Integer id;

    /**
     * 题目id
     */
    @Schema(description = "题目id")
    private Integer questionId;

    /**
     * 题目
     */
    @Schema(description = "题目")
    private String title;

    /**
     * 题目类型
     */
    @Schema(description = "题目类型")
    private Integer type;

    /**
     * 媒体文件类型 1-文本 2-图片 3-音频 4-视频
     */
    @Schema(description = "媒体文件类型")
    private Integer mediaType;

    /**
     * 媒体文件URL
     */
    @Schema(description = "媒体文件URL")
    private List<QuestionMediaApiDTO> mediaUrl;

    /**
     * 辅助识图
     */
    @Schema(description = "辅助识图")
    private List<QuestionRecognitionApiDTO> aidedRecognitionUrl;

    /**
     * 选项类型
     */
    @Schema(description = "选项类型")
    private Integer optionType;

    /**
     * 学科名称
     */
    @Schema(description = "学科名称")
    private String subjectName;


    /**
     * 用户答案
     */
    @Schema(description = "用户答案")
    private String userAnswer;

    /**
     * 用户选择的答案ID
     */
    @Schema(description = "用户选择的答案ID")
    private String userAnswerIds;

    /**
     * 正确答案（当includeAnswers=true时返回）
     */
    @Schema(description = "正确答案")
    private String correctAnswer;

    /**
     * 正确答案ID（当includeAnswers=true时返回）
     */
    @Schema(description = "正确答案ID")
    private String correctAnswerIds;

    /**
     * 解析
     */
    @Schema(description = "解析")
    private String analyzes;

    /**
     * 选项列表
     */
    @Schema(description = "选项列表")
    private List<WrongQuestionOptionApiDTO> options;

    /**
     * 完形填空空位结果列表（仅完形填空题使用）
     */
    @Schema(description = "完形填空空位结果列表")
    private List<WrongQuestionBlankApiDTO> blankResults;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 格式化后的日期（当年显示MM-DD，非当年显示YYYY-MM-DD）
     */
    @Schema(description = "格式化后的日期")
    private String formattedDate;

    /**
     * 唯一标识一次错题事件
     */
    @Schema(description = "唯一标识一次错题事件")
    private String stateId;
}