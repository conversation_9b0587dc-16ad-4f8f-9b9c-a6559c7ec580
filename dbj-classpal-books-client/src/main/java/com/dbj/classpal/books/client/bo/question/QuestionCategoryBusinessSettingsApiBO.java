package com.dbj.classpal.books.client.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 题库业务设置API BO
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "题库业务设置API BO")
public class QuestionCategoryBusinessSettingsApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID（更新时必填）
     */
    @Schema(description = "ID（更新时必填）")
    private Integer id;

    /**
     * 业务ID
     */
    @Schema(description = "业务ID")
    @NotNull(message = "业务ID不能为空")
    private Integer businessId;

    /**
     * 业务类型（1-学习模块答题 2-图书书内码答题 3-点读答题）
     */
    @Schema(description = "业务类型（1-学习模块答题 2-图书书内码答题 3-点读答题）")
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;

    /**
     * 题库集合
     */
    @Schema(description = "题库集合")
    @NotEmpty(message = "题库集合不能为空")
    private List<QuestionCategoryBusinessRefSaveApiBO> questionCategories;

    /**
     * 出题方式
     */
    @Schema(description = "出题方式")
    @NotNull(message = "出题方式不能为空")
    private Integer questionMethod;

    /**
     * 题目数量
     */
    @Schema(description = "题目数量")
    @NotNull(message = "题目数量不能为空")
    private Integer questionNum;

    /**
     * 状态（1-启用 0-禁用）
     */
    @Schema(description = "状态（1-启用 0-禁用）")
    private Integer status;
}
