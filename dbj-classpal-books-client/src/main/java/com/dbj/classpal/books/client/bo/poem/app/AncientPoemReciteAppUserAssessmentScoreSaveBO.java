package com.dbj.classpal.books.client.bo.poem.app;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className AncientPoemReciteAppUserAssessmentScoreSaveBO
 * @description
 * @date 2025-05-28 11:15
 **/
@Data
public class AncientPoemReciteAppUserAssessmentScoreSaveBO  implements Serializable {



    @Schema(description = "关联古诗ID")
    private Integer poemId;

    @Schema(description = "总分（0-100）")
    private BigDecimal totalScore;

    @Schema(description = "流利度得分（0-20）")
    private BigDecimal fluencyScore;

    @Schema(description = "完整度得分（0-20）")
    private BigDecimal completenessScore;

    @Schema(description = "韵律得分（0-20）")
    private BigDecimal rhythmScore;

    @Schema(description = "声调得分（0-20）")
    private BigDecimal toneScore;

    @Schema(description = "发音得分（0-20）")
    private BigDecimal pronunciationScore;

    @Schema(description = "评语")
    private String comment;

    @Schema(description = "内容")
    private String content;

}
