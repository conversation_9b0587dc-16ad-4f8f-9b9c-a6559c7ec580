package com.dbj.classpal.books.client.dto.books.app;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 产品分类配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="BooksUserRef对象", description="产品分类配置表")
public class BooksUserApiDTO implements Serializable {
    @Schema(description ="图书id")
    private Integer id;
    @Schema(description ="图书id")
    private String bookName;
    @Schema(description =  "封面url")
    private String picUrl;
    @Schema(description =  "配套书内码内容数量")
    private Long contentsNum;
    @Schema(description =  "是否已添加 0 否 1是")
    private Integer isAdd;

}
