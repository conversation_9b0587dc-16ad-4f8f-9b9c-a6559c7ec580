package com.dbj.classpal.books.client.dto.studycenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "答题设置-详情DTO")
public class AppStudyModuleQuestionRefApiDTO {
    /**
     * 题目分类ID
     */
    @Schema(description = "题目分类ID")
    private Integer questionCategoryId;
    /**
     * 题目分类ID
     */
    @Schema(description = "题目分类ID")
    private Integer id;
    /**
     * 题目分类名称
     */
    @Schema(description = "题目分类名称")
    private String name;
    /**
     * 题目数量
     */
    @Schema(description = "题目数量")
    private Integer currentQuestionCount;

    /**
     * 业务ID
     */
    @Schema(description = "业务ID")
    private Integer businessId;

    /**
     * 业务类型
     */
    @Schema(description = "业务类型")
    private Integer businessType;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sortNum;
}
