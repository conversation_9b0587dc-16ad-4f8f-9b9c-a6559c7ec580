package com.dbj.classpal.books.client.bo.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <p>
 * 音频分类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
public class AudioClassifyAddBO {

    private Integer id;

    @Schema(description = "父节点ID")
    private Integer parentId;

    @NotEmpty(message = "分类名称不能为空")
    @Schema(description = "音频分类名称", requiredMode = RequiredMode.REQUIRED)
    private String classifyName;

}
