package com.dbj.classpal.books.client.api.evaluation;

import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminEvaluationNodeReCoverApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminEvaluationNodeEditApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminEvaluationNodeSaveApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminEvaluationNodeSortApiBO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AdminEvaluationNodeApi {

    /**
     * 新增评测项
     * @param bo
     * @return
     */
    @PostMapping("/evaluationNode/saveEvaluationNode")
    RestResponse<Boolean> saveEvaluationNode(@RequestBody @Valid AdminEvaluationNodeSaveApiBO bo) throws BusinessException;

    /**
     * 修改评测项
     * @param bo
     * @return
     */
    @PostMapping("/evaluationNode/editEvaluationNode")
    RestResponse<Boolean> editEvaluationNode(@RequestBody @Valid AdminEvaluationNodeEditApiBO bo) throws BusinessException;


    /**
     * 评测项排序
     * @param bo
     * @return
     */
    @PostMapping("/evaluationNode/reSort")
    RestResponse<Boolean> reSort(@RequestBody @Valid AdminEvaluationNodeSortApiBO bo) throws BusinessException;

    /**
     * 删除评测项
     * @param bo
     * @return
     */
    @PostMapping("/evaluationNode/delete")
    RestResponse<Boolean> delete(@RequestBody @Valid CommonIdsApiBO bo) throws BusinessException;

}
