package com.dbj.classpal.books.client.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema(description = "错题完形填空空位结果")
@Data
public class WrongQuestionBlankApiDTO implements Serializable {

    /**
     * 空位区域ID
     */
    @Schema(description = "空位区域ID")
    private Integer blankAreaId;

    /**
     * 题目ID
     */
    @Schema(description = "题目ID")
    private Integer questionId;

    /**
     * 空位序号
     */
    @Schema(description = "空位序号")
    private Integer blankIndex;

    /**
     * 用户答案
     */
    @Schema(description = "用户答案")
    private String userAnswer;

    /**
     * 用户选择的答案ID
     */
    @Schema(description = "用户选择的答案ID")
    private String userAnswerIds;

    /**
     * 正确答案
     */
    @Schema(description = "正确答案")
    private String correctAnswer;

    /**
     * 正确答案ID
     */
    @Schema(description = "正确答案ID")
    private String correctAnswerIds;
    /**
     * 唯一标识一次错题事件
     */
    @Schema(description = "唯一标识一次错题事件")
    private String stateId;
    /**
     * 选项列表
     */
    @Schema(description = "选项列表")
    private List<WrongQuestionOptionApiDTO> options;
}