package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 单书异步处理API请求参数BO
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@Schema(description = "异步任务请求参数")
public class AppEBookAsyncProcessTaskApiBO implements Serializable {

    @Schema(description = "taskId")
    @NotEmpty(message = "taskId不能为空")
    private String taskId;

}
