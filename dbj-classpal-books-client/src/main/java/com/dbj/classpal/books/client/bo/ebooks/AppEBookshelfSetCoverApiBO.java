package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 设置书架封面业务参数对象
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
@Data
@Schema(description = "设置书架封面参数BO")
public class AppEBookshelfSetCoverApiBO implements Serializable {

    @Schema(description = "书架ID")
    @NotNull(message = "书架ID不能为空")
    private Integer id;
    
    @Schema(description = "单书ID，用于获取封面")
    @NotNull(message = "单书ID不能为空")
    private Integer bookId;
    
    @Schema(description = "自定义封面URL，优先级高于bookId",hidden = true)
    @NotNull(message = "封面URL不能为空")
    private String coverUrl;
} 