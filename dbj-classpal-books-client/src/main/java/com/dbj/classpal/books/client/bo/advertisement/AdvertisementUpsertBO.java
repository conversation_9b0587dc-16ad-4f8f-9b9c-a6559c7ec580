
package com.dbj.classpal.books.client.bo.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 广告新增/修改BO
 * <AUTHOR> <PERSON>
 * @since 2025-04-21 16:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
@Tag(name="广告新增/修改BO", description="广告新增/修改BO")
public class AdvertisementUpsertBO implements Serializable  {

	@Schema(description = "广告id")
	private Integer id;
	/**
	 * 广告类型字典项的值
	 */
	@NotNull
	@Schema(description = "广告类型字典项的值", requiredMode = Schema.RequiredMode.REQUIRED)
	private String type;
	/**
	 * 描述
	 */
	@Schema(description = "描述")
	private String description;
	/**
	 * 跳转类型 0-无跳转 1-链接 2-小程序 3-基础图文
	 */
	@Schema(description = "跳转类型 0-无跳转 1-链接 2-小程序 3-基础图文")
	private Integer redirectType;
//	/**
//	 * 跳转标识
//	 */
//	@Schema(description = "跳转标识")
//	private String redirectFlag;
	/**
	 * 跳转地址
	 */
	@Schema(description = "跳转地址")
	private String 	redirectUri;
	/**
	 * 外部小程序appid
	 */
	@Schema(description = "外部小程序appid")
	private String redirectAppId;
//	/**
//	 * 落地页面id
//	 */
//	@Schema(description = "落地页面id")
//	private String advertisingLandingId;
	/**
	 * 标题
	 */
	@Size(max = 32)
	@NotBlank
	@Schema(description = "标题", requiredMode = Schema.RequiredMode.REQUIRED)
	private String title;
	/**
	 * 富文本内容
	 */
	@Schema(description = "富文本内容")
	private String content;

	@Schema(description = "展示次数 1-仅一次 2-每次 3-每天一次")
	private Integer showType;

	/**
	 * 排序（越大越靠前）
	 */
	@Schema(description = "排序（越大越靠前）")
	private Integer sort;
	/**
	 * 是否启用 1-是 0-否
	 */
	@NotNull
	@Schema(description = "是否启用 true-是 false-否", requiredMode = Schema.RequiredMode.REQUIRED)
	private Boolean status;
	/**
	 * 封面路径
	 */
	@NotBlank
	@Schema(description = "封面路径", requiredMode = Schema.RequiredMode.REQUIRED)
	private String coverUrl;

	@Valid
	@Schema(description = "展示条件")
	private List<AdvertisementLevelConditionBO> advertisementLevelConditionList;
}
