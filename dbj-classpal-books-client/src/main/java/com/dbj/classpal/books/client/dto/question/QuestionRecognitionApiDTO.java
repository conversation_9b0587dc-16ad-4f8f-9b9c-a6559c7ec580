package com.dbj.classpal.books.client.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
@Schema
@Data
public class QuestionRecognitionApiDTO implements Serializable {
    /**
     * 素材库ID
     */
    @Schema(description = "素材库ID")
    private Integer materialId;
    /**
     * 素材库地址
     */
    @Schema(description = "素材库地址")
    private String materialPath;

    @Schema(description = "资源名称")
    private String materialName;

    @Schema(description = "排序")
    private Integer sortNum;
}
