package com.dbj.classpal.books.client.api.ebooks;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.ebooks.AppEBookResourceBatchQueryApiBO;
import com.dbj.classpal.books.client.dto.ebooks.AppEBookResourceApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * 单书资源API接口
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}", path = "/api/classpal/books/${spring.application.version}")
public interface AppEBookResourceApi {

    /**
     * 批量查询书籍资源（按书籍分组）
     *
     * @param queryBO 批量查询参数
     * @return 按书籍ID分组的资源列表
     */
    @PostMapping("/ebook/resource/batch/query")
    RestResponse<Map<Integer, List<AppEBookResourceApiDTO>>> batchQueryByBooks(@RequestBody @Validated AppEBookResourceBatchQueryApiBO queryBO) throws BusinessException;

    /**
     * 批量查询书架资源（按书架分组）
     *
     * @param queryBO 批量查询参数
     * @return 按书架ID分组的资源列表
     */
    @PostMapping("/ebook/resource/batch/shelves")
    RestResponse<Map<Integer, List<AppEBookResourceApiDTO>>> batchQueryByShelves(@RequestBody @Validated AppEBookResourceBatchQueryApiBO queryBO) throws BusinessException;

    /**
     * 分页查询书城资源
     *
     * @param pageRequest 分页查询参数
     * @return 书城资源分页数据
     */
    @PostMapping("/ebook/resource/store/page")
    RestResponse<Page<AppEBookResourceApiDTO>> pageStoreResources(@RequestBody @Validated PageInfo<AppEBookResourceBatchQueryApiBO> pageRequest) throws BusinessException;

    /**
     * 批量查询指定书籍的资源
     *
     * @param bookIds 书籍ID列表
     * @return 按书籍ID分组的资源列表
     */
    @PostMapping("/ebook/resource/books/simple")
    RestResponse<Map<Integer, List<AppEBookResourceApiDTO>>> batchQueryResourcesByBookIds(@RequestBody List<Integer> bookIds) throws BusinessException;

    /**
     * 批量查询指定书籍的特定类型资源
     *
     * @param queryBO 查询参数（包含书籍ID列表和资源类型）
     * @return 按书籍ID分组的资源列表
     */
    @PostMapping("/ebook/resource/books/typed")
    RestResponse<Map<Integer, List<AppEBookResourceApiDTO>>> batchQueryTypedResourcesByBookIds(@RequestBody @Validated AppEBookResourceBatchQueryApiBO queryBO) throws BusinessException;
}
