
package com.dbj.classpal.books.client.bo.poem;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 古诗背诵合集表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ancient_poem_recite_collection")
public class AncientPoemReciteCollectionUpdateTitleBO implements Serializable {

    @Schema(description =  "ID")
    @NotNull(message = "主键ID不能为空")
    private Integer id;

    @Schema(description =  "合集标题")
    @NotEmpty(message = "合集标题不能为空")
    private String title;



}
