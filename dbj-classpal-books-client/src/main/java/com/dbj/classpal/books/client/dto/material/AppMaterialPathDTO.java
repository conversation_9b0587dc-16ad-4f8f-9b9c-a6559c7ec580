package com.dbj.classpal.books.client.dto.material;

import com.dbj.classpal.framework.utils.util.TreeVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2025/6/30 11:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AppMaterialPathDTO extends TreeVO<Integer, AppMaterialPathDTO> {

    @Schema(description = "素材名称")
    private String materialName;

}
