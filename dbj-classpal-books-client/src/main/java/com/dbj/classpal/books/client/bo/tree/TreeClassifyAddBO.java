package com.dbj.classpal.books.client.bo.tree;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 树形分类AddBO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
public class TreeClassifyAddBO implements Serializable {

    @NotBlank
    @Size(max = 32)
    @Schema(description = "分类名称")
    private String name;

    @NotNull
    @Schema(description = "父分类id, null表示顶级分类")
    private Integer parentId;
}
