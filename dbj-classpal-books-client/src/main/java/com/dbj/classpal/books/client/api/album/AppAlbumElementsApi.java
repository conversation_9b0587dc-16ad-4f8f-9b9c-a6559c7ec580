package com.dbj.classpal.books.client.api.album;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.album.AppAlbumElementsIdsApiBO;
import com.dbj.classpal.books.client.bo.album.AppAlbumElementsQueryApiBO;
import com.dbj.classpal.books.client.bo.album.AppAlbumElementsSaveApiBO;
import com.dbj.classpal.books.client.bo.album.AppAlbumElementsUpdateApiBO;
import com.dbj.classpal.books.client.bo.album.AppAlbumElementsUpdateCoverApiBO;
import com.dbj.classpal.books.client.bo.album.AppAlbumElementsUpdateRemarkApiBO;
import com.dbj.classpal.books.client.bo.album.AppAlbumElementsUpdateStatusApiBO;
import com.dbj.classpal.books.client.bo.album.AppAlbumElementsUpdateTitleApiBO;
import com.dbj.classpal.books.client.bo.album.AppAlbumElementsUpdateVisibleApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.dto.album.AppAlbumElementsQueryApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumApi
 * Date:     2025-04-15 11:13:46
 * Description: 表名： ,描述： 表
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AppAlbumElementsApi {

    /**
     * 分页查询专辑列表
     * @return
     */
    @PostMapping("/albumElements/getAppAlbumElementsList")
    RestResponse<List<AppAlbumElementsQueryApiDTO>> getAppAlbumElementsList(@RequestBody @Validated AppAlbumElementsQueryApiBO bo) throws BusinessException;

    /**
     * 分页查询专辑列表
     * @return
     */
    @PostMapping("/albumElements/pageAlbumElements")
    RestResponse<Page<AppAlbumElementsQueryApiDTO>> pageAlbumElements(@RequestBody @Validated PageInfo<AppAlbumElementsQueryApiBO>bo) throws BusinessException;

    /**
     * 获取单个专辑信息
     * @param bo
     * @return
     */
    @PostMapping("/albumElements/getAppAlbumElement")
    RestResponse<AppAlbumElementsQueryApiDTO> getAppAlbumElement(@RequestBody @Valid CommonIdApiBO bo);

    /**
     * 新增专辑
     * @return
     */
    @PostMapping("/albumElements/saveAlbumElements")
    RestResponse<Boolean> saveAlbumElements(@RequestBody @Valid AppAlbumElementsSaveApiBO bo) throws BusinessException;

    /**
     * 修改专辑
     * @return
     */
    @PostMapping("/albumElements/updateAlbumElements")
    RestResponse<Boolean> updateAlbumElements(@RequestBody @Valid AppAlbumElementsUpdateApiBO bo) throws BusinessException;

    /**
     * 批量删除专辑
     * @return
     */
    @PostMapping("/albumElements/deleteAppAlbumElements")
    RestResponse<Boolean> deleteAppAlbumElements(@RequestBody @Valid CommonIdsApiBO bo) throws BusinessException;

    /**
     * 修改专辑标题
     * @param bo
     * @return
     */
    @PostMapping("/albumElements/updateAppAlbumElementTitle")
    RestResponse<Boolean> updateAppAlbumElementTitle(@RequestBody @Valid AppAlbumElementsUpdateTitleApiBO bo) throws BusinessException;


    /**
     * 修改专辑简介
     * @param bo
     * @return
     */
    @PostMapping("/albumElements/updateAppAlbumElementRemark")
    RestResponse<Boolean> updateAppAlbumElementRemark(@RequestBody @Valid AppAlbumElementsUpdateRemarkApiBO bo) throws BusinessException;

    /**
     * 修改专辑隐藏状态
     * @param bo
     * @return
     */
    @PostMapping("/albumElements/updateAppAlbumElementVisible")
    RestResponse<Boolean> updateAppAlbumElementVisible(@RequestBody @Valid AppAlbumElementsUpdateVisibleApiBO bo) throws BusinessException;

    /**
     * 修改专辑上架状态
     * @param bo
     * @return
     */
    @PostMapping("/albumElements/updateAppAlbumElementStatus")
    RestResponse<Boolean> updateAppAlbumElementStatus(@RequestBody @Valid AppAlbumElementsUpdateStatusApiBO bo) throws BusinessException;

    /**
     * 修改封面
     * @param bo
     * @return
     */
    @PostMapping("/albumElements/updateAppAlbumElementCover")
    RestResponse<Boolean> updateAppAlbumElementCover(@RequestBody @Valid AppAlbumElementsUpdateCoverApiBO bo) throws BusinessException;

    /**
     * 判断专辑是否全部存在
     * @param bo
     * @return
     */
    @PostMapping("/albumElements/allExists")
    RestResponse<Boolean> allExists(@RequestBody @Valid AppAlbumElementsIdsApiBO bo);

    /**
     * 根据id集合查询专辑信息
     * @title: selectAppAlbumElementsListByIds
     * @description:
     * @author: subei
     * @date: 2025/7/18 9:43
     * @param ids
     * @return: java.util.List<com.dbj.classpal.books.common.dto.album.AppAlbumElementsQueryDTO>
     **/
    @PostMapping("/albumElements/selectAppAlbumElementsListByIds")
    RestResponse<List<AppAlbumElementsQueryApiDTO>> selectAppAlbumElementsListByIds(@RequestBody @Valid Collection<Integer> ids);

}
