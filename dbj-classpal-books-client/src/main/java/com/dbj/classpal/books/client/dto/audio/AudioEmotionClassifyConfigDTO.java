package com.dbj.classpal.books.client.dto.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 发音人
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AudioEmotionClassifyConfigDTO {

    private Integer id;

    @Schema(description = "发音人id")
    private Integer audioSpeakerId;

    @Schema(description = "情感：neutral（中性）、happy（开心）、angry（生气）、sad（悲伤）、fear（害怕）、hate（憎恨）、surprise（惊讶）、arousal（激动）、serious（严肃）、disgust（厌恶）、jealousy（嫉妒）、embarrassed（尴尬）、frustrated（沮丧）、affectionate（深情）、gentle（温柔）、newscast（播报）、customer-service（客服）、story（小说）、living（直播）")
    private String emotion;

    @Schema(description = "发音人模板url")
    private String audioModelUrl;

    @Schema(description = "情感名称")
    private String emotionName;
    
}
