package com.dbj.classpal.books.client.api.pinyin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinAppBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinDelBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinPageBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinStatusUpdateBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinUpsertBO;
import com.dbj.classpal.books.client.dto.pinyin.PinyinAppDTO;
import com.dbj.classpal.books.client.dto.pinyin.PinyinDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * 汉语拼音 远程Api
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface PinyinApi {
    /**----------------------------------- admin调用Api -----------------------------------------*/
    /**
     * 分页查询拼音信息
     * @param pageInfo 包含分页信息的请求对象
     * @return 包含分页拼音信息的RestResponse对象
     */
    @PostMapping("/pinyin/getPinyinPage")
    RestResponse<Page<PinyinDTO>> getPinyinPage(@RequestBody @Validated PageInfo<PinyinPageBO> pageInfo);
    /**
     * 根据拼音ID获取拼音详细信息
     * @param bo 包含拼音ID的请求对象
     * @return 包含拼音信息的RestResponse对象
     */
    @PostMapping(value = "/pinyin/getPinyin")
    RestResponse<PinyinDTO> getPinyinInfo(@RequestBody @Validated CommonIdApiBO bo);
    /**
     * 保存拼音信息
     * @param bo 包含拼音信息的请求对象
     * @return 包含保存拼音信息的RestResponse对象
     * @throws BusinessException 业务异常
     */
    @PostMapping(value = "/pinyin/savePinyin")
    RestResponse<Boolean> savePinyin(@RequestBody @Validated PinyinUpsertBO bo) throws BusinessException;
    /**
     * 更新拼音信息
     * @param bo 包含拼音信息的请求对象
     * @return 包含更新拼音信息的RestResponse对象
     * @throws BusinessException 业务异常
     */
    @PostMapping(value = "/pinyin/updatePinyin")
    RestResponse<Boolean> updatePinyin(@RequestBody @Validated PinyinUpsertBO bo) throws BusinessException;
    /**
     * 更新拼音状态(启用/禁用)
     * @param bo 包含拼音状态的请求对象
     * @return 包含更新拼音状态的RestResponse对象
     */
    @PostMapping("/pinyin/updatePinyinStatus")
    RestResponse<Boolean> updatePinyinStatus(@RequestBody @Validated PinyinStatusUpdateBO bo);
    /**
     * 删除拼音信息
     * @param bo 包含拼音ID的请求对象
     * @return 包含删除拼音信息的RestResponse对象
     * @throws BusinessException 业务异常
     */
    @PostMapping(value = "/pinyin/deletePinyin")
    RestResponse<Boolean> deletePinyin(@RequestBody @Validated PinyinDelBO bo) throws BusinessException;


    /**----------------------------------- App调用Api -----------------------------------------*/
    /**
     * 获取拼音分类的拼音列表
     * @param bo 包含用户拼音信息的请求对象
     * @return 包含用户拼音信息的RestResponse对象
     */
    @PostMapping("/pinyin/getAppPinyinData")
    RestResponse<PinyinAppDTO> getAppPinyinData(@RequestBody @Validated PinyinAppBO bo);
}
