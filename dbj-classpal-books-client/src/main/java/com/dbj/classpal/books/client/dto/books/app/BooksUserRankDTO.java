package com.dbj.classpal.books.client.dto.books.app;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 图书卷册赠册表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="BooksRankInfo对象", description="图书卷册赠册表")
public class BooksUserRankDTO implements Serializable {
    @Schema(description =  "主键id")
    private Integer id;

    @Schema(description =  "封面格式 : 横板竖版")
    private String coverType;

    @Schema(description =  "产品id")
    private Integer bookId;

    @Schema(description =  "是否赠册 1-卷册 2-赠册")
    private Integer type;

    @Schema(description =  "序号")
    private Integer serialNo;

    @Schema(description =  "卷册名称")
    private String productItemName;

    @Schema(description =  "封面url")
    private String picUrl;

    @Schema(description =  "强跳链接")
    private String forcePromotionUrl;

}
