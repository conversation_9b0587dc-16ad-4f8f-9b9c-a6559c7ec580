package com.dbj.classpal.books.client.bo.studycenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "资源")
public class AppStudyModuleQuestionResourceApiBO {
    @Schema(description = "资源ID")
    private Integer resourceId;

    @Schema(description = "资源名称")
    private String resourceName;

    @Schema(description = "资源icon")
    private String resourceIcon;
} 