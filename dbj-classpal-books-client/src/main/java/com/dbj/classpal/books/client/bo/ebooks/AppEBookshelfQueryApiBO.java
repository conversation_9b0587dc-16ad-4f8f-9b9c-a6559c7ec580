package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 书架查询业务参数对象
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
@Data
@Schema(description = "书架查询参数BO")
public class AppEBookshelfQueryApiBO implements Serializable {

    @Schema(description = "书架名称")
    private String shelfTitle;

    @Schema(description = "启用状态：0-禁用，1-启用")
    private Integer launchStatus;

    @Schema(description = "允许下载：0-否，1-是")
    private Integer allowDownload;
} 