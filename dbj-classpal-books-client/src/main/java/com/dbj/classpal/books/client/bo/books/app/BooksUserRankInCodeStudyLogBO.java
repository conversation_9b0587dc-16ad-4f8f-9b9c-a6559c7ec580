package com.dbj.classpal.books.client.bo.books.app;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className BooksUserRankInCodeStudyLogBO
 * @description
 * @date 2025-04-27 10:16
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="书内码对象", description="书内码对象")
public class BooksUserRankInCodeStudyLogBO {

    @Schema(description ="书内码id")
    private Integer contentsId;
}
