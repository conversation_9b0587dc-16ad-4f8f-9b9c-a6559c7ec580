package com.dbj.classpal.books.client.bo.books;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/7/31 16:38
 */
@Data
public class BooksRankClassifySaveBO {

    @Schema(description = "id")
    private Integer id;

    @NotEmpty
    @Schema(description = "名称", requiredMode = RequiredMode.REQUIRED)
    private String name;

    @NotEmpty
    @Schema(description = "类型 BOOK_IN_CODES-图书资源 POINT_READING-点读 AUDIO_ALBUM-音频专辑 VIDEO_ALBUM-视频专辑", requiredMode = RequiredMode.REQUIRED)
    private String type;

    @NotNull
    @Schema(description = "排序", requiredMode = RequiredMode.REQUIRED)
    private Integer weight;
}
