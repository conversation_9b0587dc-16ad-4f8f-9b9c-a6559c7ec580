package com.dbj.classpal.books.client.api.audio;


import com.dbj.classpal.books.client.bo.audio.AudioBackgroundBO;
import com.dbj.classpal.books.client.bo.audio.AudioReorderBO;
import com.dbj.classpal.books.client.bo.audio.AudioTypeBO;
import com.dbj.classpal.books.client.dto.audio.AudioBackgroundDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 背景音
 * <AUTHOR>
 * @since 2025-06-27
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}", path = "/api/classpal/books/${spring.application.version}")
public interface AudioBackgroundApi {
    /**
     * 保存背景音
     * @param bo
     * @return
     * @throws BusinessException
     */
    @PostMapping("/background/save")
    RestResponse<Integer> save(@RequestBody List<AudioBackgroundBO> bo) throws BusinessException;
    /**
     * 重排序
     * @param bo
     * @return
     * @throws BusinessException
     */
    @PostMapping("/background/reorder")
    RestResponse<Integer> reorder(@RequestBody List<Integer> bo) throws BusinessException;
    /**
     * 查询背景音列表
     * @param bo
     * @return
     * @throws BusinessException
     */
    @PostMapping("/background/getDefinitionBgm")
    RestResponse<List<AudioBackgroundDTO>> getDefinitionBgm(@RequestBody AudioTypeBO bo) throws BusinessException;
}
