package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 设置书城封面业务参数对象
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
@Data
@Schema(description = "设置书城封面参数BO")
public class AppEBookstoreSetCoverApiBO implements Serializable {

    @Schema(description = "书城ID")
    @NotNull(message = "书城ID不能为空")
    private Integer id;
    
    @Schema(description = "书架ID，用于获取封面")
    @NotNull(message = "书架ID不能为空")
    private Integer shelfId;
    
    @Schema(description = "自定义封面URL，优先级高于shelfId",hidden = true)
    private String coverUrl;
} 