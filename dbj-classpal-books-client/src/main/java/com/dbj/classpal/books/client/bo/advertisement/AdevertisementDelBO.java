package com.dbj.classpal.books.client.bo.advertisement;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * Copyright (C), 2025, com.dbj
 * FileName: AdevertisementDelBO
 * Date:     2025-05-13 10:43:02
 * Description: 广告资源位删除BO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
@Tag(name="广告资源位删除BO", description="广告资源位删除BO")
public class AdevertisementDelBO implements Serializable {

    /** 广告类型字典项 */
    @NotBlank
    @Schema(description = "广告类型字典项", requiredMode = Schema.RequiredMode.REQUIRED)
    private String advertisementType;

    @Schema(description = "主键ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "主键ID列表不能为空")
    private List<Integer> ids;

}
