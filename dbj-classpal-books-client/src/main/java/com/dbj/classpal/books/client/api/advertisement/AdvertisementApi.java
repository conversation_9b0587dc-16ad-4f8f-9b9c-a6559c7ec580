package com.dbj.classpal.books.client.api.advertisement;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.advertisement.AdevertisementDelBO;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementAppBO;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementPageBO;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementStatusUpdateBO;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementUpsertBO;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementAppDTO;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <p>
 * 广告信息表 远程Api
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AdvertisementApi {
    /**----------------------------------- admin调用Api -----------------------------------------*/
    /**
     * 分页查询广告信息
     * @param pageInfo 包含分页信息的请求对象
     * @return 包含分页广告信息的RestResponse对象
     */
    @PostMapping("/advertisement/getAdvertisementPage")
    RestResponse<Page<AdvertisementDTO>> getAdvertisementPage(@RequestBody @Validated PageInfo<AdvertisementPageBO> pageInfo);
    /**
     * 根据广告ID获取广告详细信息
     * @param bo 包含广告ID的请求对象
     * @return 包含广告信息的RestResponse对象
     * @throws BusinessException 业务异常
     */
    @PostMapping(value = "/advertisement/getAdvertisement")
    RestResponse<AdvertisementDTO> getAdvertisementInfo(@RequestBody @Validated CommonIdApiBO bo) throws BusinessException;
    /**
     * 保存广告信息
     * @param bo 包含广告信息的请求对象
     * @return 包含保存广告信息的RestResponse对象
     * @throws BusinessException 业务异常
     */
    @PostMapping(value = "/advertisement/saveAdvertisement")
    RestResponse<Boolean> saveAdvertisement(@RequestBody @Validated AdvertisementUpsertBO bo) throws BusinessException;
    /**
     * 更新广告信息
     * @param bo 包含广告信息的请求对象
     * @return 包含更新广告信息的RestResponse对象
     * @throws BusinessException 业务异常
     */
    @PostMapping(value = "/advertisement/updateAdvertisement")
    RestResponse<Boolean> updateAdvertisement(@RequestBody @Validated AdvertisementUpsertBO bo) throws BusinessException;
    /**
     * 更新广告状态(启用/禁用)
     * @param bo 包含广告状态的请求对象
     * @return 包含更新广告状态的RestResponse对象
     */
    @PostMapping("/advertisement/updateAdvertisementStatus")
    RestResponse<Boolean> updateAdvertisementStatus(@RequestBody @Validated AdvertisementStatusUpdateBO bo);
    /**
     * 删除广告信息
     * @param bo 包含广告ID的请求对象
     * @return 包含删除广告信息的RestResponse对象
     * @throws BusinessException 业务异常
     */
    @PostMapping(value = "/advertisement/deleteAdvertisement")
    RestResponse<Boolean> deleteAdvertisement(@RequestBody @Validated AdevertisementDelBO bo) throws BusinessException;


    /**----------------------------------- App调用Api -----------------------------------------*/
    /**
     * 获取用户广告信息
     * @param bo 包含用户广告信息的请求对象
     * @return 包含用户广告信息的RestResponse对象
     */
    @PostMapping("/advertisement/getAppUserAdvertisement")
    RestResponse<List<AdvertisementAppDTO>> getAppUserAdvertisement(@RequestBody AdvertisementAppBO bo) throws BusinessException;
}
