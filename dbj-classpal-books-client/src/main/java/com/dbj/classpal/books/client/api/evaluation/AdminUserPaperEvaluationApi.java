package com.dbj.classpal.books.client.api.evaluation;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminUserPaperEvaluationQueryApiBO;
import com.dbj.classpal.books.client.bo.evaluation.app.AppUserPaperEvaluationSaveApiBO;
import com.dbj.classpal.books.client.dto.evaluation.AdminEvaluationReportQueryApiDTO;
import com.dbj.classpal.books.client.dto.evaluation.AdminUserPaperEvaluationQueryPageApiDTO;
import com.dbj.classpal.books.client.dto.evaluation.app.AppUserPaperEvaluationSaveApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AdminUserPaperEvaluationApi {

    /**
     * 分页查询用户评测报告列表
     * @param pageRequest
     * @return
     */
    @PostMapping("/userPaperEvaluation/pageInfo")
    RestResponse<Page<AdminUserPaperEvaluationQueryPageApiDTO>> pageInfo(@RequestBody PageInfo<AdminUserPaperEvaluationQueryApiBO> pageRequest) throws BusinessException;

    /**
     * 查询用户评测报告详情
     * @param bo
     * @return
     */
    @PostMapping("/userPaperEvaluation/getEvaluationReport")
    RestResponse<AdminEvaluationReportQueryApiDTO> getEvaluationReport(@RequestBody  @Valid CommonIdApiBO bo) throws BusinessException;
}
