package com.dbj.classpal.books.client.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema
@Data
public class QuestionBlankAreaApiBO implements Serializable {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Integer id;

    /**
     * 题目id
     */
    @Schema(description = "题目id")
    private Integer questionId;

    /**
     * 空位序号
     */
    @Schema(description = "空位序号")
    private Integer blankIndex;

    /**
     * 答案列表
     */
    @Schema(description = "答案列表")
    private List<QuestionAnswerApiBO> answers;

    /**
     * 是否启用 1-是 0-否
     */
    @Schema(description = "是否启用 1-是 0-否")
    private Boolean status;
} 