package com.dbj.classpal.books.client.dto.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 音频文件详情
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AudioDetailsDTO {

    @Schema(description = "音频大小（kb）")
    private double fileSizeInKB;

    @Schema(description = "音频大小（mb）")
    private double fileSizeInMB;

    @Schema(description = "音频时长（小时）")
    private int hours;

    @Schema(description = "音频时长（分钟）")
    private int minutes;

    @Schema(description = "音频时长（秒）")
    private int seconds;

    @Schema(description = "音频时长（毫秒）")
    private long milliseconds;

    @Schema(description = "音频采样率")
    private int samplingRate;

    @Schema(description = "声道类型：1 单声道 2 双声道")
    private int channelType;

    @Schema(description = "音频位深")
    private int bitDepth;


}
