package com.dbj.classpal.books.client.dto.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 试听音频合成任务信息
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AudioSynthesizerTaskInfoDTO {

    @Schema(description = "任务id")
    private String taskId;

    @Schema(description = "音频地址")
    private String audioUrl;

    @Schema(description = "任务状态：1 合成中 2 已合成（成功）3 任务失败 5 任务过期")
    private Integer taskStatus;

    @Schema(description = "任务失败错误信息")
    private AudioTaskErrorDTO errorMsg;

    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public enum TaskStatusEnum {
        COMPOSING(1, "合成中"),
        COMPOSED(2, "已合成"),
        FAILED(3, "任务失败"),
        EXPIRED(5, "任务过期");
        private Integer code;
        private String msg;
    }

}
