package com.dbj.classpal.books.client.dto.evaluation;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppUserPaperEvaluationDTO
 * Date:     2025-05-19 14:26:00
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
@Tag(name = "评测报告",description = "评测报告")
public class AdminUserPaperEvaluationAnalysisQueryApiDTO implements Serializable {

    @Schema(description = "评测报告id")
    private Integer appUserPaperEvaluationId;

    @Schema(description = "评测项id")
    private Integer appEvaluationNodeId;

    @Schema(description = "评测项名称")
    private String appEvaluationNodeName;

    @Schema(description = "能力得分")
    private Double abilityScore;

    @Schema(description = "能力分析")
    private String abilityAnalysis;

    @Schema(description = "答题正确数量")
    private Integer rightCount;

    @Schema(description = "答题错误数量")
    private Integer errorCount;

    @Schema(description = "总题数")
    private Integer totalCount;

    @Schema(description = "得分")
    private Double score;

    @Schema(description = "分析")
    private String analysis;

    @Schema(description = "建议")
    private String suggest;

    @Schema(description = "排序")
    private Integer orderNum;

}
