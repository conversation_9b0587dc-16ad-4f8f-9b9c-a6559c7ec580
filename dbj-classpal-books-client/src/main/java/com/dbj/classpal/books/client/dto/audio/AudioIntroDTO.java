package com.dbj.classpal.books.client.dto.audio;

import com.dbj.classpal.books.client.dto.material.AppMaterialPathDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/30 11:23
 */
@Data
public class AudioIntroDTO {

    @Schema(description = "id")
    private Integer id;

    @Schema(description = "音频名称")
    private String name;

    @Schema(description = "所属分类id")
    private Integer audioClassifyId;

    @Schema(description = "分类路径")
    private List<AudioClassifyPathDTO> classifyPathList;

    @Schema(description = "是否有背景音乐")
    private Boolean isBackgroundMusic;

    @Schema(description = "音频时长")
    private Integer duration;

    @Schema(description = "音频大小(kb)")
    private Double size;

    @Schema(description = "合成次数")
    private Integer frequency;

    @Schema(description = "素材id")
    private Integer appMaterialId;

    @Schema(description = "素材目录路径")
    private List<AppMaterialPathDTO> materialPathList;

    @Schema(description = "成品音频链接地址")
    private String audioUrl;

    @Schema(description = "失败原因")
    private String failReason;

    @Schema(description = "合成状态：0 待合成 1 合成中 2 已合成（成功）3 合成失败 4 取消")
    private Integer status;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "是否取消 0 否 1 是")
    private Integer isCancel;
}
