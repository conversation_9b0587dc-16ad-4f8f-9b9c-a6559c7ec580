package com.dbj.classpal.books.client.api.question;

import com.dbj.classpal.books.client.bo.question.*;
import com.dbj.classpal.books.client.dto.question.QuestionCategoryApiDTO;
import com.dbj.classpal.books.client.dto.question.QuestionCategoryRefApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface QuestionCategoryApi {

    /**
     * 获取分类详情
     */
    @PostMapping("/category/detail")
    RestResponse<QuestionCategoryApiDTO> getCategory(@RequestBody QuestionCategoryIdApiBO apiBO);

    /**
     * 获取分类列表
     */
    @PostMapping("/category/list")
    RestResponse<List<QuestionCategoryApiDTO>> getCategoryList(@RequestBody @Valid  QuestionCategoryIdApiBO apiBO);


    /**
     * 获取分类引用
     */
    @PostMapping("/category/reference")
    RestResponse<List<QuestionCategoryRefApiDTO>> getBusinessRefs(@RequestBody @Valid QuestionCategoryIdQueryApiBO apiBO);


//    /**
//     * 分页查询分类
//     */
//    @PostMapping("/category/page")
//    RestResponse<PageResponse<CategoryDTO>> getCategoryPage(@RequestBody @Valid QueryCategoryApiBO apiBO) throws BusinessException;

    /**
     * 创建分类
     */
    @PostMapping("/category/create")
    RestResponse<Integer> createCategory(@RequestBody @Valid  QuestionCategoryApiBO apiBO) throws BusinessException;

    /**
     * 更新分类
     */
    @PostMapping("/category/edit")
    RestResponse<Void> updateCategory(@RequestBody @Valid  QuestionCategoryApiBO apiBO) throws BusinessException;

    /**
     * 删除分类
     */
    @PostMapping("/category/batch/delete")
    RestResponse<Void> batchDeleteCategory(@RequestBody @Valid  QuestionCategoryIdsApiBO apiBO) throws BusinessException;

    /**
     * 更新分类排序
     */
    @PostMapping("/category/sort")
    RestResponse<Void> updateSort(@RequestBody @Valid QuestionCategorySortApiBO apiBO) throws BusinessException;

    /**
     * 检查分类名称是否重复
     */
    @PostMapping("/category/check-name")
    RestResponse<Boolean> checkNameExists(@RequestBody @Valid  QuestionCategoryApiBO apiBO);
} 