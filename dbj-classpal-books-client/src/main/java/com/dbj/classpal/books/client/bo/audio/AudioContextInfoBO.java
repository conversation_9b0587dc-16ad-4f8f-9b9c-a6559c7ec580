package com.dbj.classpal.books.client.bo.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 预置提示音表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AudioContextInfoBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "音频简介id")
    @NotNull(message = "音频简介id不能为空")
    private Integer audioIntroId;

    @Schema(description = "素材库id")
//    @NotNull(message = "素材库id不能为空")
    private Integer appMaterialId;

    @Schema(description = "全局配置")
    private List<AudioGlobalConfigAddBO> globalConfigList;

    @Schema(description = "音频文本详情")
    @NotNull(message = "音频文本详情不能为空")
    private List<AudioContextInfoAddBO> contextList;


}
