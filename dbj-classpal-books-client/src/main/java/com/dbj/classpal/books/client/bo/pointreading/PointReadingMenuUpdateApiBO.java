package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Data;

/**
 * 点读书目录更新API BO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingMenuUpdateApiBO", description = "点读书目录更新API BO")
public class PointReadingMenuUpdateApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "目录ID")
    @NotNull(message = "目录ID不能为空")
    private Integer id;

    @Schema(description = "所属点读书ID")
    @NotNull(message = "所属点读书ID不能为空")
    private Integer bookId;

    @Schema(description = "目录名称")
    @NotBlank(message = "目录名称不能为空")
    private String name;

    @Schema(description = "父级目录ID")
    private Integer parentId;

    @Schema(description = "目录层级")
    private Integer level;

    @Schema(description = "排序")
    private Integer sortNum;

}
