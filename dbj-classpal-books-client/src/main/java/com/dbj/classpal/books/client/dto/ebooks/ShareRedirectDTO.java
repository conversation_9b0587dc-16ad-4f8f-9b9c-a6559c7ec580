package com.dbj.classpal.books.client.dto.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 分享跳转DTO
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Data
@Schema(description = "分享跳转DTO")
public class ShareRedirectDTO {
    
    @Schema(description = "跳转URL")
    private String redirectUrl;
    
    @Schema(description = "业务类型")
    private String businessType;
    
    @Schema(description = "业务ID")
    private Integer businessId;
    
    @Schema(description = "是否有效")
    private Boolean valid;
    
    @Schema(description = "错误信息")
    private String errorMessage;
}
