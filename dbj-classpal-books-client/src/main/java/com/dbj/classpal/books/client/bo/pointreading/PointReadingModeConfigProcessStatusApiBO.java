package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 点读书模式配置处理状态API BO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingModeConfigProcessStatusApiBO", description = "点读书模式配置处理状态API BO")
public class PointReadingModeConfigProcessStatusApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "配置ID")
    @NotNull(message = "配置ID不能为空")
    private Integer id;

    @Schema(description = "处理状态：10-处理中 20-处理完成 30-处理失败")
    @NotNull(message = "处理状态不能为空")
    private Integer processStatus;
}
