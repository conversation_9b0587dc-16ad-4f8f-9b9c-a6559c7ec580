package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 单书查询业务对象
 */
@Data
@Schema(description = "单书查询参数BO")
public class AppEBookH5QueryApiBO implements Serializable {

    @Schema(description = "书架ID")
    private Integer shelfId;

    @Schema(description = "书城ID")
    private Integer storeId;

    @Schema(description = "样书标题")
    private String bookTitle;

    @Schema(description = "学科ID")
    private List<Integer> subjectIds;

    @Schema(description = "阶段ID")
    private List<Integer> stageIds;

    @Schema(description = "分类ID集合")
    private List<Integer> categoryIds;

    @Schema(description = "教材版本ID")
    private List<Integer> textbookVersionIds;

    @Schema(description = "适用年级")
    private List<Integer>  applicableGrades;

} 