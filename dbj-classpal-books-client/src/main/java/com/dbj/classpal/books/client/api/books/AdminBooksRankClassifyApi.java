package com.dbj.classpal.books.client.api.books;

import com.dbj.classpal.books.client.bo.books.BooksRankClassifyBO;
import com.dbj.classpal.books.client.bo.books.BooksRankClassifySaveBatchBO;
import com.dbj.classpal.books.client.dto.books.BooksRankClassifyDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AdminBooksRankClassifyApi {

    /**
     * <AUTHOR>
     * @Description  查询册数里的内容
     * @Date 2025/4/16 14:44 
     * @param 
     * @return 
     **/
    @PostMapping("/books/rank/classify/list")
    RestResponse<List<BooksRankClassifyDTO>> list(@RequestBody BooksRankClassifyBO boardBooksRankClassifyBO) throws BusinessException;

    @PostMapping("/books/rank/classify/save")
    RestResponse<Boolean> save(@RequestBody BooksRankClassifySaveBatchBO bo) throws BusinessException;

}
