package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 书城ID业务参数对象
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
@Data
@Schema(description = "书城ID参数BO")
public class AppEBookstoreIdApiBO implements Serializable {

    @Schema(description = "书城ID")
    @NotNull(message = "书城ID不能为空")
    private Integer id;
} 