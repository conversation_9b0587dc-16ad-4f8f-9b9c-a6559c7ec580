package com.dbj.classpal.books.client.api.books.app;

import com.dbj.classpal.books.client.bo.books.app.BooksRankInCodesContentsTreeAppBO;
import com.dbj.classpal.books.client.dto.books.app.BooksRankInCodesContentsTreeAppDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AppBooksRankInCodeContentsApi {

    @PostMapping("/app/books/rank/in/code/contents/tree")
    RestResponse<List<BooksRankInCodesContentsTreeAppDTO>> list(@RequestBody BooksRankInCodesContentsTreeAppBO bo) throws BusinessException;
}
