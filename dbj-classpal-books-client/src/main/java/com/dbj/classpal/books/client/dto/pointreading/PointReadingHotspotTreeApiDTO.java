package com.dbj.classpal.books.client.dto.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * 点读书热点区域树形API DTO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingHotspotTreeApiDTO", description = "点读书热点区域树形API DTO")
public class PointReadingHotspotTreeApiDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "热点ID")
    private Integer id;

    @Schema(description = "所属章节ID")
    private Integer chapterId;

    @Schema(description = "章节名称")
    private String chapterName;

    @Schema(description = "热点名称")
    private String name;

    @Schema(description = "前端节点ID（前端定义唯一性）")
    private String nodeId;

    @Schema(description = "父节点ID（支持层级结构）")
    private String parentNodeId;

    @Schema(description = "节点名称")
    private String nodeName;

    @Schema(description = "节点层级（1-根节点，2-二级节点...）")
    private Integer nodeLevel;

    @Schema(description = "区域类型：10-热点区域 20-标记点")
    private Integer areaType;

    @Schema(description = "区域类型描述")
    private String areaTypeDesc;

    @Schema(description = "事件类型：10-点读 20-答题 30-跟读")
    private Integer eventType;

    @Schema(description = "事件类型描述")
    private String eventTypeDesc;

    @Schema(description = "X坐标")
    private BigDecimal coordinateX;

    @Schema(description = "Y坐标")
    private BigDecimal coordinateY;

    @Schema(description = "宽度")
    private BigDecimal width;

    @Schema(description = "高度")
    private BigDecimal height;

    @Schema(description = "媒体文件ID")
    private String mediaId;

    @Schema(description = "媒体文件URL")
    private String mediaUrl;

    @Schema(description = "媒体文件名")
    private String mediaName;

    @Schema(description = "媒体类型：10-音频 20-视频")
    private Integer mediaType;

    @Schema(description = "媒体类型描述")
    private String mediaTypeDesc;

    @Schema(description = "媒体来源：10-TTS合成 20-素材中心")
    private Integer mediaSource;

    @Schema(description = "媒体来源描述")
    private String mediaSourceDesc;

    @Schema(description = "是否支持跟读：0-否 1-是")
    private Integer followRead;

    @Schema(description = "是否支持跟读描述")
    private String followReadDesc;

    @Schema(description = "跟读校验文本")
    private String verifyText;

    @Schema(description = "排序")
    private Integer sortNum;

    @Schema(description = "状态：0-停用 1-正常")
    private Integer status;

    @Schema(description = "状态描述")
    private String statusDesc;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "子节点列表")
    private List<PointReadingHotspotTreeApiDTO> children;

    @Schema(description = "媒体文件列表")
    private List<PointReadingHotspotMediaApiDTO> mediaList;
}
