package com.dbj.classpal.books.client.api.ebooks;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.ebooks.*;
import com.dbj.classpal.books.client.bo.ebooks.H5LandingPageApiBO;
import com.dbj.classpal.books.client.bo.share.GetShareInfoApiBO;
import com.dbj.classpal.books.client.bo.share.RemoveShareUrlApiBO;
import com.dbj.classpal.books.client.dto.ebooks.AppEBookApiDTO;
import com.dbj.classpal.books.client.dto.ebooks.AppEBookAsyncProcessApiDTO;
import com.dbj.classpal.books.client.dto.ebooks.AppEBookPageImageApiDTO;
import com.dbj.classpal.books.client.dto.ebooks.AppEBookResourceApiDTO;
import com.dbj.classpal.books.client.dto.ebooks.PdfTaskStatusApiDTO;
import com.dbj.classpal.books.client.dto.ebooks.H5LandingPageApiDTO;
import com.dbj.classpal.books.client.dto.ebooks.ShareUrlResultApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 单书API接口
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}", path = "/api/classpal/books/${spring.application.version}")
public interface AppEBookApi {

    /**
     * 分页查询单书列表
     *
     * @param pageRequest 分页查询参数
     * @return 单书分页数据
     */
    @PostMapping("/ebook/page")
    RestResponse<Page<AppEBookApiDTO>> page(@RequestBody PageInfo<AppEBookQueryApiBO> pageRequest) throws BusinessException;

    /**
     * H5端分页查询单书列表
     *
     * @param pageRequest 分页查询参数
     * @return 单书分页数据
     */
    @PostMapping("/ebook/h5/page")
    RestResponse<Page<AppEBookApiDTO>> pageForH5(@RequestBody PageInfo<AppEBookH5QueryApiBO> pageRequest) throws BusinessException;

    /**
     * 查询单书详情
     *
     * @param idBO 单书ID参数
     * @return 单书详情数据
     */
    @PostMapping("/ebook/detail")
    RestResponse<AppEBookApiDTO> detail(@RequestBody AppEBookIdApiBO idBO) throws BusinessException;

    /**
     * 新增单书
     *
     * @param saveBO 单书保存参数
     * @return 保存结果
     */
    @PostMapping("/ebook/save")
    RestResponse<Integer> save(@RequestBody @Validated AppEBookSaveApiBO saveBO) throws BusinessException;

    /**
     * 更新单书
     *
     * @param updateBO 单书更新参数
     * @return 更新结果
     */
    @PostMapping("/ebook/update")
    RestResponse<Boolean> update(@RequestBody @Validated AppEBookUpdateApiBO updateBO) throws BusinessException;

    /**
     * 删除单书
     *
     * @param idBO 单书ID参数
     * @return 删除结果
     */
    @PostMapping("/ebook/delete")
    RestResponse<Boolean> delete(@RequestBody @Validated AppEBookIdApiBO idBO) throws BusinessException;

    /**
     * 批量删除单书
     *
     * @param idsBO ID列表参数
     * @return 批量删除结果
     */
    @PostMapping("/ebook/delete/batch")
    RestResponse<Boolean> deleteBatch(@RequestBody @Validated AppEBookIdsApiBO idsBO) throws BusinessException;

    /**
     * 批量启用单书
     *
     * @param idsBO ID列表参数
     * @return 批量启用结果
     */
    @PostMapping("/ebook/enable/batch")
    RestResponse<Boolean> enableBatch(@RequestBody @Validated AppEBookIdsApiBO idsBO) throws BusinessException;

    /**
     * 批量禁用单书
     *
     * @param idsBO ID列表参数
     * @return 批量禁用结果
     */
    @PostMapping("/ebook/disable/batch")
    RestResponse<Boolean> disableBatch(@RequestBody @Validated AppEBookIdsApiBO idsBO) throws BusinessException;

    /**
     * 批量允许下载单书
     *
     * @param idsBO ID列表参数
     * @return 批量允许下载结果
     */
    @PostMapping("/ebook/allow-download/batch")
    RestResponse<Boolean> allowDownloadBatch(@RequestBody @Validated AppEBookIdsApiBO idsBO) throws BusinessException;

    /**
     * 批量关闭下载单书
     *
     * @param idsBO ID列表参数
     * @return 批量关闭下载结果
     */
    @PostMapping("/ebook/disable-download/batch")
    RestResponse<Boolean> disableDownloadBatch(@RequestBody @Validated AppEBookIdsApiBO idsBO) throws BusinessException;

    /**
     * 更换单书文件
     *
     * @param updateFileBO 更新文件参数
     * @return 更换结果
     */
    @PostMapping("/ebook/update-file")
    RestResponse<Boolean> updateFile(@RequestBody @Validated AppEBookUpdateFileApiBO updateFileBO) throws BusinessException;

    /**
     * 更换单书水印
     *
     * @param updateWatermarkBO 更新水印参数
     * @return 更换结果
     */
    @PostMapping("/ebook/update-watermark")
    RestResponse<Boolean> updateWatermark(@RequestBody @Validated AppEBookUpdateWatermarkApiBO updateWatermarkBO) throws BusinessException;

    /**
     * 获取封面
     *
     * @param fileApiBO
     * @return 获取封面
     */
    @PostMapping("/ebook/cover-url")
    RestResponse<String> coverUrl(@RequestBody @Validated AppEBookFileApiBO fileApiBO) throws BusinessException;

    /**
     * 异步处理PDF文件（获取封面和切图）
     *
     * @param request 异步处理请求参数
     * @return 任务ID和基本信息
     */
    @PostMapping("/ebook/async/cover-url")
    RestResponse<AppEBookAsyncProcessApiDTO> asyncCoverUrl(@RequestBody @Validated AppEBookAsyncProcessApiBO request) throws BusinessException;

    /**
     * 查询PDF处理任务状态
     *
     * @param taskApiBO 任务ID
     * @return 任务状态信息
     */
    @PostMapping("/ebook/async/task/get")
    RestResponse<PdfTaskStatusApiDTO> getTaskStatus(@RequestBody @Validated AppEBookAsyncProcessTaskApiBO taskApiBO) throws BusinessException;

    /**
     * 重新处理PDF文件（重新获取封面和切图）
     *
     * @param request 重新处理请求参数
     * @return 新的任务ID和基本信息
     */
    @PostMapping("/ebook/async/reprocess")
    RestResponse<AppEBookAsyncProcessApiDTO> reprocess(@RequestBody @Validated AppEBookReprocessApiBO request) throws BusinessException;

    /**
     * 批量查询PDF处理任务状态
     *
     * @param taskIds 任务ID列表
     * @return 任务状态列表
     */
    @PostMapping("/ebook/async/tasks/batch")
    RestResponse<List<PdfTaskStatusApiDTO>> batchGetTaskStatus(@RequestBody List<String> taskIds) throws BusinessException;


    /**
     * 分页查询单书图片资源
     *
     * @param pageRequest 查询参数
     * @return 图片资源分页数据
     */
    @PostMapping("/ebook/page-images")
    RestResponse<Page<AppEBookPageImageApiDTO>> getPageImages(@RequestBody @Validated PageInfo<AppEBookPageImagesQueryApiBO> pageRequest) throws BusinessException;


    /**
     * 短链跳转接口
     * 根据短链码获取长链接并重定向到H5页面
     *
     * @param shortCode 短链码
     * @return 重定向响应
     */
    @GetMapping("/share/redirect/{shortCode}")
    RestResponse<String> redirect(@PathVariable String shortCode) throws BusinessException;

    /**
     * 获取H5落地页数据（POST方式，用于H5页面API调用）
     *
     * @param pageRequest 分页请求参数
     * @return H5落地页数据
     */
    @PostMapping("/h5/landing")
    RestResponse<H5LandingPageApiDTO> getLandingPageData(@RequestBody @Valid PageInfo<H5LandingPageApiBO> pageRequest) throws BusinessException;

    /**
     * 获取分享信息（包括分享链接和二维码）
     * 如果不存在则自动生成，如果已存在则直接返回
     *
     * @param request 获取分享信息请求参数
     * @return 分享链接结果
     */
    @Operation(summary = "获取分享信息", description = "获取分享链接和二维码，不存在时自动生成")
    @PostMapping("/share/info")
    RestResponse<ShareUrlResultApiDTO> getShareInfo(@RequestBody @Validated GetShareInfoApiBO request) throws BusinessException;

    /**
     * 删除分享链接
     *
     * @param request 删除分享链接请求参数
     * @return 操作结果
     */
    @Operation(summary = "删除分享链接", description = "删除已生成的分享链接和二维码")
    @PostMapping("/ebook/share/remove")
    RestResponse<Boolean> removeShareUrl(@RequestBody @Validated RemoveShareUrlApiBO request) throws BusinessException;

    /**
     * 批量查询书籍资源信息
     *
     * @param bookIds 书籍ID列表
     * @return 按书籍ID分组的资源列表
     */
    @Operation(summary = "批量查询书籍资源", description = "根据书籍ID列表批量查询资源信息")
    @PostMapping("/ebook/resources/batch")
    RestResponse<Map<Integer, List<AppEBookResourceApiDTO>>> batchQueryResources(@RequestBody List<Integer> bookIds) throws BusinessException;
}
