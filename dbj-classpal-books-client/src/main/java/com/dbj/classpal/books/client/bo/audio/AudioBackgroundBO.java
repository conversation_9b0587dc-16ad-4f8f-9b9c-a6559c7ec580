package com.dbj.classpal.books.client.bo.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 预置提示音表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AudioBackgroundBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "类型：1 预置背景音 2 自定义")
    @NotNull(message = "类型不能为空")
    private Integer type;

    @Schema(description = "资源名称")
    @NotEmpty(message = "资源名称不能为空")
    private String materialName;

    @Schema(description = "资源路径")
    @NotEmpty(message = "资源路径不能为空")
    private String materialPath;

    @Schema(description = "资源大小")
    @NotNull(message = "资源大小不能为空")
    private Double materialSize;

    @Schema(description = "资源时长(s)")
    @NotNull(message = "资源时长不能为空")
    private Integer materialDuration;



}
