package com.dbj.classpal.books.client.api.audio;

import com.dbj.classpal.books.client.bo.audio.AudioReorderBO;
import com.dbj.classpal.books.client.bo.audio.AudioTypeBO;
import com.dbj.classpal.books.client.bo.audio.AudioContextHintAddBO;
import com.dbj.classpal.books.client.dto.audio.AudioContextHintDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 音频文本提示音
 * <AUTHOR>
 * @since 2025-06-25
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}", path = "/api/classpal/books/${spring.application.version}")
public interface AudioContextHintMusicApi {
    /**
     * 保存自定义提示音
     * @param bo
     * @return
     * @throws BusinessException
     */
    @PostMapping("/contextHint/save")
    RestResponse<Integer> save(@RequestBody List<AudioContextHintAddBO> bo) throws BusinessException;

    /**
     * 重排序
     * @param bo
     * @return
     * @throws BusinessException
     */
    @PostMapping("/contextHint/reorder")
    RestResponse<Integer> reorder(@RequestBody List<Integer> bo) throws BusinessException;
    /**
     * 查询背景音列表
     * @param bo
     * @return
     * @throws BusinessException
     */
    @PostMapping("/contextHint/getDefinitionHint")
    RestResponse<List<AudioContextHintDTO>> getDefinitionHint(@RequestBody AudioTypeBO bo) throws BusinessException;

}
