package com.dbj.classpal.books.client.bo.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 背景音
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AudioBackgroundInfoBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "音量")
    @NotNull(message = "音量不能为空")
    private Integer volume;

    @Schema(description = "模式：1 循环播放 2 播放一次")
    @NotNull(message = "模式不能为空")
    private Integer model;

    @Schema(description = "背景音列表")
    @NotEmpty(message = "背景音列表不能为空")
    private List<AudioBackgroundBO> bgmList;

}
