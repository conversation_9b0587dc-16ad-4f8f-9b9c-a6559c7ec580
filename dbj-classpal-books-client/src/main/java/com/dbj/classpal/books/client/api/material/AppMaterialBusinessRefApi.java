package com.dbj.classpal.books.client.api.material;

import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.material.*;
import com.dbj.classpal.books.client.dto.books.BooksRefDirectApiDTO;
import com.dbj.classpal.books.client.dto.material.*;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialBusinessRefApi
 * Date:     2025-04-18 15:06:39
 * Description: 表名： ,描述： 表
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AppMaterialBusinessRefApi {

    /**
     * 查询被引用素材列表
     * @return
     */
    @PostMapping("/materialBusinessRef/beRefBusinessList")
    RestResponse<List<AppMaterialBusinessRefMaterialQueryApiDTO>> beRefBusinessList(@RequestBody AppMaterialBusinessRefQueryCommonApiBO bo) throws BusinessException;


    /**
     * 查询引用列表
     * @return
     */
    @PostMapping("/materialBusinessRef/refBusinessList")
    RestResponse<List<AppMaterialBusinessRefMaterialQueryApiDTO>> refBusinessList(@RequestBody AppMaterialBusinessRefQueryCommonApiBO bo) throws BusinessException;


    /**
     * 查询引用列表下各素材类型数量统计
     * @return
     */
    @PostMapping("/materialBusinessRef/getMaterialBusinessRefTypeCount")
    RestResponse<List<AppMaterialBusinessRefTypeCountApiDTO>> getMaterialBusinessRefTypeCount(@RequestBody CommonIdApiBO bo) throws BusinessException;

    /**
     * 保存资源关联关系
     * @param bo
     * @return
     */
    @PostMapping("/materialBusinessRef/saveAppMaterialBusinessRef")
    RestResponse<Boolean> saveAppMaterialBusinessRef(@RequestBody @Valid AppMaterialBusinessRefSaveApiBO bo) throws BusinessException;

    /**
     * 资源关联关系重命名
     * @param bo
     * @return
     */
    @PostMapping("/materialBusinessRef/reNameAppMaterialBusinessRef")
    RestResponse<Boolean> reNameAppMaterialBusinessRef(@RequestBody @Valid AppMaterialBusinessRefReNameApiBO bo) throws BusinessException;

    /**
     * 移除关联关系
     * @param bo
     * @return
     */
    @PostMapping("/materialBusinessRef/removeAppMaterialBusinessRef")
    RestResponse<Boolean> removeAppMaterialBusinessRef(@RequestBody @Valid CommonIdApiBO bo) throws BusinessException;


    /**
     * 批量移除关联关系
     * @param bo
     * @return
     */
    @PostMapping("/materialBusinessRef/removeBatchAppMaterialBusinessRef")
    RestResponse<Boolean> removeBatchAppMaterialBusinessRef(@RequestBody @Valid CommonIdsApiBO bo) throws BusinessException;

    /**
     * 关联关系交换排序
     * @param bo
     * @return
     */
    @PostMapping("/materialBusinessRef/changeAppMaterialBusinessRefOrderNum")
    RestResponse<Boolean> changeAppMaterialBusinessRefOrderNum(@RequestBody @Valid CommonIdsApiBO bo) throws BusinessException;



    /**
     * 素材关联内容管理-专辑引用跳转页面所需参数
     */
    @PostMapping("/materialBusinessRef/getAppMaterialBusinessRefAlbum")
    RestResponse<AppMaterialBusinessRefAlbumDirectApiDTO> getAppMaterialBusinessRefAlbum(@RequestBody @Valid CommonIdApiBO bo) throws BusinessException;


    /**
     * 素材关联图书管理-图书资源引用跳转页面所需参数
     */
    @PostMapping("/materialBusinessRef/getAppMaterialBusinessRefBooks")
    RestResponse<BooksRefDirectApiDTO> getAppMaterialBusinessRefBooks(@RequestBody @Valid CommonIdApiBO bo) throws BusinessException;


    /**
     * 素材关联图书管理-题库引用跳转页面所需参数
     */
    @PostMapping("/materialBusinessRef/getAppMaterialBusinessRefQuestion")
    RestResponse<AppMaterialBusinessRefQuestionDirectApiDTO> getAppMaterialBusinessRefQuestion(@RequestBody @Valid CommonIdApiBO bo) throws BusinessException;

}
