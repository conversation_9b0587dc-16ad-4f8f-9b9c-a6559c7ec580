package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * 单书更新水印业务对象
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "单书更新水印业务对象")
public class AppEBookUpdateWatermarkApiBO implements Serializable {

    @Schema(description = "单书ID")
    @NotNull(message = "单书ID不能为空")
    private Integer id;

    @Schema(description = "水印模板ID")
    @NotNull(message = "水印模板ID不能为空")
    private Integer watermarkId;
} 