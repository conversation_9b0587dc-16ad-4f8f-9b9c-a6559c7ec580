package com.dbj.classpal.books.client.dto.pinyin;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 汉语拼音AppDTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Data
@ToString
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Tag(name="汉语拼音AppDTO", description="汉语拼音AppDTO")
public class PinyinAppDTO implements Serializable {


    @Schema(description = "ID")
    private Integer id;

    @Schema(description = "拼音")
    private String pinyin;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "排序权重")
    private Integer sort;

    @Schema(description = "拼音")
    private List<PinyinDTO> pinyins;
}
