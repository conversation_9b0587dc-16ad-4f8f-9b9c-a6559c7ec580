package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 书架单书管理业务参数对象
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
@Data
@Schema(description = "书架单书管理参数BO")
public class AppEBookshelfBooksApiBO implements Serializable {

    @Schema(description = "书架ID")
    @NotNull(message = "书架ID不能为空")
    private Integer shelfId;
    
    @Schema(description = "单书ID列表")
    @NotNull(message = "单书ID列表不能为空")
    private List<Integer> bookIds;
    
    @Schema(description = "单书排序映射，key为单书ID，value为排序序号",hidden = true)
    private Map<Integer, Integer> bookSortMap;
} 