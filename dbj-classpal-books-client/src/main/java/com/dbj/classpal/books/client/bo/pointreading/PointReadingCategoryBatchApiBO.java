package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 点读书分类批量操作API BO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Schema(name = "PointReadingCategoryBatchApiBO", description = "点读书分类批量操作API BO")
public class PointReadingCategoryBatchApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "分类ID列表")
    @NotEmpty(message = "分类ID列表不能为空")
    private List<Integer> ids;
}
