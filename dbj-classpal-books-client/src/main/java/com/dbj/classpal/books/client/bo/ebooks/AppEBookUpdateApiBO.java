package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 单书保存业务对象
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Data
@Schema(description = "单书编辑业务对象ApiBO")
public class AppEBookUpdateApiBO  implements Serializable {

    @Schema(description = "单书ID）")
    @NotNull(message = "单书ID不能为空")
    private Integer id;

    @Schema(description = "样书标题")
    @NotEmpty(message = "样书标题不能为空")
    private String bookTitle;

    @Schema(description = "简介")
    private String blurb;

    @Schema(description = "分类ID集合")
    private List<Integer> categoryIds;

    @Schema(description = "适用年级")
    private List<Integer> applicableGrades;

    @Schema(description = "学科ID")
    private Integer subjectId;

    @Schema(description = "阶段ID")
    private Integer stageId;

    @Schema(description = "教材版本ID")
    private Integer textbookVersionId;

    @Schema(description = "文件Id")
    private Integer fileId;

    @Schema(description = "文件URL")
    private String fileUrl;

    @Schema(description = "文件MD5")
    private String fileMd5;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "文件大小")
    @Max(value = 100 * 1024, message = "文件大小不能大于100mb")
    private BigDecimal fileSize;

    @Schema(description = "封面URLId")
    private Integer coverUrlId;

    @Schema(description = "封面URL")
    @NotEmpty(message = "封面不能为空")
    private String coverUrl;

    @Schema(description = "封面名称")
    @NotEmpty(message = "封面名称为空")
    private String coverUrlName;

    @Schema(description = "水印模板ID")
    private Integer watermarkId;

    @Schema(description = "启用状态：0-禁用，1-启用")
    @NotNull(message = "启用状态不能为空")
    private Integer launchStatus;

    @Schema(description = "允许下载：0-否，1-是")
    @NotNull(message = "允许下载不能为空")
    private Integer allowDownload;

    @Schema(description = "排序序号")
    private Integer sortNum;

} 