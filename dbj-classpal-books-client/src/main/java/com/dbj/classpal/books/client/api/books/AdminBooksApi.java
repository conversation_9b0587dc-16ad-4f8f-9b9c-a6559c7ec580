package com.dbj.classpal.books.client.api.books;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.books.*;
import com.dbj.classpal.books.client.dto.books.BooksInfoDetailApiDTO;
import com.dbj.classpal.books.client.dto.books.BooksInfoPageApiDTO;
import com.dbj.classpal.books.client.dto.books.ProductInfoApiDTO;
import com.dbj.classpal.books.client.dto.books.ProductInfoDetailApiDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AdminBooksApi {

    @PostMapping("/books/info/pageInfo")
    RestResponse<Page<BooksInfoPageApiDTO>> pageInfo(@RequestBody PageInfo<BooksInfoPageBO> pageRequest) throws BusinessException;
    @PostMapping("/books/info/save")
    RestResponse<Boolean> save(@RequestBody BooksInfoSaveApiBO saveBO) throws BusinessException;
    @PostMapping("/books/info/update")
    RestResponse<Boolean> update(@RequestBody BooksInfoUpdApiBO updBO) throws BusinessException;
    @PostMapping("/books/info/delete")
    RestResponse<Boolean> delete(@RequestParam Integer id) throws BusinessException;
    @PostMapping("/books/info/batchDelete")
    RestResponse<Boolean> batchDelete(@RequestBody List<Integer> ids) throws BusinessException;
    @GetMapping("/books/info/detail")
    RestResponse<BooksInfoDetailApiDTO> detail(@RequestParam Integer id) throws BusinessException;
    @PostMapping("/books/info/batchHide")
    RestResponse<Boolean> batchHide(@RequestBody BooksBatchHideApiBO batchHideBO) throws BusinessException;
    @PostMapping("/books/info/batchLaunch")
    RestResponse<Boolean> batchLaunch(@RequestBody BooksBatchLaunchApiBO batchLaunchBO) throws BusinessException;
    /**
     * 根据产品名称或者编码获取列表
     * @param request
     * @return
     */
    @PostMapping("/books/printer/info")
    RestResponse<List<ProductInfoApiDTO>> queryProductListByCodeOrName(@RequestBody ProductInfoApiBO request) throws BusinessException;

    /**
     * 根据产品ID获取详情
     * @param request
     * @return
     */
    @PostMapping("/books/printer/detail")
    RestResponse<ProductInfoDetailApiDTO> queryProductInfoById(@RequestBody ProductInfoIdApiBO request) throws BusinessException;
    @PostMapping("/books/info/list")
    RestResponse<List<BooksInfoPageApiDTO>> list(@RequestBody BooksInfoPageBO bo) throws BusinessException;
}
