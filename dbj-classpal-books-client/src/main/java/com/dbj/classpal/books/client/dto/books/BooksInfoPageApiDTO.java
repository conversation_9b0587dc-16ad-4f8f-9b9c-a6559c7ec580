package com.dbj.classpal.books.client.dto.books;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 图书表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="图书保存对象", description="图书表")
public class BooksInfoPageApiDTO implements Serializable {

    @Schema(description = "主键id")
    private Integer id;
    @Schema(description = "产品名称")
    private String bookName;
    @Schema(description = "封面url")
    private String picUrl;
    @Schema(description = "简介")
    private String blurb;
    @Schema(description = "分类id")
    private Integer categoryId;
    @Schema(description = "分类id集合")
    @NotEmpty(message = "分类id不能为空")
    private List<Integer> categoryIds;
    @Schema(description = "分类名称集合")
    private List<String> categoryTreeNames;
    @Schema(description = "卷册数")
    private Integer volumeNum;
    @Schema(description = "是否隐藏")
    private Integer isHide;
    @Schema(description = "添加数")
    private Long addNum;
    @Schema(description = "扫码数")
    private Long scanCodeNum;
    @Schema(description = "上下架状态  上架 下架")
    private Integer launchStatus;
    @Schema(description = "启用状态")
    private Boolean status;

}
