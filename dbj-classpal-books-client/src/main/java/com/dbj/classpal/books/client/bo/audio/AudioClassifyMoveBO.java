package com.dbj.classpal.books.client.bo.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NonNull;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/6/27 17:26
 */
@Data
public class AudioClassifyMoveBO {

    @NotEmpty(message = "移动的分类不能为空")
    @Schema(description = "需要移动的分类id", requiredMode = RequiredMode.REQUIRED)
    private Set<Integer> ids;

    @NotNull(message = "父级id 不能为空")
    @Schema(description = "父级分类", requiredMode = RequiredMode.REQUIRED)
    private Integer parentId;
}
