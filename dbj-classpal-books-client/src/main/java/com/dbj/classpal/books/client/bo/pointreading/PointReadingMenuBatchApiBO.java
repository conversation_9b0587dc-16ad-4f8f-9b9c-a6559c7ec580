package com.dbj.classpal.books.client.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 点读书目录批量操作API BO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingMenuBatchApiBO", description = "点读书目录批量操作API BO")
public class PointReadingMenuBatchApiBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "目录ID列表")
    @NotEmpty(message = "目录ID列表不能为空")
    private List<Integer> ids;
}
