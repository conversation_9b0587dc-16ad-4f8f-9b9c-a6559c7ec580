package com.dbj.classpal.books.client.dto.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 音频文本详情
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AudioContextInfoListDTO {

    @Schema(description = "音频简介id")
    private Integer audioIntroId;

    @Schema(description = "素材库id")
    private Integer appMaterialId;

    @Schema(description = "全局配置")
    private List<AudioGlobalConfigDTO> globalConfigList;

    @Schema(description = "音频文本详情")
    private List<AudioContextInfoDTO> contextList;

}
