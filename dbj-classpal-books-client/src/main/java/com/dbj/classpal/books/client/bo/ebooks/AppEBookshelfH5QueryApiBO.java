package com.dbj.classpal.books.client.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * H5书架查询API参数BO
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@Schema(description = "H5书架查询参数")
public class AppEBookshelfH5QueryApiBO implements Serializable {

    @Schema(description = "书架名称")
    private String shelfName;

    @Schema(description = "书城ID - 用于通过书城ID跳转过来的操作场景")
    private Integer storeId;
}
