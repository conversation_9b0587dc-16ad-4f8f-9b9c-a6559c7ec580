package com.dbj.classpal.books.client.bo.pinyin;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 拼音分类UpsertBO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Tag(name="拼音分类UpsertBO", description="拼音分类UpsertBO")
public class PinyinClassifyUpsertBO implements Serializable {


    @Schema(description = "ID")
    private Integer id;

    @NotBlank
    @Size(max = 32)
    @Schema(description = "拼音")
    private String pinyin;

    @NotBlank
    @Size(max = 16)
    @Schema(description = "标题")
    private String title;
}
