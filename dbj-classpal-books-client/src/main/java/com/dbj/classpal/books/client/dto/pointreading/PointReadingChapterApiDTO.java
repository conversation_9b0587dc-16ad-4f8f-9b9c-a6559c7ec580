package com.dbj.classpal.books.client.dto.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 点读书章节API DTO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingPageApiDTO", description = "点读书章节API DTO")
public class PointReadingChapterApiDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "章节ID")
    private Integer id;

    @Schema(description = "所属目录ID")
    private Integer menuId;

    @Schema(description = "目录名称")
    private String menuName;

    @Schema(description = "点读书名称")
    private String bookName;

    @Schema(description = "章节名称")
    private String name;

    @Schema(description = "章节图片URL")
    private String imageUrl;

    @Schema(description = "章节图片名称")
    private String imageName;

    @Schema(description = "章节音频URL")
    private String audioUrl;

    @Schema(description = "章节音频名称")
    private String audioName;

    @Schema(description = "章节内容")
    private String content;

    @Schema(description = "排序")
    private Integer sortNum;

    @Schema(description = "状态：0-停用 1-正常")
    private Integer status;

    @Schema(description = "状态描述")
    private String statusDesc;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
