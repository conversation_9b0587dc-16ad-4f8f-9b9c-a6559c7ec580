package com.dbj.classpal.books.client.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

/**
 * 题库业务关联BO
 */
@Data
@Schema(description = "题库业务关联DTO")
public class QuestionCategoryBusinessRefApiDTO implements Serializable {

    /**
     * 题目分类ID
     */
    @Schema(description = "题目分类ID")
    private Integer questionCategoryId;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sortNum;
} 