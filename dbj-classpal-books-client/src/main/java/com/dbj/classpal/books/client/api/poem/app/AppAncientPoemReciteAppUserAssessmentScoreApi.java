package com.dbj.classpal.books.client.api.poem.app;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.poem.app.AncientPoemReciteAppUserAssessmentScorePageBO;
import com.dbj.classpal.books.client.bo.poem.app.AncientPoemReciteAppUserAssessmentScoreSaveBO;
import com.dbj.classpal.books.client.dto.poem.app.AncientPoemReciteAppUserAssessmentScoreDetailDTO;
import com.dbj.classpal.books.client.dto.poem.app.AncientPoemReciteAppUserAssessmentScorePageDTO;
import com.dbj.classpal.books.client.dto.poem.app.AncientPoemReciteAppUserCountDTO;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className AncientPoemReciteCategoryApi
 * @description
 * @date 2025-05-26 09:09
 **/
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AppAncientPoemReciteAppUserAssessmentScoreApi {

    /**
     * 古诗背诵记录分页查询
     */
    @PostMapping("/app/ancientPoemReciteAppUserAssessmentScore/pageAncientPoemReciteAppUserAssessmentScore")
    RestResponse<Page<AncientPoemReciteAppUserAssessmentScorePageDTO>> pageAncientPoemReciteAppUserAssessmentScore(@RequestBody PageInfo<AncientPoemReciteAppUserAssessmentScorePageBO> pageInfo);
    /**
     * 古诗背诵记录新增
     */
    @PostMapping("/app/ancientPoemReciteAppUserAssessmentScore/save")
    RestResponse<Boolean> saveAncientPoemReciteAppUserAssessmentScore(@RequestBody AncientPoemReciteAppUserAssessmentScoreSaveBO ancientPoemReciteAppUserAssessmentScoreSaveBO);

    /**
     * 明细数据
     */
    @GetMapping("/app/ancientPoemReciteAppUserAssessmentScore/detail")
    RestResponse<AncientPoemReciteAppUserAssessmentScoreDetailDTO> detail(@RequestParam Integer poemId);

    /**
     * 查询用户背诵古诗总数
     *
     */
    @GetMapping("/app/ancientPoemReciteAppUserAssessmentScore/count")
    RestResponse<AncientPoemReciteAppUserCountDTO> count();


}
