package com.dbj.classpal.books.client.bo.books;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/31 16:40
 */
@Data
public class BooksRankClassifySaveBatchBO {

    @NotNull
    @Schema(description = "册书id", requiredMode = RequiredMode.REQUIRED)
    private Integer rankId;

    @NotEmpty
    @Schema(description = "册书页签", requiredMode = RequiredMode.REQUIRED)
    private List<BooksRankClassifySaveBO> list;
}
