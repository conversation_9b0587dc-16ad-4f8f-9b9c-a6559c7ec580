package com.dbj.classpal.books.client.api.poem.app;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.poem.AncientPoemBusinessRefListBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemBusinessRefSaveBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemBusinessRefSortBO;
import com.dbj.classpal.books.client.bo.poem.app.AncientPoemBusinessRefPageBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemBusinessRefListDTO;
import com.dbj.classpal.books.client.dto.poem.app.AncientPoemBusinessRefPageDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className IAncientPoemBusinessRefApi
 * @description
 * @date 2025-05-27 09:29
 **/
@FeignClient(value = "dbj-classpal-books-api-${spring.application.version}",path = "/api/classpal/books/${spring.application.version}")
public interface AppAncientPoemBusinessRefApi {

    @PostMapping("/app/ancientPoemBusinessRef/pageAncientPoemBusinessRef")
    RestResponse<Page<AncientPoemBusinessRefPageDTO>> pageAncientPoemBusinessRef(@RequestBody PageInfo<AncientPoemBusinessRefPageBO> pageInfo);
}

