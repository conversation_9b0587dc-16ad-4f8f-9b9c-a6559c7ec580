package com.dbj.classpal.books.client.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "学科")
@Data
public class WrongQuestionSubjectApiDTO implements Serializable {


    /**
     * 学科ID
     */
    @Schema(description = "学科ID")
    private Integer subjectId;

    /**
     * 学科名称
     */
    @Schema(description = "学科名称")
    private String subjectName;

}