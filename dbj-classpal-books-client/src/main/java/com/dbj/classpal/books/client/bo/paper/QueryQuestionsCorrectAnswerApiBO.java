package com.dbj.classpal.books.client.bo.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "查询题目正确答案请求BO")
public class QueryQuestionsCorrectAnswerApiBO implements Serializable {
    @Schema(description = "业务类型 1内容管理-音频专辑 2内容管理-视频专辑 3图书管理-图书资源 4图书管理-题库 5图书管理-音频专辑 6图书管理-视频专辑", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;

    @Schema(description = "业务类型ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "业务类型ID不能为空")
    private Integer businessId;

    @Schema(description = "用户ID", hidden = true)
    private Integer appUserId;
    @Schema(description = "题目ID,可选")
    private List<Integer> questionIds;
} 