package com.dbj.classpal.books.client.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Schema
@Data
public class QuestionCategoryIdQueryApiBO implements Serializable {


    /**
     * 分类ID
     */
    @Schema(description = "分类ID")
    private Integer categoryId;
} 