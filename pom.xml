<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.dbj</groupId>
        <artifactId>dbj-classpal-framework</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>dbj-classpal-books-bus</artifactId>
    <packaging>pom</packaging>
    <version>${revision}</version>
    <name>dbj-classpal-books-bus</name>
    <description>dbj-classpal-books-bus</description>
    <modules>
        <module>dbj-classpal-books-api</module>
        <module>dbj-classpal-books-common</module>
        <module>dbj-classpal-books-service</module>
        <module>dbj-classpal-books-client</module>
    </modules>
    <properties>
    </properties>
    <dependencies>

    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.dbj</groupId>
                <artifactId>dbj-classpal-commons-starter</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.dbj</groupId>
                <artifactId>dbj-classpal-mybatis-plus-starter</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.dbj</groupId>
                <artifactId>dbj-classpal-pdf-watermark-starter</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.dbj</groupId>
                <artifactId>dbj-classpal-books-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.dbj</groupId>
                <artifactId>dbj-classpal-books-service</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.dbj</groupId>
                <artifactId>dbj-classpal-books-client</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.dbj</groupId>
                <artifactId>dbj-classpal-books-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>2.3.1</version>
            </dependency>
            <dependency>
                <groupId>javax.activation</groupId>
                <artifactId>activation</artifactId>
                <version>1.1.1</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-runtime</artifactId>
                <version>2.3.9</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.1.0</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


    <distributionManagement>
        <repository>
            <!--id的名字可以任意取，但是在setting文件中的属性<server>的ID与这里一致 -->
            <id>releases</id>
            <!--指向仓库类型为host(宿主仓库）的储存类型为Release的仓库 -->
            <url>http://************:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <!--指向仓库类型为host(宿主仓库）的储存类型为Snapshot的仓库 -->
            <url>http://************:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>