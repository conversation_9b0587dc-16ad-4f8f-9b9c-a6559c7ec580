<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.books.BooksInfoMapper">


    <select id="pageInfo" resultType="com.dbj.classpal.books.client.dto.books.BooksInfoPageApiDTO">
        SELECT
            bi.id,
            bi.book_name,
            bi.pic_url,
            bi.blurb,
            bi.category_id,
            bi.volume_num,
            bi.is_hide,
            bi.launch_status,
            bi.create_time,
            bi.update_time
        FROM
            books_info bi
        WHERE
            bi.is_deleted = 0
        <if test="bo.bookName != null and bo.bookName != ''">
            AND bi.book_name LIKE CONCAT('%', #{bo.bookName}, '%')
        </if>
        <if test="bo.categoryIds != null and bo.categoryIds.size() > 0">
            and EXISTS (
                SELECT 1
                    FROM books_category_ref s
                WHERE
                    is_deleted = 0
                    and s.books_id = bi.id
                    AND s.category_id IN
                    <foreach collection="bo.categoryIds" item="categoryId" open="(" separator="," close=")">
                        #{categoryId}
                    </foreach>
            )
        </if>

        <if test="bo.isHide != null">
            AND bi.is_hide = #{bo.isHide}
        </if>
        <if test="bo.launchStatus != null">
            AND bi.launch_status = #{bo.launchStatus}
        </if>
        order by bi.create_time desc
    </select>
</mapper>
