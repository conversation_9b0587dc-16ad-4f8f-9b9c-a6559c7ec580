<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.ebooks.AppEBookResourceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dbj.classpal.books.service.entity.product.AppEBookResource">
        <id column="id" property="id" />
        <result column="resource_type" property="resourceType" />
        <result column="resource_url" property="resourceUrl" />
        <result column="page_num" property="pageNum" />
        <result column="file_name" property="fileName" />
        <result column="sort_num" property="sortNum" />
        <result column="business_type" property="businessType" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

</mapper> 