<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.ebooks.AppEBookstoreShelfRefMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dbj.classpal.books.service.entity.product.AppEBookstoreShelfRef">
        <id column="id" property="id" />
        <result column="store_id" property="storeId" />
        <result column="shelf_id" property="shelfId" />
        <result column="sort_num" property="sortNum" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>
    
    <!-- 根据书城ID查询关联的书架列表 -->
    <select id="getShelvesByStoreId" resultType="com.dbj.classpal.books.service.entity.product.AppEBookshelf">
        SELECT 
            s.*
        FROM 
            product_bookshelf s
        JOIN 
            product_bookstore_shelf_ref ref ON s.id = ref.shelf_id
        WHERE 
            ref.store_id = #{storeId}
            AND ref.is_deleted = 0
            AND s.is_deleted = 0
        ORDER BY 
            ref.sort_num DESC, ref.create_time ASC
    </select>
    
    <!-- 批量插入书城-书架关联 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO product_bookstore_shelf_ref (
            store_id, 
            shelf_id, 
            sort_num, 
            tenant_id, 
            create_by, 
            create_time,
            status
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.storeId}, 
                #{item.shelfId}, 
                #{item.sortNum},
                #{item.tenantId},
                #{item.createBy},
                now(),
                1
            )
        </foreach>
    </insert>

    <select id="getShelfWithDetailsByStoreIds" resultType="java.lang.Integer">
        SELECT
        s.id
        FROM
        app_ebookshelf s
        INNER JOIN (
        SELECT
        shelf_id,
        sort_num,
        create_time,
        ROW_NUMBER() OVER (PARTITION BY shelf_id ORDER BY sort_num DESC, create_time DESC) as rn
        FROM
        app_ebookstore_shelf_ref
        WHERE
        store_id IN
        <foreach collection="storeIds" item="storeId" open="(" separator="," close=")">
            #{storeId}
        </foreach>
        and is_deleted = 0
        ) AS ssr_ranked ON s.id = ssr_ranked.shelf_id AND ssr_ranked.rn = 1
        WHERE
        s.launch_status = 1
        AND s.is_deleted = 0
        ORDER BY
        ssr_ranked.sort_num asc, ssr_ranked.create_time asc
    </select>

    <!-- 批量查询多个书城的书架ID映射（使用窗口函数排序） -->
    <select id="batchGetShelfIdsByStoreIds" resultType="com.dbj.classpal.books.service.dto.StoreShelfMappingDTO">
        SELECT
        ssr_ranked.store_id as storeId,
        s.id as shelfId
        FROM
        app_ebookshelf s
        INNER JOIN (
        SELECT
        store_id,
        shelf_id,
        sort_num,
        create_time,
        ROW_NUMBER() OVER (PARTITION BY shelf_id ORDER BY sort_num DESC, create_time DESC) as rn
        FROM
        app_ebookstore_shelf_ref
        WHERE
        store_id IN
        <foreach collection="storeIds" item="storeId" open="(" separator="," close=")">
            #{storeId}
        </foreach>
        and is_deleted = 0
        ) AS ssr_ranked ON s.id = ssr_ranked.shelf_id AND ssr_ranked.rn = 1
        WHERE
        s.launch_status = 1
        AND s.is_deleted = 0
        ORDER BY
        ssr_ranked.store_id, ssr_ranked.sort_num asc, ssr_ranked.create_time asc
    </select>

</mapper>