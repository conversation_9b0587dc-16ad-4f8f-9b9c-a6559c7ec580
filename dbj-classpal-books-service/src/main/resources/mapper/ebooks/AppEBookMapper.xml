<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.ebooks.AppEBookMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dbj.classpal.books.service.entity.product.AppEBook">
        <id column="id" property="id" />
        <result column="book_title" property="bookTitle" />
        <result column="book_title" property="bookTitle" />
        <result column="book_code" property="bookCode" />
        <result column="category_ids" property="categoryIds" />
        <result column="cover_url" property="coverUrl" />
        <result column="blurb" property="blurb" />
        <result column="file_url" property="fileUrl" />
        <result column="file_status" property="fileStatus" />
        <result column="watermark_id" property="watermarkId" />
        <result column="subject_id" property="subjectId" />
        <result column="stage_id" property="stageId" />
        <result column="textbook_version_id" property="textbookVersionId" />
        <result column="applicable_grades" property="applicableGrades" />
        <result column="launch_status" property="launchStatus" />
        <result column="allow_download" property="allowDownload" />
        <result column="sort_num" property="sortNum" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- H5分页查询单书（支持书架和书城关联查询） -->
    <select id="pageForH5" resultType="com.dbj.classpal.books.service.entity.product.AppEBook">
            SELECT
            e.*
            FROM
            app_ebook e
            INNER JOIN (
            SELECT
            sbr.book_id,
            sbr.sort_num,
            sbr.create_time,
            ROW_NUMBER() OVER (PARTITION BY sbr.book_id ORDER BY sbr.sort_num DESC, sbr.create_time DESC) as rn
            FROM
            <choose>
                <when test="query.shelfId != null">
                    app_ebookshelf_book_ref sbr
                    WHERE sbr.shelf_id = #{query.shelfId}
                    AND sbr.is_deleted = 0
                </when>
                <when test="query.storeId != null">
                    app_ebookshelf_book_ref sbr
                    INNER JOIN app_ebookstore_shelf_ref ssr ON sbr.shelf_id = ssr.shelf_id
                    WHERE ssr.store_id = #{query.storeId}
                    AND ssr.is_deleted = 0
                    AND sbr.is_deleted = 0
                </when>
                <otherwise>
                    <!-- 如果没有提供shelfId和storeId，则查询默认启用的书城和书架 -->
                    app_ebookshelf_book_ref sbr
                    INNER JOIN app_ebookshelf shelf ON sbr.shelf_id = shelf.id
                    INNER JOIN app_ebookstore_shelf_ref ssr ON sbr.shelf_id = ssr.shelf_id
                    INNER JOIN app_ebookstore es ON ssr.store_id = es.id
                    WHERE es.launch_status = 1
                    AND shelf.launch_status = 1
                    AND es.is_deleted = 0
                    AND shelf.is_deleted = 0
                    AND ssr.is_deleted = 0
                    AND sbr.is_deleted = 0
                </otherwise>
            </choose>
            ) AS sbr ON e.id = sbr.book_id AND sbr.rn = 1
            <where>
                e.launch_status = 1
                <if test="query.bookTitle != null and query.bookTitle != ''">
                    AND e.book_title LIKE CONCAT('%', #{query.bookTitle}, '%')
                </if>
                <if test="query.stageIds != null and query.stageIds.size() > 0">
                    AND e.stage_id IN
                    <foreach collection="query.stageIds" item="stageId" open="(" separator="," close=")">
                        #{stageId}
                    </foreach>
                </if>
                <if test="query.subjectIds != null and query.subjectIds.size() > 0">
                    AND e.subject_id IN
                    <foreach collection="query.subjectIds" item="subjectId" open="(" separator="," close=")">
                        #{subjectId}
                    </foreach>
                </if>
                <if test="query.textbookVersionIds != null and query.textbookVersionIds.size() > 0">
                    AND e.textbook_version_id IN
                    <foreach collection="query.textbookVersionIds" item="versionId" open="(" separator="," close=")">
                        #{versionId}
                    </foreach>
                </if>
                <if test="query.applicableGrades != null and query.applicableGrades.size() > 0">
                    AND (
                    <foreach collection="query.applicableGrades" item="gradeId" separator=" OR ">
                        FIND_IN_SET(#{gradeId}, e.applicable_grades)
                    </foreach>
                    )
                </if>
                <if test="query.categoryIds != null and query.categoryIds.size() > 0">
                    AND (
                    <foreach collection="query.categoryIds" item="categoryId" separator=" OR ">
                        FIND_IN_SET(#{categoryId}, e.category_ids)
                    </foreach>
                    )
                </if>
            </where>
            ORDER BY sbr.sort_num asc, sbr.create_time asc
        </select>

</mapper>