<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dbj.classpal.books.service.mapper.advertisement.AdvertisementLevelConditionOptionMapper">

    <select id="getByAdvertisementLevelConditionIds"
            parameterType="java.util.List"
            resultType="com.dbj.classpal.books.client.dto.advertisement.AdvertisementLevelConditionOptionDTO">
        select
            *
        from
            advertisement_level_condition_option lco
        where
            is_deleted = 0
            and lco.advertisement_level_condition_id in
            <foreach collection="advertisementLevelConditionIds" item="advertisementLevelConditionId" open="(" close=")" separator=",">
                #{advertisementLevelConditionId}
            </foreach>
        order by
            create_time
    </select>
</mapper>
