package com.dbj.classpal.books.service.entity.evaluation;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluation
 * Date:     2025-05-16 11:02:51
 * Description: 表名：app_evaluation ,描述： 内容管理-评测表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("app_evaluation")
@Tag(name="内容管理-评测表", description="内容管理-评测表")
public class AppEvaluation extends BizEntity {

    @TableField("evaluation_cover")
    @Schema(name = "评测封面")
    private String evaluationCover;

    @TableField("evaluation_name")
    @Schema(name = "评测名称")
    private String evaluationName;

    @TableField("evaluation_remark")
    @Schema(name = "评测介绍")
    private String evaluationRemark;

    @TableField("evaluation_status")
    @Schema(name = "上架状态 0下架 1上架")
    private Integer evaluationStatus;

    @TableField("evaluation_visible")
    @Schema(name = "是否隐藏  0隐藏 1显示")
    private Integer evaluationVisible;

    @TableField("evaluation_open")
    @Schema(name = "是否启用  0否 1是")
    private Integer evaluationOpen;

}
