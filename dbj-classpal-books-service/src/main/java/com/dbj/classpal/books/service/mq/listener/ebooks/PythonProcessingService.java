package com.dbj.classpal.books.service.mq.listener.ebooks;

import com.dbj.classpal.framework.openapi.annotation.OpenApiInterface;
import com.dbj.classpal.framework.openapi.annotation.OpenApiPlatform;
import com.dbj.classpal.framework.openapi.annotation.PlatformFeature;
import com.dbj.classpal.framework.openapi.annotation.PoolType;
import com.dbj.classpal.framework.openapi.annotation.Priority;
import com.dbj.classpal.framework.openapi.annotation.RejectionPolicy;
import com.dbj.classpal.framework.openapi.annotation.RetryPolicy;
import com.dbj.classpal.framework.openapi.annotation.ThreadPoolStrategy;
import com.dbj.classpal.framework.openapi.callback.ApiCallback;
import com.dbj.classpal.framework.openapi.client.OpenApiClient;
import com.dbj.classpal.framework.openapi.enums.HttpMethod;
import com.dbj.classpal.framework.openapi.model.python.ApiRequest;
import com.dbj.classpal.framework.openapi.model.python.ApiResponse;
import com.dbj.classpal.framework.openapi.model.python.request.PdfApiResponse;
import com.dbj.classpal.framework.openapi.model.python.request.PdfCoverRequest;
import com.dbj.classpal.framework.openapi.model.python.response.PdfToImagesRequest;
import jakarta.annotation.Resource;
import java.util.function.Consumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * PDF处理服务
 * 展示如何在业务服务中使用OpenAPI注解
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@OpenApiPlatform(
    value = "python",
    description = "Python PDF处理服务平台",
    features = {
        PlatformFeature.HIGH_LATENCY,
        PlatformFeature.BATCH_SUPPORT,
        PlatformFeature.UNRELIABLE
    },
    defaultConfig = @OpenApiInterface(
        description = "Python平台默认配置",
        connectTimeout = 90 * 1000,
        readTimeout = 120 * 1000,
        threadPool = @ThreadPoolStrategy(
            type = PoolType.IO_INTENSIVE,
            coreSize = 1,
            maxSize = 1,
            queueCapacity = 200,
            threadNamePrefix = "python-pdf-",
            allowDynamicResize = false,
            rejectionPolicy = RejectionPolicy.CALLER_RUNS
        ),
        retry = @RetryPolicy(
            enabled = false
        ),
        priority = Priority.LOW,
        tags = {"pdf", "media", "python"}
    )
)
public class PythonProcessingService {
    
    @Resource
    private OpenApiClient openApiClient;

    /**
     * PDF转图片
     * 使用普通优先级，不重试
     */
    @OpenApiInterface(
        description = "PDF转图片接口",
        connectTimeout = 90 * 1000,
        readTimeout = 120 * 1000,
            retry = @RetryPolicy,
        priority = Priority.NORMAL,
        tags = {"convert", "images"}
    )
    public PdfApiResponse convertToImages(PdfToImagesRequest request) {

        ApiRequest apiRequest = ApiRequest.builder()
            .platform("python")
            .path("/media/pdf/to_images_v1")
            .method(HttpMethod.POST.getMethod())
            .body(request)
            .build();
        ApiResponse<PdfApiResponse> response = openApiClient.call(apiRequest, PdfApiResponse.class);

        if (response.getSuccess()) {
            log.info("PDF转图片成功: 图片数量={}", response.getData().getFile_urls().size());
            return response.getData();
        } else {
            log.error("PDF转图片失败: {}", response.getErrorMessage());
            throw new RuntimeException("PDF转图片失败: " + response.getErrorMessage());
        }
    }
    @OpenApiInterface(
            description = "PDF异步转图片接口",
            connectTimeout = 90 * 1000,
            readTimeout = 120 * 1000,
            retry = @RetryPolicy,
            priority = Priority.NORMAL,
            tags = {"convert", "images"}
    )
    public void convertToImagesWithCallback(PdfToImagesRequest request, Consumer<PdfApiResponse> successHandler, Consumer<Exception> errorHandler) {
        ApiRequest apiRequest = ApiRequest.builder()
                .platform("python")
                .path("/media/pdf/to_images_v1")
                .method(HttpMethod.POST.getMethod())
                .body(request)
                .build();

        openApiClient.callAsync(apiRequest, PdfApiResponse.class, new ApiCallback<PdfApiResponse>() {
            @Override
            public void onSuccess(ApiRequest request, ApiResponse<PdfApiResponse> response) {
                if (response.getSuccess()) {
                    log.info("PDF异步转图片成功: 图片数量={}", response.getData().getFile_urls().size());
                    successHandler.accept(response.getData());
                } else {
                    log.error("PDF异步转图片失败: {}", response.getErrorMessage());
                    errorHandler.accept(new RuntimeException("PDF异步转图片失败: " + response.getErrorMessage()));
                }
            }

            @Override
            public void onFailure(ApiRequest request, ApiResponse<PdfApiResponse> response, Exception exception) {
                log.error("PDF异步转图片API调用失败", exception);
                errorHandler.accept(exception);
            }
        });
    }
    
    /**
     * PDF封面提取
     * 使用低优先级，快速处理
     */
    @OpenApiInterface(
        description = "PDF封面提取接口",
        connectTimeout = 8000,
        readTimeout = 20000,
        retry = @RetryPolicy,
        priority = Priority.LOW,
        tags = {"extract", "cover"}
    )
    public PdfApiResponse extractCover(PdfCoverRequest request) {

        ApiRequest apiRequest = ApiRequest.builder()
            .platform("python")
            .path("/media/pdf/extract_cover_v1")
            .method(HttpMethod.POST.getMethod())
            .body(request)
            .build();
        
        ApiResponse<PdfApiResponse> response = openApiClient.call(apiRequest, PdfApiResponse.class);
        
        if (response.getSuccess()) {
            log.info("PDF封面提取成功: coverUrl={}", response.getData().getFirstFileUrl());
            return response.getData();
        } else {
            log.error("PDF封面提取失败: {}", response.getErrorMessage());
            throw new RuntimeException("PDF封面提取失败: " + response.getErrorMessage());
        }
    }

}
