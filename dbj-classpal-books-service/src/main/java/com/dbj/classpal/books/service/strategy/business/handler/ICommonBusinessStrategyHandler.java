package com.dbj.classpal.books.service.strategy.business.handler;

import com.dbj.classpal.books.service.strategy.business.common.ICommonBusinessStrategy;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppMaterialBusinessStrategy
 * Date:     2025-05-09 13:04:54
 * Description: 表名： ,描述： 表
 */
@Service
public abstract class ICommonBusinessStrategyHandler implements ICommonBusinessStrategy {


}
