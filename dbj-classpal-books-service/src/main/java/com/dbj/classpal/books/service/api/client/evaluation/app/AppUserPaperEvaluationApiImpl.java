package com.dbj.classpal.books.service.api.client.evaluation.app;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.app.client.bo.user.UserIdApiBO;
import com.dbj.classpal.app.client.dto.user.CurrentUserApiDTO;
import com.dbj.classpal.books.client.api.evaluation.app.AppUserPaperEvaluationApi;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.evaluation.app.*;
import com.dbj.classpal.books.client.dto.evaluation.AdminUserPaperEvaluationAnalysisQueryApiDTO;
import com.dbj.classpal.books.client.dto.evaluation.app.AppEvaluationReportQueryApiDTO;
import com.dbj.classpal.books.client.dto.evaluation.app.AppUserPaperEvaluationAiParamsApiDTO;
import com.dbj.classpal.books.common.bo.evaluation.app.AppUserPaperEvaluationAnalysisGenerateBO;
import com.dbj.classpal.books.common.bo.evaluation.app.AppUserPaperEvaluationGenerateBO;
import com.dbj.classpal.books.common.dto.evaluation.app.AppEvaluationReportQueryDTO;
import com.dbj.classpal.books.common.dto.evaluation.app.AppUserPaperEvaluationAnalysisQueryDTO;
import com.dbj.classpal.books.service.biz.evaluation.IAppEvaluationBiz;
import com.dbj.classpal.books.service.biz.evaluation.IAppUserPaperEvaluationBiz;
import com.dbj.classpal.books.service.entity.evaluation.AppEvaluation;
import com.dbj.classpal.books.service.entity.evaluation.AppUserPaperEvaluation;
import com.dbj.classpal.books.service.remote.sys.SysAppUserRemoteService;
import com.dbj.classpal.books.service.service.evaluation.IAppUserPaperEvaluationService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.utils.util.ContextAppUtil;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppUserPaperEvaluationApiImpl
 * Date:     2025-05-20 14:55:06
 * Description: 表名： ,描述： 表
 */
@RestController
public class AppUserPaperEvaluationApiImpl implements AppUserPaperEvaluationApi {
    @Resource
    private IAppUserPaperEvaluationBiz appUserPaperEvaluationBiz;
    @Resource
    private IAppUserPaperEvaluationService appUserPaperEvaluationService;
    @Resource
    private SysAppUserRemoteService appUserRemoteService;
    @Resource
    private IAppEvaluationBiz appEvaluationBiz;


    @Override
    public RestResponse<Boolean> resetEvaluation(AppUserPaperEvaluationSaveApiBO bo) throws BusinessException {
        AppUserPaperEvaluation saveBean = new AppUserPaperEvaluation();
        BeanUtil.copyProperties(bo, saveBean);
        UserIdApiBO userIdApiBO = new UserIdApiBO();
        userIdApiBO.setUserId(ContextAppUtil.getAppUserIdInt());
        saveBean.setAppUserId(ContextAppUtil.getAppUserIdInt());
        CurrentUserApiDTO currentUser = appUserRemoteService.getCurrentUser(userIdApiBO);
        if (ObjectUtils.isEmpty(currentUser)){
            throw new BusinessException(OPENAPI_GET_USER_INFO_NOT_EXIST_CODE,OPENAPI_GET_USER_INFO_NOT_EXIST_MSG);
        }
        AppEvaluation evaluation = appEvaluationBiz.getById(bo.getAppEvaluationId());
        if (ObjectUtils.isEmpty(evaluation)){
            throw new BusinessException(APP_EVALUATION_NOT_EXIST_CODE,APP_EVALUATION_NOT_EXIST_MSG);
        }
        saveBean.setGradeId(currentUser.getGradeId());
        saveBean.setGradeName(currentUser.getGradeName());
        saveBean.setRegion(currentUser.getIpCity());
        if (!appUserPaperEvaluationBiz.save(saveBean)){
            throw new BusinessException(APP_EVALUATION_RESET_FAIL_CODE,APP_EVALUATION_RESET_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<AppUserPaperEvaluationAiParamsApiDTO> getAiParams(CommonIdApiBO bo) throws BusinessException {
        return RestResponse.success(appUserPaperEvaluationService.getAiParams(bo));
    }

    @Override
    public RestResponse<Boolean> checkSubmitEvaluationReport(CommonIdApiBO bo) throws BusinessException {
        return RestResponse.success(appUserPaperEvaluationService.checkSubmitEvaluationReport(bo));
    }

    @Override
    public RestResponse<AppEvaluationReportQueryApiDTO> genEvaluationReport(AppUserPaperEvaluationGenerateApiBO bo) throws BusinessException {
        if(ObjectUtils.isEmpty(bo)){
            throw new BusinessException("参数不能为空");
        }
        if(bo.getId() == null){
            throw new BusinessException("参数不能为空");
        }
        AppUserPaperEvaluationGenerateBO evaluationGenerateBO = new AppUserPaperEvaluationGenerateBO();
        BeanUtil.copyProperties(bo, evaluationGenerateBO);
        AppEvaluationReportQueryDTO appEvaluationReportQueryDTO = appUserPaperEvaluationService.genEvaluationReport(evaluationGenerateBO);
        AppEvaluationReportQueryApiDTO appEvaluationReportQueryApiDTO = new AppEvaluationReportQueryApiDTO();
        BeanUtil.copyProperties(appEvaluationReportQueryDTO, appEvaluationReportQueryApiDTO);
        List<AppUserPaperEvaluationAnalysisQueryDTO> analysisQueryDTOList = appEvaluationReportQueryDTO.getAnalysisQueryDTOList();
        List<AdminUserPaperEvaluationAnalysisQueryApiDTO>analysisQueryApiDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(analysisQueryDTOList)){
            analysisQueryApiDTOList = BeanUtil.copyToList(analysisQueryDTOList, AdminUserPaperEvaluationAnalysisQueryApiDTO.class);
        }
        appEvaluationReportQueryApiDTO.setAnalysisQueryApiDTOList(analysisQueryApiDTOList);
        return RestResponse.success(appEvaluationReportQueryApiDTO);
    }
}
