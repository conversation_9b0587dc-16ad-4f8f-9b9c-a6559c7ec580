package com.dbj.classpal.books.service.util;

import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.books.common.constant.RedisKeyConstants;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.redisson.config.RedissonRedisUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.security.SecureRandom;
import java.text.MessageFormat;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-framework
 * @className ShortUrlGenerator
 * @description
 * @date 2025-04-23 09:08
 **/
@Component
public class ShortUrlGenerator {

    // Base62字符表
    private static final String BASE62 = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
    // 固定长度8位
    private static final int CODE_LENGTH = 8;
    // 使用加密安全的随机数生成器
    private static final SecureRandom RANDOM = new SecureRandom();
    // 线程安全的已生成代码集合
    @Resource
    private RedissonRedisUtils redisUtils;

    /**
     * 生成唯一的8位短链
     * @return 8位短链字符串
     */
    public String generate() throws BusinessException {
        int num = 0;
        String code;
        do {
            num++;
            // 生成6字节随机数（48位）
            byte[] bytes = new byte[6];
            RANDOM.nextBytes(bytes);
            code = base62Encode(bytes);
            // 补零到8位
            if (code.length() < CODE_LENGTH) {
                code = String.format("%" + CODE_LENGTH + "s", code).replace(' ', '0');
            }
            if(num > 80){
                throw new BusinessException(AppErrorCode.SHORT_FAIL_CODE,AppErrorCode.SHORT_FAIL_MSG);
            }
        } while (redisUtils.hasKey(code)); // 自动处理重复情况
        redisUtils.setValue(MessageFormat.format(RedisKeyConstants.SHORT_URL,code),"",-1,null);
        return code;
    }

    /**
     * Base62编码方法
     * @param bytes 输入字节数组
     * @return Base62编码字符串
     */
    private String base62Encode(byte[] bytes) {
        BigInteger number = new BigInteger(1, bytes); // 无符号处理
        StringBuilder sb = new StringBuilder();

        BigInteger base = BigInteger.valueOf(62);
        while (number.compareTo(BigInteger.ZERO) > 0) {
            BigInteger[] divRem = number.divideAndRemainder(base);
            sb.append(BASE62.charAt(divRem[1].intValue()));
            number = divRem[0];
        }

        return sb.reverse().toString();
    }

    /**
     * 验证短链格式有效性
     * @param code 待验证短链
     * @return 是否有效
     */
    public boolean isValid(String code) {
        return code != null
                && code.length() == CODE_LENGTH
                && code.chars().allMatch(c -> BASE62.indexOf(c) != -1);
    }

}
