package com.dbj.classpal.books.service.mapper.ebooks;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookH5QueryBO;
import com.dbj.classpal.books.service.entity.product.AppEBook;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 单书表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Mapper
public interface AppEBookMapper extends BaseMapper<AppEBook> {

    /**
     * H5分页查询单书（支持书架和书城关联查询）
     *
     * @param page 分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    Page<AppEBook> pageForH5(@Param("page") Page<AppEBook> page, @Param("query") AppEBookH5QueryBO query);

}