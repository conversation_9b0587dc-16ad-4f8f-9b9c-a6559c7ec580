package com.dbj.classpal.books.service.biz.ebooks;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookshelfQueryBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookshelfH5QueryBO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookshelfDTO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookDTO;
import com.dbj.classpal.books.service.entity.product.AppEBookshelf;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 书架 业务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
public interface IAppEBookshelfBiz extends IService<AppEBookshelf> {

    /**
     * 分页查询书架列表
     *
     * @param page 分页对象
     * @param queryBO 查询参数
     * @return 书架分页数据
     */
    Page<AppEBookshelfDTO> pageBookshelf(Page<AppEBookshelf> page, AppEBookshelfQueryBO queryBO) throws BusinessException;

    /**
     * H5分页查询书架列表（支持书城关联查询）
     *
     * @param pageRequest 分页查询参数
     * @return 书架分页数据
     */
    Page<AppEBookshelfDTO> pageForH5(PageInfo<AppEBookshelfH5QueryBO> pageRequest) throws BusinessException;

    /**
     * 查询书架详情
     * 
     * @param id 书架ID
     * @return 书架详情数据
     */
    AppEBookshelfDTO getBookshelfDetail(Integer id) throws BusinessException;

    /**
     * 设置书架封面
     * 
     * @param id 书架ID
     * @param bookId 单书ID，用于获取封面，可为null
     * @param coverUrl 自定义封面URL，优先级高于bookId
     * @return 设置结果
     */
    boolean setBookshelfCover(Integer id, Integer bookId, String coverUrl) throws BusinessException;
    
    /**
     * 向书架添加单书
     * 
     * @param shelfId 书架ID
     * @param bookIds 单书ID列表
     * @return 添加结果
     */
    boolean addBooksToShelf(Integer shelfId, List<Integer> bookIds) throws BusinessException;
    
    /**
     * 从书架移除单书
     * 
     * @param shelfId 书架ID
     * @param bookIds 单书ID列表
     * @return 移除结果
     */
    boolean removeBooksFromShelf(Integer shelfId, List<Integer> bookIds) throws BusinessException;
    
    /**
     * 调整书架单书排序
     * 
     * @param shelfId 书架ID
     * @param bookSortMap 单书排序映射，key为单书ID，value为排序序号
     * @return 排序结果
     */
    boolean sortBooksInShelf(Integer shelfId, Map<Integer, Integer> bookSortMap) throws BusinessException;
    /**
     * 批量允许下载书架
     *
     * @param ids ID列表
     * @return 批量允许下载结果
     */
    boolean allowDownloadBatch(List<Integer> ids) throws BusinessException;

    /**
     * 批量关闭下载书架
     *
     * @param ids ID列表
     * @return 批量关闭下载结果
     */
    boolean disableDownloadBatch(List<Integer> ids) throws BusinessException;

    /**
     * 获取书架中的书籍数量
     *
     * @param shelfId 书架ID
     * @return 书籍数量
     */
    int countBooks(Integer shelfId) throws BusinessException;

    /**
     * 批量统计多个书架的书籍数量（高性能版本）
     * 一次查询获取多个书架的书籍数量，避免N+1查询问题
     *
     * @param shelfIds 书架ID列表
     * @return 书架ID到书籍数量的映射
     */
    Map<Integer, Integer> batchCountBooks(List<Integer> shelfIds);
    
    /**
     * 根据条件过滤获取书架中的单书列表
     *
     * @param shelfId 书架ID
     * @param bookTitle 书籍标题
     * @param subjectIds 学科ID列表
     * @param stageIds 阶段ID列表
     * @param categoryIds 分类ID列表
     * @param textbookVersionIds 教材版本ID列表
     * @param applicableGrades 适用年级列表
     * @return 过滤后的单书列表
     */
    List<AppEBookDTO> getFilteredBooksForShelf(Integer shelfId, 
                                             String bookTitle,
                                             List<Integer> subjectIds,
                                             List<Integer> stageIds,
                                             List<Integer> categoryIds,
                                             List<Integer> textbookVersionIds,
                                             List<Integer> applicableGrades) throws BusinessException;
} 