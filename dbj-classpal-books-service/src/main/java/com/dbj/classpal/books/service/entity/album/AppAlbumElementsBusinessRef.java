package com.dbj.classpal.books.service.entity.album;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumElementsBusinessRef
 * Date:     2025-04-15 10:14:12
 * Description: 表名： ,描述： 表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_album_elements_business_ref")
@Tag(name="专辑与业务关联关系表", description="专辑与业务关联关系表")
public class AppAlbumElementsBusinessRef extends BizEntity implements Serializable {

    @TableField("app_album_id")
    @Schema(description = "专辑ID")
    private Integer appAlbumId;

    @TableField("business_id")
    @Schema(description = "业务id")
    private Integer businessId;

    @TableField("business_type")
    @Schema(description = "业务类型 1内容管理-音频专辑 2内容管理-视频专辑 3图书管理-图书资源 4图书管理-题库 5图书管理-音频专辑 6图书管理-视频专辑")
    private Integer businessType;

    @TableField("order_num")
    @Schema(description = "排序")
    private Integer orderNum;
}
