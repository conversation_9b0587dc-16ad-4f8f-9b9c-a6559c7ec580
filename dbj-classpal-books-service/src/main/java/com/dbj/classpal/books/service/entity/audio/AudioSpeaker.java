package com.dbj.classpal.books.service.entity.audio;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 发音人配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("audio_speaker")
@ApiModel(value="AudioSpeaker对象", description="发音人配置表")
public class AudioSpeaker extends BizEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "发音人名称")
    private String name;

    @ApiModelProperty(value = "（阿里云）音色值")
    private String voice;

    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "音频模板url")
    private String audioModelUrl;

    @ApiModelProperty(value = "音色类型：1 男声 2 女声 3 童声 4 多情感")
    private Integer voiceType;

    @ApiModelProperty(value = "自定义提示词")
    private String prompt;

    @ApiModelProperty(value = "语言类型：1 中文 2 英文 3 粤语")
    private Integer language;

    @ApiModelProperty(value = "类型名称")
    private String typeName;

    @ApiModelProperty(value = "适用场景")
    private String usageScenarios;

    @ApiModelProperty(value = "支持语言")
    private String supportedLanguages;

    @ApiModelProperty(value = "是否支持儿化音, 0 否 1 是")
    private String isRhotic;



}
