package com.dbj.classpal.books.service.entity.album;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumMenus
 * Date:     2025-04-08 10:15:31
 * Description: 表名： ,描述： 表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_album_menus")
@Tag(name="内容管理-专辑分类", description="内容管理-专辑分类")
public class AppAlbumMenus extends BizEntity implements Serializable {

    @TableField("is_root")
    @Schema(description = "是否根节点 0否 1是")
    private Integer isRoot;


    @TableField("default_type")
    @Schema(description = "是否默认分类 0否 1是")
    private Integer defaultType;

    @TableField("parent_id")
    @Schema(description = "父节点ID")
    private Integer parentId;


    @TableField("album_menu_name")
    @Schema(description = "专辑分类名称")
    private String albumMenuName;

    @TableField("album_type")
    @Schema(description = "专辑分类类型 1音频 2视频")
    private Integer albumType;

    @TableField("album_menu_status")
    @Schema(description = "状态  0禁用 1启用")
    private Integer albumMenuStatus;

    @TableField("order_num")
    @Schema(description = "排序")
    private Integer orderNum;
}
