package com.dbj.classpal.books.service.service.ebooks.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.bo.ebooks.*;
import com.dbj.classpal.books.common.bo.ebooks.GetShareInfoBO;
import com.dbj.classpal.books.common.bo.ebooks.RemoveShareUrlBO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookAsyncProcessDTO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookDTO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookResourceDTO;
import com.dbj.classpal.books.common.dto.ebooks.PdfTaskStatusDTO;
import com.dbj.classpal.books.common.dto.ebooks.ShareUrlResultDTO;
import com.dbj.classpal.books.common.enums.FileStatusEnum;
import com.dbj.classpal.books.common.enums.PdfTaskStatusEnum;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookBiz;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookPdfTaskBiz;
import com.dbj.classpal.books.service.biz.ebooks.impl.AppEBookHelper;
import com.dbj.classpal.books.service.entity.product.AppEBookPdfTask;
import com.dbj.classpal.books.service.entity.ebooks.AppEbooksConfigWatermarkTemplate;
import com.dbj.classpal.books.service.entity.ebooks.AppEBook;
import com.dbj.classpal.books.service.mapper.ebooks.AppEBookMapper;
import com.dbj.classpal.books.service.service.ebooks.IAppEBookService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import com.dbj.classpal.framework.pdf.enums.PdfWaterMarkBusinessTypeEnum;
import com.dbj.classpal.framework.utils.util.enums.ShareUrlTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <p>
 * 单书 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Slf4j
@Service
public class AppEBookServiceImpl extends ServiceImpl<AppEBookMapper, AppEBook> implements IAppEBookService {

    // 静态常量定义
    private static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    private static final Integer ESTIMATED_DURATION_SECONDS = 60;
    private static final Integer PROGRESS_PROCESSING = 50;
    private static final Integer PROGRESS_SUCCESS = 100;
    private static final Integer PROGRESS_FAILED = 0;
    private static final String UNKNOWN_STATUS_DESC = "未知状态";
    private static final String CLEAR_ERROR_MSG = "";
    private static final String REPROCESS_SUCCESS_MSG = "重新处理成功";
    private static final String PROCESSING_STATUS_MSG = "处理中";
    private static final Integer MAX_BATCH_QUERY_SIZE = 100;

    @Resource
    private IAppEBookBiz eBookBiz;

    @Resource
    private AppEBookHelper eBookHelper;

    @Resource
    private IAppEBookPdfTaskBiz pdfTaskBiz;

    @Override
    public Page<AppEBookDTO> page(PageInfo<AppEBookQueryBO> pageRequest) throws BusinessException {
        return eBookBiz.page(pageRequest);
    }

    @Override
    public AppEBookDTO detail(Integer id) throws BusinessException {
        return eBookBiz.detail(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(AppEBookSaveBO saveBO) throws BusinessException {
        return eBookBiz.save(saveBO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(AppEBookUpdateBO updateBO) throws BusinessException {
        return eBookBiz.update(updateBO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Integer id) throws BusinessException {
        return eBookBiz.delete(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBatch(List<Integer> ids) throws BusinessException {
        return eBookBiz.deleteBatch(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableBatch(List<Integer> ids) throws BusinessException {
        return eBookBiz.enableBatch(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableBatch(List<Integer> ids) throws BusinessException {
        return eBookBiz.disableBatch(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean allowDownloadBatch(List<Integer> ids) throws BusinessException {
        return eBookBiz.allowDownloadBatch(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableDownloadBatch(List<Integer> ids) throws BusinessException {
        return eBookBiz.disableDownloadBatch(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFile(AppEBookUpdateFileBO updateFileBO) throws BusinessException {
        return eBookBiz.updateFile(updateFileBO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateWatermark(Integer id, Integer watermarkId) throws BusinessException {
        return eBookBiz.updateWatermark(id, watermarkId);
    }

    @Override
    public String coverUrl(AppEBookFileBO fileBO) throws BusinessException {
        return eBookBiz.coverUrl(fileBO);
    }

    @Override
    public Page<AppEBookDTO> pageForH5(PageInfo<AppEBookH5QueryBO> pageRequest) throws BusinessException {
        return eBookBiz.pageForH5(pageRequest);
    }

    @Override
    public AppEBookAsyncProcessDTO asyncProcess(AppEBookAsyncProcessBO request) throws BusinessException {
        log.info("异步处理PDF文件，参数：{}", request);

        // 获取水印模板
        AppEbooksConfigWatermarkTemplate template = null;
        Integer waterMarkBusinessType = eBookHelper.getWatermarkBusinessType(request.getWatermarkTemplateId());
        String coverUrl = eBookHelper.getCoverUrlByMd5(request.getFileMd5(),waterMarkBusinessType);
        if(StringUtils.isNotEmpty(coverUrl)){
            List<AppEBookPdfTask> taskList =  pdfTaskBiz.queryTaskByBusinessKey(request.getFileMd5());
            AppEBookAsyncProcessDTO response = new AppEBookAsyncProcessDTO();
            response.setBusinessId(request.getBusinessId());
            Optional<AppEBookPdfTask> taskOptional = taskList.stream()
                    .filter(e -> e.getStatus().equals(YesOrNoEnum.YES.getCode()))
                    .findFirst()
                    .or(() -> taskList.stream().findFirst());
            if (taskOptional.isEmpty() && CollectionUtils.isNotEmpty(taskList)) {
                taskOptional = Optional.of(taskList.get(0));
            }
            taskOptional.ifPresent(task -> response.setTaskId(task.getTaskId()));
            response.setCoverUrl(coverUrl);
            return response;
        }
        if(StringUtils.isNotEmpty(request.getFileUrl())) {

            if(Objects.nonNull(request.getWatermarkTemplateId()) && !request.getWatermarkTemplateId().equals(YesOrNoEnum.NO.getCode())) {
                template = eBookHelper.getWatermarkTemplate(request.getWatermarkTemplateId());
            }
        }

        // 调用异步处理方法
        String taskId = eBookHelper.parsePDFAsync(null,
                request.getFileMd5(),
                waterMarkBusinessType,
                template,
                request.getFileUrl(),
                request.getFileName()
        );

        // 构建响应数据
        AppEBookAsyncProcessDTO response = new AppEBookAsyncProcessDTO();
        response.setTaskId(taskId);
        response.setBusinessId(request.getBusinessId());
        response.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern(DATE_TIME_FORMAT)));
        response.setEstimatedDuration(ESTIMATED_DURATION_SECONDS);

        log.info("异步处理PDF文件任务已创建，taskId：{}", taskId);
        return response;
    }

    @Override
    public PdfTaskStatusDTO getTaskStatus(String taskId) throws BusinessException {
        log.info("查询PDF处理任务状态，taskId：{}", taskId);

        // 查询任务信息
        AppEBookPdfTask task = eBookHelper.getPdfTaskStatus(taskId);

        // 转换为DTO
        PdfTaskStatusDTO response = new PdfTaskStatusDTO();
        response.setTaskId(task.getTaskId());
        response.setBusinessId(task.getBusinessId());
        response.setBusinessKey(task.getBusinessKey());
        response.setStatus(task.getStatus());
        response.setCoverUrl(task.getCoverUrl());
        response.setErrorMsg(task.getErrorMsg());

        // 设置状态描述
        PdfTaskStatusEnum statusEnum = PdfTaskStatusEnum.getByCode(task.getStatus());
        response.setStatusDesc(statusEnum != null ? statusEnum.getDesc() : UNKNOWN_STATUS_DESC);

        // 设置进度
        if (task.getStatus().equals(PdfTaskStatusEnum.PROCESSING.getCode())) {
            response.setProgress(PROGRESS_PROCESSING);
        } else if (task.getStatus().equals(PdfTaskStatusEnum.SUCCESS.getCode())) {
            response.setProgress(PROGRESS_SUCCESS);
        } else {
            response.setProgress(PROGRESS_FAILED);
        }

        log.info("查询PDF处理任务状态成功，taskId：{}，status：{}", taskId, response.getStatus());
        return response;
    }

    @Override
    public AppEBookAsyncProcessDTO reprocess(AppEBookReprocessBO request) throws BusinessException {
        log.info("重新处理PDF文件，单书ID：{}", request.getBookId());

        // 根据单书ID获取单书信息
        AppEBookDTO eBook = eBookBiz.detail(request.getBookId());
        if (eBook == null) {
            throw new BusinessException(AppErrorCode.EBOOK_NOT_EXIST_CODE, AppErrorCode.EBOOK_NOT_EXIST_MSG + "，ID：" + request.getBookId());
        }

        // 检查单书是否有PDF文件
        if (eBook.getFileUrl() == null || eBook.getFileUrl().isEmpty()) {
            throw new BusinessException(AppErrorCode.EBOOK_FILE_URL_NAME_EMPTY_CODE, AppErrorCode.EBOOK_FILE_URL_NAME_EMPTY_MSG);
        }

        // 检查单书的文件状态和错误信息
        if (eBook.getFileStatus() == null || !eBook.getFileStatus().equals(FileStatusEnum.FAILED.getCode())) {
            throw new BusinessException(AppErrorCode.EBOOK_FILE_PROCESSING_CODE, "单书文件状态不是失败状态，无需重新处理。当前状态：" +
                (eBook.getFileStatus() != null ? FileStatusEnum.fromCode(eBook.getFileStatus()).getDesc() : "未知"));
        }

        // 检查是否有错误信息
        if (eBook.getFileErrMsg() == null || eBook.getFileErrMsg().trim().isEmpty()) {
            throw new BusinessException(AppErrorCode.EBOOK_FILE_PROCESSING_CODE, AppErrorCode.EBOOK_FILE_PROCESSING_MSG);
        }

        log.info("单书文件处理失败，错误信息：{}，开始重新处理", eBook.getFileErrMsg());

        // 查找并清理该单书的失败任务记录
        cleanupFailedTasks(request.getBookId());

        // 清理单书的错误状态，设置为处理中
        clearBookErrorStatus(request.getBookId());

        // 获取水印模板（使用单书配置的水印模板）
        AppEbooksConfigWatermarkTemplate template = null;
        if (eBook.getWatermarkId() != null) {
            template = eBookHelper.getWatermarkTemplate(eBook.getWatermarkId());
        }

        // 创建新的异步处理任务
        // 默认使用切图模式（仅生成封面）
        Integer waterMarkBusinessType = PdfWaterMarkBusinessTypeEnum.ONLY_SLICING_IMAGE.getCode();

        String newTaskId = eBookHelper.parsePDFAsync(eBook.getId(),
                eBook.getFileMd5(),
                waterMarkBusinessType,
                template,
                eBook.getFileUrl(),
                eBook.getFileName()
        );

        // 构建响应数据
        AppEBookAsyncProcessDTO response = new AppEBookAsyncProcessDTO();
        response.setTaskId(newTaskId);
        response.setBusinessId(request.getBookId());
        response.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern(DATE_TIME_FORMAT)));
        response.setEstimatedDuration(ESTIMATED_DURATION_SECONDS);

        log.info("重新处理PDF文件任务已创建，单书ID：{}，新taskId：{}", request.getBookId(), newTaskId);
        return response;
    }

    /**
     * 清理失败的任务记录
     */
    private void cleanupFailedTasks(Integer bookId) {
        try {
            // 查找该单书的失败任务
            List<AppEBookPdfTask> failedTasks = eBookHelper.getFailedTasksByBusinessId(bookId);
            if (!failedTasks.isEmpty()) {
                log.info("发现{}个失败任务，准备清理，单书ID：{}", failedTasks.size(), bookId);

                // 可以选择删除或标记为已清理
                for (AppEBookPdfTask task : failedTasks) {
                    eBookHelper.markTaskAsCleaned(task.getTaskId());
                }

                log.info("已清理失败任务，单书ID：{}", bookId);
            }
        } catch (Exception e) {
            log.warn("清理失败任务时出现异常，单书ID：{}，错误：{}", bookId, e.getMessage());
            // 不抛出异常，允许重新处理继续进行
        }
    }

    /**
     * 清理单书的错误状态
     */
    private void clearBookErrorStatus(Integer bookId) {
        try {
            // 更新单书状态为处理中，清空错误信息
            AppEBook updateFileStatus = new AppEBook();
            updateFileStatus.setId(bookId);
            updateFileStatus.setFileStatus(FileStatusEnum.PROCESSING.getCode());
            updateFileStatus.setFileErrMsg(CLEAR_ERROR_MSG); // 清空错误信息
            eBookBiz.updateById(updateFileStatus);
            log.info("已清理单书错误状态，设置为处理中，单书ID：{}", bookId);

        } catch (Exception e) {
            log.warn("清理单书错误状态时出现异常，单书ID：{}，错误：{}", bookId, e.getMessage());
            // 不抛出异常，允许重新处理继续进行
        }
    }

    @Override
    public List<PdfTaskStatusDTO> batchGetTaskStatus(List<String> taskIds) throws BusinessException {
        log.info("批量查询PDF处理任务状态，任务数量：{}", taskIds.size());

        if (taskIds.isEmpty()) {
            throw new BusinessException(AppErrorCode.PARAMS_NOT_NULL_CODE, AppErrorCode.PARAMS_NOT_NULL_MSG);
        }

        if (taskIds.size() > MAX_BATCH_QUERY_SIZE) {
            throw new BusinessException(AppErrorCode.PARAMS_NOT_NULL_CODE, "批量查询任务数量不能超过" + MAX_BATCH_QUERY_SIZE + "个");
        }

        List<PdfTaskStatusDTO> results = new ArrayList<>();

        for (String taskId : taskIds) {
            try {
                // 查询单个任务状态
                AppEBookPdfTask task = eBookHelper.getPdfTaskStatus(taskId);

                // 转换为DTO
                PdfTaskStatusDTO dto = new PdfTaskStatusDTO();
                dto.setTaskId(task.getTaskId());
                dto.setBusinessId(task.getBusinessId());
                dto.setStatus(task.getStatus());
                dto.setCoverUrl(task.getCoverUrl());
                dto.setErrorMsg(task.getErrorMsg());

                // 设置状态描述
                PdfTaskStatusEnum statusEnum = PdfTaskStatusEnum.getByCode(task.getStatus());
                dto.setStatusDesc(statusEnum != null ? statusEnum.getDesc() : UNKNOWN_STATUS_DESC);

                // 设置进度
                if (task.getStatus().equals(PdfTaskStatusEnum.PROCESSING.getCode())) {
                    dto.setProgress(PROGRESS_PROCESSING);
                } else if (task.getStatus().equals(PdfTaskStatusEnum.SUCCESS.getCode())) {
                    dto.setProgress(PROGRESS_SUCCESS);
                } else {
                    dto.setProgress(PROGRESS_FAILED);
                }

                results.add(dto);

            } catch (Exception e) {
                log.warn("查询任务状态失败，taskId：{}，错误：{}", taskId, e.getMessage());

                // 创建一个错误状态的DTO
                PdfTaskStatusDTO errorDto = new PdfTaskStatusDTO();
                errorDto.setTaskId(taskId);
                errorDto.setStatus(PdfTaskStatusEnum.FAILED.getCode());
                errorDto.setStatusDesc("查询失败");
                errorDto.setErrorMsg("任务不存在或查询异常：" + e.getMessage());
                errorDto.setProgress(PROGRESS_FAILED);

                results.add(errorDto);
            }
        }

        log.info("批量查询PDF处理任务状态完成，查询数量：{}，返回数量：{}", taskIds.size(), results.size());
        return results;
    }

    @Override
    public ShareUrlResultDTO getShareInfo(GetShareInfoBO request) throws BusinessException {
        log.info("获取分享信息，业务类型：{}，业务ID：{}", request.getBusinessType(), request.getBusinessId());

        // 参数验证
        ShareUrlTypeEnum typeEnum = ShareUrlTypeEnum.getByCode(request.getBusinessType());
        if (typeEnum == null) {
            throw new BusinessException(AppErrorCode.EBOOK_BUSINESS_TYPE_NOT_SUPPORT_CODE, AppErrorCode.EBOOK_BUSINESS_TYPE_NOT_SUPPORT_MSG + "：" + request.getBusinessType());
        }

        // 调用业务层获取分享信息
        return eBookBiz.getShareInfo(request);
    }

    @Override
    public void removeShareUrl(RemoveShareUrlBO request) throws BusinessException {
        log.info("删除分享链接，业务类型：{}，业务ID：{}", request.getBusinessType(), request.getBusinessId());

        // 参数验证
        ShareUrlTypeEnum typeEnum = ShareUrlTypeEnum.getByCode(request.getBusinessType());
        if (typeEnum == null) {
            throw new BusinessException(AppErrorCode.EBOOK_BUSINESS_TYPE_NOT_SUPPORT_CODE, AppErrorCode.EBOOK_BUSINESS_TYPE_NOT_SUPPORT_MSG + "：" + request.getBusinessType());
        }

        // 调用业务层删除分享信息
        eBookBiz.removeShareUrl(request);
    }

    @Override
    public Map<Integer, List<AppEBookResourceDTO>> batchQueryResources(List<Integer> bookIds, Integer resourceType) throws BusinessException {
        log.info("批量查询书籍资源信息，书籍数量：{}，资源类型：{}", bookIds != null ? bookIds.size() : 0, resourceType);
        return eBookBiz.batchQueryResources(bookIds, resourceType);
    }

    @Override
    public Map<Integer, String> batchQueryWatermarkedFileUrls(List<Integer> bookIds) {
        return eBookBiz.batchQueryWatermarkedFileUrls(bookIds);
    }
}