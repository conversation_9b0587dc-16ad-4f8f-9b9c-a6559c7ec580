package com.dbj.classpal.books.service.api.client.poem.app;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.poem.app.AppAncientPoemBusinessRefApi;
import com.dbj.classpal.books.client.bo.poem.app.AncientPoemBusinessRefPageBO;
import com.dbj.classpal.books.client.dto.poem.app.AncientPoemBusinessRefPageDTO;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemBiz;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemBusinessRefBiz;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemReciteAppUserAssessmentScoresBiz;
import com.dbj.classpal.books.service.entity.poem.AncientPoemReciteAppUserAssessmentScores;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.utils.util.ContextAppUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className IAncientPoemBusinessRefApi
 * @description
 * @date 2025-05-27 09:29
 **/
@RestController
public class AppAncientPoemBusinessRefApiImpl implements AppAncientPoemBusinessRefApi {

    @Resource
    private IAncientPoemBusinessRefBiz ancientPoemBusinessRefBiz;
    @Resource
    private IAncientPoemReciteAppUserAssessmentScoresBiz ancientPoemReciteAppUserAssessmentScoresBiz;
    @Resource
    private IAncientPoemBiz ancientPoemBiz;

    @Override
    public RestResponse<Page<AncientPoemBusinessRefPageDTO>> pageAncientPoemBusinessRef(PageInfo<AncientPoemBusinessRefPageBO> pageInfo) {
        Page<AncientPoemBusinessRefPageDTO> page = ancientPoemBusinessRefBiz.pageAncientPoemBusinessRef(pageInfo);
        //查询用户与古诗相关数据
        Integer appUserId = ContextAppUtil.getAppUserIdInt();
        if(appUserId != null){
            List<AncientPoemBusinessRefPageDTO> ancientPoemBusinessRefPageDTOList =  page.getRecords();
            if(CollectionUtils.isNotEmpty(ancientPoemBusinessRefPageDTOList)){
                List<Integer> ids = ancientPoemBusinessRefPageDTOList.stream().map(AncientPoemBusinessRefPageDTO::getAncientPoemId).collect(Collectors.toList());
                List<AncientPoemReciteAppUserAssessmentScores> ancientPoemReciteAppUserAssessmentScoresList = ancientPoemReciteAppUserAssessmentScoresBiz.lambdaQuery().eq(AncientPoemReciteAppUserAssessmentScores::getAppUserId,appUserId)
                        .in(AncientPoemReciteAppUserAssessmentScores::getPoemId,ids).list();
                if(CollectionUtils.isNotEmpty(ancientPoemReciteAppUserAssessmentScoresList)){
                    Map<Integer, BigDecimal> totalScoreMap = ancientPoemReciteAppUserAssessmentScoresList.stream().collect(Collectors.toMap(AncientPoemReciteAppUserAssessmentScores::getPoemId,AncientPoemReciteAppUserAssessmentScores::getTotalScore));
                    for (AncientPoemBusinessRefPageDTO ancientPoemBusinessRefPageDTO : ancientPoemBusinessRefPageDTOList) {
                        ancientPoemBusinessRefPageDTO.setTotalScore(totalScoreMap.get(ancientPoemBusinessRefPageDTO.getAncientPoemId()));
                    }
                }
            }
        }
        return RestResponse.success(page);
    }
}

