package com.dbj.classpal.books.service.api.client.shorts;

import com.dbj.classpal.books.client.api.shorts.ShortUrlInfoApi;
import com.dbj.classpal.books.client.dto.shorts.ShortUrlInfoDTO;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.books.service.biz.shorts.IShortUrlInfoBiz;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className ShortUrlInfoApiImpl
 * @description
 * @date 2025-04-23 14:49
 **/
@RestController
public class ShortUrlInfoApiImpl implements ShortUrlInfoApi {

    @Resource
    private IShortUrlInfoBiz shortUrlInfoBiz;


    @Override
    public RestResponse<ShortUrlInfoDTO> getLongUrl(String shortUrl) throws BusinessException {
        String longUrl = shortUrlInfoBiz.getLongUrl(shortUrl);
        if(StringUtils.isEmpty(longUrl)){
            throw new BusinessException(AppErrorCode.SHORT_INVALID_CODE, AppErrorCode.SHORT_INVALID_MSG);
        }

        ShortUrlInfoDTO shortUrlInfoDTO = new ShortUrlInfoDTO();
        shortUrlInfoDTO.setLongUrl(longUrl);
        return RestResponse.success(shortUrlInfoDTO);
    }
}
