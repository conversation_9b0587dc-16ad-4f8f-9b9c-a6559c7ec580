package com.dbj.classpal.books.service.api.client.audio;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dbj.classpal.books.client.bo.audio.AudioContextInfoAddBO;
import com.dbj.classpal.books.client.bo.audio.AudioContextInfoBO;
import com.dbj.classpal.books.client.bo.audio.AudioGlobalConfigAddBO;
import com.dbj.classpal.books.common.constant.AudioConstants;
import com.dbj.classpal.books.common.enums.audio.AudioIntroStatus;
import com.dbj.classpal.books.service.biz.audio.*;
import com.dbj.classpal.books.service.entity.audio.AudioContextInfo;
import com.dbj.classpal.books.service.entity.audio.AudioGlobalConfig;
import com.dbj.classpal.books.service.entity.audio.AudioIntro;
import com.dbj.classpal.books.service.entity.audio.AudioSpeaker;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/11 9:28
 * @description
 */
@Component
public class AudioSynthesisService {
    @Autowired
    private IAudioContextInfoBiz audioContextInfoBiz;
    @Autowired
    private IAudioIntroBiz audioIntroBiz;
    @Autowired
    private IAudioGlobalConfigBiz audioGlobalConfigBiz;
    @Autowired
    private IAudioSpeakerBiz audioSpeakerBiz;
    /**
     * 保存音频文本
     * @param bo
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveAudioContextInfo(AudioContextInfoBO bo) throws BusinessException {
        if (bo.getAppMaterialId() != null) {
            // 更新音频所属素材中心目录
            AudioIntro audioIntro = new AudioIntro();
            audioIntro.setId(bo.getAudioIntroId());
            audioIntro.setAppMaterialId(bo.getAppMaterialId());
            audioIntroBiz.updateById(audioIntro);
        }

        // 查询是否已有音频文本
        List<AudioContextInfo> infoList = audioContextInfoBiz.lambdaQuery().eq(AudioContextInfo::getAudioIntroId, bo.getAudioIntroId()).list();
        Integer version = 1;
        if (CollectionUtil.isNotEmpty(infoList)) {
            version = infoList.get(0).getVersion() + 1;
            // 删除旧音频文本
            audioContextInfoBiz.remove(Wrappers.lambdaQuery(AudioContextInfo.class).eq(AudioContextInfo::getAudioIntroId, bo.getAudioIntroId()));
        }

        // 全局配置
        if (CollectionUtil.isNotEmpty(bo.getGlobalConfigList())) {
            List<AudioGlobalConfigAddBO> configBOList = bo.getGlobalConfigList();
            // 删除配置
            audioGlobalConfigBiz.remove(Wrappers.lambdaQuery(AudioGlobalConfig.class).eq(AudioGlobalConfig::getAudioIntroId, bo.getAudioIntroId()));
            saveGlobalConfig(configBOList);
        } else {
            audioGlobalConfigBiz.remove(Wrappers.lambdaQuery(AudioGlobalConfig.class).eq(AudioGlobalConfig::getAudioIntroId, bo.getAudioIntroId()));
        }

        AudioSpeaker defaultSpeaker = audioSpeakerBiz.lambdaQuery().eq(AudioSpeaker::getVoice, AudioConstants.ALIYUN_DEFAULT_VOICE).one();
        if (defaultSpeaker == null) {
            throw new BusinessException("默认发音人查询为空");
        }
        // 保存音频文本
        List<AudioContextInfoAddBO> contextList = bo.getContextList();
        List<AudioContextInfo> saveList = new ArrayList<>();
        for (int i = 0; i < contextList.size(); i++) {
            AudioContextInfoAddBO contextBO = contextList.get(i);
            AudioContextInfo contextInfo = new AudioContextInfo();
            BeanUtils.copyProperties(contextBO, contextInfo, "audioHintMusicIds");
            // 发音人如果不传，默认为“xiaoyun”
            if (contextBO.getAudioSpeakerId() == null) {
                contextInfo.setAudioSpeakerId(defaultSpeaker.getId());
            }
            contextInfo.setAudioIntroId(bo.getAudioIntroId());
            contextInfo.setOrderNum((contextBO.getOrderNum() == null ? i + 1 : contextBO.getOrderNum()));
            contextInfo.setVersion(version);
            if (StringUtils.isNotBlank(contextBO.getAudioHintMusicIds())) {
                contextInfo.setAudioHintMusicIds(removeDuplicates(contextBO.getAudioHintMusicIds()));
            }
            saveList.add(contextInfo);
        }
        boolean save = audioContextInfoBiz.saveBatch(saveList);
        Assert.isTrue(save, "保存失败");
    }


    public static String removeDuplicates(String input) {
        return JSON.toJSONString(Arrays.stream(StrUtil.splitToInt(input, ',')).boxed().distinct().toList());
    }

    private void saveGlobalConfig(List<AudioGlobalConfigAddBO> configBOList) {
        configBOList.forEach(conf -> {
            AudioGlobalConfig config = new AudioGlobalConfig();
            BeanUtils.copyProperties(conf, config);
            boolean save = audioGlobalConfigBiz.save(config);
            Assert.isTrue(save, "保存失败");
        });
    }

    /**
     * 更新音频文本状态
     * @param audioIntroId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateFrequency(Integer audioIntroId) {
        AudioIntro audioIntro = new AudioIntro();
        audioIntro.setId(audioIntroId);
        audioIntro.setStatus(AudioIntroStatus.COMPOUNDING.getValue());
        audioIntro.setIsCancel(YesOrNoEnum.NO.getCode());
        boolean update = audioIntroBiz.updateById(audioIntro);
        Assert.isTrue(update, "更新音频文本状态失败");
    }

}
