package com.dbj.classpal.books.service.job.audio;

import com.alibaba.fastjson.JSON;
import com.dbj.classpal.books.common.constant.AudioConstants;
import com.dbj.classpal.books.service.util.audio.DeleteFile;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 清理音频临时文件
 * <AUTHOR>
 * @since 2025-06-25
 */
@Slf4j
@Component
public class AudioCleanTemporaryFileScheduled {

    @XxlJob("audioCleanTemporaryFileScheduled")
    public ReturnT<String> audioCleanTemporaryFileScheduled() throws BusinessException {
        XxlJobHelper.log("开始清理音频临时文件");
        try {
            List<String> dirList = new ArrayList<>();
            dirList.add(AudioConstants.AUDIO_INPUT_DIR);
            dirList.add(AudioConstants.AUDIO_OUTPUT_DIR);
            dirList.add(AudioConstants.WAV_TO_MP3_OUTPUT_DIR);
            XxlJobHelper.log("文件目录为：{}", JSON.toJSONString(dirList));
            dirList.forEach(DeleteFile::deleteDir);
            XxlJobHelper.log("清理完毕！");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error(e.getMessage());
            XxlJobHelper.log("清理音频临时文件失败: {}", e);
            return ReturnT.FAIL;
        }
    }
}
