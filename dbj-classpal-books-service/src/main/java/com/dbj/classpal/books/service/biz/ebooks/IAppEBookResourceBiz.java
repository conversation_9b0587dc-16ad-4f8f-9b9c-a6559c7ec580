package com.dbj.classpal.books.service.biz.ebooks;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookResourceBatchQueryBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookResourceQueryBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookResourceSaveBO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookResourceDTO;
import com.dbj.classpal.books.service.entity.product.AppEBookResource;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 单书资源 业务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-10
 */
public interface IAppEBookResourceBiz extends IService<AppEBookResource> {

    /**
     * 分页查询单书资源
     *
     * @param pageRequest 分页查询参数
     * @return 分页结果
     * @throws BusinessException 业务异常
     */
    Page<AppEBookResourceDTO> page(PageInfo<AppEBookResourceQueryBO> pageRequest) throws BusinessException;

    /**
     * 根据单书ID查询资源列表
     *
     * @param bookId 单书ID
     * @return 资源列表
     * @throws BusinessException 业务异常
     */
    List<AppEBookResourceDTO> listByBookId(Integer bookId) throws BusinessException;

    /**
     * 根据单书ID和资源类型查询资源列表
     *
     * @param bookId        单书ID
     * @param resourceTypes 资源类型
     * @return 资源列表
     * @throws BusinessException 业务异常
     */
    List<AppEBookResourceDTO> listByBookIdAndType(Integer bookId, List<Integer> resourceTypes) throws BusinessException;

    /**
     * 批量根据单书ID列表和资源类型查询资源列表
     *
     * @param bookIds 单书ID列表
     * @param resourceType 资源类型
     * @return 资源列表
     * @throws BusinessException 业务异常
     */
    List<AppEBookResourceDTO> listByBookIdsAndType(List<Integer> bookIds, Integer resourceType) throws BusinessException;

    /**
     * 保存单书资源
     *
     * @param saveBO 保存参数
     * @return 资源ID
     * @throws BusinessException 业务异常
     */
    Integer save(AppEBookResourceSaveBO saveBO) throws BusinessException;

    /**
     * 批量保存单书资源
     *
     * @param saveBOList 保存参数列表
     * @return 是否成功
     * @throws BusinessException 业务异常
     */
    boolean saveBatch(List<AppEBookResourceSaveBO> saveBOList) throws BusinessException;

    /**
     * 删除单书资源
     *
     * @param id 资源ID
     * @return 是否成功
     * @throws BusinessException 业务异常
     */
    boolean delete(Integer id) throws BusinessException;

    /**
     * 根据单书ID删除所有资源
     *
     * @param bookId 单书ID
     * @return 是否成功
     * @throws BusinessException 业务异常
     */
    boolean deleteByBookId(Integer bookId) throws BusinessException;

    /**
     * 分页查询单书图片资源
     *
     * @param pageRequest 查询参数
     * @return 图片资源分页数据
     * @throws BusinessException 业务异常
     */
    Page<AppEBookResourceDTO> pageImages(PageInfo<AppEBookResourceBatchQueryBO> pageRequest) throws BusinessException;

    /**
     * 批量查询书籍资源（按书籍分组）
     *
     * @param queryBO 批量查询参数
     * @return 按书籍ID分组的资源列表
     * @throws BusinessException 业务异常
     */
    Map<Integer, List<AppEBookResourceDTO>> batchQueryByBooks(AppEBookResourceBatchQueryBO queryBO) throws BusinessException;

    /**
     * 批量查询书架资源（按书架分组）
     *
     * @param queryBO 批量查询参数
     * @return 按书架ID分组的资源列表
     * @throws BusinessException 业务异常
     */
    Map<Integer, List<AppEBookResourceDTO>> batchQueryByShelves(AppEBookResourceBatchQueryBO queryBO) throws BusinessException;

    /**
     * 分页查询书城资源
     *
     * @param pageRequest 分页查询参数
     * @return 书城资源分页数据
     * @throws BusinessException 业务异常
     */
    Page<AppEBookResourceDTO> pageStoreResources(PageInfo<AppEBookResourceBatchQueryBO> pageRequest) throws BusinessException;
} 