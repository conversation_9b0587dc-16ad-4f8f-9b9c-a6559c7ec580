package com.dbj.classpal.books.service.biz.books.impl;

import com.dbj.classpal.books.service.entity.books.BooksRankInCodesContentsQuestion;
import com.dbj.classpal.books.service.mapper.books.BooksRankInCodesContentsQuestionMapper;
import com.dbj.classpal.books.service.biz.books.IBooksRankInCodesContentsQuestionBiz;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 图书书内码题库表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class BooksRankInCodesContentsQuestionBizImpl extends ServiceImpl<BooksRankInCodesContentsQuestionMapper, BooksRankInCodesContentsQuestion> implements IBooksRankInCodesContentsQuestionBiz {

}
