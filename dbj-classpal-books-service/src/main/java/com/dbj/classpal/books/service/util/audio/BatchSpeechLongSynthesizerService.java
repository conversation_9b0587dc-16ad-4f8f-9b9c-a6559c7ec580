package com.dbj.classpal.books.service.util.audio;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.nls.client.protocol.NlsClient;
import com.alibaba.nls.client.protocol.OutputFormatEnum;
import com.alibaba.nls.client.protocol.SampleRateEnum;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizer;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizerListener;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizerResponse;
import com.dbj.classpal.books.common.bo.audio.SpeechBO;
import com.dbj.classpal.books.common.constant.AudioConstants;
import com.dbj.classpal.books.common.dto.audio.SpeechDTO;
import com.dbj.classpal.books.common.dto.audio.TaskErrorDTO;
import com.dbj.classpal.framework.redisson.config.RedissonRedisUtils;
import com.dbj.classpal.framework.tts.audio.GetTTSToken;
import com.dbj.classpal.framework.tts.config.AliyunTTSConfig;
import com.dbj.classpal.framework.tts.entity.TTSToken;
import com.dbj.classpal.framework.tts.enums.aliyun.AliyunTtsErrorCodeEnum;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 批量合成音频, 将一长串文本调用语音合成接口
 */
@Slf4j
@Component
public class BatchSpeechLongSynthesizerService {

    @Autowired
    private RedissonRedisUtils redisUtils;

    private String token = "";

    // 通过构造函数注入线程池
    private final ExecutorService threadPool;
    private NlsClient client;
    private AliyunTTSConfig aliyunTtsConfig;

    // 其他成员变量保持不变
    private final Map<String, TaskErrorDTO> errorMsgMap = new ConcurrentHashMap<>();
    private final Map<String, File> audioFileMap = new ConcurrentHashMap<>();
    private final List<SpeechDTO> audioSaveFiles = new CopyOnWriteArrayList<>();
    private final Map<String, AtomicBoolean> taskCompletionStatus = new ConcurrentHashMap<>();
    private final Map<String, Future<?>> taskFutures = new ConcurrentHashMap<>();
    // 添加音频输出目录管理
    private final File audioOutputDir = new File(AudioConstants.AUDIO_OUTPUT_DIR);
    // 每批处理的数量
    private static final int BATCH_SIZE = 2;

    /**
     * Spring Bean 销毁时自动调用，优雅关闭资源
     */
    @PreDestroy
    public void destroy() {
        log.info("【阿里云语音合成器】- 开始销毁，释放资源...");
        // 立即关闭客户端连接
        if (client != null) {
            client.shutdown();
            client = null;
        }
        // 关闭线程池
        shutdown();
    }

    /**
     * 构造函数 初始化线程池
     */
    @Autowired
    public BatchSpeechLongSynthesizerService(@Qualifier("ttsThreadPool") ExecutorService threadPool, AliyunTTSConfig aliyunTtsConfig) {
        this.threadPool = threadPool;
        this.aliyunTtsConfig = aliyunTtsConfig;
        if (StringUtils.isBlank(aliyunTtsConfig.getUrl())) {
            throw new RuntimeException("【阿里云语音合成器】- nacos url is empty！");
        }
        client = new NlsClient(aliyunTtsConfig.getUrl(), token);

    }

    /**
     * 非Spring环境下的构造函数(本地测试)
     */
    public BatchSpeechLongSynthesizerService() {
        client = new NlsClient(aliyunTtsConfig.getUrl(), token);

        // 初始化线程池
        int corePoolSize = 5; // 核心线程数
        int maxPoolSize = 10; // 最大线程数
        long keepAliveTime = 60; // 空闲线程存活时间
        BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>(100); // 任务队列

        // 自定义线程工厂，设置有意义的线程名称
        ThreadFactory threadFactory = new ThreadFactory() {
            private final AtomicInteger threadNumber = new AtomicInteger(1);

            @Override
            public Thread newThread(Runnable r) {
                return new Thread(r, "tts-task-" + threadNumber.getAndIncrement());
            }
        };

        // 自定义拒绝策略
        RejectedExecutionHandler rejectionHandler = new ThreadPoolExecutor.CallerRunsPolicy();

        threadPool = new ThreadPoolExecutor(
                corePoolSize,
                maxPoolSize,
                keepAliveTime,
                TimeUnit.SECONDS,
                workQueue,
                threadFactory,
                rejectionHandler
        );
    }

    /**
     * 动态设置Token（核心改造点）
     */
    public void setToken() {
        try {
            String token;
            Long expire = redisUtils.getExpire(AudioConstants.ALIYUN_TTS_TOKEN_KEY);
            if (expire <= 0) {
                TTSToken accessToken = GetTTSToken.createToken(aliyunTtsConfig.getAccessId(), aliyunTtsConfig.getAccessSecret());
                if (accessToken == null || StringUtils.isBlank(accessToken.getToken())) {
                    throw new RuntimeException("【阿里云语音合成器】- 获取Token失败！");
                }
                token = accessToken.getToken();
                log.info("【阿里云语音合成器】- 获取Token成功, 过期时间：{}", accessToken.getExpireTime());
//                redisUtils.setValue(AudioConstants.ALIYUN_TTS_TOKEN_KEY, token, (int) (accessToken.getExpireTime() > 0 ? accessToken.getExpireTime() : AudioConstants.ALIYUN_TTS_TOKEN_EXPIRE), TimeUnit.SECONDS);
                redisUtils.setValue(AudioConstants.ALIYUN_TTS_TOKEN_KEY, token, AudioConstants.ALIYUN_TTS_TOKEN_EXPIRE, TimeUnit.SECONDS);
            } else {
                token = redisUtils.getValue(AudioConstants.ALIYUN_TTS_TOKEN_KEY);
                log.info("【阿里云语音合成器】- 获取redis Token: {}", token);
            }
            Assert.isFalse(StringUtils.isBlank(token), "【阿里云语音合成器】- Token为空！");
            this.token = token;
            // 重新初始化客户端
            if (client != null) {
                client.shutdown();
            }
            client = new NlsClient(token);
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error("【阿里云语音合成器】- 获取Token失败！", e);
        }

    }

    /**
     * 初始化音频输出目录
     */
    private void initAudioDir() {
        if (!audioOutputDir.exists()) {
            if (audioOutputDir.mkdirs()) {
                log.info("【阿里云语音合成器】- 创建音频输出目录: {}", audioOutputDir.getAbsolutePath());
            } else {
                log.error("【阿里云语音合成器】- 无法创建音频输出目录: {}", audioOutputDir.getAbsolutePath());
            }
        }
    }

    /**
     * 分批处理多个文本的语音合成
     */
    public List<SpeechDTO> processBatch(List<SpeechBO> speechList) {
//        initAudioDir();

        // 最终结果集
//        Map<String, File> finalResult = new HashMap<>();
        List<SpeechDTO> finalResult = new ArrayList<>();

        long batchStartTime = System.currentTimeMillis();
        log.info("【阿里云语音合成器】- 开始批量语音合成，文本数量: {}", speechList.size());

        // 计算批次数
        int totalBatches = (int) Math.ceil((double) speechList.size() / BATCH_SIZE);

        for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
            int fromIndex = batchIndex * BATCH_SIZE;
            int toIndex = Math.min(fromIndex + BATCH_SIZE, speechList.size());

            List<SpeechBO> currentBatch = speechList.subList(fromIndex, toIndex);
            log.info("【阿里云语音合成器】- 开始处理第 {} 批，共 {} 条记录", batchIndex + 1, currentBatch.size());

            // 处理当前批次
//            Map<String, File> batchResult = processSingleBatch(currentBatch);
//            finalResult.putAll(batchResult);
            List<SpeechDTO> batchResult = processSingleBatch(currentBatch);
            finalResult.addAll(batchResult);

            log.info("【阿里云语音合成器】- 第 {} 批处理完成，成功 {} 条", batchIndex + 1, batchResult.size());

            // 重置连接
            resetConnection();
        }

        if (CollectionUtil.isNotEmpty(errorMsgMap)) {
            finalResult.forEach(dto -> {
                if (errorMsgMap.containsKey(dto.getTaskId())) {
                    dto.setErrorMsg(errorMsgMap.get(dto.getTaskId()));
                }
            });
        }

        shutdown();
        // 计算总耗时等统计信息...
        long currentTimeMillis = System.currentTimeMillis();
        log.info("【阿里云语音合成器】- 批量语音合成完成，总耗时: {} 毫秒", currentTimeMillis - batchStartTime);

        return finalResult;
    }

    /**
     * 重置连接
     */
    private void resetConnection() {
        try {
            if (client != null) {
                client.shutdown();
                log.info("【阿里云语音合成器】-已关闭旧的NlsClient连接");
            }
            // 短暂休眠，避免频繁创建连接被限流
            Thread.sleep(500);
            // 重新初始化客户端
            setToken();
            log.info("【阿里云语音合成器】-已重新初始化NlsClient连接");
        } catch (Exception e) {
            log.error("【阿里云语音合成器】-重置连接失败", e);
        }
    }

    /**
     * 批量处理多个文本的语音合成
     * @param speechList 待合成的文本列表
     * @return 包含所有生成音频文件的列表
     */
    public List<SpeechDTO> processSingleBatch(List<SpeechBO> speechList) {
        // 初始化音频输出目录
        initAudioDir();

        // 清空上次的结果
        audioSaveFiles.clear();
        errorMsgMap.clear();
        audioFileMap.clear();
        taskCompletionStatus.clear();
        taskFutures.clear();

        // 设置Token
        setToken();

        // 记录开始时间
        long batchStartTime = System.currentTimeMillis();
        log.info("开始批量语音合成，文本数量: {}", speechList.size());

        // 提交所有任务
        for (SpeechBO bo : speechList) {
            submitSynthesisTask(bo);
        }

        // 等待所有任务完成（带超时）
        boolean allCompleted = waitForAllTasks(AudioConstants.TIMEOUT, TimeUnit.MINUTES);

        // 计算完成率
        int totalTasks = taskCompletionStatus.size();

        // 计算成功率（成功生成音频文件的比例）
        int successCount = audioSaveFiles.size();
        double successRate = (double) successCount / speechList.size() * 100;

        // 记录批次处理结果
        long batchTotalTime = System.currentTimeMillis() - batchStartTime;
        log.info("【阿里云语音合成处理器】- 批量语音合成完成 - 总耗时: {}ms, 总任务数: {}, 成功数: {}, 成功率: {:.2f}%, 所有任务完成: {}",
                batchTotalTime, totalTasks, successCount, successRate, allCompleted);

        // 校验结果一致性
        if (successCount != speechList.size()) {
            log.warn("【阿里云语音合成处理器】- 警告: 成功生成的音频文件数量({})与输入文本数量({})不一致", successCount, speechList.size());
            if (!allCompleted) {
                throw new RuntimeException("【阿里云语音合成处理器】- 批量语音合成未完成，部分任务超时或失败");
            }
        }

        return new ArrayList<>(audioSaveFiles);
    }

    /**
     * 提交语音合成任务到线程池
     */
    private void submitSynthesisTask(SpeechBO bo) {
        try {
            // 生成唯一文件名
            String fileName =  bo.getVoice() + "_" + UUID.randomUUID().toString().replace("-", "") + AudioConstants.BGM_FORMAT_WAV;
            //  TODO 2025/6/26 待完善：测试专用文件名,生成后需注释
//            String fileName = bo.getVoice() + "_" + "template" + AudioConstants.bgm_format_wav;
            File outputFile = new File(audioOutputDir, fileName); // 使用统一目录

            SpeechSynthesizer synthesizer = new SpeechSynthesizer(client, getSynthesizerListener(outputFile, bo));
            synthesizer.setAppKey(aliyunTtsConfig.getAppKey());
            synthesizer.setFormat(OutputFormatEnum.WAV);
            synthesizer.setSampleRate(SampleRateEnum.SAMPLE_RATE_16K);
            synthesizer.setVoice(StringUtils.isEmpty(bo.getVoice()) ? AudioConstants.ALIYUN_DEFAULT_VOICE : bo.getVoice());
            synthesizer.setLongText(bo.getLongText());
            if (bo.getPitchRate() != null) {
                synthesizer.setPitchRate(bo.getPitchRate());
            }
            if (bo.getSpeechRate() != null) {
                synthesizer.setSpeechRate(bo.getSpeechRate());
            }
            if (bo.getVolume() != null) {
                synthesizer.setVolume(bo.getVolume());
            }
            synthesizer.addCustomedParam("enable_subtitle", true);

            // 启动合成
            synthesizer.start();

            // 记录任务信息
            String taskId = synthesizer.getTaskId();
            audioFileMap.put(fileName, outputFile);
            SpeechDTO speechDTO = new SpeechDTO();
            speechDTO.setAudioContextInfoId(bo.getAudioContextInfoId());
            speechDTO.setFile(outputFile);
            speechDTO.setFileName(fileName);
            speechDTO.setTaskId(taskId);
            speechDTO.setFilePath(outputFile.getAbsolutePath());
            audioSaveFiles.add(speechDTO);
            taskCompletionStatus.put(taskId, new AtomicBoolean(false));

            // 提交任务到线程池
            Future<?> future = threadPool.submit(() -> {
                try {
                    log.info("【阿里云语音合成处理器】- {} 开始执行，文本长度: {}", taskId, bo.getLongText().length());
                    synthesizer.waitForComplete();
                    log.info("【阿里云语音合成处理器】- {} 已完成，文件: {}", taskId, outputFile.getAbsolutePath());

                } catch (InterruptedException e) {
                    log.error("【阿里云语音合成处理器】- {} 被中断", taskId, e);
                    Thread.currentThread().interrupt();
                    // 确保中断后关闭资源
                    synthesizer.close();
                } catch (Exception e) {
                    log.error("【阿里云语音合成处理器】- {} 执行异常: {}", taskId, e.getMessage(), e);
                    // 标记任务失败但完成
                    markTaskCompleted(taskId);
                    // 关闭资源
                    synthesizer.close();
                }
            });

            // 保存Future对象用于后续管理
            taskFutures.put(taskId, future);

        } catch (Exception e) {
            // 生成一个虚拟任务ID，确保统计一致性
            String virtualTaskId = AudioConstants.VIRTUAL + UUID.randomUUID().toString().replace("-", "");
            log.error("【阿里云语音合成处理器】- 提交合成任务失败，虚拟任务ID: {}", virtualTaskId, e);
            taskCompletionStatus.put(virtualTaskId, new AtomicBoolean(true));
        }
    }

    /**
     * 创建带有文件信息的监听器
     */
    private SpeechSynthesizerListener getSynthesizerListener(File outputFile, SpeechBO bo) {
        return new SpeechSynthesizerListener() {
            private FileOutputStream fout;
            private boolean firstRecvBinary = true;
            private long taskStartTime = System.currentTimeMillis();
            private String taskId;
            private static final int BUFFER_SIZE = 8192; // 缓冲区大小
            private final byte[] buffer = new byte[BUFFER_SIZE];

            {
                try {
                    fout = new FileOutputStream(outputFile);
                } catch (IOException e) {
                    log.error("【阿里云语音合成处理器】- 创建文件输出流失败", e);
                }
            }

            @Override
            public void onComplete(SpeechSynthesizerResponse response) {
                taskId = response.getTaskId();
                try {
                    fout.flush(); // 确保数据写入磁盘
                    log.info("【阿里云语音合成处理器】- 完成: {}, 文件大小: {}KB", taskId, outputFile.length() / 1024);
                } catch (IOException e) {
                    log.error("【阿里云语音合成处理器】- 刷新文件流失败", e);
                } finally {
                    // 确保流关闭
                    try {
                        fout.close();
                    } catch (IOException e) {
                        log.error("【阿里云语音合成处理器】- 关闭文件流失败", e);
                    }
                }

                // 添加到结果列表
//                audioSaveFiles.add(outputFile);
                markTaskCompleted(taskId);
            }

            @Override
            public void onMessage(ByteBuffer message) {
                try {
                    if (firstRecvBinary) {
                        firstRecvBinary = false;
                        long firstLatency = System.currentTimeMillis() - taskStartTime;
                        log.info("首包延迟: {}ms, 任务ID: {}", firstLatency, taskId);
                    }

                    // 分段读取音频数据，避免大内存占用
                    while (message.hasRemaining()) {
                        int length = Math.min(message.remaining(), BUFFER_SIZE);
                        message.get(buffer, 0, length);
                        fout.write(buffer, 0, length);
                    }
                } catch (IOException e) {
                    log.error("写入音频数据失败", e);
                }
            }

            @Override
            public void onFail(SpeechSynthesizerResponse response) {
                taskId = response.getTaskId();
                log.error("【阿里云语音合成处理器】- 合成失败: task_id={}, status={}, error={}", taskId, response.getStatus(), response.getStatusText());
//                try {
//                    if (fout != null) {
//                        fout.close();
//                        // 删除失败的文件
//                        if (outputFile.exists()) {
//                            outputFile.delete();
//                        }
//                    }
//                } catch (Exception e) {
//                    log.error("【阿里云语音合成处理器】- 关闭失败任务的文件流失败", e);
//                }
                TaskErrorDTO errorDTO = new TaskErrorDTO();
                errorDTO.setAudioContextInfoId(bo.getAudioContextInfoId());
                errorDTO.setStatus(response.getStatus());
                errorDTO.setErrorMessage(response.getStatusText());
                errorDTO.setTaskId(response.getTaskId());
                AliyunTtsErrorCodeEnum aliyunTtsErrorCodeEnum = AliyunTtsErrorCodeEnum.fromCode(errorDTO.getStatus());
                if (aliyunTtsErrorCodeEnum != null) {
                    errorDTO.setReason(aliyunTtsErrorCodeEnum.getReason());
                    errorDTO.setSolution(aliyunTtsErrorCodeEnum.getSolution());
                }
                errorMsgMap.put(taskId, errorDTO);

                // 标记任务完成（即使失败）
                markTaskCompleted(taskId);
            }
        };
    }

    private void markTaskCompleted(String taskId) {
        if (taskId != null && taskCompletionStatus.containsKey(taskId)) {
            taskCompletionStatus.get(taskId).set(true);
            log.info("【阿里云语音合成处理器】- 任务 {} 已标记为完成", taskId);
        }
    }

    /**
     * 等待所有任务完成，带超时机制
     * @return 是否所有任务都成功完成
     */
    private boolean waitForAllTasks(long timeout, TimeUnit unit) {
        try {
            long startTime = System.currentTimeMillis();
            long timeoutMillis = unit.toMillis(timeout);
            log.info("【阿里云语音合成处理器】-开始等待 {} 个任务完成，超时时间: {}ms", taskCompletionStatus.size(), timeoutMillis);

            while (System.currentTimeMillis() - startTime < timeoutMillis) {
                boolean allCompleted = true;
                for (Map.Entry<String, AtomicBoolean> entry : taskCompletionStatus.entrySet()) {
                    if (!entry.getValue().get()) {
                        allCompleted = false;
                        break;
                    }
                }

                if (allCompleted) {
                    log.info("【阿里云语音合成处理器】- 所有任务已完成，耗时: {}ms", System.currentTimeMillis() - startTime);
                    return true;
                }

                // 短暂休眠，避免CPU占用过高
                Thread.sleep(AudioConstants.SLEEP_TIME);
            }

            // 超时处理
            handleTimeoutTasks();
            return false;

        } catch (InterruptedException e) {
            log.error("【阿里云语音合成处理器】- 等待任务完成被中断", e);
            Thread.currentThread().interrupt();
            handleInterruptedTasks();
            return false;
        }
    }

    /**
     * 处理超时任务
     */
    private void handleTimeoutTasks() {
        List<String> incompleteTasks = new ArrayList<>();
        int activeCount = 0;

        if (threadPool instanceof ThreadPoolExecutor) {
            ThreadPoolExecutor tpe = (ThreadPoolExecutor) threadPool;
            activeCount = tpe.getActiveCount();
        }

        for (Map.Entry<String, AtomicBoolean> entry : taskCompletionStatus.entrySet()) {
            if (!entry.getValue().get()) {
                incompleteTasks.add(entry.getKey());

                // 尝试取消未完成的任务
                Future<?> future = taskFutures.get(entry.getKey());
                if (future != null && !future.isDone()) {
                    log.warn("尝试取消超时任务: {}", entry.getKey());
                    future.cancel(true);
                }
            }
        }

        log.error("【阿里云语音合成处理器】-等待超时: 总任务数={}, 未完成={}, 活跃线程数={}",
                taskCompletionStatus.size(), incompleteTasks.size(), activeCount);
    }

    /**
     * 处理被中断的任务
     */
    private void handleInterruptedTasks() {
        // 标记所有任务为完成状态，避免永久等待
        for (Map.Entry<String, AtomicBoolean> entry : taskCompletionStatus.entrySet()) {
            if (!entry.getValue().get()) {
                entry.getValue().set(true);
                log.warn("【阿里云语音合成处理器】- {} 被中断，强制标记为完成", entry.getKey());
            }
        }
    }

    /**
     * 优雅关闭资源
     */
    public void shutdown() {
        // 关闭线程池
        if (threadPool != null && !threadPool.isShutdown()) {
            log.info("【阿里云语音合成处理器】- 开始关闭线程池...");

            // 第一步：停止接受新任务
            threadPool.shutdown();

            try {
                // 第二步：等待已提交任务完成
                if (!threadPool.awaitTermination(AudioConstants.AWAIT_TASK_TIMEOUT, TimeUnit.SECONDS)) {
                    // 第三步：强制终止未完成的任务
                    log.warn("线程池未能在30秒内优雅关闭，开始强制终止...");
                    List<Runnable> pendingTasks = threadPool.shutdownNow();
                    log.warn("已强制终止 {} 个待处理任务", pendingTasks.size());

                    // 第四步：再次等待，给终止任务一些时间
                    if (!threadPool.awaitTermination(AudioConstants.AWAIT_TASK_TIMEOUT, TimeUnit.SECONDS)) {
                        log.error("【阿里云语音合成处理器】- 线程池未能完全关闭");
                    }
                }

                log.info("【阿里云语音合成处理器】- 线程池已成功关闭");
            } catch (InterruptedException e) {
                log.error("【阿里云语音合成处理器】- 关闭线程池被中断", e);
                // 再次调用shutdownNow以确保所有任务被终止
                threadPool.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 关闭客户端连接
        if (client != null) {
            client.shutdown();
        }
    }

    public static void main(String[] args) throws Exception {
    }
}
