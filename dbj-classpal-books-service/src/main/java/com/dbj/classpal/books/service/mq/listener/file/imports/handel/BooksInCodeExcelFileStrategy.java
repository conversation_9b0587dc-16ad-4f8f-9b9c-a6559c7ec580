package com.dbj.classpal.books.service.mq.listener.file.imports.handel;

import com.dbj.classpal.books.common.bo.books.BooksInCodeImportBO;
import com.dbj.classpal.books.common.enums.books.ConnectionTypeEnum;
import com.dbj.classpal.books.service.biz.books.IBooksCompatibilityBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInCodesContentsBiz;
import com.dbj.classpal.books.service.entity.books.BooksCompatibility;
import com.dbj.classpal.books.service.entity.books.BooksRankInCodesContents;
import com.dbj.classpal.framework.utils.bo.SysFileImportExcelBO;
import com.dbj.classpal.framework.utils.util.ImportExcelUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className BooksInCodeExcelFileStrategy
 * @description
 * @date 2025-04-17 15:45
 **/
@Service("booksInCodeExcelFileStrategy")
@Slf4j
public class BooksInCodeExcelFileStrategy extends AdminExcelFileStrategy<BooksInCodeImportBO>{

    @Resource
    private IBooksRankInCodesContentsBiz booksRankInCodesContentsBiz;
    @Resource
    private IBooksCompatibilityBiz bookCompatibilityBiz;

    /**
     * 转换读取出来的文件类
     * @param file
     * @return
     */
    @Override
    public List<BooksInCodeImportBO> convert(File file) {
        try {
            return  ImportExcelUtil.readFile(file, BooksInCodeImportBO.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 根据实际业务处理数据
     * @param importBOList 导入的数据
     * @param fileDomain 文件状态数据
     * @param tmpFileName 临时文件名称
     * @return
     * @throws Exception
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void business(List<BooksInCodeImportBO> importBOList, SysFileImportExcelBO fileDomain, String tmpFileName) throws Exception {
            //修改强调连接和兼容连接
            for(BooksInCodeImportBO bookInCodeImportBO : importBOList){
                Integer contentId = bookInCodeImportBO.getContentId();
                String forcePromotionUrl = bookInCodeImportBO.getForcePromotionUrl();
                if(StringUtils.isNotEmpty(forcePromotionUrl)){
                    booksRankInCodesContentsBiz.lambdaUpdate().eq(BooksRankInCodesContents::getId,contentId).set(BooksRankInCodesContents::getForcePromotionUrl,forcePromotionUrl).update();
                }
                //查询当前的兼容链接的前三条 如果为空则全部新增，否则一一对应 对应不上的做新增
                List<BooksCompatibility> booksCompatibilityList = bookCompatibilityBiz.lambdaQuery().eq(BooksCompatibility::getConnectionType, ConnectionTypeEnum.contents.getCode()).eq(BooksCompatibility::getInternalId,contentId).last("LIMIT 3").list();

                String compatibilityUrl1 = bookInCodeImportBO.getCompatibilityUrl1();
                String compatibilityUrl2 = bookInCodeImportBO.getCompatibilityUrl2();
                String compatibilityUrl3 = bookInCodeImportBO.getCompatibilityUrl3();
                BooksCompatibility booksCompatibility1 = null;
                if(StringUtils.isNotEmpty(compatibilityUrl1)){
                    booksCompatibility1 = new BooksCompatibility();
                    booksCompatibility1.setInternalId(contentId);
                    booksCompatibility1.setConnectionType(ConnectionTypeEnum.contents.getCode());
                    booksCompatibility1.setInternalCode(compatibilityUrl1);
                }
                BooksCompatibility booksCompatibility2 = null;
                if(StringUtils.isNotEmpty(compatibilityUrl2)){
                    booksCompatibility2 = new BooksCompatibility();
                    booksCompatibility2.setInternalId(contentId);
                    booksCompatibility2.setConnectionType(ConnectionTypeEnum.contents.getCode());
                    booksCompatibility2.setInternalCode(compatibilityUrl2);
                }
                BooksCompatibility booksCompatibility3 = null;
                if(StringUtils.isNotEmpty(compatibilityUrl3)){
                    booksCompatibility3 = new BooksCompatibility();
                    booksCompatibility3.setInternalId(contentId);
                    booksCompatibility3.setConnectionType(ConnectionTypeEnum.contents.getCode());
                    booksCompatibility3.setInternalCode(compatibilityUrl3);
                }




                List<BooksCompatibility> addbooksCompatibilityList = new ArrayList<>();
                if(CollectionUtils.isNotEmpty(booksCompatibilityList) ){
                    Integer num = booksCompatibilityList.size();
                    switch(num) {
                        case 1:
                            if(booksCompatibility1 != null){
                                booksCompatibility1.setId(booksCompatibilityList.get(0).getId());
                                addbooksCompatibilityList.add(booksCompatibility1);
                            }
                            if(booksCompatibility2 != null){
                                addbooksCompatibilityList.add(booksCompatibility2);
                            }
                            if(booksCompatibility3 != null){
                                addbooksCompatibilityList.add(booksCompatibility3);
                            }
                            break;
                        case 2:
                            if(booksCompatibility1 != null){
                                booksCompatibility1.setId(booksCompatibilityList.get(0).getId());
                                addbooksCompatibilityList.add(booksCompatibility1);
                            }
                            if(booksCompatibility2 != null){
                                booksCompatibility2.setId(booksCompatibilityList.get(1).getId());
                                addbooksCompatibilityList.add(booksCompatibility2);
                            }
                            if(booksCompatibility3 != null){
                                addbooksCompatibilityList.add(booksCompatibility3);
                            }
                            break;
                        case 3:
                            if(booksCompatibility1 != null){
                                booksCompatibility1.setId(booksCompatibilityList.get(0).getId());
                                addbooksCompatibilityList.add(booksCompatibility1);
                            }
                            if(booksCompatibility2 != null){
                                booksCompatibility2.setId(booksCompatibilityList.get(1).getId());
                                addbooksCompatibilityList.add(booksCompatibility2);
                            }
                            if(booksCompatibility3 != null){
                                booksCompatibility3.setId(booksCompatibilityList.get(2).getId());
                                addbooksCompatibilityList.add(booksCompatibility3);
                            }
                            break;
                        default:
                            break;
                    }
                }else{
                    if(booksCompatibility1 != null){
                        addbooksCompatibilityList.add(booksCompatibility1);
                    }
                    if(booksCompatibility2 != null){
                        addbooksCompatibilityList.add(booksCompatibility2);
                    }
                    if(booksCompatibility3 != null){
                        addbooksCompatibilityList.add(booksCompatibility3);
                    }
                }
                if(CollectionUtils.isNotEmpty(addbooksCompatibilityList)){
                    bookCompatibilityBiz.saveOrUpdateBatch(addbooksCompatibilityList);
                }


            }

    }


    /**
     * 根据实际业务做数据校验,无需校验则直接返回true
     * @param importBOList 数据
     * @param fileDomain 上传文件对象
     * @param errFileName 错误文件名称
     * @return 是否通过校验
     */
    @Override
    public boolean dataCheck(List<BooksInCodeImportBO> importBOList, SysFileImportExcelBO fileDomain, String errFileName) {
        //检查页面id一页面id和册数id是否是对应的不是瞎填的
        List<Integer> contractIds = importBOList.stream().map(BooksInCodeImportBO::getContentId).collect(Collectors.toList());
        List<BooksRankInCodesContents> booksRankInCodesContentsList = booksRankInCodesContentsBiz.listByIds(contractIds);
        Map<Integer, BooksRankInCodesContents> booksRankInCodesContentsMap = new HashMap<>();

        if(CollectionUtils.isNotEmpty(booksRankInCodesContentsList)){
            booksRankInCodesContentsMap = booksRankInCodesContentsList.stream().collect(Collectors.toMap(a -> a.getId(), booksRankInCodesContents -> booksRankInCodesContents));
        }
        int errNum = 0;
        for(BooksInCodeImportBO bookInCodeImportBO : importBOList){
            boolean flag = false;
            BooksRankInCodesContents booksRankInCodesContents = booksRankInCodesContentsMap.get(bookInCodeImportBO.getContentId());
            if(Objects.isNull(booksRankInCodesContents)){
                bookInCodeImportBO.setRowTips("页面id:"+bookInCodeImportBO.getContentId()+"不存在");
                flag = true;
            }
            if(!flag && !Objects.equals(bookInCodeImportBO.getRandId(),booksRankInCodesContents.getRankId())){
                bookInCodeImportBO.setRowTips("页面id:"+ bookInCodeImportBO.getContentId() +"和册数id不对应，请确认后重试");
                flag = true;
            }
            String forcePromotionUrl = bookInCodeImportBO.getForcePromotionUrl();
            String compatibilityUrl1 = bookInCodeImportBO.getCompatibilityUrl1();
            String compatibilityUrl2 = bookInCodeImportBO.getCompatibilityUrl2();
            String compatibilityUrl3 = bookInCodeImportBO.getCompatibilityUrl3();
            if(StringUtils.isEmpty(forcePromotionUrl)
                    && StringUtils.isEmpty(compatibilityUrl1)
                    && StringUtils.isEmpty(compatibilityUrl2)
                    && StringUtils.isEmpty(compatibilityUrl3)){
                bookInCodeImportBO.setRowTips("强跳链接和兼容链接至少填写一个");
                flag = true;
            }


            if(flag){
                errNum++;
            }
        }
        if(errNum > 0){
            handleProcessingFailedBusiness(importBOList,fileDomain,errFileName,errNum);
            return false;
        }
        return true;
    }


}
