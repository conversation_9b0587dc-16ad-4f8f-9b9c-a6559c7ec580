package com.dbj.classpal.books.service.api.client.audio;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dbj.classpal.books.client.api.audio.AudioGlobalConfigApi;

import com.dbj.classpal.books.client.bo.audio.AudioGlobalConfigAddBO;
import com.dbj.classpal.books.client.bo.audio.AudioIntroIdBO;
import com.dbj.classpal.books.client.dto.audio.AudioGlobalConfigDTO;
import com.dbj.classpal.books.service.biz.audio.IAudioGlobalConfigBiz;
import com.dbj.classpal.books.service.entity.audio.AudioGlobalConfig;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 全局配置
 * <AUTHOR>
 * @since 2025-06-27
 */
@RestController
public class AudioGlobalConfigApiImpl implements AudioGlobalConfigApi {

    @Autowired
    private IAudioGlobalConfigBiz audioGlobalConfigBiz;

    @Override
    public RestResponse<List<AudioGlobalConfigDTO>> getGlobalConfig(AudioIntroIdBO bo) throws BusinessException {
        List<AudioGlobalConfigDTO> result = new ArrayList<>();
        List<AudioGlobalConfig> configList = audioGlobalConfigBiz.list(Wrappers.lambdaQuery(AudioGlobalConfig.class).eq(AudioGlobalConfig::getAudioIntroId, bo.getAudioIntroId()));
        if (CollectionUtil.isEmpty(configList)) {
            return RestResponse.success(new ArrayList<>());
        }
        configList.forEach(item -> {
            AudioGlobalConfigDTO dto = new AudioGlobalConfigDTO();
            BeanUtils.copyProperties(item, dto);
            result.add(dto);
        });
        return RestResponse.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<Integer> saveGlobalConfig(List<AudioGlobalConfigAddBO> list) throws BusinessException {
        list.forEach(bo -> {
            AudioGlobalConfig config = new AudioGlobalConfig();
            BeanUtils.copyProperties(bo, config);
            boolean save = audioGlobalConfigBiz.save(config);
            Assert.isTrue(save, "保存失败");
        });
        return RestResponse.success(1);
    }

}
