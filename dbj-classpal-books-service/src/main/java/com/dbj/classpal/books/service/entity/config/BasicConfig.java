package com.dbj.classpal.books.service.entity.config;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("basic_config")
public class BasicConfig extends BizEntity {

    /**
     * 配置名称（如学科名/模块名等)
     */
    private String name;

    /**
     * 封面
     */
    private String icon;

    /**
     * 封面名称
     */
    private String iconName;


    /**
     * 背景图片
     */
    private String background;

    /**
     * 背景图片名称
     */
    private String backgroundName;
    /**
     * 排序权重
     */
    private Integer sortNum;

    /**
     * 关联业务类型（如题库-question、学习模块-study-center、学科- subject等）
     */
    private String bizType;
    /**
     * 关联业务数量
     */
    private Integer bizCount;

    /**
     * 状态0-禁用1-启用
     */
    private Integer status;
}