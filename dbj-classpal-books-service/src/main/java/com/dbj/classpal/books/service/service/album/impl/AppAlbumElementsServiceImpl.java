package com.dbj.classpal.books.service.service.album.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.album.*;
import com.dbj.classpal.books.common.bo.common.CommonIdBO;
import com.dbj.classpal.books.common.bo.common.CommonIdsBO;
import com.dbj.classpal.books.common.dto.album.AppAlbumElementsQueryDTO;
import com.dbj.classpal.books.common.enums.AlbumElementsStatusEnum;
import com.dbj.classpal.books.common.enums.AlbumElementsVisibleEnum;
import com.dbj.classpal.books.service.biz.album.IAppAlbumElementsBiz;
import com.dbj.classpal.books.service.biz.album.IAppAlbumElementsBusinessRefBiz;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBusinessRefBiz;
import com.dbj.classpal.books.service.entity.album.AppAlbumElements;
import com.dbj.classpal.books.service.entity.album.AppAlbumElementsBusinessRef;
import com.dbj.classpal.books.service.entity.material.AppMaterialBusinessRef;
import com.dbj.classpal.books.service.service.album.IAppAlbumElementsService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_ALBUM_ELEMENTS_DELETE_REF_FAIL_CODE;
import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_ALBUM_ELEMENTS_DELETE_REF_FAIL_MSG;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppAlbumElementsServiceImpl
 * Date:     2025-04-15 10:21:26
 * Description: 表名： ,描述： 表
 */
@Service
public class AppAlbumElementsServiceImpl implements IAppAlbumElementsService {

    @Resource
    private IAppAlbumElementsBiz business;
    @Resource
    private IAppMaterialBusinessRefBiz materialBusinessRef;
    @Resource
    private IAppAlbumElementsBusinessRefBiz elementsBusinessRefBiz;

    @Override
    public List<AppAlbumElementsQueryDTO> getAppAlbumElementsList(AppAlbumElementsQueryBO bo) throws BusinessException {
        // 1. 查询数据
        LambdaQueryWrapper<AppAlbumElements> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.hasText(bo.getAlbumTitle()),
                        AppAlbumElements::getAlbumTitle, bo.getAlbumTitle())
                .eq(bo.getAppAlbumMenuId() != null,
                        AppAlbumElements::getAppAlbumMenuId, bo.getAppAlbumMenuId())
                .eq(bo.getAlbumStatus() != null,
                        AppAlbumElements::getAlbumStatus, bo.getAlbumStatus())
                .eq(bo.getAlbumVisible() != null,
                        AppAlbumElements::getAlbumVisible, bo.getAlbumVisible())
                .orderByDesc(AppAlbumElements::getCreateTime,AppAlbumElements::getCreateTime);
        List<AppAlbumElements> list = business.list(wrapper);
        //2. 获取查询的id列表
        Set<Integer> idSet = list.stream().map(AppAlbumElements::getId).collect(Collectors.toSet());
        //3. 查询关联业务map
        Map<Integer,Long>businessRefMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(idSet)){
            businessRefMap = materialBusinessRef.lambdaQuery().in(AppMaterialBusinessRef::getBusinessId,idSet).eq(AppMaterialBusinessRef::getBusinessType,bo.getAlbumType()).list().stream().collect(Collectors.groupingBy(AppMaterialBusinessRef::getBusinessId,Collectors.counting()));
        }

        //4. 赋值关联业务数量
        Map<Integer, Long> finalBusinessRefMap = businessRefMap;
        return list.stream().map(d -> {
            AppAlbumElementsQueryDTO queryDTO = new AppAlbumElementsQueryDTO();
            BeanUtil.copyProperties(d,queryDTO);
            if (finalBusinessRefMap.containsKey(d.getId())) {
                queryDTO.setRefCount(finalBusinessRefMap.get(d.getId()).intValue());
            }else{
                queryDTO.setRefCount(0);
            }
            queryDTO.setAlbumVisibleStr(Objects.requireNonNull(AlbumElementsVisibleEnum.getByCode(d.getAlbumVisible())).getType());
            queryDTO.setAlbumStatusStr(Objects.requireNonNull(AlbumElementsStatusEnum.getByCode(d.getAlbumStatus())).getType());
            return queryDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public Page<AppAlbumElementsQueryDTO> pageInfo(PageInfo<AppAlbumElementsQueryBO> pageRequest) {

        // 1. 构建分页对象
        Page<AppAlbumElements> page = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        // 2. 查询数据
        page = business.pageAlbumElements(page, pageRequest.getData());

        //3. 获取查询的id列表
        Set<Integer> idSet = page.getRecords().stream().map(AppAlbumElements::getId).collect(Collectors.toSet());

        //4. 查询关联业务map
        Map<Integer,Long>businessRefMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(idSet)){
            businessRefMap = materialBusinessRef.lambdaQuery().in(AppMaterialBusinessRef::getBusinessId,idSet).eq(AppMaterialBusinessRef::getBusinessType,pageRequest.getData().getAlbumType()).list().stream().collect(Collectors.groupingBy(AppMaterialBusinessRef::getBusinessId,Collectors.counting()));
        }

        //5. 转换为VO
        Page<AppAlbumElementsQueryDTO> result =(Page<AppAlbumElementsQueryDTO>) page.convert(this::convertToDTO);

        //6. 赋值关联业务数量
        Map<Integer, Long> finalBusinessRefMap = businessRefMap;

        Page<AppAlbumElementsQueryDTO> newPage = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        List<AppAlbumElementsQueryDTO> newRecords = result.getRecords().stream().peek(d -> {
            if (finalBusinessRefMap.containsKey(d.getId())) {
                d.setRefCount(finalBusinessRefMap.get(d.getId()).intValue());
            } else {
                d.setRefCount(0);
            }
            d.setAlbumVisibleStr(Objects.requireNonNull(AlbumElementsVisibleEnum.getByCode(d.getAlbumVisible())).getType());
            d.setAlbumStatusStr(Objects.requireNonNull(AlbumElementsStatusEnum.getByCode(d.getAlbumStatus())).getType());
        }).collect(Collectors.toList());
        newPage.setRecords(newRecords);
        newPage.setTotal(page.getTotal());
        return newPage;
    }

    @Override
    public Boolean saveAppAlbumElements(AppAlbumElementsSaveBO bo) {
        AppAlbumElements appAlbumElements = new AppAlbumElements();
        BeanUtil.copyProperties(bo, appAlbumElements);
        return business.save(appAlbumElements);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteAppAlbumElements(CommonIdsBO bo) throws BusinessException {
        int refCount = elementsBusinessRefBiz.lambdaQuery().in(AppAlbumElementsBusinessRef::getAppAlbumId, bo.getIds()).count().intValue();
        if (refCount>0){
            throw new BusinessException(APP_ALBUM_ELEMENTS_DELETE_REF_FAIL_CODE,APP_ALBUM_ELEMENTS_DELETE_REF_FAIL_MSG);
        }
        materialBusinessRef.lambdaUpdate().in(AppMaterialBusinessRef::getBusinessId, bo.getIds()).remove();
        return business.removeBatchByIds(bo.getIds());
    }

    @Override
    public AppAlbumElementsQueryDTO getAppAlbumElement(CommonIdBO bo) {
        AppAlbumElementsQueryDTO queryDTO = new AppAlbumElementsQueryDTO();
        AppAlbumElements byId = business.getById(bo.getId());
        BeanUtil.copyProperties(byId, queryDTO);
        return queryDTO;
    }

    @Override
    public Boolean updateAppAlbumElement(AppAlbumElementsUpdateBO bo) {
        AppAlbumElements appAlbumElements = new AppAlbumElements();
        BeanUtil.copyProperties(bo, appAlbumElements);
        return business.updateById(appAlbumElements);
    }

    @Override
    public Boolean updateAppAlbumElementTitle(AppAlbumElementsUpdateBO bo) {
        return business.lambdaUpdate().eq(AppAlbumElements::getId,bo.getId()).set(AppAlbumElements::getAlbumTitle,bo.getAlbumTitle()).update();
    }

    @Override
    public Boolean updateAppAlbumElementRemark(AppAlbumElementsUpdateBO bo) {
        return business.lambdaUpdate().eq(AppAlbumElements::getId,bo.getId()).set(AppAlbumElements::getAlbumRemark,bo.getAlbumRemark()).update();
    }

    @Override
    public Boolean updateAppAlbumElementVisible(AppAlbumElementsUpdateVisibleBO bo) {
        return business.lambdaUpdate().eq(AppAlbumElements::getId,bo.getId()).set(AppAlbumElements::getAlbumVisible,bo.getAlbumVisible()).update();
    }

    @Override
    public Boolean updateAppAlbumElementStatus(AppAlbumElementsUpdateStatusBO bo) {
        return business.lambdaUpdate().eq(AppAlbumElements::getId,bo.getId()).set(AppAlbumElements::getAlbumStatus,bo.getAlbumStatus()).update();
    }

    @Override
    public Boolean updateAppAlbumElementCover(AppAlbumElementsUpdateBO bo) {
        return business.lambdaUpdate().eq(AppAlbumElements::getId,bo.getId()).set(AppAlbumElements::getAlbumCover,bo.getAlbumCover()).update();
    }


    private AppAlbumElementsQueryDTO convertToDTO(AppAlbumElements appAlbumElements) {
        AppAlbumElementsQueryDTO vo = new AppAlbumElementsQueryDTO();
        BeanUtil.copyProperties(appAlbumElements, vo);
        return vo;
    }
}
