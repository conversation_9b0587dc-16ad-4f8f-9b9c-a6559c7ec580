package com.dbj.classpal.books.service.entity.paper;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户试卷信息
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_user_paper_info")
@Schema(description = "用户试卷信息")
public class AppUserPaperInfo extends BizEntity implements Serializable {


    /**
     * 业务类型
     */
    @Schema(description = "业务类型 1内容管理-音频专辑 2内容管理-视频专辑 3图书管理-图书资源 4图书管理-题库 5图书管理-音频专辑 6图书管理-视频专辑",requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer businessType;

    /**
     * 业务id
     */
    @Schema(description = "业务id")
    private Integer businessId;

    /**
     * 试卷名称
     */
    @Schema(description = "试卷名称")
    private String paperName;

    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private Integer appUserId;

    /**
     * 是否提交 0 否 1是
     */
    @Schema(description = "是否提交 0 否 1是")
    private Integer isSubmit;
    /**
     * 题目顺序，逗号分隔的题目ID
     */
    @Schema(description = "题目顺序，逗号分隔的题目ID")
    private String questionOrder;

    /**
     * 是否启用 1-是 0-否
     */
    @Schema(description = "是否启用 1-是 0-否")
    private Integer status;
} 