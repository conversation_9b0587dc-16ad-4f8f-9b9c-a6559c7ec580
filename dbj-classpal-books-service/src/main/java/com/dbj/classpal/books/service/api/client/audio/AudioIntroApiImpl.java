package com.dbj.classpal.books.service.api.client.audio;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.audio.AudioIntroApi;
import com.dbj.classpal.books.client.bo.audio.*;
import com.dbj.classpal.books.client.dto.audio.AudioClassifyPathDTO;
import com.dbj.classpal.books.client.dto.audio.AudioIntroDTO;
import com.dbj.classpal.books.client.dto.material.AppMaterialPathDTO;
import com.dbj.classpal.books.common.enums.audio.AudioGlobalConfigTypeEnum;
import com.dbj.classpal.books.common.enums.audio.AudioIntroStatus;
import com.dbj.classpal.books.service.biz.audio.IAudioClassifyBiz;
import com.dbj.classpal.books.service.biz.audio.IAudioContextInfoBiz;
import com.dbj.classpal.books.service.biz.audio.IAudioGlobalConfigBiz;
import com.dbj.classpal.books.service.biz.audio.IAudioIntroBiz;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBiz;
import com.dbj.classpal.books.service.entity.audio.AudioContextInfo;
import com.dbj.classpal.books.service.entity.audio.AudioGlobalConfig;
import com.dbj.classpal.books.service.entity.audio.AudioIntro;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.commons.utils.Assert;
import com.dbj.classpal.framework.utils.util.TreeUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/6/30 10:57
 */
@RestController
@RequiredArgsConstructor
public class AudioIntroApiImpl implements AudioIntroApi {

    private final IAudioIntroBiz audioIntroBiz;
    private final IAppMaterialBiz appMaterialBiz;
    private final IAudioClassifyBiz audioClassifyBiz;
    private final IAudioContextInfoBiz audioContextInfoBiz;
    private final IAudioGlobalConfigBiz audioGlobalConfigBiz;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RestResponse<Boolean> save(AudioIntroSaveBO bo) throws BusinessException {
        if (bo.getId() == null) {
            Assert.notNull(bo.getAudioClassifyId(), "分类id不能为空");
        }
        AudioIntro intro = new AudioIntro()
                .setName(bo.getName())
                .setAudioClassifyId(bo.getAudioClassifyId())
                .setStatus(bo.getId() == null ? AudioIntroStatus.WAIT_COMPOUND.getValue() : null);
        intro.setId(bo.getId());
        return RestResponse.success(audioIntroBiz.saveOrUpdate(intro));
    }

    @Override
    public RestResponse<Page<AudioIntroDTO>> page(PageInfo<AudioIntroQueryBO> pageInfo) throws BusinessException {
        AudioIntroQueryBO bo = pageInfo.getData();
        // 查询分类下子分类
        Set<Integer> children = audioClassifyBiz.getChildren(Set.of(bo.getAudioClassifyId()));
        IPage<AudioIntroDTO> page = audioIntroBiz.lambdaQuery()
                .in(CollUtil.isNotEmpty(children), AudioIntro::getAudioClassifyId, children)
                .like(StrUtil.isNotEmpty(bo.getName()), AudioIntro::getName, bo.getName())
                .orderByDesc(AudioIntro::getUpdateTime)
                .page(pageInfo.getPage())
                .convert(e -> BeanUtil.copyProperties(e, AudioIntroDTO.class));
        List<AudioIntroDTO> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return RestResponse.success((Page<AudioIntroDTO>) page);
        }

        List<Integer> materialIds = records.stream().map(AudioIntroDTO::getAppMaterialId).filter(Objects::nonNull).distinct().toList();
        Map<Integer, AppMaterialPathDTO> materialPathMap = new HashMap<>();
        if (CollUtil.isNotEmpty(materialIds)) {
            // 素材
            List<AppMaterialPathDTO> materialPathList = appMaterialBiz.getPathList(materialIds);
            materialPathMap = materialPathList.stream().collect(Collectors.toMap(AppMaterialPathDTO::getId, Function.identity()));
        }

        List<Integer> ids = records.stream().map(AudioIntroDTO::getId).toList();
        // 分类
        List<AudioClassifyPathDTO> classifyPathList = audioClassifyBiz.getPathList(ids);
        Map<Integer, List<AudioClassifyPathDTO>> classifyPathMap = classifyPathList.stream()
                .collect(Collectors.groupingBy(AudioClassifyPathDTO::getAudioIntroId));

        // 音频配置信息
        List<AudioGlobalConfig> configList = audioGlobalConfigBiz.lambdaQuery()
                .select(AudioGlobalConfig::getId, AudioGlobalConfig::getAudioIntroId)
                .eq(AudioGlobalConfig::getAudioType, AudioGlobalConfigTypeEnum.BACKGROUND.getCode())
                .in(AudioGlobalConfig::getAudioIntroId, ids)
                .list();
        Map<Integer, Integer> configMap = configList.stream().collect(Collectors.toMap(AudioGlobalConfig::getAudioIntroId, AudioGlobalConfig::getId));

        for (AudioIntroDTO record : records) {
            record.setIsBackgroundMusic(configMap.containsKey(record.getId()));
            record.setMaterialPathList(convertMaterial(record.getAppMaterialId(), materialPathMap));
            record.setClassifyPathList(TreeUtil.convertTree(classifyPathMap.get(record.getId())));
        }
        return RestResponse.success((Page<AudioIntroDTO>) page);
    }

    private List<AppMaterialPathDTO> convertMaterial(Integer materialId, Map<Integer, AppMaterialPathDTO> materialPathMap) {
        List<AppMaterialPathDTO> pathList = new ArrayList<>();
        AppMaterialPathDTO current = materialPathMap.get(materialId);
        while (current != null) {
            pathList.add(current);
            if (current.getParentId() == null) {
                break;
            }
            current = materialPathMap.get(current.getParentId());
        }
        return TreeUtil.convertTree(pathList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RestResponse<Boolean> remove(AudioIntroDelBO bo) throws BusinessException {
        boolean exists = audioIntroBiz.lambdaQuery()
                .in(AudioIntro::getId, bo.getIds())
                .eq(AudioIntro::getStatus, AudioIntroStatus.COMPOUNDING.getValue())
                .exists();
        Assert.isFalse(exists, "存在合成中的任务，请取消后重试");

        audioIntroBiz.removeBatchByIds(bo.getIds());

        audioContextInfoBiz.remove(Wrappers.lambdaQuery(AudioContextInfo.class)
                .in(AudioContextInfo::getAudioIntroId, bo.getIds()));

        return RestResponse.success();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RestResponse<Boolean> move(AudioIntroMoveBO bo) throws BusinessException {
        boolean exists = audioIntroBiz.lambdaQuery()
                .in(AudioIntro::getId, bo.getIds())
                .eq(AudioIntro::getStatus, AudioIntroStatus.COMPOUNDING.getValue())
                .exists();
        Assert.isFalse(exists, "存在合成中的任务，请取消后重试");
        return RestResponse.success(audioIntroBiz.lambdaUpdate()
                .set(AudioIntro::getAudioClassifyId, bo.getAudioClassifyId())
                .in(AudioIntro::getId, bo.getIds())
                .update());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RestResponse<Boolean> copy(AudioIntroCopyBO bo) throws BusinessException {
        List<AudioIntro> list = audioIntroBiz.listByIds(bo.getIds());
        Assert.isFalse(list.stream().anyMatch(e -> AudioIntroStatus.COMPOUNDING.getValue().equals(e.getStatus())),
                "存在合成中的任务，请取消后重试");
        List<Integer> ids = list.stream().map(AudioIntro::getId).toList();

        List<AudioIntro> copyIntroList = list.stream()
                .map(e -> new AudioIntro()
                        .setOriginId(e.getId())
                        .setName(e.getName())
                        .setAudioClassifyId(bo.getAudioClassifyId())
                        .setStatus(AudioIntroStatus.WAIT_COMPOUND.getValue()))
                .collect(Collectors.toList());

        Assert.isTrue(audioIntroBiz.saveBatch(copyIntroList));

        // id对应关系
        Map<Integer, Integer> idMap = copyIntroList.stream()
                .collect(Collectors.toMap(AudioIntro::getOriginId, AudioIntro::getId));

        List<AudioContextInfo> audioContextInfoList = audioContextInfoBiz.lambdaQuery()
                .in(AudioContextInfo::getAudioIntroId, ids)
                .list();
        if (CollUtil.isNotEmpty(audioContextInfoList)) {
            List<AudioContextInfo> copyContextInfoList = BeanUtil.copyToList(audioContextInfoList,
                    AudioContextInfo.class, AudioIntroCopyBO.COPY_OPTIONS);
            copyContextInfoList.forEach(e -> e.setAudioIntroId(idMap.get(e.getAudioIntroId())));
            Assert.isTrue(audioContextInfoBiz.saveBatch(copyContextInfoList));
        }

        List<AudioGlobalConfig> audioGlobalConfigList = audioGlobalConfigBiz.lambdaQuery()
                .in(AudioGlobalConfig::getAudioIntroId, ids)
                .list();
        if (CollUtil.isNotEmpty(audioGlobalConfigList)) {
            List<AudioGlobalConfig> copyGlobalConfigList = BeanUtil.copyToList(audioGlobalConfigList,
                    AudioGlobalConfig.class, AudioIntroCopyBO.COPY_OPTIONS);
            copyGlobalConfigList.forEach(e -> e.setAudioIntroId(idMap.get(e.getAudioIntroId())));
            Assert.isTrue(audioGlobalConfigBiz.saveBatch(copyGlobalConfigList));
        }
        return RestResponse.success();
    }

    @Override
    public RestResponse<AudioIntroDTO> getDetails(AudioIntroIdBO bo) throws BusinessException {
        AudioIntro audioIntro = audioIntroBiz.lambdaQuery().eq(AudioIntro::getId, bo.getAudioIntroId()).one();
        Assert.isFalse(audioIntro == null, "音频简介不存在!");
        AudioIntroDTO dto = new AudioIntroDTO();
        BeanUtils.copyProperties(audioIntro, dto);
        return RestResponse.success(dto);
    }
}
