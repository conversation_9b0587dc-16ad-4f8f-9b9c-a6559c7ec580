package com.dbj.classpal.books.service.api.client.audio;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dbj.classpal.books.client.api.audio.AudioContextInfoApi;
import com.dbj.classpal.books.client.bo.audio.*;
import com.dbj.classpal.books.client.dto.audio.*;
import com.dbj.classpal.books.common.bo.audio.AudioIntroMQBO;
import com.dbj.classpal.books.common.bo.audio.SpeechBO;
import com.dbj.classpal.books.common.constant.AudioConstants;
import com.dbj.classpal.books.common.constant.ExchangeConstant;
import com.dbj.classpal.books.common.constant.RedisKeyConstants;
import com.dbj.classpal.books.common.constant.RoutingKeyConstant;
import com.dbj.classpal.books.common.dto.audio.AudioTrialUseGlobalConfigDTO;
import com.dbj.classpal.books.common.dto.audio.SpeechDTO;
import com.dbj.classpal.books.common.dto.audio.TaskErrorDTO;
import com.dbj.classpal.books.common.enums.audio.AudioBgmModelEnum;
import com.dbj.classpal.books.common.enums.audio.AudioGlobalConfigAudioType;
import com.dbj.classpal.books.common.enums.audio.AudioIntroStatus;
import com.dbj.classpal.books.common.enums.audio.AudioSpeakerTypeEnum;
import com.dbj.classpal.books.service.biz.audio.*;
import com.dbj.classpal.books.service.entity.audio.*;
import com.dbj.classpal.books.service.util.audio.*;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import com.dbj.classpal.framework.mq.pool.DbjRabbitTemplate;
import com.dbj.classpal.framework.redisson.config.RedissonRedisUtils;
import com.dbj.classpal.framework.tts.config.AliyunTTSConfig;
import com.dbj.classpal.framework.tts.enums.aliyun.AliyunTtsErrorCodeEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

/**
 * 音频文本语音合成
 * <AUTHOR>
 * @since 2025-06-25
 */
@RestController
public class AudioContextInfoApiImpl implements AudioContextInfoApi {
    private static final Logger log = LoggerFactory.getLogger(AudioContextInfoApiImpl.class);
    @Autowired
    private IAudioContextInfoBiz audioContextInfoBiz;
    @Autowired
    private IAudioIntroBiz audioIntroBiz;
    @Autowired
    private IAudioGlobalConfigBiz audioGlobalConfigBiz;
    @Autowired
    private IAudioSpeakerBiz audioSpeakerBiz;
    @Autowired
    private IAudioEmotionClassifyConfigBiz audioEmotionClassifyConfigBiz;
    @Autowired
    private RedissonRedisUtils redissonRedisUtils;
    @Autowired
    private DbjRabbitTemplate dbjRabbitTemplate;
    @Autowired
    private BatchSpeechLongSynthesizerService synthesizerService;
    @Autowired
    private SpeechLongSynthesizer speechLongSynthesizer;
    @Autowired
    private AudioUploadOSS audioUploadOSS;
    @Autowired
    private AliyunTTSConfig aliyunTtsConfig;
    @Autowired
    private AudioSynthesisService audioSynthesisService;
    @Autowired
    private IAudioHintMusicBiz audioHintMusicBiz;
    @Autowired
    private IAudioContextHintMusicBiz audioContextHintMusicBiz;

    @Override
    public RestResponse<Integer> save(AudioContextInfoBO bo) throws BusinessException {
        // 分布式锁
        String lockKey = MessageFormat.format(RedisKeyConstants.DBJ_AUDIO_SAVE_LOCK_KEY, bo.getAudioIntroId());
        Lock lock = redissonRedisUtils.getLock(lockKey);
        try {
            if (lock.tryLock(RedisKeyConstants.LOCK_TIME_OUT, TimeUnit.SECONDS)) {
                if (CollectionUtil.isEmpty(bo.getContextList())) {
                    throw new BusinessException("音频文本详情不能为空");
                }
                audioSynthesisService.saveAudioContextInfo(bo);
            }

        } catch (Exception e) {
            log.error("【保存音频文本】分布式锁异常：{}", e.getMessage());
            throw new BusinessException(e.getMessage());
        } finally {
            try {
                log.info("【保存音频文本】释放锁");
                lock.unlock();
            } catch (Exception e) {
                log.error(e.getMessage());
                log.error("【保存音频文本】 - 释放锁异常：{}", e.getMessage());
            }
        }
        return RestResponse.success(1);
    }

    @Override
    public RestResponse<Integer> synthesis(AudioContextInfoBO bo) throws BusinessException {
        // 分布式锁
        String lockKey = MessageFormat.format(RedisKeyConstants.DBJ_AUDIO_SYNTHESIS_LOCK_KEY, bo.getAudioIntroId());
        Lock lock = redissonRedisUtils.getLock(lockKey);
        try {
            if (lock.tryLock(RedisKeyConstants.LOCK_TIME_OUT, TimeUnit.SECONDS)) {
                // 先保存文本信息
                audioSynthesisService.saveAudioContextInfo(bo);
                // 更新音频文本合成频次
                audioSynthesisService.updateFrequency(bo.getAudioIntroId());

                // 发送MQ消息异步合成音频
                AudioIntroMQBO mqBO = new AudioIntroMQBO();
                mqBO.setAudioIntroId(bo.getAudioIntroId());
                sendMQ(mqBO);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error("【合成音频】异常：{}", e.getMessage());
            throw new BusinessException("【合成音频】异常："+ e.getMessage());
        } finally {
            try {
                log.info("【合成音频】释放锁");
                lock.unlock();
            } catch (Exception e) {
                log.error(e.getMessage());
                log.error("【合成音频】 - 释放锁异常：{}", e.getMessage());
            }
        }
        return RestResponse.success(1);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<Integer> resynthesis(AudioIntroIdBO bo) throws BusinessException {
        // 分布式锁
        String lockKey = MessageFormat.format(RedisKeyConstants.DBJ_AUDIO_RESYNTHESIS_LOCK_KEY, bo.getAudioIntroId());
        Lock lock = redissonRedisUtils.getLock(lockKey);
        try {
            if (lock.tryLock(RedisKeyConstants.LOCK_TIME_OUT, TimeUnit.SECONDS)) {
                AudioIntro audioIntro = audioIntroBiz.lambdaQuery().eq(AudioIntro::getId, bo.getAudioIntroId()).one();
                Assert.isFalse(audioIntro == null, "音频简介不存在");
                Assert.isFalse(audioIntro.getAppMaterialId() == null, "音频未选择存储路径");

                // 校验是否存在音频文本
                List<AudioContextInfo> infoList = audioContextInfoBiz.lambdaQuery().eq(AudioContextInfo::getAudioIntroId, bo.getAudioIntroId()).list();
                Assert.isFalse(CollectionUtil.isEmpty(infoList), "请在完成编辑后进行合成");
                // 更新音频文本合成频次
                audioSynthesisService.updateFrequency(bo.getAudioIntroId());

                // 发送MQ消息异步合成音频
                AudioIntroMQBO mqBO = new AudioIntroMQBO();
                mqBO.setAudioIntroId(bo.getAudioIntroId());
                sendMQ(mqBO);
            }
        }catch (Exception e){
            log.error("【重新合成/立即合成音频失败】 - 原因是：{}", e.getMessage() );
            throw new BusinessException(e.getMessage());
        } finally {
            try {
                log.info("【重新合成/立即合成音频】释放锁");
                lock.unlock();
            } catch (Exception e) {
                log.error(e.getMessage());
                log.error("【重新合成/立即合成音频】 - 释放锁异常：{}", e.getMessage());
            }
        }
        return RestResponse.success(1);
    }

    private void sendMQ(AudioIntroMQBO mqBO) throws BusinessException {
        try {
            dbjRabbitTemplate.sendExchangeEntityMessage(mqBO, ExchangeConstant.CLASSPAL_BOOKS_EXCHANGE, RoutingKeyConstant.AUDIO_PRODUCTION_ROUTING_KEY);
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error("【重新合成/立即合成音频】 - 发送MQ异常：{}", e.getMessage());
            throw new BusinessException(e.getMessage());
        }
    }




    @Override
    public RestResponse<AudioContextInfoListDTO> getContextInfoList(AudioIntroIdBO bo) throws BusinessException {
        AudioContextInfoListDTO result = new AudioContextInfoListDTO();
        result.setAudioIntroId(bo.getAudioIntroId());

        AudioIntro audioIntro = audioIntroBiz.lambdaQuery().eq(AudioIntro::getId, bo.getAudioIntroId()).one();
        Assert.isFalse(audioIntro == null, "音频简介不存在");

        result.setAppMaterialId(audioIntro.getAppMaterialId());

        // 查询音频文本列表
        List<AudioContextInfo> contextList = audioContextInfoBiz.lambdaQuery().eq(AudioContextInfo::getAudioIntroId, bo.getAudioIntroId()).list();

        // 查询全局配置
        List<AudioGlobalConfig> globalConfigList = audioGlobalConfigBiz.lambdaQuery().eq(AudioGlobalConfig::getAudioIntroId, bo.getAudioIntroId()).list();
        if (CollectionUtil.isNotEmpty(globalConfigList)) {
            List<AudioGlobalConfigDTO> configDTOList = new ArrayList<>();
            for (AudioGlobalConfig config : globalConfigList) {
                AudioGlobalConfigDTO configDTO = new AudioGlobalConfigDTO();
                BeanUtils.copyProperties(config, configDTO);
                configDTOList.add(configDTO);
            }
            result.setGlobalConfigList(configDTOList);
        }

        if (CollectionUtil.isNotEmpty(contextList)) {
            List<AudioContextInfoDTO> contextDTOList = new ArrayList<>();
            // 查询发音人
            Set<Integer> speakerIds = contextList.stream().map(AudioContextInfo::getAudioSpeakerId).collect(Collectors.toSet());
            List<AudioSpeaker> speakerList = audioSpeakerBiz.lambdaQuery().in(AudioSpeaker::getId, speakerIds).list();
            Assert.isFalse(CollectionUtil.isEmpty(speakerList), "发音人不存在!");

            Map<Integer, AudioSpeaker> speakerMaps = speakerList.stream().collect(Collectors.toMap(AudioSpeaker::getId, v -> v));
            boolean isEmotion = speakerList.stream().anyMatch(v -> v.getVoiceType().equals(AudioSpeakerTypeEnum.EMOTION.getCode()));
            Map<Integer, List<AudioEmotionClassifyConfig>> emotionMaps = new HashMap<>();
            if (isEmotion) {
                // 查询发音人多情感
                Set<Integer> speakerEmotionIds = speakerList.stream().filter(v -> v.getVoiceType().equals(AudioSpeakerTypeEnum.EMOTION.getCode()))
                        .map(AudioSpeaker::getId).collect(Collectors.toSet());
                List<AudioEmotionClassifyConfig> emotionList = audioEmotionClassifyConfigBiz.lambdaQuery()
                        .in(AudioEmotionClassifyConfig::getAudioSpeakerId, speakerEmotionIds).list();
                Assert.isFalse(CollectionUtil.isEmpty(emotionList), "查询多情感分类配置为空!");
                emotionMaps = emotionList.stream().collect(Collectors.groupingBy(AudioEmotionClassifyConfig::getAudioSpeakerId));
            }
            for (AudioContextInfo contextDTO : contextList) {
                AudioContextInfoDTO dto = new AudioContextInfoDTO();
                BeanUtils.copyProperties(contextDTO, dto);
                AudioSpeakerDTO speakerDTO = new AudioSpeakerDTO();
                if (speakerMaps.containsKey(contextDTO.getAudioSpeakerId())) {
                    // 发音人
                    AudioSpeaker audioSpeaker = speakerMaps.get(contextDTO.getAudioSpeakerId());
                    BeanUtils.copyProperties(audioSpeaker, speakerDTO);
                    if (CollectionUtil.isNotEmpty(emotionMaps) && emotionMaps.containsKey(audioSpeaker.getId())) {
                        // 多情感
                        List<AudioEmotionClassifyConfig> emotionsList = emotionMaps.get(audioSpeaker.getId());
                        List<AudioEmotionClassifyConfigDTO> emotionDTOList = new ArrayList<>();
                        emotionsList.forEach(emotion -> {
                            AudioEmotionClassifyConfigDTO emotionDTO = new AudioEmotionClassifyConfigDTO();
                            BeanUtils.copyProperties(emotion, emotionDTO);
                            emotionDTOList.add(emotionDTO);
                        });
                        speakerDTO.setEmotionList(emotionDTOList);
                    }
                }
                dto.setSpeakerDTO(speakerDTO);
                contextDTOList.add(dto);
            }
            result.setContextList(contextDTOList);
        }

        return RestResponse.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<Integer> cancel(AudioIntroIdBO bo) throws BusinessException {
        // 更新音频简介状态为取消
        boolean update = audioIntroBiz.lambdaUpdate().eq(AudioIntro::getId, bo.getAudioIntroId())
                .set(AudioIntro::getIsCancel, YesOrNoEnum.YES.getCode())
                .set(AudioIntro::getStatus, AudioIntroStatus.WAIT_COMPOUND.getValue()).update();
        Assert.isTrue(update, "取消失败");
        return RestResponse.success(1);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<AudioSynthesizerTaskInfoDTO> submitSynthesis(AudioTrialUseBO bo) throws BusinessException {
        // 分布式锁
        String lockKey = MessageFormat.format(RedisKeyConstants.DBJ_AUDIO_TRIAL_USE_LOCK_KEY, bo.getAudioIntroId());
        Lock lock = redissonRedisUtils.getLock(lockKey);
        // 同步生成任务ID
        String taskId = UUID.randomUUID().toString().replace("-", "");
        AudioSynthesizerTaskInfoDTO dto = new AudioSynthesizerTaskInfoDTO();
        try {
            if (lock.tryLock(RedisKeyConstants.LOCK_TIME_OUT, TimeUnit.SECONDS)) {
                // 异步处理音频合成，不阻塞当前请求
                CompletableFuture.runAsync(() -> {
                    try {
                        SpeechBO speechBO = new SpeechBO();
                        speechBO.setLongText(bo.getText());
                        speechBO.setVoice(StringUtils.isEmpty(bo.getVoice()) ? AudioConstants.ALIYUN_DEFAULT_VOICE : bo.getVoice());
                        speechBO.setVolume(bo.getVolume());
                        speechBO.setPitchRate(bo.getPitchRate());
                        speechBO.setSpeechRate(bo.getSpeechRate());
                        // 不需要分布式锁，后台异步处理
//                        List<SpeechDTO> speechList = synthesizerService.processBatch(Collections.singletonList(speechBO));
                        SpeechDTO speechDTO = speechLongSynthesizer.process(speechBO);
                        if (speechDTO == null) {
                            throw new BusinessException("试听音频合成失败!");
                        }
//                        SpeechDTO speechDTO = speechDTO.get(0);
                        AudioGlobalConfigAddBO audioGlobalConfig = bo.getAudioGlobalConfig();
                        if (audioGlobalConfig != null && audioGlobalConfig.getAudioType() !=  null && audioGlobalConfig.getAudioBackgroundId() != null) {
                            AudioTrialUseGlobalConfigDTO trailUsrConfig = new AudioTrialUseGlobalConfigDTO();
                            BeanUtils.copyProperties(audioGlobalConfig, trailUsrConfig);
                            speechDTO.setGlobalConfig(trailUsrConfig);
                        }
                        redissonRedisUtils.setValue(RedisKeyConstants.ALIYUN_TRIAL_USE_TASK_ID_KEY + taskId, JSON.toJSONString(speechDTO), AudioConstants.ALIYUN_TRIAL_USE_TASK_ID_EXPIRE, TimeUnit.SECONDS);
                    } catch (Exception e) {
                        log.error("音频合成异常, taskId: {}", taskId, e);
                        // 可以添加失败回调逻辑
                    }
                });
            }
            redissonRedisUtils.setValue(RedisKeyConstants.ALIYUN_TRIAL_USE_TASK_ID_KEY + taskId, "", AudioConstants.ALIYUN_TRIAL_USE_TASK_ID_EXPIRE, TimeUnit.SECONDS);
            dto.setTaskId(taskId);
            return RestResponse.success(dto);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BusinessException("任务提交失败:"+ e.getMessage());
        }finally {
            try {
                lock.unlock();
            } catch (Exception e) {
                log.error(e.getMessage());
                log.error("试听音频 - 释放锁异常：{}", e.getMessage());
            }
        }
    }

    @Override
    public RestResponse<AudioSynthesizerTaskInfoDTO> getTaskInfo(AudioTaskBO bo) throws BusinessException {
        AudioSynthesizerTaskInfoDTO result = new AudioSynthesizerTaskInfoDTO();
        // 判断任务是否已生成ossurl，防止重复上传
        String audioUrl = redissonRedisUtils.getValue(RedisKeyConstants.ALIYUN_TRIAL_USE_OSS_URL_KEY + bo.getTaskId());
        if (StringUtils.isNotEmpty(audioUrl)) {
            result.setAudioUrl(audioUrl);
            result.setTaskId(bo.getTaskId());
            result.setTaskStatus(AudioSynthesizerTaskInfoDTO.TaskStatusEnum.COMPOSING.getCode());
            return RestResponse.success(result);
        }

        // 没有上传oss得任务重新上传
        Long expire = redissonRedisUtils.getExpire(RedisKeyConstants.ALIYUN_TRIAL_USE_TASK_ID_KEY + bo.getTaskId());
        if (expire <= 0) {
            result.setTaskId(bo.getTaskId());
            result.setTaskStatus(AudioSynthesizerTaskInfoDTO.TaskStatusEnum.EXPIRED.getCode());
            return RestResponse.success(result);
        }
        String jsonBO = redissonRedisUtils.getValue(RedisKeyConstants.ALIYUN_TRIAL_USE_TASK_ID_KEY + bo.getTaskId());
        if (StringUtils.isEmpty(jsonBO)) {
            result.setTaskId(bo.getTaskId());
            result.setTaskStatus(AudioSynthesizerTaskInfoDTO.TaskStatusEnum.COMPOSING.getCode());
            return RestResponse.success(result);
        }

        SpeechDTO speechDTO = JSONObject.parseObject(jsonBO, SpeechDTO.class);
        if (speechDTO == null) {
            throw new BusinessException("查询任务信息不存在！");
        }
        if (speechDTO.getErrorMsg() != null) {
            TaskErrorDTO errorDTO = speechDTO.getErrorMsg();
            AudioTaskErrorDTO taskErrorDTO = new AudioTaskErrorDTO();
            taskErrorDTO.setStatus(errorDTO.getStatus());
            taskErrorDTO.setErrorMessage(errorDTO.getErrorMessage());
            AliyunTtsErrorCodeEnum aliyunTtsErrorCodeEnum = AliyunTtsErrorCodeEnum.fromCode(errorDTO.getStatus());
            if (aliyunTtsErrorCodeEnum != null) {
                taskErrorDTO.setReason(aliyunTtsErrorCodeEnum.getReason());
                taskErrorDTO.setSolution(aliyunTtsErrorCodeEnum.getSolution());
            }
            result.setErrorMsg(taskErrorDTO);

            result.setTaskId(bo.getTaskId());
            result.setTaskStatus(AudioSynthesizerTaskInfoDTO.TaskStatusEnum.FAILED.getCode());
            return RestResponse.success(result);
        }
        File audioFile = new File(speechDTO.getFilePath());
        if (!audioFile.exists() || audioFile.length() <= 0) {
            log.error("试听任务合成 - 音频文件不存在或者长度为0，请检查任务ID：{}", bo.getTaskId());
            result.setTaskId(bo.getTaskId());
            result.setTaskStatus(AudioSynthesizerTaskInfoDTO.TaskStatusEnum.COMPOSING.getCode());
            return RestResponse.success(result);
        }


        // 添加背景音
//        String bgmUrl = "";
//        AudioTrialUseGlobalConfigDTO globalConfig = speechDTO.getGlobalConfig();
//        if (globalConfig!= null && globalConfig.getAudioType() != null && globalConfig.getAudioBackgroundId() != null) {
//            if (globalConfig.getAudioType().equals(AudioGlobalConfigAudioType.ADVANCED.getCode())) {
//                // 预置
//                AudioHintMusic presetMusic = audioHintMusicBiz.lambdaQuery().eq(AudioHintMusic::getId, globalConfig.getAudioBackgroundId()).one();
//                bgmUrl = presetMusic != null && StringUtils.isNotEmpty(presetMusic.getMaterialUrl()) ? presetMusic.getMaterialUrl() : "";
//            } else {
//                // 自定义
//                AudioContextHintMusic definitionBgm = audioContextHintMusicBiz.lambdaQuery().eq(AudioContextHintMusic::getId, globalConfig.getAudioBackgroundId()).one();
//                bgmUrl = definitionBgm != null && StringUtils.isNotEmpty(definitionBgm.getMaterialPath()) ? definitionBgm.getMaterialPath() : "";
//            }
//        }

//        AudioDetailsDTO audioDetail;
//        if (StringUtils.isNotEmpty(bgmUrl)) {
//            log.info("【试听音频合成】 - 获取全局背景音，调用音频合成背景音乐工具类");
//            // 音频添加bgm后的保存路径
//            String audioMusicOutputPath = AudioConstants.AUDIO_OUTPUT_DIR + File.separator + AudioConstants.BGM_OUTPUT_PREFIX
//                    + UUID.randomUUID().toString().replace("-", "") + AudioConstants.BGM_FORMAT_WAV;
//            try {
//                AudioBackgroundMixer.mixAudioWithBackground(
//                        audioFile,
//                        bgmUrl,
//                        AudioConstants.AUDIO_INPUT_DIR,
//                        audioMusicOutputPath,
//                        // 默认50
//                        globalConfig.getVolume() == null ? 50 : globalConfig.getVolume(),
//                        // 默认循环
//                        globalConfig.getModel() == null ? AudioBgmModelEnum.LOOP.getCode() : globalConfig.getModel());
//
//                // 暂停1s等文件合成
//                Thread.sleep(1000);
//            } catch (Exception e) {
//                log.error("试听任务合成 - 音频合成背景音乐工具类异常： {}", e.getMessage());
//                throw new RuntimeException(e);
//            }
//            File audioMusicOutputFile = new File(audioMusicOutputPath);
//            audioDetail = AudioDurationUtils.getAudioDurationMp3(audioMusicOutputFile);
//        } else {
//            audioDetail = AudioDurationUtils.getAudioDurationMp3(audioFile);
//        }
        AudioDetailsDTO audioDetail = AudioDurationUtils.getAudioDurationMp3(audioFile);

        if (audioDetail == null) {
            log.error("试听任务合成 - 计算音频时长为空： {}", bo.getTaskId());
            result.setTaskId(bo.getTaskId());
            result.setTaskStatus(AudioSynthesizerTaskInfoDTO.TaskStatusEnum.COMPOSING.getCode());
            return RestResponse.success(result);
        }

        if (audioDetail.getMilliseconds() < aliyunTtsConfig.getMinDuration() || audioDetail.getFileSizeInKB() < aliyunTtsConfig.getMinSize()) {
            log.error("试听任务合成 - 音频文件时长不符合条件： {}", bo.getTaskId());
            result.setTaskId(bo.getTaskId());
            result.setTaskStatus(AudioSynthesizerTaskInfoDTO.TaskStatusEnum.FAILED.getCode());
            AudioTaskErrorDTO taskErrorDTO = new AudioTaskErrorDTO();
            taskErrorDTO.setErrorMessage(AudioConstants.PUBLIC_ERROR_MESSAGE);
            result.setErrorMsg(taskErrorDTO);
            return RestResponse.success(result);
        }

        String ossUrl = audioUploadOSS.upload(audioFile.getName(), audioFile, AudioConstants.OSS_AUDIO_TRIAL_USE_OUTPUT_DIR);
        if (StringUtils.isEmpty(ossUrl)) {
            throw new BusinessException("音频上传oss失败!");
        }
        redissonRedisUtils.setValue(RedisKeyConstants.ALIYUN_TRIAL_USE_OSS_URL_KEY + bo.getTaskId(), ossUrl, AudioConstants.ALIYUN_TRIAL_USE_TASK_ID_EXPIRE, TimeUnit.SECONDS);
        result.setTaskId(bo.getTaskId());
        result.setTaskStatus(AudioSynthesizerTaskInfoDTO.TaskStatusEnum.COMPOSED.getCode());
        result.setAudioUrl(ossUrl);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<SynthesisResultDTO> getSynthesizeStatus(AudioIntroIdBO bo) {
        AudioIntro audioIntro = audioIntroBiz.lambdaQuery().eq(AudioIntro::getId, bo.getAudioIntroId()).one();
        Assert.isFalse(audioIntro == null, "音频简介不存在!");
        SynthesisResultDTO dto = new SynthesisResultDTO();
        dto.setAudioIntroId(audioIntro.getId());
        dto.setStatus(audioIntro.getStatus());
        return RestResponse.success(dto);
    }

}
