package com.dbj.classpal.books.service.biz.ebooks;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.service.entity.product.AppEBookPdfTask;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;

/**
 * PDF处理任务业务接口
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
public interface IAppEBookPdfTaskBiz extends IService<AppEBookPdfTask> {

    /**
     * 创建PDF处理任务
     *
     * @param businessId            业务ID
     * @param businessKey
     * @param waterMarkBusinessType 水印业务类型
     * @param pdfUrl                PDF文件URL
     * @param pdfName               PDF文件名
     * @return 任务ID
     */
    String createTask(Integer businessId,String businessKey, Integer waterMarkBusinessType, String pdfUrl, String pdfName) throws BusinessException;

    /**
     * 根据任务ID查询任务状态
     *
     * @param taskId 任务ID
     * @return 任务信息
     */
    AppEBookPdfTask getTaskByTaskId(String taskId) throws BusinessException;


    /**
     * 根据BusinessKey查询任务状态
     *
     * @param BusinessKey 业务key
     * @return 任务信息
     */
    List<AppEBookPdfTask> queryTaskByBusinessKey(String BusinessKey) throws BusinessException;

    /**
     * 更新任务状态为成功
     *
     * @param taskId 任务ID
     * @param coverUrl 封面URL
     * @param resultJson 处理结果JSON
     */
    void updateTaskSuccess(String taskId, String coverUrl, String resultJson) throws BusinessException;

    /**
     * 更新任务状态为失败
     *
     * @param taskId 任务ID
     * @param errorMsg 错误信息
     */
    void updateTaskFailed(String taskId, String errorMsg) throws BusinessException;

    /**
     * 清理长时间处理中的任务（超时任务）
     *
     * @param timeoutMinutes 超时时间（分钟）
     * @return 清理的任务数量
     */
    int cleanupTimeoutTasks(int timeoutMinutes) throws BusinessException;
}
