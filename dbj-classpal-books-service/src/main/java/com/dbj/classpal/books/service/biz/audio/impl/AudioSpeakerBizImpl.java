package com.dbj.classpal.books.service.biz.audio.impl;

import com.dbj.classpal.books.service.entity.audio.AudioSpeaker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.service.biz.audio.IAudioSpeakerBiz;
import com.dbj.classpal.books.service.mapper.audio.AudioSpeakerMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 发音人配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Service
public class AudioSpeakerBizImpl extends ServiceImpl<AudioSpeakerMapper, AudioSpeaker> implements IAudioSpeakerBiz {

}
