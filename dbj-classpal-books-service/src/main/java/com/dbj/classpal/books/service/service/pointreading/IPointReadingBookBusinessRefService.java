package com.dbj.classpal.books.service.service.pointreading;

import com.dbj.classpal.books.common.bo.pointreading.PointReadingBookBusinessRefBatchSaveBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingBookBusinessRefSaveBO;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingBookBusinessRefDTO;
import com.dbj.classpal.books.service.entity.pointreading.PointReadingBookBusinessRef;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;

/**
 * 点读书业务关联 服务接口
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface IPointReadingBookBusinessRefService {

    /**
     * 保存点读书业务关联
     *
     * @param saveBO 保存参数
     * @return 关联ID
     */
    Integer save(PointReadingBookBusinessRefSaveBO saveBO) throws BusinessException;

    /**
     * 批量保存点读书业务关联
     *
     * @param batchSaveBO 批量保存参数
     * @return 是否成功
     */
    Boolean saveBatch(PointReadingBookBusinessRefBatchSaveBO batchSaveBO) throws BusinessException;

    /**
     * 删除点读书业务关联
     *
     * @param id 关联ID
     * @return 是否成功
     */
    Boolean delete(Integer id) throws BusinessException;

    /**
     * 根据点读书ID查询业务关联列表
     *
     * @param bookId 点读书ID
     * @return 业务关联列表
     */
    List<PointReadingBookBusinessRefDTO> getRefsByBookId(Integer bookId) throws BusinessException;

    /**
     * 根据业务类型和业务ID查询关联的点读书列表
     *
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 业务关联列表
     */
    List<PointReadingBookBusinessRefDTO> getRefsByBusiness(String businessType, Integer businessId) throws BusinessException;

    /**
     * 根据业务类型查询关联的点读书列表
     *
     * @param businessType 业务类型
     * @return 业务关联列表
     */
    List<PointReadingBookBusinessRefDTO> getRefsByBusinessType(String businessType) throws BusinessException;

    /**
     * 删除点读书的所有业务关联
     *
     * @param bookId 点读书ID
     * @return 是否成功
     */
    Boolean deleteByBookId(Integer bookId) throws BusinessException;

    /**
     * 删除业务的所有点读书关联
     *
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 是否成功
     */
    Boolean deleteByBusiness(String businessType, Integer businessId) throws BusinessException;

    /**
     * 绑定点读书到业务
     *
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param bookIds 点读书ID列表
     * @return 是否成功
     */
    Boolean bindBooksToBusiness(String businessType, Integer businessId, List<Integer> bookIds) throws BusinessException;

    /**
     * 绑定业务到点读书
     *
     * @param bookId 点读书ID
     * @param businessType 业务类型
     * @param businessIds 业务ID列表
     * @return 是否成功
     */
    Boolean bindBusinessToBook(Integer bookId, String businessType, List<Integer> businessIds) throws BusinessException;

    PointReadingBookBusinessRef getRef(String businessType, Integer businessId);
}
