package com.dbj.classpal.books.service.entity.studycenter;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_study_module_progress")
@Tag(name = "学习进度", description = "学习中心-模块学习进度表")
@AllArgsConstructor
@NoArgsConstructor
public class AppStudyModuleProgress extends BizEntity implements Serializable {


    /** 用户ID */
    private Integer userId;

    /** 学习模块ID */
    private Integer moduleId;

    /** 最后学习时间 */
    private LocalDateTime lastLearnTime;

    /** 状态 0-禁用 1-启用 */
    private Integer status;
}