package com.dbj.classpal.books.service.entity.advertisement;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 广告层级条件选项关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("advertisement_level_condition_option")
@Tag(name="AdvertisementLevelConditionOption对象", description="广告层级条件选项关联表")
public class AdvertisementLevelConditionOption extends BizEntity implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "广告id")
    private Integer advertisementId;

    @Schema(description = "广告条件id")
    private Integer advertisementLevelConditionId;

    @Schema(description = "选项类型 1-年级 2-性别 3-关系 4-图书类别 5-图书数量区间 6-指定图书 7-伴学营数量区间 8-指定伴学营 9-打卡本数量区间 10-指定打卡本 11-属地省级 12-属地市级 13-属地分级标签 14-场景值")
    private Integer type;

    @Schema(description = "选项值")
    private String optionValue;

    @Schema(description = "选项名称")
    private String optionName;

    @Schema(description = "最小数量")
    private Integer minQty;

    @Schema(description = "最大数量")
    private Integer maxQty;

    @Schema(description = "是否启用 1-是 0-否")
    private Boolean status;
}
