package com.dbj.classpal.books.service.biz.poem;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionBO;
import com.dbj.classpal.books.client.bo.poem.app.AppAncientPoemReciteCollectionPageBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemReciteCollectionDTO;
import com.dbj.classpal.books.client.dto.poem.app.AppAncientPoemReciteCollectionPageDTO;
import com.dbj.classpal.books.service.entity.poem.AncientPoemReciteCollection;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.framework.commons.request.PageInfo;

import java.util.List;

/**
 * <p>
 * 古诗背诵合集表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
public interface IAncientPoemReciteCollectionBiz extends IService<AncientPoemReciteCollection> {

    /**
     * 分页查询古诗背诵合集
     */
    List<AncientPoemReciteCollectionDTO> listAncientPoemReciteCollection(AncientPoemReciteCollectionBO ancientPoemReciteCollectionBO);


    Page<AppAncientPoemReciteCollectionPageDTO> pageAncientPoemReciteCollection(PageInfo<AppAncientPoemReciteCollectionPageBO> pageInfo);

}
