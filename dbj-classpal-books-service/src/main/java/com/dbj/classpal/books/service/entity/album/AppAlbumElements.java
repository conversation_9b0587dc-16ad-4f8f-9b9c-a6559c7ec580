package com.dbj.classpal.books.service.entity.album;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumMenus
 * Date:     2025-04-08 10:15:31
 * Description: 表名： ,描述： 表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_album_elements")
@Tag(name="内容管理-专辑内容", description="内容管理-专辑内容")
public class AppAlbumElements extends BizEntity implements Serializable {

    @TableField("app_album_menu_id")
    @Schema(description = "专辑菜单ID")
    private Integer appAlbumMenuId;

    @TableField("album_type")
    @Schema(description = "专辑类型 1音频 2视频")
    private Integer albumType;

    @TableField("album_cover")
    @Schema(description = "专辑封面")
    private String albumCover;

    @TableField("album_title")
    @Schema(description = "专辑标题")
    private String albumTitle;

    @TableField("album_remark")
    @Schema(description = "专辑简介")
    private String albumRemark;

    @TableField("album_visible")
    @Schema(description = "是否隐藏 0显示 1隐藏")
    private Integer albumVisible;

    @TableField("album_status")
    @Schema(description = "上架状态 0下架 1上架")
    private Integer albumStatus;
}
