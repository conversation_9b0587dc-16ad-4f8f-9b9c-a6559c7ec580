package com.dbj.classpal.books.service.biz.advertisement.impl;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementPageBO;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementDTO;
import com.dbj.classpal.books.service.biz.advertisement.IAdvertisementBiz;
import com.dbj.classpal.books.service.entity.advertisement.Advertisement;
import com.dbj.classpal.books.service.mapper.advertisement.AdvertisementMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 广告信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Service
public class AdvertisementBizImpl extends ServiceImpl<AdvertisementMapper, Advertisement> implements IAdvertisementBiz {

    @Override
    public Page<AdvertisementDTO> getAdvertisementPageList(Page page, AdvertisementPageBO bo) {
        return this.baseMapper.getAdvertisementPageList(page, bo);
    }
}
