package com.dbj.classpal.books.service.biz.poem.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.poem.app.AncientPoemReciteAppUserAssessmentScorePageBO;
import com.dbj.classpal.books.client.dto.poem.app.AncientPoemReciteAppUserAssessmentScorePageDTO;
import com.dbj.classpal.books.service.entity.poem.AncientPoemReciteAppUserAssessmentScores;
import com.dbj.classpal.books.service.mapper.poem.AncientPoemReciteAppUserAssessmentScoresMapper;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemReciteAppUserAssessmentScoresBiz;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.framework.commons.request.PageInfo;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 古诗背诵评测得分 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Service
public class AncientPoemReciteAppUserAssessmentScoresBizImpl extends ServiceImpl<AncientPoemReciteAppUserAssessmentScoresMapper, AncientPoemReciteAppUserAssessmentScores> implements IAncientPoemReciteAppUserAssessmentScoresBiz {

    @Override
    public Page<AncientPoemReciteAppUserAssessmentScorePageDTO> pageAncientPoemReciteAppUserAssessmentScore(PageInfo<AncientPoemReciteAppUserAssessmentScorePageBO> pageInfo) {
        return baseMapper.pageAncientPoemReciteAppUserAssessmentScore(pageInfo.getPage(), pageInfo.getData());
    }
}
