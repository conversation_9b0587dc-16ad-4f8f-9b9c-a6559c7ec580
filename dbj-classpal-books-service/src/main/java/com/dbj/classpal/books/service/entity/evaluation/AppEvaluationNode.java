package com.dbj.classpal.books.service.entity.evaluation;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationNode
 * Date:     2025-05-16 11:02:51
 * Description: 表名：app_evaluation_node ,描述： 内容管理-评测-评测项
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("app_evaluation_node")
@Tag(name="内容管理-评测-评测项表", description="内容管理-评测-评测项表")
public class AppEvaluationNode extends BizEntity {

    @TableField("app_evaluation_id")
    @Schema(name = "评测表id")
    private Integer appEvaluationId;

    @TableField("app_evaluation_node_name")
    @Schema(name = "评测项名称")
    private String appEvaluationNodeName;

    @TableField("app_evaluation_order")
    @Schema(name = "评测项排序")
    private Integer appEvaluationOrder;

    @TableField("app_evaluation_node_cover")
    @Schema(name = "评测项封面")
    private String appEvaluationNodeCover;

    @TableField("app_evaluation_node_cover_name")
    @Schema(name = "评测项封面名称")
    private String appEvaluationNodeCoverName;

}
