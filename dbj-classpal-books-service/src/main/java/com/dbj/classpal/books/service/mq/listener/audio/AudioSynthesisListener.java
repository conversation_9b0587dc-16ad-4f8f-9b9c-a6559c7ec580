package com.dbj.classpal.books.service.mq.listener.audio;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.dbj.classpal.books.client.dto.audio.AudioDetailsDTO;
import com.dbj.classpal.books.client.enums.MaterialTypeEnum;
import com.dbj.classpal.books.common.bo.audio.AudioIntroMQBO;
import com.dbj.classpal.books.common.bo.audio.SpeechBO;
import com.dbj.classpal.books.common.bo.material.AppMaterialSaveBO;
import com.dbj.classpal.books.common.constant.AudioConstants;
import com.dbj.classpal.books.common.constant.QueueConstant;
import com.dbj.classpal.books.common.constant.RedisKeyConstants;
import com.dbj.classpal.books.common.dto.audio.SpeechDTO;
import com.dbj.classpal.books.common.dto.audio.TaskErrorDTO;
import com.dbj.classpal.books.common.enums.audio.*;
import com.dbj.classpal.books.service.biz.audio.*;
import com.dbj.classpal.books.service.entity.audio.*;
import com.dbj.classpal.books.service.entity.material.AppMaterial;
import com.dbj.classpal.books.service.service.material.IAppMaterialService;
import com.dbj.classpal.books.service.util.audio.*;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import com.dbj.classpal.framework.mq.annotation.ExtractHeader;
import com.dbj.classpal.framework.redisson.config.RedissonRedisUtils;
import com.dbj.classpal.framework.tts.config.AliyunTTSConfig;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

/**
 * 音频合成监听器
 * <AUTHOR>
 * @date 2025-06-30
 **/
@Component
@Slf4j
public class AudioSynthesisListener {

    @Autowired
    private IAudioIntroBiz audioIntroBiz;
    @Autowired
    private IAudioIntroHistoryBiz audioIntroHistoryBiz;
    @Autowired
    private IAudioContextInfoBiz audioContextInfoBiz;
    @Autowired
    private IAudioGlobalConfigBiz audioGlobalConfigBiz;
    @Autowired
    private IAudioSpeakerBiz audioSpeakerBiz;
    @Autowired
    private IAudioHintMusicBiz audioHintMusicBiz;
    @Autowired
    private IAudioContextHintMusicBiz audioContextHintMusicBiz;
    @Autowired
    private IAudioBackgroundBiz audioBackgroundBiz;
    @Autowired
    private BatchSpeechLongSynthesizerService synthesizerService;
    @Autowired
    private SpeechLongSynthesizer speechLongSynthesizer;
    @Autowired
    private AudioUploadOSS audioUploadOSS;
    @Autowired
    private RedissonRedisUtils redissonRedisUtils;
    @Autowired
    private IAppMaterialService appMaterialService;
    @Autowired
    private AliyunTTSConfig aliyunTtsConfig;

    // 等待音频合成结束时长/s
    private static final int BASE_SLEEP_TIME = 5000;
    // 每增加N个请求，增加的等待时间（毫秒）
    private static final int INCREMENT_PER_ITEM = 200;


    @ExtractHeader
    @RabbitListener(queues = {QueueConstant.AUDIO_PRODUCTION_QUEUE})
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void audioSynthesisHandler(AudioIntroMQBO bo, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag, Message message) throws IOException, BusinessException {
        log.info("【异步音频合成】 - 开始处理音频合成任务: {}", JSON.toJSONString(bo));
        if (bo == null || bo.getAudioIntroId() == null) {
            throw new BusinessException("【异步音频合成】 - MQ接收参数为空");
        }

        // 分布式锁
        String lockKey = MessageFormat.format(RedisKeyConstants.DBJ_AUDIO_RESYNTHESIS_MQ_LOCK_KEY, bo.getAudioIntroId());
        Lock lock = redissonRedisUtils.getLock(lockKey);

        Integer frequency = 0;
        String cancelReason = "手动取消";

        // 初始化目录
        initDir(Arrays.asList(AudioConstants.AUDIO_OUTPUT_DIR, AudioConstants.AUDIO_OUTPUT_DIR, AudioConstants.WAV_TO_MP3_OUTPUT_DIR));

        // 多音频合并后保存路径
        File multiAudioOutputFile = new File(AudioConstants.AUDIO_OUTPUT_DIR + File.separator + AudioConstants.MULTI_AUDIO_MERGE_OUTPUT_PREFIX
                + UUID.randomUUID().toString().replace("-", "") + AudioConstants.BGM_FORMAT_WAV);

        // 音频添加bgm后的保存路径
        String audioMusicOutputPath = AudioConstants.AUDIO_OUTPUT_DIR + File.separator + AudioConstants.BGM_OUTPUT_PREFIX
                + UUID.randomUUID().toString().replace("-", "") + AudioConstants.BGM_FORMAT_WAV;
        File audioMusicOutputFile = new File(audioMusicOutputPath);

        // wav转mp3文件路径
        File wavTomp3File = new File(AudioConstants.WAV_TO_MP3_OUTPUT_DIR + File.separator + AudioConstants.WAV_TO_MP3_PREFIX
                + UUID.randomUUID().toString().replace("-", "") + AudioConstants.BGM_FORMAT_MP3);

        try {
            if (lock.tryLock(RedisKeyConstants.LOCK_TIME_OUT, TimeUnit.SECONDS)) {
                // 先查询状态查询状态
                AudioIntro audioIntro = getAudioIntro(bo.getAudioIntroId());

                List<AudioIntroHistory> historyList = audioIntroHistoryBiz.lambdaQuery().eq(AudioIntroHistory::getAudioIntroId, bo.getAudioIntroId()).orderByAsc(AudioIntroHistory::getVersion).list();

                frequency = CollectionUtil.isEmpty(historyList) ? 1 : historyList.get(0).getVersion() + 1;
                if (Objects.equals(audioIntro.getIsCancel(), YesOrNoEnum.YES.getCode())) {
                    updateAudioStatus(bo.getAudioIntroId(), AudioIntroStatus.WAIT_COMPOUND.getValue(), cancelReason);
                    insertAudioHistory(bo.getAudioIntroId(), null, cancelReason, frequency);
                    log.info("【异步音频合成】 - begin 查询音频合成状态, 状态为: {}", audioIntro.getStatus());
                    return;
                }

                // 1 查询音频文本列表
                List<AudioContextInfo> contextList = audioContextInfoBiz.lambdaQuery().eq(AudioContextInfo::getAudioIntroId, bo.getAudioIntroId()).orderByAsc(AudioContextInfo::getOrderNum).list();
                Assert.isFalse(CollectionUtil.isEmpty(contextList), "【异步音频合成】 - 音频文本列表为空");
                // 2 获取全局配置
                List<AudioGlobalConfig> configList = audioGlobalConfigBiz.lambdaQuery().eq(AudioGlobalConfig::getAudioIntroId, bo.getAudioIntroId()).list();
                // 查询发音人
                Set<Integer> speakerIds = contextList.stream().map(AudioContextInfo::getAudioSpeakerId).filter(Objects::nonNull).collect(Collectors.toSet());
                Map<Integer, AudioSpeaker> speakerMaps = new HashMap<>();
                if (CollectionUtil.isNotEmpty(speakerIds)) {
                    List<AudioSpeaker> speakerList = audioSpeakerBiz.lambdaQuery().in(AudioSpeaker::getId, speakerIds).list();
                    speakerMaps = speakerList.stream().collect(Collectors.toMap(AudioSpeaker::getId, v -> v));
                }

                Integer status1 = getAudioIntro(bo.getAudioIntroId()).getIsCancel();
                if (Objects.equals(status1, YesOrNoEnum.YES.getCode())) {
                    updateAudioStatus(bo.getAudioIntroId(), AudioIntroStatus.WAIT_COMPOUND.getValue(), cancelReason);
                    insertAudioHistory(bo.getAudioIntroId(), null, cancelReason, frequency);
                    log.info("【异步音频合成】 - 3 再次查询音频状态, 状态为: {}", status1 );
                    return;
                }

                log.info("【异步音频合成】 - 4 组装阿里云长文本转语音入参");
                List<SpeechBO> speechList = new ArrayList<>();
                for (AudioContextInfo item : contextList) {
                    if (CollectionUtil.isEmpty(speakerMaps) || !speakerMaps.containsKey(item.getAudioSpeakerId())) {
                        log.error("【异步音频合成】 - 获取发音人信息失败, 发音人ID: {}", item.getAudioSpeakerId());
                        continue;
                    }
                    SpeechBO speechBO = new SpeechBO(bo.getAudioIntroId(), item.getId(), item.getPitchRate(),
                            item.getSpeechRate(), speakerMaps.get(item.getAudioSpeakerId()).getVoice(), item.getText(), item.getVolume());
                    speechList.add(speechBO);
                }
                Assert.isFalse(CollectionUtil.isEmpty(speechList), "【异步音频合成】 - 未识别发音人信息语音无法合成！");
                log.info("【异步音频合成】 - 组装后的参数: {}", JSON.toJSONString(speechList));

                log.info("【异步音频合成】 - 5 调用阿里云长文本语音合成");
//                List<SpeechDTO> audioFileList = synthesizerService.processBatch(speechList);
                List<SpeechDTO> audioFileList = new ArrayList<>();
                for (SpeechBO speechBO : speechList) {
                    SpeechDTO speechDTO = speechLongSynthesizer.process(speechBO);
                    if (speechDTO != null) {
                        audioFileList.add(speechDTO);
                    }
                }
                Assert.isFalse(CollectionUtil.isEmpty(audioFileList), "阿里云音频合成失败，返回为空！");
                boolean hasErrorMsg = audioFileList.stream().anyMatch(speechDTO -> speechDTO.getErrorMsg() != null);
                if (hasErrorMsg) {
                    List<TaskErrorDTO> errorMsgList = audioFileList.stream().map(SpeechDTO::getErrorMsg).filter(Objects::nonNull).toList();
                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < errorMsgList.size(); i++) {
                        TaskErrorDTO errorMsg = errorMsgList.get(i);
                        sb.append((i + 1)).append(".").append(errorMsg.getReason()).append(" (【阿里云官方】错误状态信息：").append(errorMsg.getErrorMessage()).append(") ;");
                    }
                    updateAudioStatus(bo.getAudioIntroId(), AudioIntroStatus.FAILED.getValue(), sb.toString());
                    insertAudioHistory(bo.getAudioIntroId(), null, JSON.toJSONString(errorMsgList), frequency);
                    log.error("【异步音频合成】 - 5 调用阿里云长文本语音合成失败，原因：{}", JSON.toJSONString(errorMsgList));
                    return;
                }

                try {
                    // 动态计算等待时间
                    int itemCount = speechList.size();
                    int waitTime = Math.max(BASE_SLEEP_TIME, itemCount * INCREMENT_PER_ITEM);
                    log.info("【异步音频合成】 - 6 休眠 {} 秒，等待阿里云语音合成完成...", BASE_SLEEP_TIME / 1000);
                    Thread.sleep(waitTime);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }

                Integer status2 = getAudioIntro(bo.getAudioIntroId()).getIsCancel();
                if (Objects.equals(status2, YesOrNoEnum.YES.getCode())) {
                    updateAudioStatus(bo.getAudioIntroId(), AudioIntroStatus.WAIT_COMPOUND.getValue(), cancelReason);
                    insertAudioHistory(bo.getAudioIntroId(), null, cancelReason, frequency);
                    log.error("【异步音频合成】 - 6 再次查询音频状态, 状态为: {}", status1 );
                    return;
                }

                log.info("【异步音频合成】 - 7 获取音频合成结果，调用多个音频合并工具类, 多音频合并之后的文件：{}", multiAudioOutputFile.getPath());
                List<File> listFile = audioFileList.stream().map(SpeechDTO::getFile).toList();
                AudioMerger.mergeAudioFiles(listFile, multiAudioOutputFile);

                Integer status3 = getAudioIntro(bo.getAudioIntroId()).getIsCancel();
                if (Objects.equals(status3, YesOrNoEnum.YES.getCode())) {
                    updateAudioStatus(bo.getAudioIntroId(), AudioIntroStatus.WAIT_COMPOUND.getValue(), cancelReason);
                    insertAudioHistory(bo.getAudioIntroId(), null, cancelReason, frequency);
                    log.info("【异步音频合成】 - 8 再次查询音频状态, 状态为: {}", status1 );
                    return;
                }
                if (isEmptyFile(multiAudioOutputFile)) {
                    updateAudioStatus(bo.getAudioIntroId(), AudioIntroStatus.FAILED.getValue(), "多文件合并之后的结果返回为null");
                    insertAudioHistory(bo.getAudioIntroId(), null, "多文件合并之后的结果返回为null", frequency);
                    log.error("【异步音频合成】 - 8 多文件合并之后的结果返回为null");
                    return;
                }

                // 判断是否配置全局背景音
                boolean hasBgm = configList.stream().anyMatch(config -> Objects.equals(config.getType(), AudioGlobalConfigTypeEnum.BACKGROUND.getCode()));
                // 查询全局bgm
                String bgmUrl = "";
                if (CollectionUtil.isNotEmpty(configList) && hasBgm) {
                    Optional<AudioGlobalConfig> bgmConfig = configList.stream().filter(v -> v.getType().equals(AudioGlobalConfigTypeEnum.BACKGROUND.getCode())).findFirst();
                    if (bgmConfig.isPresent()) {
                        AudioGlobalConfig audioGlobalConfig = bgmConfig.get();
                        if (audioGlobalConfig.getAudioType().equals(AudioGlobalConfigAudioType.ADVANCED.getCode())) {
                            // 预置bgm
                            AudioHintMusic presetMusic = audioHintMusicBiz.lambdaQuery().eq(AudioHintMusic::getId, audioGlobalConfig.getAudioBackgroundId()).one();
                            bgmUrl = presetMusic != null && StringUtils.isNotEmpty(presetMusic.getMaterialUrl()) ? presetMusic.getMaterialUrl() : "";
                        } else {
                            // 自定义bgm
                            AudioBackground definitionBgm = audioBackgroundBiz.lambdaQuery().eq(AudioBackground::getId, audioGlobalConfig.getAudioBackgroundId()).one();
                            bgmUrl = definitionBgm != null && StringUtils.isNotEmpty(definitionBgm.getMaterialPath()) ? definitionBgm.getMaterialPath() : "";
                        }
                        log.info("【异步音频合成】 全局 bgmUrl: {}", bgmUrl);
                        if (StringUtils.isNotEmpty(bgmUrl)) {
                            log.info("【异步音频合成】 - 9 获取音频合成结果，调用音频合成背景音乐工具类");
                            AudioBackgroundMixer.mixAudioWithBackground(
                                    multiAudioOutputFile,
                                    bgmUrl,
                                    AudioConstants.AUDIO_INPUT_DIR,
                                    audioMusicOutputPath,
                                    // 默认50
                                    audioGlobalConfig.getVolume() == null ? AudioConstants.BGM_VOLUME : audioGlobalConfig.getVolume(),
                                    // 默认循环
                                    audioGlobalConfig.getModel() == null ? AudioBgmModelEnum.LOOP.getCode() : audioGlobalConfig.getModel());
                        }
                    }
                } else {
                    log.info("【异步音频合成】 - 9 获取音频合成结果，无背景音乐配置");
                }

                AudioIntro status4 = getAudioIntro(bo.getAudioIntroId());
                if (Objects.equals(status4.getIsCancel(), YesOrNoEnum.YES.getCode())) {
                    updateAudioStatus(bo.getAudioIntroId(), AudioIntroStatus.WAIT_COMPOUND.getValue(), cancelReason);
                    insertAudioHistory(bo.getAudioIntroId(), null, cancelReason, frequency);
                    log.info("【异步音频合成】 - 10 再次获取音频状态, 状态为: {}", status4.getIsCancel());
                    return;
                }

                log.info("【异步音频合成】 - 11 获取背景音乐合成结果，调用MP3转MP4工具类");
                if (StringUtils.isNotEmpty(bgmUrl)) {
                    if (isEmptyFile(audioMusicOutputFile)) {
                        updateAudioStatus(bo.getAudioIntroId(), AudioIntroStatus.FAILED.getValue(), "背景音乐合成结果文件返回为null");
                        insertAudioHistory(bo.getAudioIntroId(), null, "背景音乐合成结果文件返回为null", frequency);
                        log.error("【异步音频合成】 - 11 背景音乐合成结果文件返回为null");
                        return;
                    }
                    WavToMp3Converter.wavToMp3Converter(audioMusicOutputFile, wavTomp3File);
                } else {
                    WavToMp3Converter.wavToMp3Converter(multiAudioOutputFile, wavTomp3File);
                }

                Integer status5 = getAudioIntro(bo.getAudioIntroId()).getIsCancel();
                if (Objects.equals(status5, YesOrNoEnum.YES.getCode())) {
                    updateAudioStatus(bo.getAudioIntroId(), AudioIntroStatus.WAIT_COMPOUND.getValue(), cancelReason);
                    insertAudioHistory(bo.getAudioIntroId(), null, cancelReason, frequency);
                    log.info("【异步音频合成】 - 12 再次获取音频状态, 状态为: {}", status5);
                    return;
                }

                if (isEmptyFile(wavTomp3File)) {
                    updateAudioStatus(bo.getAudioIntroId(), AudioIntroStatus.FAILED.getValue(), "wav转mp3之后的文件返回为null");
                    insertAudioHistory(bo.getAudioIntroId(), null, "wav转mp3之后的文件返回为null", frequency);
                    log.error("【异步音频合成】 - 13 wav转mp3之后的文件返回为null");
                    return;
                }
                AudioDetailsDTO audioDetail = AudioDurationUtils.getAudioDurationMp3(wavTomp3File);
                if (audioDetail == null) {
                    updateAudioStatus(bo.getAudioIntroId(), AudioIntroStatus.FAILED.getValue(), AudioConstants.PUBLIC_ERROR_MESSAGE);
                    insertAudioHistory(bo.getAudioIntroId(), null, "获取音频时长、大小失败", frequency);
                    log.error("【异步音频合成】 - 13 获取音频时长、大小失败, 返回为空");
                    return;
                }

                if (audioDetail.getMilliseconds() < aliyunTtsConfig.getMinDuration() || audioDetail.getFileSizeInKB() < aliyunTtsConfig.getMinSize()) {
                    updateAudioStatus(bo.getAudioIntroId(), AudioIntroStatus.FAILED.getValue(), AudioConstants.PUBLIC_ERROR_MESSAGE);
                    insertAudioHistory(bo.getAudioIntroId(), null, AudioConstants.PUBLIC_ERROR_MESSAGE, frequency);
                    log.error("【异步音频合成】 - 13 音频时长不满足条件，时长为: {}， 大小为：{}(kb)", audioDetail.getMilliseconds(), audioDetail.getFileSizeInKB());
                    return;
                }

                String ossUrl = audioUploadOSS.upload(wavTomp3File.getName(), wavTomp3File, AudioConstants.OSS_AUDIO_OUTPUT_DIR);
                log.info("【异步音频合成】 - 14 音频上传oss成功，oss地址为: {}", ossUrl);
                if (StringUtils.isEmpty(ossUrl)) {
                    updateAudioStatus(bo.getAudioIntroId(), AudioIntroStatus.FAILED.getValue(), "音频上传oss失败");
                    insertAudioHistory(bo.getAudioIntroId(), null, "音频上传oss失败", frequency);
                    log.error("【异步音频合成】 - 14 音频上传oss失败，oss地址为空");
                    return;
                }

                AudioIntro latestStatus = getAudioIntro(bo.getAudioIntroId());
                if (Objects.equals(latestStatus.getIsCancel(), YesOrNoEnum.YES.getCode())) {
                    updateAudioStatus(bo.getAudioIntroId(), AudioIntroStatus.WAIT_COMPOUND.getValue(), cancelReason);
                    insertAudioHistory(bo.getAudioIntroId(), null, cancelReason, frequency);
                    log.info("【异步音频合成】 - 15 再次获取音频状态, 状态为: {}", latestStatus.getIsCancel());
                    return;
                }

                int totalSeconds = audioDetail.getHours() * 3600 + audioDetail.getMinutes() * 60 + audioDetail.getSeconds();

                // 带格式
//                String mp3FileName = wavTomp3File.getName();
                AppMaterialSaveBO saveBO = new AppMaterialSaveBO();
                saveBO.setParentId(latestStatus.getAppMaterialId());
                saveBO.setMaterialName(latestStatus.getName());
                saveBO.setMaterialType(MaterialTypeEnum.FILE_TYPE_AUDIO.getCode());
                saveBO.setMaterialPath(ossUrl);
                saveBO.setMaterialOriginUrl(ossUrl);
                saveBO.setMaterialSize(audioDetail.getFileSizeInKB());
                saveBO.setMaterialDuration(totalSeconds);
                log.info("【异步音频合成】 - 16 上传素材中心参数：{}", JSON.toJSONString(saveBO));
                AppMaterial appMaterial = appMaterialService.saveMaterial(saveBO);
                if (appMaterial != null && appMaterial.getId() != null) {
                    log.info("【异步音频合成】 - 16 更新音频简介信息");
                    // 音频总时长（秒）
                    updateAudioIntro(bo.getAudioIntroId(), AudioIntroStatus.COMPLETE.getValue(), JSON.toJSONString(appMaterial) , totalSeconds, audioDetail.getFileSizeInKB(), ossUrl, audioIntro.getFrequency() != null ? audioIntro.getFrequency() + 1: 1);

                    log.info("【异步音频合成】 - 17 新增音频简介合成历史记录");
                    insertAudioHistory(bo.getAudioIntroId(), ossUrl,  JSON.toJSONString(appMaterial) , frequency);

                    log.info("【异步音频合成】 - 18 更新音频文本");
                    updateAudioContext(audioFileList);
                    log.info("【异步音频合成】 - 19 上传素材中心成功：{}", saveBO);
                } else {
                    log.error("【异步音频合成】 - 19 上传素材中心失败：{}", saveBO);
                    updateAudioIntro(bo.getAudioIntroId(), AudioIntroStatus.FAILED.getValue(), "上传素材中心失败, 素材中心返回参数： " + JSON.toJSONString(appMaterial), null, null, null, null);
                    insertAudioHistory(bo.getAudioIntroId(), null, "上传素材中心失败, 素材中心返回参数：" + JSON.toJSONString(appMaterial), frequency);
                }

            }

        } catch (Exception e) {
            log.error("【异步音频合成】异常：{}", e.getMessage());
            updateAudioIntro(bo.getAudioIntroId(), AudioIntroStatus.FAILED.getValue(), e.getMessage(), null, null, null, null);
            insertAudioHistory(bo.getAudioIntroId(), null, "合成失败: "+e.getMessage(), frequency);

        } finally {
            try {
                log.info("【异步音频合成】释放锁");
                lock.unlock();
            } catch (Exception e) {
                log.error(e.getMessage());
                log.error("【异步音频合成】 - 20 释放锁异常：{}", e.getMessage());
            }
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false); // 拒绝并重新入队
        }
    }

    private void initDir(List<String> paths) {
        paths.forEach(path -> {
            File file = new File(path);
            if (!file.exists()) {
                file.mkdirs();
            }
        });
    }

    /**
     * 实时获取音频状态
     * @param audioIntroId
     * @return
     * @throws BusinessException
     */
    private AudioIntro getAudioIntro(Integer audioIntroId) throws BusinessException {
        AudioIntro audioIntro = audioIntroBiz.getById(audioIntroId);
        if (audioIntro == null) {
            throw new BusinessException("【异步音频合成】异常, 音频不存在, 音频简介id：" + audioIntroId);
        }
        return audioIntro;
    }

    /**
     * 更新音频简介信息
     * @param audioIntroId
     * @param status
     * @param reason
     * @throws BusinessException
     */
    private void updateAudioIntro(Integer audioIntroId, Integer status, String reason,Integer duration, Double size, String audioUrl, Integer frequency) {
        AudioIntro audioIntro = new AudioIntro();
        audioIntro.setId(audioIntroId);
        audioIntro.setStatus(status);
        if (StringUtils.isNotEmpty(reason)) {
            audioIntro.setFailReason(reason);
        }
        if (duration != null) {
            audioIntro.setDuration(duration);
        }
        if (size != null) {
            audioIntro.setSize(size);
        }
        if (StringUtils.isNotEmpty(audioUrl)) {
            audioIntro.setAudioUrl(audioUrl);
        }
        if (frequency != null) {
            audioIntro.setFrequency(frequency);
        }
        audioIntroBiz.updateById(audioIntro);
    }

    /**
     * 更新音频状态
     * @param audioIntroId
     * @param status
     */
    private void updateAudioStatus(Integer audioIntroId, Integer status, String reason) {
        AudioIntro audioIntro = new AudioIntro();
        audioIntro.setId(audioIntroId);
        audioIntro.setStatus(status);
        if (StringUtils.isNotEmpty(reason)) {
            audioIntro.setFailReason(reason);
        }
        audioIntroBiz.updateById(audioIntro);
    }

    /**
     * 插入合成历史记录
     * @param audioIntroId
     * @param audioUrl
     * @param reason
     * @param version
     */
    private void insertAudioHistory(Integer audioIntroId, String audioUrl, String reason,Integer version) {
        AudioIntroHistory history = new AudioIntroHistory();
        history.setAudioIntroId(audioIntroId);
        history.setVersion(version);
        if (StringUtils.isNotEmpty(audioUrl)) {
            history.setAudioUrl(audioUrl);
        }
        if (StringUtils.isNotEmpty(reason)) {
            history.setFailReason(reason);
        }
        audioIntroHistoryBiz.save(history);
    }

    /**
     * 更新音频文本
     * @param speechDTOList
     */
    private void updateAudioContext(List<SpeechDTO> speechDTOList) {
        List<AudioContextInfo> list = new ArrayList<>();
        speechDTOList.forEach(dto -> {
            AudioContextInfo audioContextInfo = new AudioContextInfo();
            audioContextInfo.setId(dto.getAudioContextInfoId());
            audioContextInfo.setTaskId(dto.getTaskId());
            list.add(audioContextInfo);
        });
        audioContextInfoBiz.updateBatchById(list);
    }

    private boolean isEmptyFile(File file) {
        return !file.exists() || file.length() <= 0;
    }

}

