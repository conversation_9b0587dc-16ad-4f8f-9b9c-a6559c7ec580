package com.dbj.classpal.books.service.biz.audio;

import com.dbj.classpal.books.service.entity.audio.AudioContextHintMusic;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 音频文本提示音 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
public interface IAudioContextHintMusicBiz extends IService<AudioContextHintMusic> {

    /**
     * 批量删除
     * @param outCountIds
     */
    int deleteByIds(List<Integer> outCountIds);
}
