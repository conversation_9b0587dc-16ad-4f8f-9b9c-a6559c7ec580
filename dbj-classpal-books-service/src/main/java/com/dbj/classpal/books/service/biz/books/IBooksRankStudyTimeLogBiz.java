package com.dbj.classpal.books.service.biz.books;

import com.dbj.classpal.books.service.entity.books.BooksRankStudyTimeLog;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 图书卷册赠册用户学习时长表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
public interface IBooksRankStudyTimeLogBiz extends IService<BooksRankStudyTimeLog> {

    
    /**
     * <AUTHOR>
     * @Description  统计如数使用时常
     * @Date 2025/4/21 16:46
     * @param 
     * @return 
     **/
    Long getLastStudyTime(Integer rankId,Integer appUserId);
}
