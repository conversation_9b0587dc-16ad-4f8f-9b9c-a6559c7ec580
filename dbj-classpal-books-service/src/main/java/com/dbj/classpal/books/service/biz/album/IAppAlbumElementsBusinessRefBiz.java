package com.dbj.classpal.books.service.biz.album;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.client.enums.BusinessTypeEnum;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsBusinessRefQueryCommonBO;
import com.dbj.classpal.books.common.dto.album.AppAlbumElementsBusinessRefQueryDTO;
import com.dbj.classpal.books.service.entity.album.AppAlbumElementsBusinessRef;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppAlbumElementsBusinessRefBiz
 * Date:     2025-04-15 10:24:17
 * Description: 表名： ,描述： 表
 */
public interface IAppAlbumElementsBusinessRefBiz extends IService<AppAlbumElementsBusinessRef> {

    /**
     * 判断专辑分类的专辑是否存在业务关联引用
     * @param id
     * @return
     */
    Integer checkMenuRefCount(Integer id);

    /**
     * 查询专辑引用列表
     * @param bo
     * @return
     */
    List<AppAlbumElementsBusinessRefQueryDTO> getRefBusinessList(AppAlbumElementsBusinessRefQueryCommonBO bo);

    /**
     * 删除专辑引用
     * @param businessIds
     * @param typeEnum
     * @return
     */
    Boolean removeAlbumElementsBusinessRef(Set<Integer> businessIds, BusinessTypeEnum typeEnum);
}
