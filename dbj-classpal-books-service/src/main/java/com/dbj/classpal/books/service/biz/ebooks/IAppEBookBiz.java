package com.dbj.classpal.books.service.biz.ebooks;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.common.bo.ebooks.*;
import com.dbj.classpal.books.common.bo.ebooks.GetShareInfoBO;
import com.dbj.classpal.books.common.bo.ebooks.RemoveShareUrlBO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookDTO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookResourceDTO;
import com.dbj.classpal.books.common.dto.ebooks.ShareUrlResultDTO;
import com.dbj.classpal.books.service.entity.ebooks.AppEBook;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 单书 业务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
public interface IAppEBookBiz extends IService<AppEBook> {

    /**
     * 分页查询单书列表
     * 
     * @param pageRequest 分页查询参数
     * @return 单书分页数据
     */
    Page<AppEBookDTO> page(PageInfo<AppEBookQueryBO> pageRequest) throws BusinessException;

    /**
     * H5端分页查询单书列表
     * 
     * @param pageRequest H5分页查询参数
     * @return 单书分页数据
     */
    Page<AppEBookDTO> pageForH5(PageInfo<AppEBookH5QueryBO> pageRequest) throws BusinessException;

    /**
     * 查询单书详情
     * 
     * @param id 单书ID
     * @return 单书详情数据
     */
    AppEBookDTO detail(Integer id) throws BusinessException;

    /**
     * 查询单书详情
     *
     * @param ids 单书ID
     * @return 单书详情数据
     */
    List<AppEBookDTO> getDetailList(List<Integer> ids) throws BusinessException;

    /**
     * 查询单书详情（可选择是否包含资源信息）
     *
     * @param ids 单书ID列表
     * @param includeResources 是否包含资源信息
     * @return 单书详情数据
     */
    List<AppEBookDTO> getDetailList(List<Integer> ids, boolean includeResources) throws BusinessException;

    /**
     * 新增单书
     * 
     * @param saveBO 单书保存参数
     * @return 新增单书ID
     */
    Integer save(AppEBookSaveBO saveBO) throws BusinessException;

    /**
     * 更新单书
     *
     * @param updateBO 单书更新参数
     * @return 更新结果
     */
    boolean update(AppEBookUpdateBO updateBO) throws BusinessException;

    /**
     * 删除单书
     * 
     * @param id 单书ID
     * @return 删除结果
     */
    boolean delete(Integer id) throws BusinessException;

    /**
     * 批量删除单书
     * 
     * @param ids ID列表
     * @return 批量删除结果
     */
    boolean deleteBatch(List<Integer> ids) throws BusinessException;

    /**
     * 批量启用单书
     * 
     * @param ids ID列表
     * @return 批量启用结果
     */
    boolean enableBatch(List<Integer> ids) throws BusinessException;

    /**
     * 批量禁用单书
     * 
     * @param ids ID列表
     * @return 批量禁用结果
     */
    boolean disableBatch(List<Integer> ids) throws BusinessException;

    /**
     * 批量允许下载单书
     * 
     * @param ids ID列表
     * @return 批量允许下载结果
     */
    boolean allowDownloadBatch(List<Integer> ids) throws BusinessException;

    /**
     * 批量关闭下载单书
     * 
     * @param ids ID列表
     * @return 批量关闭下载结果
     */
    boolean disableDownloadBatch(List<Integer> ids) throws BusinessException;

    /**
     * 更换单书文件
     *
     * @param updateFileBO@return 更换结果
     */
    boolean updateFile(AppEBookUpdateFileBO updateFileBO) throws BusinessException;

    /**
     * 更换单书水印
     * 
     * @param id 单书ID
     * @param watermarkId 水印模板ID
     * @return 更换结果
     */
    boolean updateWatermark(Integer id, Integer watermarkId) throws BusinessException;

    /**
     * 获取封面
     *
     * @return 获取封面
     */
    String coverUrl(AppEBookFileBO fileBO) throws BusinessException;

    /**
     * 获取分享信息（包括分享链接和二维码）
     * 如果不存在则自动生成，如果已存在则直接返回
     *
     * @param request 获取分享信息请求参数
     * @return 分享链接结果
     */
    ShareUrlResultDTO getShareInfo(GetShareInfoBO request) throws BusinessException;

    /**
     * 删除分享链接
     *
     * @param request 删除分享链接请求参数
     */
    void removeShareUrl(RemoveShareUrlBO request) throws BusinessException;

    /**
     * 批量查询书籍资源信息
     *
     * @param bookIds 书籍ID列表
     * @param resourceType 资源类型（可选）
     * @return 按书籍ID分组的资源列表
     */
    Map<Integer, List<AppEBookResourceDTO>> batchQueryResources(List<Integer> bookIds, Integer resourceType) throws BusinessException;

    /**
     *  批量查询水印PDF
     * @param bookIds
     * @return
     */
    Map<Integer, String> batchQueryWatermarkedFileUrls(List<Integer> bookIds);
}