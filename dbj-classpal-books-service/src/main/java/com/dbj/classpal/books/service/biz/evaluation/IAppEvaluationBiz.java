package com.dbj.classpal.books.service.biz.evaluation;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.common.bo.evaluation.AdminEvaluationQueryBO;
import com.dbj.classpal.books.service.entity.evaluation.AppEvaluation;
import com.dbj.classpal.framework.commons.request.PageInfo;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppEvaluationBiz
 * Date:     2025-04-08 16:26:13
 * Description: 表名： ,描述： 表
 */
public interface IAppEvaluationBiz extends IService<AppEvaluation> {

    /**
     * 分页查询评测模板列表
     */
    Page<AppEvaluation> pageInfo(PageInfo<AdminEvaluationQueryBO> page);

}
