package com.dbj.classpal.books.service.entity.poem;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 古诗背诵合集表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ancient_poem_recite_collection")
@Tag(name="AncientPoemReciteCollection对象", description="古诗背诵合集表")
public class AncientPoemReciteCollection extends BizEntity implements Serializable {



    @Schema(description =  "所属分类ID")
    @TableField("category_id")
    private Integer categoryId;

    @Schema(description =  "合集标题")
    @TableField("title")
    private String title;

    @Schema(description =  "合集描述")
    @TableField("description")
    private String description;

    @Schema(description =  "封面URL")
    @TableField("cover_url")
    private String coverUrl;
    @Schema(description =  "封面URL")
    @TableField("cover_name")
    private String coverName;

    @Schema(description =  "状态（0下架，1上架）")
    @TableField("launch_status")
    private Integer launchStatus;

    @Schema(description =  "是否启用 0 否 1是")
    @TableField("status")
    private Integer status;

    @Schema(description =  "权重排序")
    @TableField("sort")
    private Integer sort;



}
