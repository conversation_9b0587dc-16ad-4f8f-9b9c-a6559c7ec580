package com.dbj.classpal.books.service.biz.studycenter.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.bo.studycenter.AppStudyModuleQuestionExtBO;
import com.dbj.classpal.books.common.bo.studycenter.AppStudyModuleQuestionExtCreateBO;
import com.dbj.classpal.books.common.enums.BusinessTypeEnum;
import com.dbj.classpal.books.service.biz.question.IQuestionCategoryBusinessRefBiz;
import com.dbj.classpal.books.service.biz.studycenter.IAppStudyModuleQuestionExtBiz;
import com.dbj.classpal.books.service.entity.question.QuestionCategoryBusinessRef;
import com.dbj.classpal.books.service.entity.studycenter.AppStudyModuleQuestionExt;
import com.dbj.classpal.books.service.mapper.studycenter.AppStudyModuleQuestionExtMapper;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AppStudyModuleQuestionExtBizImpl extends ServiceImpl<AppStudyModuleQuestionExtMapper, AppStudyModuleQuestionExt> implements IAppStudyModuleQuestionExtBiz {
    @Resource
    private IQuestionCategoryBusinessRefBiz questionCategoryBusinessRefBiz;
    @Override
    public boolean create(AppStudyModuleQuestionExtCreateBO bo, Integer moduleId) {
        AppStudyModuleQuestionExt entity = BeanUtil.copyProperties(bo, AppStudyModuleQuestionExt.class);
        entity.setModuleId(moduleId);
        this.save(entity);
        if(CollectionUtils.isNotEmpty(bo.getQuestion())){
            List<QuestionCategoryBusinessRef> questionCategoryBusinessRefList = Lists.newArrayList();
            for (AppStudyModuleQuestionExtBO questionExtBO : bo.getQuestion()) {
                QuestionCategoryBusinessRef categoryBusinessRef = new QuestionCategoryBusinessRef();
                categoryBusinessRef.setQuestionCategoryId(questionExtBO.getId());
                categoryBusinessRef.setBusinessId(entity.getId());
                categoryBusinessRef.setBusinessType(BusinessTypeEnum.STUDY_CENTER_QUESTION_BUSINESS.getCode());
                categoryBusinessRef.setSortNum(questionExtBO.getSortNum());
                questionCategoryBusinessRefList.add(categoryBusinessRef);
            }
            questionCategoryBusinessRefBiz.saveBatch(questionCategoryBusinessRefList);
        }
        return Boolean.TRUE;
    }

    @Override
    public boolean update(AppStudyModuleQuestionExtCreateBO bo, Integer moduleId) {
        this.remove(new LambdaQueryWrapper<AppStudyModuleQuestionExt>().eq(AppStudyModuleQuestionExt::getModuleId,moduleId));
        return this.create(bo,moduleId);
    }
}