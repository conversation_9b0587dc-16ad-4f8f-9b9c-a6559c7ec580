package com.dbj.classpal.books.service.biz.material;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.client.dto.material.AppMaterialPathDTO;
import com.dbj.classpal.books.common.bo.material.AppMaterialIOBO;
import com.dbj.classpal.books.common.bo.material.AppMaterialQueryBO;
import com.dbj.classpal.books.common.dto.material.AppMaterialQueryDTO;
import com.dbj.classpal.books.service.entity.material.AppMaterial;
import com.dbj.classpal.framework.commons.request.PageInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppMaterialBusiness
 * Date:     2025-04-08 16:26:13
 * Description: 表名： ,描述： 表
 */
public interface IAppMaterialBiz extends IService<AppMaterial> {

    /**
     * 分页查询素材中心
     */
    Page<AppMaterial> pageMaterials(PageInfo<AppMaterialQueryBO> page);

    /**
     * 查询该文件夹父节点列表
     * @param id
     * @return
     */
    List<AppMaterial> getMaterialParentsPath(Integer id);


    /**
     * 查询该文件夹父节点拼接文件名
     * @param id
     * @return
     */
    String getMaterialParentsNames(Integer id);


    /**
     * 查询所有子节点，并判断目标ID是否存在
     * @param ioBo
     * @return
     */
    Integer checkAimIdInChildren(AppMaterialIOBO ioBo);



    /**
     * 获取当前文件夹下所有文件
     * @return
     */
    List<AppMaterial> getChildrenFiles(Integer id);



    /**
     * 获取多个文件夹下所有文件
     * @return
     */
    List<AppMaterial> getBatchDirChildrenFiles(List<Integer>ids);


    /**
     * 查询总时长
     * @param idSet
     * @return
     */
    Integer sumDuration(Set<Integer>idSet);

    List<AppMaterialPathDTO> getPathList(List<Integer> materialIds);
}
