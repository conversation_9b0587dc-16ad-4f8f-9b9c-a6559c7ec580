package com.dbj.classpal.books.service.mapper.ebooks;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dbj.classpal.books.service.dto.StoreShelfMappingDTO;
import com.dbj.classpal.books.service.entity.product.AppEBookshelf;
import com.dbj.classpal.books.service.entity.product.AppEBookstoreShelfRef;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 书城-书架关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Mapper
public interface AppEBookstoreShelfRefMapper extends BaseMapper<AppEBookstoreShelfRef> {

    /**
     * 根据书城ID查询关联的书架列表
     *
     * @param storeId 书城ID
     * @return 书架列表
     */
    List<AppEBookshelf> getShelvesByStoreId(@Param("storeId") Long storeId);
    
    /**
     * 批量插入书城-书架关联
     *
     * @param refs 关联记录列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<AppEBookstoreShelfRef> refs);

    /**
     * 根据书城ID列表查询关联的书架ID列表（使用窗口函数排序）
     *
     * @param storeIds 书城ID列表
     * @return 书架ID列表
     */
    List<Integer> getShelfWithDetailsByStoreIds(@Param("storeIds") List<Integer> storeIds);

    /**
     * 批量查询多个书城的书架ID映射（使用窗口函数排序）
     *
     * @param storeIds 书城ID列表
     * @return 书城ID和书架ID的映射关系列表
     */
    List<StoreShelfMappingDTO> batchGetShelfIdsByStoreIds(@Param("storeIds") List<Integer> storeIds);

}