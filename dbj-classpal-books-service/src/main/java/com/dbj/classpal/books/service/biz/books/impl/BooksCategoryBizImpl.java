package com.dbj.classpal.books.service.biz.books.impl;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.business.external.client.model.response.BasicConfigProductCategoryResponse;
import com.dbj.classpal.books.client.dto.books.BooksCategoryNameTreeApiDTO;
import com.dbj.classpal.books.service.api.client.printer.service.PrinterRemoteService;
import com.dbj.classpal.books.service.entity.books.BooksCategory;
import com.dbj.classpal.books.service.mapper.books.BooksCategoryMapper;
import com.dbj.classpal.books.service.biz.books.IBooksCategoryBiz;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.service.openapi.facade.ApiServiceFacade;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Stack;
import java.util.stream.Collectors;

/**
 * <p>
 * 产品分类配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Service
public class BooksCategoryBizImpl extends ServiceImpl<BooksCategoryMapper, BooksCategory> implements IBooksCategoryBiz {


    @Resource
    private ApiServiceFacade apiServiceFacade;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sync() throws BusinessException {
        PrinterRemoteService printerRemoteService = apiServiceFacade.printerRemoteService();
        List<BasicConfigProductCategoryResponse> basicConfigProductCategoryResponses = printerRemoteService.getProductCategoryTreeList();
        if(CollectionUtils.isNotEmpty(basicConfigProductCategoryResponses)){

            List<BooksCategory> booksCategoryList = BeanUtil.copyToList(flattenTreeIterative(basicConfigProductCategoryResponses),BooksCategory.class);

            List<Integer> syncIds = booksCategoryList.stream().map(BooksCategory::getId).collect(Collectors.toList());
            //查询现有所有分类
            List<BooksCategory> booksCategoryListExist = this.list();

            if(CollectionUtils.isNotEmpty(booksCategoryListExist)){
                List<Integer> categoryIds =booksCategoryListExist.stream().map(BooksCategory::getId).collect(Collectors.toList());
                //左边移除右边 剩余的表示被删除了
                List<Integer> tremIds = new ArrayList<>(categoryIds);
                tremIds.removeAll(syncIds);
                if(CollectionUtils.isNotEmpty(tremIds)){
                    //删除
                    baseMapper.delete(tremIds);
                }
                for(BooksCategory bookCategory : booksCategoryList){
                    if(categoryIds.contains(bookCategory.getId())){
                        this.updateById(bookCategory);
                    }else {
                        this.save(bookCategory);
                    }
                }
            }else{

                this.saveBatch(booksCategoryList);
            }
        }
    }

    @Override
    public List<BooksCategoryNameTreeApiDTO> categoryNameTree(List<Integer> categoryIds) {
        return baseMapper.categoryNameTree(categoryIds);
    }


    public static List<BasicConfigProductCategoryResponse> flattenTreeIterative(List<BasicConfigProductCategoryResponse> roots) {
        List<BasicConfigProductCategoryResponse> result = new ArrayList<>();
        Stack<BasicConfigProductCategoryResponse> stack = new Stack<>();

        // 初始化栈
        roots.forEach(stack::push);
        while (!stack.isEmpty()) {
            BasicConfigProductCategoryResponse node = stack.pop();
            result.add(node);
            // 子节点逆序入栈，保证顺序一致
            if (node.getChildList() != null) {
                for (int i = node.getChildList().size() - 1; i >= 0; i--) {
                    stack.push(node.getChildList().get(i));
                }
            }
        }
        return result;
    }
}
