package com.dbj.classpal.books.service.biz.audio;

import com.dbj.classpal.books.client.dto.audio.AudioClassifyPathDTO;
import com.dbj.classpal.books.service.entity.audio.AudioClassify;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 音频分类 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
public interface IAudioClassifyBiz extends IService<AudioClassify> {

    List<AudioClassifyPathDTO> getPathList(List<Integer> audioIntroIds);

    Set<Integer> getChildren(Set<Integer> ids);

    Set<Integer> getParentIds(Integer id);
}
