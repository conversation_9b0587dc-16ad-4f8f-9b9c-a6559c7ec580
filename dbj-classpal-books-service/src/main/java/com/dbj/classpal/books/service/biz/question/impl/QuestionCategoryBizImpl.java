package com.dbj.classpal.books.service.biz.question.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.bo.question.QuestionCategoryBO;
import com.dbj.classpal.books.common.bo.question.QuestionCategoryQueryBO;
import com.dbj.classpal.books.common.bo.question.QuestionCategorySortBO;
import com.dbj.classpal.books.common.dto.question.QuestionCategoryDTO;
import com.dbj.classpal.books.service.biz.question.QuestionCategoryBiz;
import com.dbj.classpal.books.service.entity.question.Question;
import com.dbj.classpal.books.service.entity.question.QuestionCategory;
import com.dbj.classpal.books.service.mapper.question.QuestionCategoryMapper;
import com.dbj.classpal.books.service.mapper.question.QuestionMapper;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * 题目分类业务实现类
 */
@Service
@RequiredArgsConstructor
public class QuestionCategoryBizImpl extends ServiceImpl<QuestionCategoryMapper, QuestionCategory> implements QuestionCategoryBiz {

    private final QuestionCategoryMapper questionCategoryMapper;
    private final QuestionMapper questionMapper;

    /**
     * 根节点的父ID
     */
    public static final Integer ROOT_FATHER_ID = 0;

    public static final Integer DEPTH = 5;

    @Override
    public List<QuestionCategoryDTO> getCategoryTree(QuestionCategoryQueryBO queryBO) {
        LambdaQueryWrapper<QuestionCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(queryBO.getFatherId()), QuestionCategory::getFatherId, queryBO.getFatherId())
              .eq(Objects.nonNull(queryBO.getStatus()), QuestionCategory::getStatus, queryBO.getStatus())
              .like(StringUtils.isNotEmpty(queryBO.getName()), QuestionCategory::getName, queryBO.getName())
              .orderByAsc(QuestionCategory::getSortNum);

        List<QuestionCategory> categories = list(wrapper);
        if (CollectionUtils.isEmpty(categories)) {
            return new ArrayList<>();
        }

        List<QuestionCategoryDTO> dtoList = categories.stream()
                .map(category -> {
                    QuestionCategoryDTO dto = convertToDTO(category);
                    // 获取所有子节点数量（包含所有层级）
                    assert dto != null;
                    dto.setChildrenCount(countAllChildren(category.getId()));
                    // 获取当前节点的题目数量
                    dto.setQuestionCount(countQuestionsByCategoryId(category.getId()));
                    return dto;
                })
                .toList();

        // 如果需要包含子分类，则构建树形结构
//        if (Boolean.TRUE.equals(queryBO.getIncludeChildren())) {
//            return buildTree(categories, queryBO.getFatherId());
//        }
        return buildTree(categories, queryBO.getFatherId() == null ? ROOT_FATHER_ID : queryBO.getFatherId());
    }

    @Override
    public List<QuestionCategoryDTO> getCategoryWithChildren(Integer categoryId) {
        if (Objects.isNull(categoryId)) {
            return new ArrayList<>();
        }

        List<QuestionCategoryDTO> result = new ArrayList<>();
        QuestionCategory category = getById(categoryId);
        if (Objects.isNull(category)) {
            return result;
        }

        QuestionCategoryDTO dto = convertToDTO(category);
        result.add(dto);

        // 递归获取子分类
        LambdaQueryWrapper<QuestionCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(QuestionCategory::getFatherId, categoryId)
              .orderByAsc(QuestionCategory::getSortNum);
        List<QuestionCategory> children = list(wrapper);

        if (!CollectionUtils.isEmpty(children)) {
            for (QuestionCategory child : children) {
                result.addAll(getCategoryWithChildren(child.getId()));
            }
        }

        return result;
    }

    @Override
    public Integer createCategory(QuestionCategoryBO categoryBO) throws BusinessException {
        if (Objects.isNull(categoryBO)) {
            throw new BusinessException(APP_QUESTION_CATEGORY_PARAM_NOT_NULL_CODE,APP_QUESTION_CATEGORY_PARAM_NOT_NULL_MSG);
        }
        if (StringUtils.isEmpty(categoryBO.getName())) {
            throw new BusinessException(APP_QUESTION_CATEGORY_NAME_NOT_NULL_CODE,APP_QUESTION_CATEGORY_NAME_NOT_NULL_MSG);
        }
        
        if (categoryBO.getFatherId() != null && !Objects.equals(categoryBO.getFatherId(), ROOT_FATHER_ID)) {
            int depth = calculateNodeDepth(categoryBO.getFatherId());
            if (depth >= DEPTH) {
                throw new BusinessException(APP_ALBUM_MENUS_ORDER_COVER_MAX_FAIL_CODE, APP_ALBUM_MENUS_ORDER_COVER_MAX_FAIL_MSG);
            }
        }

        QuestionCategory category = new QuestionCategory();
        BeanUtil.copyProperties(categoryBO, category);
        Integer maxSortNum = getMaxSortNum(categoryBO.getFatherId());
        category.setSortNum(maxSortNum + 1);
        save(category);
        return category.getId();
    }

    @Override
    public void updateCategory(QuestionCategoryBO categoryBO) throws BusinessException {
        if (Objects.isNull(categoryBO) || Objects.isNull(categoryBO.getId())) {
            throw new BusinessException(APP_QUESTION_CATEGORY_PARAM_NOT_NULL_CODE,APP_QUESTION_CATEGORY_PARAM_NOT_NULL_MSG);
        }
        if (StringUtils.isEmpty(categoryBO.getName())) {
            throw new BusinessException(APP_QUESTION_CATEGORY_NAME_NOT_NULL_CODE,APP_QUESTION_CATEGORY_NAME_NOT_NULL_MSG);
        }

        QuestionCategory category = getById(categoryBO.getId());

        if (Objects.isNull(category)) {
            throw new BusinessException(APP_QUESTION_CATEGORY_NOT_EXIST_CODE,APP_QUESTION_CATEGORY_NOT_EXIST_MSG);
        }

        if (categoryBO.getFatherId() != null && !Objects.equals(categoryBO.getFatherId(), ROOT_FATHER_ID)
                && !Objects.equals(categoryBO.getFatherId(), category.getFatherId())) {
            int depth = calculateNodeDepth(categoryBO.getFatherId());
            if (depth >= DEPTH) {
                throw new BusinessException(APP_ALBUM_MENUS_ORDER_COVER_MAX_FAIL_CODE, APP_ALBUM_MENUS_ORDER_COVER_MAX_FAIL_MSG);
            }
        }

        BeanUtil.copyProperties(categoryBO, category);
        updateById(category);
    }


    @Override
    public void updateSort(QuestionCategorySortBO sortBO) throws BusinessException {
        if (Objects.isNull(sortBO) || Objects.isNull(sortBO.getId())) {
            throw new BusinessException(APP_QUESTION_CATEGORY_PARAM_NOT_NULL_CODE, APP_QUESTION_CATEGORY_PARAM_NOT_NULL_MSG);
        }

        QuestionCategory category = getById(sortBO.getId());
        if (Objects.isNull(category)) {
            throw new BusinessException(APP_QUESTION_CATEGORY_NOT_EXIST_CODE, APP_QUESTION_CATEGORY_NOT_EXIST_MSG);
        }

        QuestionCategory parentCategory = null;
        if (sortBO.getFatherId() != 0) {
            parentCategory = getById(sortBO.getFatherId());
            if (Objects.isNull(parentCategory)) {
                throw new BusinessException(APP_QUESTION_CATEGORY_NOT_EXIST_CODE, APP_QUESTION_CATEGORY_NOT_EXIST_MSG);
            }
            
            int depth = calculateNodeDepth(sortBO.getFatherId());
            if (depth >= DEPTH) {
                throw new BusinessException(APP_ALBUM_MENUS_ORDER_COVER_MAX_FAIL_CODE, APP_ALBUM_MENUS_ORDER_COVER_MAX_FAIL_MSG);
            }
        }

        category.setFatherId(sortBO.getFatherId());

        if (sortBO.getAimId() != null && sortBO.getAimId() != -1) {
            QuestionCategory aimCategory = getById(sortBO.getAimId());
            if (Objects.isNull(aimCategory)) {
                throw new BusinessException(APP_QUESTION_CATEGORY_NOT_EXIST_CODE, APP_QUESTION_CATEGORY_NOT_EXIST_MSG);
            }
            if (!Objects.equals(aimCategory.getFatherId(), sortBO.getFatherId())) {
                throw new BusinessException(APP_QUESTION_CATEGORY_MOVE_NOT_SUPPORT_CODE, APP_QUESTION_CATEGORY_MOVE_NOT_SUPPORT_MSG);
            }
            List<QuestionCategory> siblings = list(new LambdaQueryWrapper<QuestionCategory>()
                    .eq(QuestionCategory::getFatherId, sortBO.getFatherId())
                    .orderByAsc(QuestionCategory::getSortNum));

            if (sortBO.getOrder() != null && sortBO.getOrder() == -1) {
                int targetIndex = -1;
                for (int i = 0; i < siblings.size(); i++) {
                    if (Objects.equals(siblings.get(i).getId(), sortBO.getAimId())) {
                        targetIndex = i;
                        break;
                    }
                }

                if (targetIndex != -1) {
                    category.setSortNum(siblings.get(targetIndex).getSortNum());

                    for (int i = targetIndex; i < siblings.size(); i++) {
                        QuestionCategory sibling = siblings.get(i);
                        if (!Objects.equals(sibling.getId(), sortBO.getId())) {
                            sibling.setSortNum(sibling.getSortNum() + 1);
                            updateById(sibling);
                        }
                    }
                }
            } else {
                int targetIndex = -1;
                for (int i = 0; i < siblings.size(); i++) {
                    if (Objects.equals(siblings.get(i).getId(), sortBO.getAimId())) {
                        targetIndex = i;
                        break;
                    }
                }

                if (targetIndex != -1) {
                    category.setSortNum(siblings.get(targetIndex).getSortNum() + 1);

                    for (int i = targetIndex + 1; i < siblings.size(); i++) {
                        QuestionCategory sibling = siblings.get(i);
                        if (!Objects.equals(sibling.getId(), sortBO.getId())) {
                            sibling.setSortNum(sibling.getSortNum() + 1);
                            updateById(sibling);
                        }
                    }
                }
            }
        } else {
            Integer maxSortNum = baseMapper.selectMaxSortNum(sortBO.getFatherId());
            category.setSortNum(maxSortNum == null ? 1 : maxSortNum + 1);
        }

        updateById(category);
    }

    @Override
    public Long countQuestionsByCategoryId(Integer categoryId) {
        return questionCategoryMapper.countQuestionsByCategoryId(categoryId);
    }

    @Override
    public Long countCurrentQuestionsByCategoryId(Integer categoryId) {
        return questionMapper.selectCount(new LambdaQueryWrapper<Question>().eq(Question::getQuestionCategoryId, categoryId));
    }

    @Override
    public boolean checkNameExists(String name, Integer fatherId, Integer excludeId) {
        if (StringUtils.isEmpty(name)) {
            return false;
        }

        LambdaQueryWrapper<QuestionCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(QuestionCategory::getName, name)
              .eq(QuestionCategory::getFatherId, fatherId)
              .ne(Objects.nonNull(excludeId), QuestionCategory::getId, excludeId);
        return count(wrapper) > 0;
    }

    @Override
    public boolean hasChildren(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        LambdaQueryWrapper<QuestionCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(QuestionCategory::getFatherId, ids);
        return count(wrapper) > 0;
    }

    /**
     * 获取同级分类的最大排序号
     */
    private Integer getMaxSortNum(Integer fatherId) {
        LambdaQueryWrapper<QuestionCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(QuestionCategory::getFatherId, fatherId)
              .orderByDesc(QuestionCategory::getSortNum)
              .last("LIMIT 1");
        QuestionCategory category = getOne(wrapper);
        return Objects.nonNull(category) ? category.getSortNum() : 0;
    }

    /**
     * 统计所有层级的子节点数量
     *
     * @param categoryId 分类ID
     * @return 所有子节点数量
     */
    private Long countAllChildren(Integer categoryId) {
        LambdaQueryWrapper<QuestionCategory> directWrapper = new LambdaQueryWrapper<>();
        directWrapper.eq(QuestionCategory::getFatherId, categoryId);
        List<QuestionCategory> directChildren = list(directWrapper);
        
        if (CollectionUtils.isEmpty(directChildren)) {
            return 0L;
        }
        
        long count = directChildren.size();
        for (QuestionCategory child : directChildren) {
            count += countAllChildren(child.getId());
        }
        
        return count;
    }

    /**
     * 构建树形结构
     */
    private List<QuestionCategoryDTO> buildTree(List<QuestionCategory> categories, Integer fatherId) {
        return categories.stream()
                .filter(category -> Objects.equals(category.getFatherId(), fatherId))
                .map(category -> {
                    QuestionCategoryDTO dto = convertToDTO(category);
                    assert dto != null;

                    List<QuestionCategoryDTO> children = buildTree(categories, category.getId());

                    if (!CollectionUtils.isEmpty(children)) {
                        dto.setChildren(children);
                        dto.setChildrenCount(countAllChildren(category.getId()));
                    } else {
                        dto.setChildrenCount(0L);
                    }

                    dto.setQuestionCount(countQuestionsByCategoryId(category.getId()));
                    dto.setCurrentQuestionCount(countCurrentQuestionsByCategoryId(category.getId()));

                    return dto;
                })
                .collect(Collectors.toList());
    }

    /**
     * 重新排序同级分类
     */
    private void reorderSiblings(Integer fatherId) {
        LambdaQueryWrapper<QuestionCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(QuestionCategory::getFatherId, fatherId)
              .orderByAsc(QuestionCategory::getSortNum);
        List<QuestionCategory> siblings = list(wrapper);

        if (!CollectionUtils.isEmpty(siblings)) {
            for (int i = 0; i < siblings.size(); i++) {
                QuestionCategory sibling = siblings.get(i);
                if (!Objects.equals(sibling.getSortNum(), i + 1)) {
                    sibling.setSortNum(i + 1);
                    updateById(sibling);
                }
            }
        }
    }

    /**
     * 转换为 DTO
     */
    private QuestionCategoryDTO convertToDTO(QuestionCategory category) {
        if (Objects.isNull(category)) {
            return null;
        }
        QuestionCategoryDTO dto = new QuestionCategoryDTO();
        BeanUtil.copyProperties(category, dto);
        return dto;
    }

    /**
     * 计算节点深度
     * 
     * @param categoryId 分类ID
     * @return 节点深度
     */
    private int calculateNodeDepth(Integer categoryId) {
        if (categoryId == null || Objects.equals(categoryId, ROOT_FATHER_ID)) {
            return 0;
        }
        
        int depth = 1;
        QuestionCategory category = getById(categoryId);
        
        while (category != null && !Objects.equals(category.getFatherId(), ROOT_FATHER_ID)) {
            depth++;
            category = getById(category.getFatherId());
        }
        
        return depth;
    }
} 