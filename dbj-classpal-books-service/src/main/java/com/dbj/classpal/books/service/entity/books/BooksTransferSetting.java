package com.dbj.classpal.books.service.entity.books;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 图书跳转设置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("books_transfer_setting")
@Tag(name="BooksTransferSetting对象", description="图书跳转设置")
public class BooksTransferSetting extends BizEntity implements Serializable {




    @Schema(description = "图书id")
    @TableField("book_id")
    private Integer bookId;

    @Schema(description = "中专类型 H5")
    @TableField("transfer_type")
    private String transferType;

    @Schema(description = "是否启用 1-是 0-否")
    @TableField("status")
    private Integer status;

}
