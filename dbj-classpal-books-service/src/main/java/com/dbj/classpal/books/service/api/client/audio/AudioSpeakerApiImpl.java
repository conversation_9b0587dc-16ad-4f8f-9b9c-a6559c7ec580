package com.dbj.classpal.books.service.api.client.audio;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dbj.classpal.books.client.api.audio.AudioSpeakerApi;
import com.dbj.classpal.books.client.bo.audio.AudioSpeakerBO;
import com.dbj.classpal.books.client.dto.audio.AudioEmotionClassifyConfigDTO;
import com.dbj.classpal.books.client.dto.audio.AudioSpeakerDTO;
import com.dbj.classpal.books.common.enums.audio.AudioSpeakerTypeEnum;
import com.dbj.classpal.books.service.biz.audio.IAudioContextInfoBiz;
import com.dbj.classpal.books.service.biz.audio.IAudioEmotionClassifyConfigBiz;
import com.dbj.classpal.books.service.biz.audio.IAudioSpeakerBiz;
import com.dbj.classpal.books.service.entity.audio.AudioEmotionClassifyConfig;
import com.dbj.classpal.books.service.entity.audio.AudioSpeaker;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 发音人
 * <AUTHOR>
 * @since 2025-06-25
 */
@RestController
public class AudioSpeakerApiImpl implements AudioSpeakerApi {

    @Autowired
    private IAudioSpeakerBiz audioSpeakerBiz;
    @Autowired
    private IAudioEmotionClassifyConfigBiz audioEmotionClassifyConfigBiz;

    @Override
    public RestResponse<List<AudioSpeakerDTO>> list(AudioSpeakerBO bo) throws BusinessException {
        LambdaQueryWrapper<AudioSpeaker> wrapper = Wrappers.lambdaQuery();
        if (CollectionUtil.isNotEmpty(bo.getVoiceType())) {
            wrapper.in(AudioSpeaker::getVoiceType, bo.getVoiceType());
        }
        if (CollectionUtil.isNotEmpty(bo.getLanguage())) {
            wrapper.in(AudioSpeaker::getLanguage, bo.getLanguage());
        }
        if (bo.getVoiceName() != null) {
            wrapper.like(AudioSpeaker::getName, bo.getVoiceName());
        }
        List<AudioSpeaker> speakerList = audioSpeakerBiz.list(wrapper);
        if (CollectionUtil.isEmpty(speakerList)) {
            return RestResponse.success(new ArrayList<>());
        }

        List<AudioSpeakerDTO> resultList = speakerList.stream().map(speaker -> {
            AudioSpeakerDTO dto = new AudioSpeakerDTO();
            BeanUtils.copyProperties(speaker, dto);
            return dto;
        }).toList();

        // 查询多情感列表
        boolean isEmotion = speakerList.stream().anyMatch(v -> Objects.equals(v.getVoiceType(), AudioSpeakerTypeEnum.EMOTION.getCode()));
        if (isEmotion) {
            Set<Integer> speakerIds = speakerList.stream().filter(v -> Objects.equals(v.getVoiceType(), AudioSpeakerTypeEnum.EMOTION.getCode()))
                    .map(AudioSpeaker::getId).collect(Collectors.toSet());
            List<AudioEmotionClassifyConfig> emotionList = audioEmotionClassifyConfigBiz.lambdaQuery()
                    .in(AudioEmotionClassifyConfig::getAudioSpeakerId, speakerIds).list();
            Assert.isFalse(CollectionUtil.isEmpty(emotionList), "多情感分类配置为空！");
            Map<Integer, List<AudioEmotionClassifyConfig>> emotionGrouping = emotionList.stream().collect(Collectors.groupingBy(AudioEmotionClassifyConfig::getAudioSpeakerId));
            for (AudioSpeakerDTO speaker : resultList) {
                if (!Objects.equals(speaker.getVoiceType(), AudioSpeakerTypeEnum.EMOTION.getCode())) {
                    continue;
                }
                // 封装多情感
                List<AudioEmotionClassifyConfigDTO> emotionDTOList = new ArrayList<>();
                if (emotionGrouping.containsKey(speaker.getId())) {
                    emotionGrouping.get(speaker.getId()).forEach(emotion -> {
                        AudioEmotionClassifyConfigDTO emotionDTO = new AudioEmotionClassifyConfigDTO();
                        BeanUtils.copyProperties(emotion, emotionDTO);
                        emotionDTOList.add(emotionDTO);
                    });
                    speaker.setEmotionList(emotionDTOList);
                }
            }

        }
        return RestResponse.success(resultList);
    }
}
