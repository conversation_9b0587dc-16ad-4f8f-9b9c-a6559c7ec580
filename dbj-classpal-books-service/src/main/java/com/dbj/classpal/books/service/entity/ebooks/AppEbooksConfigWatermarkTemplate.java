package com.dbj.classpal.books.service.entity.ebooks;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.models.auth.In;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_ebooks_config_watermark_template")
@Tag(name="电子样书-样书配置-水印模板", description="电子样书-样书配置-水印模板")
public class AppEbooksConfigWatermarkTemplate extends BizEntity {

    @TableField("watermark")
    @Schema(name = "水印图片")
    private String watermark;

    @TableField("watermark_name")
    @Schema(name = "水印图片名称")
    private String watermarkName;

    @TableField("template_name")
    @Schema(name = "模板名称")
    private String templateName;

    @TableField("scale")
    @Schema(name = "水印大小（倍率0.1~1）")
    private Float scale;

    @TableField("transparency")
    @Schema(name = "透明度（倍率0.1~1）")
    private Float transparency;

    @TableField("rotation_angle")
    @Schema(name = "旋转角度（0°~360°)")
    private Float rotationAngle;

    @TableField("horizontal_spacing")
    @Schema(name = "横向间距（倍率0.1~2）")
    private Float horizontalSpacing;

    @TableField("vertical_spacing")
    @Schema(name = "纵向间距（倍率0.1~2）")
    private Float verticalSpacing;

    @TableField("sort")
    @Schema(name = "权重")
    private Integer sort;
}
