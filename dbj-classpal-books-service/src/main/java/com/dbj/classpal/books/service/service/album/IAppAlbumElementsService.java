package com.dbj.classpal.books.service.service.album;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.common.bo.album.*;
import com.dbj.classpal.books.common.bo.common.CommonIdBO;
import com.dbj.classpal.books.common.bo.common.CommonIdsBO;
import com.dbj.classpal.books.common.dto.album.AppAlbumElementsQueryDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppAlbumElementsService
 * Date:     2025-04-15 10:18:23
 * Description: 表名： ,描述： 表
 */
@Mapper
public interface IAppAlbumElementsService {

    /**
     * 查询专辑列表
     * @param appAlbumElementsQueryBO
     * @return
     * @throws BusinessException
     */
    List<AppAlbumElementsQueryDTO> getAppAlbumElementsList(AppAlbumElementsQueryBO appAlbumElementsQueryBO) throws BusinessException;


    /**
     * 分页查询专辑列表
     * @param pageRequest
     * @return
     */
    Page<AppAlbumElementsQueryDTO> pageInfo(PageInfo<AppAlbumElementsQueryBO> pageRequest);


    /**
     * 新增专辑
     * @param bo
     * @return
     */
    Boolean saveAppAlbumElements(AppAlbumElementsSaveBO bo);


    /**
     * 批量删除专辑
     * @param bo
     * @return
     */
    Boolean deleteAppAlbumElements(CommonIdsBO bo) throws BusinessException;


    /**
     * 获取单个专辑信息
     * @param bo
     * @return
     */
    AppAlbumElementsQueryDTO getAppAlbumElement(CommonIdBO bo);

    /**
     * 修改专辑标题
     * @param bo
     * @return
     */
    Boolean updateAppAlbumElement(AppAlbumElementsUpdateBO bo);


    /**
     * 修改专辑标题
     * @param bo
     * @return
     */
    Boolean updateAppAlbumElementTitle(AppAlbumElementsUpdateBO bo);


    /**
     * 修改专辑简介
     * @param bo
     * @return
     */
    Boolean updateAppAlbumElementRemark(AppAlbumElementsUpdateBO bo);

    /**
     * 修改专辑隐藏状态
     * @param bo
     * @return
     */
    Boolean updateAppAlbumElementVisible(AppAlbumElementsUpdateVisibleBO bo);

    /**
     * 修改专辑上架状态
     * @param bo
     * @return
     */
    Boolean updateAppAlbumElementStatus(AppAlbumElementsUpdateStatusBO bo);


    /**
     * 修改封面
     * @param bo
     * @return
     */
    Boolean updateAppAlbumElementCover(AppAlbumElementsUpdateBO bo);
}
