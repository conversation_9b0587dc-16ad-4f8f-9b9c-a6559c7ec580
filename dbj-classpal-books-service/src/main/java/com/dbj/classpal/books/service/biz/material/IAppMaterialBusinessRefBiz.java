package com.dbj.classpal.books.service.biz.material;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.client.bo.material.AppCommonMediaBO;
import com.dbj.classpal.books.client.dto.material.AppCommonMediaDTO;
import com.dbj.classpal.books.client.dto.material.AppMaterialPathDTO;
import com.dbj.classpal.books.common.bo.common.CommonIdBO;
import com.dbj.classpal.books.common.bo.material.AppMaterialBusinessRefQueryBO;
import com.dbj.classpal.books.common.bo.material.AppMaterialBusinessRefQueryCommonBO;
import com.dbj.classpal.books.common.dto.material.AppMaterialBusinessRefDTO;
import com.dbj.classpal.books.common.dto.material.AppMaterialBusinessRefTypeCountDTO;
import com.dbj.classpal.books.client.enums.BusinessTypeEnum;
import com.dbj.classpal.books.service.entity.material.AppMaterialBusinessRef;
import com.dbj.classpal.framework.commons.request.PageInfo;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppMaterialBusiness
 * Date:     2025-04-14 09:59:34
 * Description: 表名： ,描述： 表
 */
public interface IAppMaterialBusinessRefBiz extends IService<AppMaterialBusinessRef> {

    /**
     * 查询关联引用列表
     * @param bo
     * @return
     */
    List<AppMaterialBusinessRefDTO> refBusinessList(AppMaterialBusinessRefQueryCommonBO bo);


    /**
     * <AUTHOR>
     * @Description
     * @Date 2025/4/25 13:51
     * @param businessIds 业务id集合
     * @param businessType 业务类型
     * @param appMaterialTypes 资源类型
     * @return AppMaterialBusinessRefDTO
     **/
    List<AppMaterialBusinessRefDTO> getBusinessList(List<Integer> businessIds,
                                                    Integer businessType,
                                                    List<Integer> appMaterialTypes);

    /**
     * 查询关联引用素材类型关联数量
     * @param bo
     * @return
     */
    List<AppMaterialBusinessRefTypeCountDTO> refBusinessTypeCount(CommonIdBO bo);


    /**
     * 分页素材中心查询关联
     * @param bo
     * @return
     */
    Page<AppMaterialBusinessRefDTO>pageInfo(PageInfo<AppMaterialBusinessRefQueryBO>bo);

    /**
     * 获取通用素材中心列表
     */
    List<AppCommonMediaDTO> getCommonBusinessList(Collection<Integer> businessIds, BusinessTypeEnum typeEnum);

    /**
     * 插入通用素材中心
     */
    boolean insert(BusinessTypeEnum typeEnum, Integer businessId, List<AppCommonMediaBO> medias);
    /**
     * 删除通用素材中心
     */
    boolean delete(Collection<Integer> businessIds, BusinessTypeEnum typeEnum);
}
