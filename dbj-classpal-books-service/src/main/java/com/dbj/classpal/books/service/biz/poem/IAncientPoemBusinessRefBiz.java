package com.dbj.classpal.books.service.biz.poem;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.client.bo.poem.AncientPoemBusinessRefBO;
import com.dbj.classpal.books.client.bo.poem.app.AncientPoemBusinessRefPageBO;
import com.dbj.classpal.books.client.dto.poem.app.AncientPoemBusinessRefPageDTO;
import com.dbj.classpal.books.common.enums.poem.PoemBusinessTypeEnum;
import com.dbj.classpal.books.client.bo.poem.AncientPoemBusinessRefListBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemBusinessRefListDTO;
import com.dbj.classpal.books.common.dto.poem.AncientPoemBusinessRefCountDTO;
import com.dbj.classpal.books.service.entity.poem.AncientPoemBusinessRef;
import com.dbj.classpal.framework.commons.request.PageInfo;

import java.util.Collection;
import java.util.List;

import java.util.List;

/**
 * <p>
 * 古诗文关联业务关系表 业务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
public interface IAncientPoemBusinessRefBiz extends IService<AncientPoemBusinessRef> {

    /**
     * 插入通用素材中心
     */
    boolean insert(PoemBusinessTypeEnum businessTypeEnum, Integer businessId, List<AncientPoemBusinessRefBO> refs);
    /**
     * 删除通用素材中心
     */
    boolean delete(Collection<Integer> businessIds, PoemBusinessTypeEnum businessTypeEnum);


    List<AncientPoemBusinessRefCountDTO> getCount(List<Integer> businessIds, Integer businessType);

    List<AncientPoemBusinessRefListDTO> listAncientPoemBusinessRef(AncientPoemBusinessRefListBO anomalyPoemBusinessRefListBO);

    Page<AncientPoemBusinessRefPageDTO> pageAncientPoemBusinessRef(PageInfo<AncientPoemBusinessRefPageBO> pageInfo);
}
