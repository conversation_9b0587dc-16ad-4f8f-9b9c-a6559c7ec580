//package com.dbj.classpal.books.service.entity.config;
//
//import com.baomidou.mybatisplus.annotation.TableName;
//import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
//@Data
//@EqualsAndHashCode(callSuper = true)
//@TableName("content_config_category")
//public class ContentConfigCategory extends BizEntity {
//
//    private Integer parentId;
//
//    /**
//     * 配置类型code
//     */
//    private String code;
//
//    /**
//     * 配置类型 1-题库学科 2-学习中心
//     */
//    private Integer type;
//
//    /**
//     * 配置名称
//     */
//    private String name;
//
//    /**
//     * 配置图标
//     */
//    private String icon;
//
//    /**
//     * 排序权重
//     */
//    private Integer sortNum;
//
//    /**
//     * 状态0-禁用1-启用
//     */
//    private Integer status;
//}