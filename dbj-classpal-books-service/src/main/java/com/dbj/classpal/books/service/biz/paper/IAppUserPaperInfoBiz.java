package com.dbj.classpal.books.service.biz.paper;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.common.bo.paper.CreateUserPaperBO;
import com.dbj.classpal.books.common.dto.paper.UserPaperDTO;
import com.dbj.classpal.books.service.entity.paper.AppUserPaperInfo;
import com.dbj.classpal.framework.commons.exception.BusinessException;

/**
 * 用户试卷信息业务接口
 */
public interface IAppUserPaperInfoBiz extends IService<AppUserPaperInfo> {
    
    /**
     * 创建试卷
     *
     * @param createBO 创建试卷参数
     * @return 试卷信息
     */
    UserPaperDTO createPaper(CreateUserPaperBO createBO) throws BusinessException;

    /**
     * 获取试卷信息
     *
     * @param paperId 试卷ID
     * @return 试卷信息
     */
    UserPaperDTO getPaperInfo(Integer paperId) throws BusinessException;
} 