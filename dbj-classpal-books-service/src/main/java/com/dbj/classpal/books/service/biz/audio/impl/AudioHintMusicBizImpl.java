package com.dbj.classpal.books.service.biz.audio.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.audio.AudioHintMusicDelBO;
import com.dbj.classpal.books.client.bo.audio.AudioHintMusicPageBO;
import com.dbj.classpal.books.client.dto.audio.AudioHintMusicPageDTO;
import com.dbj.classpal.books.service.entity.audio.AudioHintMusic;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.service.biz.audio.IAudioHintMusicBiz;
import com.dbj.classpal.books.service.mapper.audio.AudioHintMusicMapper;
import com.dbj.classpal.framework.commons.request.PageInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 预置提示音表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Service
@RequiredArgsConstructor
public class AudioHintMusicBizImpl extends ServiceImpl<AudioHintMusicMapper, AudioHintMusic> implements IAudioHintMusicBiz {

}
