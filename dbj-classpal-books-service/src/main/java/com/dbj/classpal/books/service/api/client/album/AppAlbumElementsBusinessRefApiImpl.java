package com.dbj.classpal.books.service.api.client.album;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.books.client.api.album.AppAlbumElementsBusinessRefApi;
import com.dbj.classpal.books.client.bo.album.AppAlbumElementsBusinessApiBO;
import com.dbj.classpal.books.client.bo.album.AppAlbumElementsBusinessRefQueryApiBO;
import com.dbj.classpal.books.client.bo.album.AppAlbumElementsBusinessRefQueryCommonApiBO;
import com.dbj.classpal.books.client.bo.album.AppAlbumElementsBusinessRefRemoveApiBO;
import com.dbj.classpal.books.client.bo.album.AppAlbumElementsBusinessRefSaveApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.dto.album.AppAlbumElementsBusinessRefMaterialQueryApiDTO;
import com.dbj.classpal.books.client.dto.album.AppAlbumElementsBusinessRefQueryApiDTO;
import com.dbj.classpal.books.client.dto.books.BooksRefDirectApiDTO;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsBusinessRefQueryBO;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsBusinessRefQueryCommonBO;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsBusinessRefSaveBO;
import com.dbj.classpal.books.common.bo.common.CommonIdBO;
import com.dbj.classpal.books.common.dto.album.AppAlbumElementsBusinessRefMaterialQueryDTO;
import com.dbj.classpal.books.common.dto.album.AppAlbumElementsBusinessRefQueryDTO;
import com.dbj.classpal.books.common.dto.books.BooksRefDirectDTO;
import com.dbj.classpal.books.service.service.album.IAppElementsBusinessRefService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_ALBUM_ELEMENTS_REF_UPDATE_FAIL_CODE;
import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_ALBUM_ELEMENTS_REF_UPDATE_FAIL_MSG;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumApiImpl
 * Date:     2025-04-15 11:25:28
 * Description: 表名： ,描述： 表
 */
@RestController
public class AppAlbumElementsBusinessRefApiImpl implements AppAlbumElementsBusinessRefApi {
    @Resource
    private IAppElementsBusinessRefService elementsBusinessRefService;


    @Override
    public RestResponse<List<AppAlbumElementsBusinessRefQueryApiDTO>> getElementsBusinessRef(AppAlbumElementsBusinessRefQueryCommonApiBO bo) {
        AppAlbumElementsBusinessRefQueryCommonBO queryBO = new AppAlbumElementsBusinessRefQueryCommonBO();
        BeanUtil.copyProperties(bo, queryBO);
        return RestResponse.success(elementsBusinessRefService.getRefBusinessList(queryBO).stream().map(d ->{
            AppAlbumElementsBusinessRefQueryApiDTO apiDTO = new AppAlbumElementsBusinessRefQueryApiDTO();
            BeanUtil.copyProperties(d, apiDTO);
            return apiDTO;
        }).collect(Collectors.toList()));
    }

    @Override
    public RestResponse<AppAlbumElementsBusinessRefMaterialQueryApiDTO> getElementsBusinessRefMaterialRef(AppAlbumElementsBusinessRefQueryApiBO bo) {
        AppAlbumElementsBusinessRefQueryBO queryBO = new AppAlbumElementsBusinessRefQueryBO();
        BeanUtil.copyProperties(bo, queryBO);
        AppAlbumElementsBusinessRefMaterialQueryDTO refQueryDTO = elementsBusinessRefService.getElementsBusinessRefMaterialRef(queryBO);
        AppAlbumElementsBusinessRefMaterialQueryApiDTO apiDTO = new AppAlbumElementsBusinessRefMaterialQueryApiDTO();
        if (!ObjectUtils.isEmpty(refQueryDTO)) {
            BeanUtil.copyProperties(refQueryDTO, apiDTO);
        }
        return RestResponse.success(apiDTO);
    }

    @Override
    public RestResponse<Boolean> saveOrUpdateElementsBusinessRefMaterialRef(AppAlbumElementsBusinessRefSaveApiBO bo) throws BusinessException {
        AppAlbumElementsBusinessRefSaveBO saveBO = new AppAlbumElementsBusinessRefSaveBO();
        BeanUtil.copyProperties(bo, saveBO);
        if (!elementsBusinessRefService.saveOrUpdateElementsBusinessRefMaterialRef(saveBO)) {
            throw new BusinessException(APP_ALBUM_ELEMENTS_REF_UPDATE_FAIL_CODE,APP_ALBUM_ELEMENTS_REF_UPDATE_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<BooksRefDirectApiDTO> getAlbumElementsBusinessRefBooks(CommonIdApiBO bo) throws BusinessException {
        CommonIdBO commonIdBO = new CommonIdBO();
        BeanUtil.copyProperties(bo, commonIdBO);
        BooksRefDirectDTO refBooks = elementsBusinessRefService.getAppAlbumElementsBusinessRefBooks(commonIdBO);
        BooksRefDirectApiDTO directApiDTO = new BooksRefDirectApiDTO();
        BeanUtil.copyProperties(refBooks, directApiDTO);
        return RestResponse.success(directApiDTO);
    }

    @Override
    public RestResponse<Boolean> removeAlbumElementsBusinessRef(AppAlbumElementsBusinessRefRemoveApiBO bo) {
        return RestResponse.success(elementsBusinessRefService.removeAlbumElementsBusinessRef(bo));
    }

    @Override
    public RestResponse<Map<Integer, List<AppAlbumElementsBusinessRefQueryApiDTO>>> getAlbumElementsBusiness(AppAlbumElementsBusinessApiBO bo) {
        List<AppAlbumElementsBusinessRefQueryDTO> albumElementsBusiness = elementsBusinessRefService.getAlbumElementsBusiness(
                new AppAlbumElementsBusinessRefQueryCommonBO()
                        .setBusinessType(bo.getBusinessType())
                        .setBusinessIds(bo.getBusinessIds())
        );
        List<AppAlbumElementsBusinessRefQueryApiDTO> appAlbumElementsBusinessRefQueryApiDTOList = BeanUtil.copyToList(albumElementsBusiness, AppAlbumElementsBusinessRefQueryApiDTO.class);
        return RestResponse.success(
                appAlbumElementsBusinessRefQueryApiDTOList.stream().collect(Collectors.groupingBy(AppAlbumElementsBusinessRefQueryApiDTO::getBusinessId))
        );
    }

    @Override
    public RestResponse<Map<Integer, AppAlbumElementsBusinessRefQueryApiDTO>> getAlbumElementsBusinessList(AppAlbumElementsBusinessApiBO bo) {
        List<AppAlbumElementsBusinessRefQueryDTO> albumElementsBusiness = elementsBusinessRefService.getAlbumElementsBusiness(
                new AppAlbumElementsBusinessRefQueryCommonBO()
                        .setBusinessType(bo.getBusinessType())
                        .setBusinessIds(bo.getBusinessIds())
        );
        List<AppAlbumElementsBusinessRefQueryApiDTO> appAlbumElementsBusinessRefQueryApiDTOList = BeanUtil.copyToList(albumElementsBusiness, AppAlbumElementsBusinessRefQueryApiDTO.class);
        return RestResponse.success(
                appAlbumElementsBusinessRefQueryApiDTOList.stream().collect(Collectors.toMap(AppAlbumElementsBusinessRefQueryApiDTO::getBusinessId, a -> a))
        );
    }
}
