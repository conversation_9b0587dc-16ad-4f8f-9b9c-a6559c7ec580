package com.dbj.classpal.books.service.entity.material;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterial
 * Date:     2025-04-08 10:04:50
 * Description: 表名： ,描述： 表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_material")
@Tag(name="素材中心表", description="素材中心表")
public class AppMaterial extends BizEntity implements Serializable {

    @TableField("is_root")
    @Schema(description = "是否根节点 0否 1是")
    private Integer isRoot;

    @TableField("parent_id")
    @Schema(description = "父节点ID")
    private Integer parentId;

    @TableField("material_name")
    @Schema(description = "资源名称")
    private String materialName;

    @TableField("first_pin_yin")
    @Schema(description = "拼音首字母")
    private String firstPinYin;

    @TableField("pin_yin")
    @Schema(description = "拼音")
    private String pinYin;

    @TableField("material_type")
    @Schema(description = "资源类型 1文件夹 2图片  3音频 4视频 5文档 6压缩包")
    private Integer materialType;

    @TableField("material_path")
    @Schema(description = "资源路径")
    private String materialPath;

    @TableField("material_origin_url")
    @Schema(description = "原始资源oss路径")
    private String materialOriginUrl;

    @TableField("material_size")
    @Schema(description = "资源大小")
    private Double materialSize;

    @TableField("material_duration")
    @Schema(description = "资源时长(s)")
    private Integer materialDuration;

    @TableField("material_caption")
    @Schema(description = "字幕")
    private String materialCaption;

    @TableField("material_extension")
    @Schema(description = "后缀名")
    private String materialExtension;

    @TableField("material_status")
    @Schema(description = "状态 0-禁用 1-启用")
    private Integer materialStatus;

    @TableField("material_md5")
    @Schema(description = "资源md5值")
    private String materialMd5;

    @TableField("job_id")
    @Schema(description = "阿里云转码任务jobId")
    private String jobId;

    @TableField("order_num")
    @Schema(description = "排序")
    private Integer orderNum;


}
