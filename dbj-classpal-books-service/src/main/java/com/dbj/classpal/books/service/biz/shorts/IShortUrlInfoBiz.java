package com.dbj.classpal.books.service.biz.shorts;

import com.dbj.classpal.books.service.entity.shorts.ShortUrlInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 图书表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-23
 */
public interface IShortUrlInfoBiz extends IService<ShortUrlInfo> {


    Boolean saveShortUrlInfo(String code, String longUrl);

    String getLongUrl(String shortUrl);

    Boolean batchSaveShortUrlInfo(List<ShortUrlInfo> shortUrlInfoList);
}
