package com.dbj.classpal.books.service.biz.advertisement;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementPageBO;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementDTO;
import com.dbj.classpal.books.service.entity.advertisement.Advertisement;

/**
 * <p>
 * 广告信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
public interface IAdvertisementBiz extends IService<Advertisement> {

    /**
     * 获取广告分页列表
     *
     * @param page
     * @param bo
     * @return
     */
    Page<AdvertisementDTO> getAdvertisementPageList(Page page, AdvertisementPageBO bo);
}
