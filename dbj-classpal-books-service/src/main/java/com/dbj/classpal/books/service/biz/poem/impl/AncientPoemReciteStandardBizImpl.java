package com.dbj.classpal.books.service.biz.poem.impl;

import com.dbj.classpal.books.service.entity.poem.AncientPoemReciteStandard;
import com.dbj.classpal.books.service.mapper.poem.AncientPoemReciteStandardMapper;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemReciteStandardBiz;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <p>
 * 古诗背诵评分标准表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Service
public class AncientPoemReciteStandardBizImpl extends ServiceImpl<AncientPoemReciteStandardMapper, AncientPoemReciteStandard> implements IAncientPoemReciteStandardBiz {

    @Override
    public AncientPoemReciteStandard getByScore(BigDecimal score,Integer type) {
        return baseMapper.getByScore(score,type);
    }
}
