package com.dbj.classpal.books.service.api.client.ebooks;

import com.dbj.classpal.books.client.api.ebooks.AppEbooksConfigCategoryApi;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigCategoryQueryApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigCategorySaveApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigCategoryUpdateApiBO;
import com.dbj.classpal.books.client.dto.ebooks.AppEbooksConfigCategoryQueryApiDTO;
import com.dbj.classpal.books.service.service.ebooks.IAppEbooksConfigCategoryService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEbooksConfigWatermarkTemplateApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@RestController
public class AppEbooksConfigCategoryApiImpl implements AppEbooksConfigCategoryApi {

    @Resource
    private IAppEbooksConfigCategoryService appEbooksConfigCategoryService;

    @Override
    public RestResponse<List<AppEbooksConfigCategoryQueryApiDTO>> getAllCategory(AppEbooksConfigCategoryQueryApiBO bo) throws BusinessException {
        return RestResponse.success(appEbooksConfigCategoryService.getAllCategory(bo));
    }

    @Override
    public RestResponse<Boolean> saveCategory(AppEbooksConfigCategorySaveApiBO bo) throws BusinessException {
        return RestResponse.success(appEbooksConfigCategoryService.saveCategory(bo));
    }

    @Override
    public RestResponse<Boolean> updateCategory(AppEbooksConfigCategoryUpdateApiBO bo) throws BusinessException {
        return RestResponse.success(appEbooksConfigCategoryService.updateCategory(bo));
    }

    @Override
    public RestResponse<Boolean> deleteCategory(CommonIdsApiBO bo) throws BusinessException {
        return RestResponse.success(appEbooksConfigCategoryService.deleteCategory(bo));
    }
}
