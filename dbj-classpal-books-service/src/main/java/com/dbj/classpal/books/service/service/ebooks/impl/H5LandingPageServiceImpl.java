package com.dbj.classpal.books.service.service.ebooks.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookH5QueryBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookshelfH5QueryBO;
import com.dbj.classpal.books.common.bo.ebooks.H5LandingPageBO;
import com.dbj.classpal.books.common.dto.ebooks.*;
import com.dbj.classpal.books.service.service.ebooks.IAppEBookService;
import com.dbj.classpal.books.service.service.ebooks.IAppEBookshelfService;
import com.dbj.classpal.books.service.service.ebooks.IAppEBookstoreService;
import com.dbj.classpal.books.service.service.ebooks.IH5LandingPageService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.utils.util.enums.ShareUrlTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * H5落地页服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Slf4j
@Service
public class H5LandingPageServiceImpl implements IH5LandingPageService {

    @Resource
    private IAppEBookService eBookService;
    
    @Resource
    private IAppEBookshelfService eBookshelfService;
    
    @Resource
    private IAppEBookstoreService eBookstoreService;

    @Override
    public H5LandingPageDTO getLandingPageData(H5LandingPageBO request) throws BusinessException {
        log.info("获取H5落地页数据，请求参数：{}", request);

        H5LandingPageDTO response = new H5LandingPageDTO();
        response.setType(request.getType());
        response.setValid(true);

        try {
            // 验证参数
            validateRequest(request);
            ShareUrlTypeEnum shareUrlTypeEnum = ShareUrlTypeEnum.getByCode(request.getType());
            // 根据类型获取相应的数据
            switch (shareUrlTypeEnum) {
                case BOOK:
                    handleBookShare(response, request);
                    break;
                case BOOKSHELF:
                    handleBookshelfShare(response, request);
                    break;
                case BOOKSTORE:
                    handleBookstoreShare(response, request);
                    break;
                default:
                    throw new BusinessException("不支持的分享类型：" + request.getType());
            }

        } catch (Exception e) {
            log.error("获取H5落地页数据失败：{}", e.getMessage(), e);
            response.setValid(false);
            response.setErrorMessage(e.getMessage());
        }

        return response;
    }
    
    /**
     * 验证请求参数
     */
    private void validateRequest(H5LandingPageBO request) throws BusinessException {
        if (StringUtils.isBlank(request.getType())) {
            throw new BusinessException("分享类型不能为空");
        }

        String type = request.getType();
        ShareUrlTypeEnum shareUrlTypeEnum = ShareUrlTypeEnum.getByCode(type);

        switch (shareUrlTypeEnum) {
            case BOOK:
                if (request.getEbookId() == null) {
                    throw new BusinessException("单书查看缺少单书ID");
                }
                break;
            case BOOKSHELF:
                if (request.getShelfId() == null) {
                    throw new BusinessException("书架查看缺少书架ID");
                }
                break;
            case BOOKSTORE:
                if (request.getStoreId() == null) {
                    throw new BusinessException("书城查看缺少书城ID");
                }
                break;
            default:
                throw new BusinessException("不支持的分享类型：" + request.getType());
        }
    }

    /**
     * 处理单书查看
     */
    private void handleBookShare(H5LandingPageDTO response, H5LandingPageBO request) throws BusinessException {
        AppEBookDTO ebook = eBookService.detail(request.getEbookId());
        if (ebook == null) {
            throw new BusinessException("单书不存在，ID：" + request.getEbookId());
        }
        Map<Integer, String> watermarkedFileUrlMap = eBookService.batchQueryWatermarkedFileUrls(List.of(ebook.getId()));
        ebook.setWatermarkedFileUrl(watermarkedFileUrlMap.getOrDefault(ebook.getId(),ebook.getFileUrl()));
        response.setEbook(BeanUtil.copyProperties(ebook, AppEBookLandingDTO.class));

        response.setPageTitle(ebook.getBookTitle());
    }

    /**
     * 处理书架查看
     */
    private void handleBookshelfShare(H5LandingPageDTO response, H5LandingPageBO request) throws BusinessException {
        AppEBookshelfDTO bookshelf = eBookshelfService.detail(request.getShelfId());
        if (bookshelf == null) {
            throw new BusinessException("书架不存在，ID：" + request.getShelfId());
        }

        response.setBookshelf(BeanUtil.copyProperties(bookshelf,AppEBookshelfLandingDTO.class));

        // 使用分页查询书架中的单书
        AppEBookH5QueryBO ebookQuery = new AppEBookH5QueryBO();
        ebookQuery.setShelfId(request.getShelfId());
        if (request.getStoreId() != null) {
            ebookQuery.setStoreId(request.getStoreId());
        }

        PageInfo<AppEBookH5QueryBO> pageRequest = new PageInfo<>();
        pageRequest.setPageNum(request.getPageNum());
        pageRequest.setPageSize(request.getPageSize());
        pageRequest.setData(ebookQuery);

        Page<AppEBookDTO> ebooksPage = eBookService.pageForH5(pageRequest);

        // 类型转换：AppEBookDTO -> AppEBookLandingDTO
        Page<AppEBookLandingDTO> landingEbooksPage = (Page<AppEBookLandingDTO>) ebooksPage.convert(dto ->
            BeanUtil.copyProperties(dto, AppEBookLandingDTO.class)
        );
        response.setEbooks(landingEbooksPage);

        response.setPageTitle(bookshelf.getShelfTitle());
    }

    /**
     * 处理书城查看
     */
    private void handleBookstoreShare(H5LandingPageDTO response, H5LandingPageBO request) throws BusinessException {
        AppEBookstoreDTO bookstore = eBookstoreService.detail(request.getStoreId());
        if (bookstore == null) {
            throw new BusinessException("书城不存在，ID：" + request.getStoreId());
        }

        response.setBookstore(BeanUtil.copyProperties(bookstore,AppEBookstoreLandingDTO.class));

        // 使用分页查询书城中的书架
        AppEBookshelfH5QueryBO shelfQuery = new AppEBookshelfH5QueryBO();
        shelfQuery.setStoreId(request.getStoreId());

        PageInfo<AppEBookshelfH5QueryBO> pageRequest = new PageInfo<>();
        pageRequest.setPageNum(request.getPageNum());
        pageRequest.setPageSize(request.getPageSize());
        pageRequest.setData(shelfQuery);

        Page<AppEBookshelfDTO> shelvesPage = eBookshelfService.pageForH5(pageRequest);

        // 类型转换：AppEBookshelfDTO -> AppEBookshelfLandingDTO
        Page<AppEBookshelfLandingDTO> landingShelvesPage = (Page<AppEBookshelfLandingDTO>) shelvesPage.convert(dto ->
            BeanUtil.copyProperties(dto, AppEBookshelfLandingDTO.class)
        );
        response.setBookshelves(landingShelvesPage);

        response.setPageTitle(bookstore.getStoreTitle());
    }
}
