package com.dbj.classpal.books.service.biz.audio.impl;

import com.dbj.classpal.books.service.biz.audio.IAudioContextInfoHistoryBiz;
import com.dbj.classpal.books.service.entity.audio.AudioContextInfoHistory;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.service.mapper.audio.AudioContextInfoHistoryMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 音频文本合成历史记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Service
public class AudioContextInfoHistoryBizImpl extends ServiceImpl<AudioContextInfoHistoryMapper, AudioContextInfoHistory> implements IAudioContextInfoHistoryBiz {

}
