package com.dbj.classpal.books.service.entity.evaluation;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppUserPaperEvaluation
 * Date:     2025-05-16 11:23:51
 * Description: 表名： ,描述： 表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("app_user_paper_evaluation_analysis")
@Tag(name="内容管理-评测-评测报告-分析内容表", description="内容管理-评测-评测报告-分析内容表")
public class AppUserPaperEvaluationAnalysis extends BizEntity {

    @Schema(description = "评测报告id")
    @TableField("app_user_paper_evaluation_id")
    private Integer appUserPaperEvaluationId;

    @Schema(description = "评测项id")
    @TableField("app_evaluation_node_id")
    private Integer appEvaluationNodeId;

    @TableField("app_user_paper_info_id")
    @Schema(name = "试卷id")
    private Integer appUserPaperInfoId;

    @Schema(description = "评测项名称")
    @TableField("app_evaluation_node_name")
    private String appEvaluationNodeName;

    @Schema(description = "能力得分")
    @TableField("ability_score")
    private Double abilityScore;

    @Schema(description = "能力分析")
    @TableField("ability_analysis")
    private String abilityAnalysis;

    @Schema(description = "答题正确数量")
    @TableField("right_count")
    private Integer rightCount;

    @Schema(description = "答题错误数量")
    @TableField("error_count")
    private Integer errorCount;

    @Schema(description = "总题数")
    @TableField("total_count")
    private Integer totalCount;

    @Schema(description = "得分")
    @TableField("score")
    private Double score;

    @Schema(description = "分析")
    @TableField("analysis")
    private String analysis;

    @Schema(description = "建议")
    @TableField("suggest")
    private String suggest;
}
