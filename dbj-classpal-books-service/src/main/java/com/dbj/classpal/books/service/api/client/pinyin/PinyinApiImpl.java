package com.dbj.classpal.books.service.api.client.pinyin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.pinyin.PinyinApi;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinAppBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinDelBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinPageBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinStatusUpdateBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinUpsertBO;
import com.dbj.classpal.books.client.dto.pinyin.PinyinAppDTO;
import com.dbj.classpal.books.client.dto.pinyin.PinyinDTO;
import com.dbj.classpal.books.service.service.pinyin.IPinyinService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 广告信息表 远程Api 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@RestController
public class PinyinApiImpl implements PinyinApi {

    @Resource
    private IPinyinService pinyinService;

    @Override
    public RestResponse<Page<PinyinDTO>> getPinyinPage(PageInfo<PinyinPageBO> pageInfo) {
        return RestResponse.success(pinyinService.getPinyinPage(pageInfo));
    }

    @Override
    public RestResponse<PinyinDTO> getPinyinInfo(CommonIdApiBO bo) {
        return RestResponse.success(pinyinService.getPinyinInfo(bo));
    }

    @Override
    public RestResponse<Boolean> savePinyin(PinyinUpsertBO bo) throws BusinessException {
        return RestResponse.success(pinyinService.savePinyin(bo));
    }

    @Override
    public RestResponse<Boolean> updatePinyin(PinyinUpsertBO bo) throws BusinessException {
        return RestResponse.success(pinyinService.updatePinyin(bo));
    }

    @Override
    public RestResponse<Boolean> updatePinyinStatus(PinyinStatusUpdateBO bo) {
        return RestResponse.success(pinyinService.updatePinyinStatus(bo));
    }

    @Override
    public RestResponse<Boolean> deletePinyin(PinyinDelBO bo) {
        return RestResponse.success(pinyinService.deletePinyin(bo));
    }

    @Override
    public RestResponse<PinyinAppDTO> getAppPinyinData(PinyinAppBO bo) {
        return RestResponse.success(pinyinService.getAppPinyinData(bo));
    }
}
