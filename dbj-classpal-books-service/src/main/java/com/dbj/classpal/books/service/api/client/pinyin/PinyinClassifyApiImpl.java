package com.dbj.classpal.books.service.api.client.pinyin;

import com.dbj.classpal.books.client.api.pinyin.PinyinClassifyApi;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinClassifyBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinClassifyUpsertBO;
import com.dbj.classpal.books.client.dto.pinyin.PinyinClassifyDTO;
import com.dbj.classpal.books.service.service.pinyin.IPinyinClassifyService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 汉语拼音分类 远程Api 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@RestController
public class PinyinClassifyApiImpl implements PinyinClassifyApi {

    @Resource
    private IPinyinClassifyService pinyinClassifyService;

    @Override
    public RestResponse<List<PinyinClassifyDTO>> getPinyinClassifyAll(PinyinClassifyBO bo) {
        return RestResponse.success(pinyinClassifyService.getPinyinClassifyAll(bo));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RestResponse<Boolean> savePinyinClassify(PinyinClassifyUpsertBO bo) throws BusinessException {
        return RestResponse.success(pinyinClassifyService.savePinyinClassify(bo));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RestResponse<Boolean> updatePinyinClassify(PinyinClassifyUpsertBO bo) throws BusinessException {
        return RestResponse.success(pinyinClassifyService.updatePinyinClassify(bo));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RestResponse<Boolean> deletePinyinClassify(CommonIdApiBO bo) {
        return RestResponse.success(pinyinClassifyService.deletePinyinClassify(bo));
    }

    @Override
    public RestResponse<Boolean> sortPinyinClassify(CommonIdsApiBO bo) {
        return RestResponse.success(pinyinClassifyService.sortPinyinClassify(bo));
    }

    @Override
    public RestResponse<List<PinyinClassifyDTO>> getAppPinyinClassify(PinyinClassifyBO bo) {
        return RestResponse.success(pinyinClassifyService.getAppPinyinClassify(bo));
    }
}
