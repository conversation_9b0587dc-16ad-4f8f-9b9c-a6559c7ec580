package com.dbj.classpal.books.service.entity.pointreading;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 点读书模式配置表
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("point_reading_mode_config")
@Schema(name = "PointReadingModeConfig", description = "点读书模式配置表")
public class PointReadingModeConfig extends BizEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "模式配置字典ID")
    private Integer configDictId;

    @Schema(description = "点读书ID")
    private Integer bookId;

    @Schema(description = "显示名称")
    private String displayName;

    @Schema(description = "处理状态：10-处理中 20-处理完成 30-处理失败")
    private Integer processStatus;

    @Schema(description = "原始文件ID")
    private String originalFileId;

    @Schema(description = "原始文件URL")
    private String originalUrl;

    @Schema(description = "原始文件名")
    private String originalName;

    @Schema(description = "原始文件MD5")
    private String originalMd5;

    @Schema(description = "排序")
    private Integer sortNum;

    @Schema(description = "状态：0-停用 1-正常")
    private Integer status;

    @Schema(description = "版本号")
    private Integer version;
}
