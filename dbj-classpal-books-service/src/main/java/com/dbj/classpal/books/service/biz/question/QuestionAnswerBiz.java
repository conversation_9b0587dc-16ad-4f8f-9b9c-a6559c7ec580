package com.dbj.classpal.books.service.biz.question;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.service.entity.question.QuestionAnswer;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;

public interface QuestionAnswerBiz extends IService<QuestionAnswer> {
    
    /**
     * 根据题目ID获取答案列表
     *
     * @param questionId 题目ID
     * @return 答案列表
     */
    List<QuestionAnswer> getAnswersByQuestionId(Integer questionId);

    /**
     * 根据空位区域ID获取答案列表
     *
     * @param questionId
     * @param blankAreaId 空位区域ID
     * @return 答案列表
     * @throws BusinessException 业务异常
     */
    List<QuestionAnswer> getAnswersByBlankAreaId(Integer questionId,Integer blankAreaId) ;

    /**
     * 保存答案列表
     *
     * @param answers 答案列表
     * @return 是否保存成功
     */
    boolean saveAnswers(List<QuestionAnswer> answers) throws BusinessException;

    /**
     * 更新答案列表
     *
     * @param questionId 题目ID
     * @param answers 答案列表
     * @return 是否更新成功
     */
    boolean updateAnswers(Integer questionId, List<QuestionAnswer> answers) throws BusinessException;


    /**
     * 更新答案列表
     *
     * @param blankAreaId 题目ID
     * @param answers 答案列表
     * @return 是否更新成功
     */
    boolean updateBlankAnswers(Integer blankAreaId, List<QuestionAnswer> answers) throws BusinessException;


    /**
     * 根据题目ID删除答案
     *
     * @param questionIds 题目ID列表
     * @return 是否删除成功
     */
    boolean deleteByQuestionId(List<Integer> questionIds) throws BusinessException;

    /**
     * 根据areaID删除答案
     * @param areaIds
     * @return
     * @throws BusinessException
     */
    boolean deleteByBlankAreaId(List<Integer> areaIds) throws BusinessException;

} 