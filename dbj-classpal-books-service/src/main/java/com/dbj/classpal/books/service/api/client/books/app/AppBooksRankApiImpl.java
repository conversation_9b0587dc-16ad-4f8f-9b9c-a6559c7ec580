package com.dbj.classpal.books.service.api.client.books.app;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.books.client.api.books.app.AppBooksRankApi;
import com.dbj.classpal.books.client.bo.books.BooksRankInfoApiBO;
import com.dbj.classpal.books.client.dto.books.BooksRankInfoApiDTO;
import com.dbj.classpal.books.client.dto.books.app.BooksRankInCodesContentsRankCountDTO;
import com.dbj.classpal.books.client.dto.material.AppMaterialBusinessRefMaterialQueryApiDTO;
import com.dbj.classpal.books.common.dto.material.AppMaterialBusinessRefDTO;
import com.dbj.classpal.books.client.enums.BusinessTypeEnum;
import com.dbj.classpal.books.client.enums.MaterialTypeEnum;
import com.dbj.classpal.books.common.enums.books.ContentsTypeEnum;
import com.dbj.classpal.books.common.enums.books.RankClassifyEnum;
import com.dbj.classpal.books.service.biz.books.IBooksRankClassifyBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInCodesContentsBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInfoBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankStudyTimeLogBiz;
import com.dbj.classpal.books.service.biz.books.IBooksUserRankStudyLogBiz;
import com.dbj.classpal.books.service.biz.books.IBooksUserRefBiz;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBusinessRefBiz;
import com.dbj.classpal.books.service.entity.books.BooksRankClassify;
import com.dbj.classpal.books.service.entity.books.BooksRankInCodesContents;
import com.dbj.classpal.books.service.entity.books.BooksRankInfo;
import com.dbj.classpal.books.service.entity.books.BooksUserRankStudyLog;
import com.dbj.classpal.books.service.entity.books.BooksUserRef;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import com.dbj.classpal.framework.utils.util.ContextAppUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@RestController
public class AppBooksRankApiImpl implements AppBooksRankApi {

    @Resource
    private IBooksRankInfoBiz bookRankBiz;
    @Resource
    private IBooksUserRankStudyLogBiz booksUserRankStudyLogBiz;
    @Resource
    private IBooksRankStudyTimeLogBiz booksRankStudyTimeLogBiz;
    @Resource
    private IBooksRankInCodesContentsBiz booksRankInCodesContentsBiz;

    @Resource
    private IBooksRankClassifyBiz booksRankClassifyBiz;
    @Resource
    private IAppMaterialBusinessRefBiz appMaterialBusinessRefBiz;

    @Resource
    private IBooksUserRefBiz booksUserRefBiz;

    @Override
    public RestResponse<List<BooksRankInfoApiDTO>> list(BooksRankInfoApiBO bookRankInfoApiBO) throws BusinessException {
        List<BooksRankInfo> booksRankInfoList =  bookRankBiz.lambdaQuery().eq(BooksRankInfo::getBookId, bookRankInfoApiBO.getBookId())
                .eq(BooksRankInfo::getStatus, YesOrNoEnum.YES.getCode()).orderByAsc(BooksRankInfo::getType).orderByAsc(BooksRankInfo::getSerialNo).list();

        //查询当前书的最近学习记录
        Integer appUserId = ContextAppUtil.getAppUserIdInt();
        List<BooksRankInfoApiDTO> booksRankInfoDTOList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(booksRankInfoList)){

            booksRankInfoDTOList = BeanUtil.copyToList(booksRankInfoList,BooksRankInfoApiDTO.class);
            List<Integer> rankIdList = booksRankInfoList.stream().map(BooksRankInfo::getId).collect(Collectors.toList());

            List<BooksRankInCodesContentsRankCountDTO> booksRankInCodesContentsRankCountDTOS = booksRankInCodesContentsBiz.listRankCount(rankIdList);
            Map<Integer,Long> booksRankInCodesContentsRankCountMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(booksRankInCodesContentsRankCountDTOS)){
                booksRankInCodesContentsRankCountMap = booksRankInCodesContentsRankCountDTOS.stream().collect(Collectors.toMap(BooksRankInCodesContentsRankCountDTO::getRankId,BooksRankInCodesContentsRankCountDTO::getContentsNum, (v1, v2) -> v1));
            }

            Integer lastStudyRankId = null;
            Integer booksId = bookRankInfoApiBO.getBookId();

            if(appUserId != null){
                //判断用户的这本书是否在书架 如果不在需要添加
                List<BooksUserRef> booksUserRefList = booksUserRefBiz.lambdaQuery().eq(BooksUserRef::getAppUserId, appUserId).eq(BooksUserRef::getBooksId, booksId).list();
                if(CollectionUtils.isEmpty(booksUserRefList)){
                    BooksUserRef booksUserRef = new BooksUserRef();
                    booksUserRef.setAppUserId(appUserId);
                    booksUserRef.setBooksId(booksId);
                    booksUserRefBiz.save(booksUserRef);
                }
                // 新增用的最近查看图书的记录
                BooksUserRankStudyLog booksUserRankStudyLog =  booksUserRankStudyLogBiz.lambdaQuery().eq(BooksUserRankStudyLog::getAppUserId, ContextAppUtil.getAppUserIdInt())
                        .in(BooksUserRankStudyLog::getRankId, rankIdList)
                        .orderByDesc(BooksUserRankStudyLog::getIsLastStudy).orderByDesc(BooksUserRankStudyLog::getLastStudyTime).last("LIMIT 1").one();
                //累计学习时常查询
                if(booksUserRankStudyLog != null){
                    lastStudyRankId = booksUserRankStudyLog.getRankId();
                }
            }
            for(BooksRankInfoApiDTO booksRankInfo : booksRankInfoDTOList){
                Boolean flag = false;
                if(lastStudyRankId != null){
                    if(Objects.equals(booksRankInfo.getId(),lastStudyRankId)){
                        flag = true;
                    }
                    booksRankInfo.setLastStudyTime(booksRankStudyTimeLogBiz.getLastStudyTime(lastStudyRankId,appUserId));
                }
                booksRankInfo.setIsLastStudy(flag);

                booksRankInfo.setContentsNum(booksRankInCodesContentsRankCountMap.getOrDefault(booksRankInfo.getId(),0L));
            }
        }
        return RestResponse.success(booksRankInfoDTOList);
    }

    @Override
    public RestResponse<List<AppMaterialBusinessRefMaterialQueryApiDTO>> refBusinessListByRankId(Integer rankId) throws BusinessException {
        //先查询出所有的资源目录
        List<AppMaterialBusinessRefMaterialQueryApiDTO> appMaterialBusinessRefMaterialQueryApiDTOList = new ArrayList<>();

        List<BooksRankClassify> booksRankClassifyList = booksRankClassifyBiz.lambdaQuery().eq(BooksRankClassify::getRankId, rankId).eq(BooksRankClassify::getType, RankClassifyEnum.BOOK_IN_CODES.getCode()).list();
        if(CollectionUtils.isNotEmpty(booksRankClassifyList)){
            Integer classifyId = booksRankClassifyList.get(0).getId();
            List<BooksRankInCodesContents> booksRankInCodesContentsList = booksRankInCodesContentsBiz.lambdaQuery().eq(BooksRankInCodesContents::getRankClassifyId,classifyId).eq(BooksRankInCodesContents::getType, ContentsTypeEnum.RESOURCE).list();
            if(CollectionUtils.isNotEmpty(booksRankInCodesContentsList)){
                List<Integer> contestIds = booksRankInCodesContentsList.stream().map(BooksRankInCodesContents::getId).collect(Collectors.toList());
                List<AppMaterialBusinessRefDTO> appMaterialBusinessRefDTOList = appMaterialBusinessRefBiz.getBusinessList(contestIds, BusinessTypeEnum.BOOKS_RESOURCE_BUSINESS.getCode(), Arrays.asList(MaterialTypeEnum.FILE_TYPE_AUDIO.getCode(),MaterialTypeEnum.FILE_TYPE_VIDEO.getCode()));
                if(CollectionUtils.isNotEmpty(appMaterialBusinessRefDTOList)){
                    appMaterialBusinessRefMaterialQueryApiDTOList = BeanUtil.copyToList(appMaterialBusinessRefDTOList, AppMaterialBusinessRefMaterialQueryApiDTO.class);
                }
            }
        }
        return RestResponse.success(appMaterialBusinessRefMaterialQueryApiDTOList);
    }
}
