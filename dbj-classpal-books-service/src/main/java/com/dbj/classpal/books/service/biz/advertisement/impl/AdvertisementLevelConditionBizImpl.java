package com.dbj.classpal.books.service.biz.advertisement.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementLevelConditionDTO;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementLevelConditionOptionDTO;
import com.dbj.classpal.books.service.biz.advertisement.IAdvertisementLevelConditionBiz;
import com.dbj.classpal.books.service.entity.advertisement.AdvertisementLevelCondition;
import com.dbj.classpal.books.service.mapper.advertisement.AdvertisementLevelConditionMapper;
import com.dbj.classpal.books.service.mapper.advertisement.AdvertisementLevelConditionOptionMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 广告条件层级表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Service
public class AdvertisementLevelConditionBizImpl extends ServiceImpl<AdvertisementLevelConditionMapper, AdvertisementLevelCondition> implements IAdvertisementLevelConditionBiz {

    @Resource
    private AdvertisementLevelConditionOptionMapper advertisementLevelConditionOptionMapper;

    @Override
    public Map<Integer, List<AdvertisementLevelConditionDTO>> getMapByAdvertisementIds(Collection<Integer> advertisementIds) {
        List<AdvertisementLevelConditionDTO> conditionDTOList = this.baseMapper.getByAdvertisementIds(advertisementIds);
        if (CollUtil.isEmpty(conditionDTOList)) {
            return Collections.emptyMap();
        }

        Map<Integer, List<AdvertisementLevelConditionDTO>> conditionDTOMap = conditionDTOList.stream()
                .collect(Collectors.groupingBy(AdvertisementLevelConditionDTO::getAdvertisementId));

        Set<Integer> ids = conditionDTOList.stream().map(AdvertisementLevelConditionDTO::getId).collect(Collectors.toSet());
        List<AdvertisementLevelConditionOptionDTO> levelConditionOptionList =
                advertisementLevelConditionOptionMapper.getByAdvertisementLevelConditionIds(ids);
        Map<Integer, Map<Integer, List<AdvertisementLevelConditionOptionDTO>>> adConditionOptionMap = levelConditionOptionList.stream()
                .collect(Collectors.groupingBy(AdvertisementLevelConditionOptionDTO::getAdvertisementId,
                        Collectors.groupingBy(AdvertisementLevelConditionOptionDTO::getAdvertisementLevelConditionId)));

        Map<Integer, List<AdvertisementLevelConditionDTO>> conditionMap = new HashMap<>(8);
        conditionDTOMap.forEach((advertisementId, conditionList) -> {
            //递归遍历子集数据
            List<AdvertisementLevelConditionDTO> parentList = conditionList.stream()
                    .filter(c -> c.getParentId() == null).collect(Collectors.toList());
            Map<Integer, List<AdvertisementLevelConditionDTO>> childrenMap = conditionList.stream()
                    .filter(c -> c.getParentId() != null)
                    .collect(Collectors.groupingBy(AdvertisementLevelConditionDTO::getParentId, LinkedHashMap::new, Collectors.toList()));
            Map<Integer, List<AdvertisementLevelConditionOptionDTO>> conditionOptionMap = adConditionOptionMap.get(advertisementId);
            generateChildren(parentList, childrenMap, conditionOptionMap);
            conditionMap.put(advertisementId, parentList);
        });

        return conditionMap;
    }

    /**
     * 递归生成子集数据
     *
     * @param parentList 父集
     * @param childrenMap 子集映射
     * @param conditionOptionMap 条件选项映射
     */
    private static void generateChildren(List<AdvertisementLevelConditionDTO> parentList,
                                         Map<Integer, List<AdvertisementLevelConditionDTO>> childrenMap,
                                         Map<Integer, List<AdvertisementLevelConditionOptionDTO>> conditionOptionMap) {
        for (AdvertisementLevelConditionDTO dto : parentList) {
            List<AdvertisementLevelConditionOptionDTO> conditionOptionDTOList = conditionOptionMap.get(dto.getId());
            if (CollUtil.isNotEmpty(conditionOptionDTOList)) {
                dto.setAdvertisementLevelConditionOptionList(conditionOptionDTOList);
            }
            List<AdvertisementLevelConditionDTO> childrenList = childrenMap.get(dto.getId());
            //当前节点的子级是否有 有则进行递归继续查找 否则下一个
            if (CollUtil.isNotEmpty(childrenList)) {
                dto.setChildrenList(childrenList);
                //根据parentId进行分组
                generateChildren(childrenList, childrenMap, conditionOptionMap);
            }
        }
    }


}
