package com.dbj.classpal.books.service.mq.listener.ebooks;

import com.alibaba.fastjson.JSON;
import com.aliyun.core.utils.StringUtils;
import com.dbj.classpal.books.common.bo.watermark.WaterMarkEntity;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.config.FileUtilConfig;
import com.dbj.classpal.framework.mq.annotation.ExtractHeader;
import com.dbj.classpal.framework.mq.pool.DbjRabbitTemplate;
import com.dbj.classpal.framework.openapi.model.python.request.PdfCoverRequest;
import com.dbj.classpal.framework.openapi.model.python.response.PdfToImagesRequest;
import com.dbj.classpal.framework.oss.utils.OssUtil;
import com.dbj.classpal.framework.pdf.config.PdfImageConfig;
import com.dbj.classpal.framework.pdf.constant.PdfWaterMarkExChangeConstant;
import com.dbj.classpal.framework.pdf.constant.PdfWaterMarkQueueConstant;
import com.dbj.classpal.framework.pdf.constant.PdfWaterMarkRoutingKeyConstant;
import com.dbj.classpal.framework.pdf.enums.PdfWaterMarkBusinessTypeEnum;
import com.dbj.classpal.framework.pdf.enums.PdfWaterMarkIgnoreTypeEnum;
import com.dbj.classpal.framework.pdf.facade.PdfImageFacade;
import com.dbj.classpal.framework.utils.util.FileUtil;
import com.rabbitmq.client.Channel;
import jakarta.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class WaterMarkListener {
    @Resource
    private DbjRabbitTemplate dbjRabbitTemplate;
    @Resource
    private OssUtil ossUtil;
    @Resource
    private FileUtilConfig fileUtilConfig;
    @Resource
    private PythonProcessingService pythonProcessingService;
    @Resource
    private PdfImageFacade pdfImageFacade;
    @Resource
    private PdfImageConfig pdfImageConfig;
    /**
     * 校验传入参数--基础必填参数
     * @param entity
     * @throws BusinessException
     */
    public static void baseValidParams(WaterMarkEntity entity) throws BusinessException {
        if(StringUtils.isEmpty(entity.getPdfOssPath())){
            throw new BusinessException("参数错误,pdf文件名称不能为空");
        }
        if(StringUtils.isEmpty(entity.getPdfName())){
            throw new BusinessException("参数错误,pdf文件oss地址不能为空");
        }
    }

    /**
     * 校验传入参数--添加水印必填参数
     * @param entity
     * @throws BusinessException
     */
    public static void validAddWaterMarkParams(WaterMarkEntity entity) throws BusinessException {
        if(entity.getScale() == null){
            throw new BusinessException("参数错误,水印大小倍率不能为空");
        }
        if(entity.getTransparency() == null){
            throw new BusinessException("参数错误,水印透明度不能为空");
        }
        if(entity.getRotationAngle() == null){
            throw new BusinessException("参数错误,水印旋转角度不能为空");
        }
        if(entity.getHorizontalSpacing() == null){
            throw new BusinessException("参数错误,水印横向间距不能为空");
        }
        if(entity.getVerticalSpacing() == null){
            throw new BusinessException("参数错误,水印纵向间距不能为空");
        }
        if(StringUtils.isEmpty(entity.getWaterMarkOssPath())){
            throw new BusinessException("参数错误,水印Oss路径不能为空");
        }
        if(StringUtils.isEmpty(entity.getWaterMarkName())){
            throw new BusinessException("参数错误,水印文件名称不能为空");
        }
    }

    /**
     * 校验传入参数--oss文件下载到本地的文件必传参数
     * @param entity
     * @throws BusinessException
     */
    public static void validLocalParams(WaterMarkEntity entity) throws BusinessException {
        if(entity.getPdfLocalPath() == null){
            throw new BusinessException("参数错误,本地的pdf路径不能为空");
        }
        if(entity.getWaterMarkLocalPath() == null){
            throw new BusinessException("参数错误,本地的水印路径不能为空");
        }
    }


    @ExtractHeader
    @RabbitListener(queues = {PdfWaterMarkQueueConstant.CLASSPAL_FILE_SERVICE_QUEUE_PDF_DOWNLOAD_FILE})
    public void downLoadFile(WaterMarkEntity entity, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag, Message message) throws Exception{
        log.info("接收到下载文件消息:{}", JSON.toJSONString(entity));
        if(entity == null || entity.getBusinessKey() == null || entity.getWaterMarkBusinessType() == null){
            // 直接确认消息进行下一条的处理
            log.info("直接确认消息进行下一条的处理");
            channel.basicAck(tag, false);  // 手动确认
            return;
        }
        Integer waterMarkBusinessType = entity.getWaterMarkBusinessType();
        try {
            entity.setIsError(false);
            baseValidParams(entity);
            String pdfName = entity.getPdfOssPath().substring(entity.getPdfOssPath().lastIndexOf("/") + 1);
            String watermarkName = "";
            if (!StringUtils.isEmpty(entity.getWaterMarkOssPath())){
                watermarkName = entity.getWaterMarkOssPath().substring(entity.getWaterMarkOssPath().lastIndexOf("/") + 1);
                entity.setWaterMarkName(watermarkName);
            }
            entity.setPdfName(pdfName);
            if(waterMarkBusinessType.equals(PdfWaterMarkBusinessTypeEnum.ONLY_SLICING_IMAGE.getCode())){
                File pdfDownLoadFile = FileUtil.downloadWithTimeout(entity.getPdfOssPath(),fileUtilConfig.getDownLoadTmpPath(),entity.getPdfName(),fileUtilConfig.getDownLoadTimeout());
                entity.setPdfLocalWatermarkPath(entity.getPdfOssPath());
                entity.setPdfLocalPath(pdfDownLoadFile.getAbsolutePath());
                dbjRabbitTemplate.sendExchangeEntityMessage(entity, PdfWaterMarkExChangeConstant.CLASSPAL_FILE_SERVICE_EXCHANGE_PDF_WATERMARK, PdfWaterMarkRoutingKeyConstant.CLASSPAL_FILE_SERVICE_ROUTING_KEY_PDF_IMAGE_SLICING);
            }else{
                validAddWaterMarkParams(entity);
                File pdfDownLoadFile = FileUtil.downloadWithTimeout(entity.getPdfOssPath(),fileUtilConfig.getDownLoadTmpPath(), entity.getPdfName(),fileUtilConfig.getDownLoadTimeout());
                File waterMarkFile =  FileUtil.downloadWithTimeout(entity.getWaterMarkOssPath(),fileUtilConfig.getDownLoadTmpPath(), entity.getWaterMarkName(),fileUtilConfig.getDownLoadTimeout());
                entity.setPdfLocalPath(pdfDownLoadFile.getAbsolutePath());
                entity.setWaterMarkLocalPath(waterMarkFile.getAbsolutePath());
                dbjRabbitTemplate.sendExchangeEntityMessage(entity, PdfWaterMarkExChangeConstant.CLASSPAL_FILE_SERVICE_EXCHANGE_PDF_WATERMARK, PdfWaterMarkRoutingKeyConstant.CLASSPAL_FILE_SERVICE_ROUTING_KEY_PDF_ADD_WATER_MARK);
            }
        }catch (Exception e){
            log.error(e.getMessage());
            entity.setIsError(true);
            entity.setErrorMsg(e.getMessage());
            if (waterMarkBusinessType.equals(PdfWaterMarkBusinessTypeEnum.ONLY_ADD_WATER_MARK.getCode())) {
                dbjRabbitTemplate.sendExchangeEntityMessage(entity, PdfWaterMarkExChangeConstant.CLASSPAL_FILE_SERVICE_EXCHANGE_PDF_WATERMARK, PdfWaterMarkRoutingKeyConstant.CLASSPAL_FILE_SERVICE_ROUTING_KEY_PDF_ADD_WATER_MARK_RESULT);
            }else{
                dbjRabbitTemplate.sendExchangeEntityMessage(entity, PdfWaterMarkExChangeConstant.CLASSPAL_FILE_SERVICE_EXCHANGE_PDF_WATERMARK, PdfWaterMarkRoutingKeyConstant.CLASSPAL_FILE_SERVICE_ROUTING_KEY_PDF_IMAGE_SLICING_RESULT);
            }
        }finally {
            channel.basicAck(tag, false);// 拒绝并重新入队
        }
    }


    @ExtractHeader
    @RabbitListener(queues = {PdfWaterMarkQueueConstant.CLASSPAL_FILE_SERVICE_QUEUE_PDF_ADD_WATER_MARK})
    public void addWaterMark(WaterMarkEntity entity, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag, Message message) throws Exception{
        log.info("接收到PDF添加水印消息:{}",entity);
        if(entity == null || entity.getBusinessKey() == null || entity.getWaterMarkBusinessType() == null){
            channel.basicAck(tag, false);
            return;
        }
        Integer waterMarkBusinessType = entity.getWaterMarkBusinessType();
        try{
            entity.setIsError(false);
            if (entity.getIgnoreType() == null){
                entity.setIgnoreType(PdfWaterMarkIgnoreTypeEnum.IGNORE_FIRST_AND_LAST_WATER_MARK_YES.getCode());
            }
            validLocalParams(entity);

            String outPutPdfPath = pdfImageFacade.addWatermark(entity.getPdfLocalPath(), entity.getWaterMarkLocalPath(), entity.getScale(), entity.getTransparency(), entity.getRotationAngle(), entity.getHorizontalSpacing(), entity.getVerticalSpacing(), entity.getIgnoreType());
            entity.setPdfLocalWatermarkPath(outPutPdfPath);
            entity = upLoadOss(entity,outPutPdfPath);
            if (waterMarkBusinessType.equals(PdfWaterMarkBusinessTypeEnum.ONLY_ADD_WATER_MARK.getCode())) {
                dbjRabbitTemplate.sendExchangeEntityMessage(entity, PdfWaterMarkExChangeConstant.CLASSPAL_FILE_SERVICE_EXCHANGE_PDF_WATERMARK, PdfWaterMarkRoutingKeyConstant.CLASSPAL_FILE_SERVICE_ROUTING_KEY_PDF_ADD_WATER_MARK_RESULT);
                if (StringUtil.isNotEmpty(outPutPdfPath)){
                    FileUtils.deleteQuietly(new File(outPutPdfPath));
                }
                if (StringUtil.isNotEmpty(entity.getPdfOutPutOssPath())){
                    FileUtils.deleteQuietly(new File(entity.getPdfLocalPath()));
                }
            }else{
                dbjRabbitTemplate.sendExchangeEntityMessage(entity, PdfWaterMarkExChangeConstant.CLASSPAL_FILE_SERVICE_EXCHANGE_PDF_WATERMARK, PdfWaterMarkRoutingKeyConstant.CLASSPAL_FILE_SERVICE_ROUTING_KEY_PDF_IMAGE_SLICING);
            }
        }catch (Exception e){
            log.error(e.getMessage());
            entity.setIsError(true);
            entity.setErrorMsg(e.getMessage());
            if (waterMarkBusinessType.equals(PdfWaterMarkBusinessTypeEnum.ONLY_ADD_WATER_MARK.getCode())) {
                dbjRabbitTemplate.sendExchangeEntityMessage(entity, PdfWaterMarkExChangeConstant.CLASSPAL_FILE_SERVICE_EXCHANGE_PDF_WATERMARK, PdfWaterMarkRoutingKeyConstant.CLASSPAL_FILE_SERVICE_ROUTING_KEY_PDF_ADD_WATER_MARK_RESULT);
                if (StringUtil.isNotEmpty(entity.getPdfOutPutOssPath())){
                    FileUtils.deleteQuietly(new File(entity.getPdfLocalWatermarkPath()));
                }
                if (StringUtil.isNotEmpty(entity.getPdfOutPutOssPath())){
                    FileUtils.deleteQuietly(new File(entity.getPdfLocalPath()));
                }
                if (StringUtil.isNotEmpty(entity.getPdfOutPutOssPath())){
                    FileUtils.deleteQuietly(new File(entity.getPdfOutPutOssPath()));
                }
            }else{
                dbjRabbitTemplate.sendExchangeEntityMessage(entity, PdfWaterMarkExChangeConstant.CLASSPAL_FILE_SERVICE_EXCHANGE_PDF_WATERMARK, PdfWaterMarkRoutingKeyConstant.CLASSPAL_FILE_SERVICE_ROUTING_KEY_PDF_IMAGE_SLICING_RESULT);
            }
        }finally {
            channel.basicAck(tag, false);// 拒绝并重新入队
        }
    }

    public WaterMarkEntity upLoadOss(WaterMarkEntity entity,String outPutPdfPath) throws Exception {
        // 1. 参数校验
        if (outPutPdfPath == null || outPutPdfPath.trim().isEmpty()) {
            throw new IllegalArgumentException("上传oss的pdf水印文件路径不能为空");
        }
        // 2. 路径标准化处理
        String normalizedPath = outPutPdfPath.replace('/', File.separatorChar)
                .replace('\\', File.separatorChar);
        File file = new File(normalizedPath);
        // 3. 路径有效性验证
        if (!file.isAbsolute()) {
            throw new IllegalArgumentException("上传oss的pdf水印文件必须为绝对路径: " + outPutPdfPath);
        }
        if (!file.exists()) {
            throw new FileNotFoundException("上传oss的pdf水印文件不存在: " + normalizedPath);
        }
        if (file.isDirectory()) {
            throw new IllegalArgumentException("上传oss的pdf水印文件路径指向目录而非文件: " + normalizedPath);
        }
        String retuenPath = ossUtil.uploadFileWithRetuenPath(entity.getPdfName(), getInputStreamFromAbsolutePath(outPutPdfPath));
        entity.setPdfOutPutOssPath(retuenPath);
        return entity;
    }


    public static InputStream getInputStreamFromAbsolutePath(String absolutePath)
            throws FileNotFoundException {
        // 1. 参数校验
        if (absolutePath == null || absolutePath.trim().isEmpty()) {
            throw new IllegalArgumentException("上传oss的pdf水印文件路径不能为空");
        }

        // 2. 路径标准化处理
        String normalizedPath = absolutePath.replace('/', File.separatorChar)
                .replace('\\', File.separatorChar);
        File file = new File(normalizedPath);

        // 3. 路径有效性验证
        if (!file.isAbsolute()) {
            throw new IllegalArgumentException("上传oss的pdf水印文件必须为绝对路径: " + absolutePath);
        }
        if (!file.exists()) {
            throw new FileNotFoundException("上传oss的pdf水印文件不存在: " + normalizedPath);
        }
        if (file.isDirectory()) {
            throw new IllegalArgumentException("上传oss的pdf水印文件路径指向目录而非文件: " + normalizedPath);
        }

        // 4. 创建输入流（由调用方关闭流）
        return new FileInputStream(file);
    }


    @ExtractHeader
    @RabbitListener(queues = {PdfWaterMarkQueueConstant.CLASSPAL_FILE_SERVICE_QUEUE_PDF_IMAGE_SLICING})
    public void slicingImage(WaterMarkEntity entity, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag, Message message) throws Exception{
        log.info("接收到PDF切图消息:{}",entity);
        if(entity == null || entity.getBusinessKey() == null || entity.getWaterMarkBusinessType() == null){
            channel.basicAck(tag, false);
            return;
        }
        try{
            entity.setIsError(false);
            Integer waterMarkBusinessType = entity.getWaterMarkBusinessType();
            String pdfPath = entity.getPdfOssPath();
            if (waterMarkBusinessType.equals(PdfWaterMarkBusinessTypeEnum.ONLY_ADD_WATER_MARK.getCode())
                    || waterMarkBusinessType.equals(PdfWaterMarkBusinessTypeEnum.ONLY_ADD_WATER_MARK_AND_SLICING_IMAGE.getCode())) {
                pdfPath = entity.getPdfOutPutOssPath();
            }
            if (StringUtil.isEmpty(pdfPath)) {
                throw new BusinessException("参数错误，切图的pdf文件地址不能为空");
            }
            PdfToImagesRequest request = PdfToImagesRequest.createWithParams(
                    pdfPath, pdfImageConfig.getDpi());

            List<String> imageList;

            if(entity.isOnlyExtraCover()){
                PdfCoverRequest coverRequest = PdfCoverRequest.createWithParams( pdfPath, pdfImageConfig.getDpi());
                imageList = pythonProcessingService.extractCover(coverRequest).getFile_urls();

            }else{
                imageList = pythonProcessingService.convertToImages(request).getFile_urls();
            }
            entity.setWaterMarkLocalPathList(imageList);
            entity.setWaterMarkOssPathList(imageList);
            dbjRabbitTemplate.sendExchangeEntityMessage(entity, PdfWaterMarkExChangeConstant.CLASSPAL_FILE_SERVICE_EXCHANGE_PDF_WATERMARK, PdfWaterMarkRoutingKeyConstant.CLASSPAL_FILE_SERVICE_ROUTING_KEY_PDF_IMAGE_SLICING_RESULT);
        }catch (Exception e){
            log.error(e.getMessage());
            entity.setIsError(true);
            entity.setErrorMsg(e.getMessage());
            dbjRabbitTemplate.sendExchangeEntityMessage(entity, PdfWaterMarkExChangeConstant.CLASSPAL_FILE_SERVICE_EXCHANGE_PDF_WATERMARK, PdfWaterMarkRoutingKeyConstant.CLASSPAL_FILE_SERVICE_ROUTING_KEY_PDF_IMAGE_SLICING_RESULT);
        }finally {
            channel.basicAck(tag, false);// 拒绝并重新入队
        }
    }

}
