package com.dbj.classpal.books.service.biz.poem;

import com.dbj.classpal.books.service.entity.poem.AncientPoemReciteStandard;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;

/**
 * <p>
 * 古诗背诵评分标准表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
public interface IAncientPoemReciteStandardBiz extends IService<AncientPoemReciteStandard> {



    AncientPoemReciteStandard getByScore(BigDecimal score,Integer type);
}
