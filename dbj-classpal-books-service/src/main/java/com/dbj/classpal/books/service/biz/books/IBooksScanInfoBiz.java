package com.dbj.classpal.books.service.biz.books;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.client.dto.books.BooksScanCountDTO;
import com.dbj.classpal.books.service.entity.books.BooksScanInfo;

import java.util.List;

/**
 * <p>
 * 图书配置-图书内容分类 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
public interface IBooksScanInfoBiz extends IService<BooksScanInfo> {


    /**
     * <AUTHOR>
     * @Description  统计图书扫码次数
     * @Date 2025/4/25 9:40 
     * @param bookIds 图书id集合
     * @return BooksScanCountDTO
     **/
    List<BooksScanCountDTO> bookScanCount(List<Integer> bookIds);

}
