package com.dbj.classpal.books.service.api.client.books.app;

import com.dbj.classpal.books.client.api.books.app.BooksUserRankInCodeStudyLogApi;
import com.dbj.classpal.books.client.bo.books.app.BooksUserRankInCodeStudyLogBO;
import com.dbj.classpal.books.service.biz.books.IBooksRankInCodesContentsBiz;
import com.dbj.classpal.books.service.biz.books.IBooksUserRankInCodeStudyLogBiz;
import com.dbj.classpal.books.service.biz.books.IBooksUserRankStudyLogBiz;
import com.dbj.classpal.books.service.biz.books.IBooksUserRefBiz;
import com.dbj.classpal.books.service.entity.books.BooksRankInCodesContents;
import com.dbj.classpal.books.service.entity.books.BooksUserRankInCodeStudyLog;
import com.dbj.classpal.books.service.entity.books.BooksUserRankStudyLog;
import com.dbj.classpal.books.service.entity.books.BooksUserRef;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import com.dbj.classpal.framework.utils.util.ContextAppUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className BooksUserRankInCodeStudyLogApiImpl
 * @description
 * @date 2025-04-27 10:18
 **/
@RestController
public class BooksUserRankInCodeStudyLogApiImpl implements BooksUserRankInCodeStudyLogApi {

    @Resource
    private IBooksUserRefBiz booksUserRefBiz;
    @Resource
    private IBooksUserRankInCodeStudyLogBiz booksUserRankInCodeStudyLogBiz;
    @Resource
    private IBooksRankInCodesContentsBiz booksRankInCodesContentsBiz;
    @Resource
    private IBooksUserRankStudyLogBiz booksUserRankStudyLogBiz;

    @Override
    public RestResponse<Boolean> save(BooksUserRankInCodeStudyLogBO bo) throws BusinessException {
        Integer appUserId = ContextAppUtil.getAppUserIdInt();
        if(appUserId !=null){
            BooksRankInCodesContents booksRankInCodesContents = booksRankInCodesContentsBiz.getById(bo.getContentsId());
            if(booksRankInCodesContents != null){
                //判断用户的这本书是否在书架 如果不在需要添加
                List<BooksUserRef> booksUserRefList = booksUserRefBiz.lambdaQuery().eq(BooksUserRef::getAppUserId, appUserId).eq(BooksUserRef::getBooksId, booksRankInCodesContents.getBooksId()).list();
                if(CollectionUtils.isEmpty(booksUserRefList)){
                    BooksUserRef booksUserRef = new BooksUserRef();
                    booksUserRef.setAppUserId(appUserId);
                    booksUserRef.setBooksId(booksRankInCodesContents.getBooksId());
                    booksUserRefBiz.save(booksUserRef);
                }
                //修改用户的学习记录
                BooksUserRankInCodeStudyLog booksUserRankInCodeStudyLog = booksUserRankInCodeStudyLogBiz.lambdaQuery().eq(BooksUserRankInCodeStudyLog::getAppUserId,appUserId).eq(BooksUserRankInCodeStudyLog::getInCodesContentsId,booksRankInCodesContents.getId()).last("LIMIT 1").one();
                //不等于空进行修改，等于空则需要新增
                if(booksUserRankInCodeStudyLog != null){
                    booksUserRankInCodeStudyLogBiz.lambdaUpdate().eq(BooksUserRankInCodeStudyLog::getId,booksUserRankInCodeStudyLog.getId())
                            .set(BooksUserRankInCodeStudyLog::getIsLastStudy, YesOrNoEnum.YES.getCode())
                            .set(BooksUserRankInCodeStudyLog::getLastStudyTime, LocalDateTime.now())
                            .update();
                }else {
                    BooksUserRankInCodeStudyLog inCodeStudyLog =  new BooksUserRankInCodeStudyLog();
                    inCodeStudyLog.setAppUserId(appUserId);
                    inCodeStudyLog.setRankId(booksRankInCodesContents.getRankId());
                    inCodeStudyLog.setBooksId(booksRankInCodesContents.getBooksId());
                    inCodeStudyLog.setInCodesContentsId(booksRankInCodesContents.getId());
                    inCodeStudyLog.setRankClassifyId(booksRankInCodesContents.getRankClassifyId());
                    inCodeStudyLog.setIsLastStudy(YesOrNoEnum.YES.getCode());
                    inCodeStudyLog.setLastStudyTime(LocalDateTime.now());
                    booksUserRankInCodeStudyLogBiz.save(inCodeStudyLog);
                }


                // 新增用的最近查看图书的记录
                BooksUserRankStudyLog booksUserRankStudyLog =  booksUserRankStudyLogBiz.lambdaQuery().eq(BooksUserRankStudyLog::getAppUserId, ContextAppUtil.getAppUserIdInt())
                        .eq(BooksUserRankStudyLog::getRankId, booksRankInCodesContents.getRankId())
                        .orderByDesc(BooksUserRankStudyLog::getIsLastStudy).orderByDesc(BooksUserRankStudyLog::getLastStudyTime).last("LIMIT 1").one();
                //不等于空进行修改，等于空则需要新增
                if(booksUserRankStudyLog != null){
                    booksUserRankStudyLogBiz.lambdaUpdate().eq(BooksUserRankStudyLog::getId,booksUserRankStudyLog.getId())
                            .set(BooksUserRankStudyLog::getIsLastStudy, YesOrNoEnum.YES.getCode())
                            .set(BooksUserRankStudyLog::getLastStudyTime, LocalDateTime.now())
                            .update();
                }else {
                    BooksUserRankStudyLog rankStudyLog = new BooksUserRankStudyLog();
                    rankStudyLog.setAppUserId(appUserId);
                    rankStudyLog.setRankId(booksRankInCodesContents.getRankId());
                    rankStudyLog.setBooksId(booksRankInCodesContents.getBooksId());
                    rankStudyLog.setIsLastStudy(YesOrNoEnum.YES.getCode());
                    rankStudyLog.setLastStudyTime(LocalDateTime.now());
                    booksUserRankStudyLogBiz.save(rankStudyLog);
                }
            }
        }
        return RestResponse.success(true);
    }
}
