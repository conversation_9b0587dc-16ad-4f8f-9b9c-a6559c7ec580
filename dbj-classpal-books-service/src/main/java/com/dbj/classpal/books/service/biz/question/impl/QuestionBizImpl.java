package com.dbj.classpal.books.service.biz.question.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.client.enums.BasicConfigBizTypeEnum;
import com.dbj.classpal.books.common.dto.question.QuestionCountDTO;
import com.dbj.classpal.books.service.biz.question.QuestionBiz;
import com.dbj.classpal.books.service.entity.question.Question;
import com.dbj.classpal.books.service.entity.question.QuestionAnswer;
import com.dbj.classpal.books.service.entity.question.QuestionBlankArea;
import com.dbj.classpal.books.service.mapper.question.QuestionAnswerMapper;
import com.dbj.classpal.books.service.mapper.question.QuestionBlankAreaMapper;
import com.dbj.classpal.books.service.mapper.question.QuestionMapper;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * 题目业务实现类
 */
@Slf4j
@Service
public class QuestionBizImpl extends ServiceImpl<QuestionMapper, Question> implements QuestionBiz {

    private final QuestionMapper questionMapper;
    @Resource
    private QuestionBlankAreaMapper questionBlankAreaMapper;
    @Resource
    private QuestionAnswerMapper answerMapper;
    public QuestionBizImpl(QuestionMapper questionMapper) {
        this.questionMapper = questionMapper;
    }

    @Override
    public Question getQuestionById(Integer id) {
        return this.getById(id);
    }

    @Override
    public List<QuestionCountDTO> countByCategoryIds(List<Integer> categoryIds) {
        return questionMapper.countByCategoryIds(categoryIds);
    }

    @Override
    public List<Question> getQuestionListByCategoryId(Integer categoryId) {
        return this.lambdaQuery()
                .eq(Question::getQuestionCategoryId, categoryId)
                .list();
    }

    @Override
    public Integer createQuestion(Question question) {
        this.save(question);
        return question.getId();
    }

    @Override
    public void updateQuestion(Question question) {
        this.updateById(question);
    }

    @Override
    public void batchDeleteQuestion(List<Integer> ids) {

        this.lambdaUpdate()
                .in(Question::getId, ids)
                .remove();
    }

    @Override
    public void batchUpdateQuestionCategory(List<Integer> questionIds, Integer targetCategoryId) {
        this.lambdaUpdate()
            .in(Question::getId, questionIds)
            .set(Question::getQuestionCategoryId, targetCategoryId)
            .update();
    }

    @Override
    public Page<Question> pageList(Page<Question> page, Question condition) {
        return questionMapper.pageList(page, condition, BasicConfigBizTypeEnum.SUBJECT.getCode());
    }

    @Override
    public boolean hasCategoryQuestions(List<Integer> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return false;
        }
        LambdaQueryWrapper<Question> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Question::getQuestionCategoryId, categoryIds);
        return count(wrapper) > 0;
    }

    @Override
    public List<Question> getAllQuestionsByCategory(Integer categoryId) {
        return baseMapper.selectList(new LambdaQueryWrapper<Question>()
                .eq(Question::getQuestionCategoryId, categoryId)
                .eq(Question::getIsDeleted, YesOrNoEnum.NO.getCode())
                .orderByDesc(Question::getWeight,Question::getId));
    }

    @Override
    public List<Question> getQuestionsByCategory(Integer categoryId, Integer limit) throws BusinessException {
        List<Question> questions = null;
        if (categoryId == null) {
            throw new BusinessException(APP_QUESTION_BANK_ID_NOT_NULL_CODE,APP_QUESTION_BANK_ID_NOT_NULL_MSG);
        }
        if (limit == null || limit <= 0) {
            questions = this.baseMapper.selectList(
                    new LambdaQueryWrapper<Question>()
                            .eq(Question::getQuestionCategoryId, categoryId)
                            .orderByAsc(Question::getId)
            );
        }else{
            questions = this.baseMapper.selectList(
                    new LambdaQueryWrapper<Question>()
                            .eq(Question::getQuestionCategoryId, categoryId)
                            .orderByAsc(Question::getId)
                            .last("LIMIT " + limit)
            );
        }
        return CollectionUtils.isEmpty(questions) ? Collections.emptyList() : questions;
    }

    @Override
    public Map<Integer, Map<Integer, List<QuestionAnswer>>> getBlankAreaAnswerMap(Set<Integer> questionIds) {
        if (CollectionUtils.isEmpty(questionIds)) {
            return Collections.emptyMap();
        }

        // 获取所有空白区域
        List<QuestionBlankArea> blankAreas = questionBlankAreaMapper.selectList(
                new LambdaQueryWrapper<QuestionBlankArea>()
                        .in(QuestionBlankArea::getQuestionId, questionIds)
        );
        if (CollectionUtils.isEmpty(blankAreas)) {
            return Collections.emptyMap();
        }

        List<Integer> blankAreaIds = blankAreas.stream().map(QuestionBlankArea::getId).collect(Collectors.toList());

        // 获取所有答案
        List<QuestionAnswer> allAnswers = answerMapper.selectList(
                new LambdaQueryWrapper<QuestionAnswer>()
                        .in(QuestionAnswer::getBlankAreaId, blankAreaIds)
        );
        if (CollectionUtils.isEmpty(allAnswers)) {
            return Collections.emptyMap();
        }

        // 创建复合键，确保答案正确对应到题目和空白区域
        Map<String, List<QuestionAnswer>> answersMap = new HashMap<>();
        for (QuestionAnswer answer : allAnswers) {
            String key = answer.getQuestionId() + ":" + answer.getBlankAreaId();
            answersMap.computeIfAbsent(key, k -> new ArrayList<>()).add(answer);
        }

        // 按题目ID和空白区域ID组织结果
        Map<Integer, Map<Integer, List<QuestionAnswer>>> result = new HashMap<>();
        for (QuestionBlankArea area : blankAreas) {
            Integer questionId = area.getQuestionId();
            Integer blankAreaId = area.getId();
            String key = questionId + ":" + blankAreaId;

            List<QuestionAnswer> answers = answersMap.getOrDefault(key, Collections.emptyList());

            // 按serialNo排序，确保答案顺序一致
            answers.sort(Comparator.comparing(QuestionAnswer::getSerialNo, Comparator.nullsLast(Comparator.naturalOrder())));

            result.computeIfAbsent(questionId, k -> new HashMap<>())
                    .put(blankAreaId, answers);
        }

        return result;
    }
} 