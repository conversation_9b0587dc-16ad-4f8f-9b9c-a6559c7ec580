package com.dbj.classpal.books.service.util.audio;

import com.dbj.classpal.books.common.constant.AudioConstants;
import com.dbj.classpal.framework.oss.utils.OssUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;

@Slf4j
@Component
public class AudioUploadOSS {
    @Resource
    private OssUtil ossUtil;

    /**
     * 音频上传到OSS
     * @param fileName
     * @param file
     * @return
     */
    public String upload(String fileName, File file, String folder) {
        try {
            FileInputStream inputStream = new FileInputStream(file);
            String path = (StringUtils.isNotEmpty(folder) ? folder : AudioConstants.OSS_AUDIO_OUTPUT_DIR) + "/"  + fileName;
            return ossUtil.uploadFileWithRetuenPath(path, inputStream);
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error("上传音频失败：{}", e.getMessage());
        }
        return null;
    }
}
