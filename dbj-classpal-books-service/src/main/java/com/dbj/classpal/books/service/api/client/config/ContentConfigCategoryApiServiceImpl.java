//package com.dbj.classpal.books.service.api.client.config;
//
//import cn.hutool.core.bean.BeanUtil;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.dbj.classpal.books.client.api.config.ContentConfigCategoryApi;
//import com.dbj.classpal.books.client.bo.content.ContentConfigCategoryApiBO;
//import com.dbj.classpal.books.client.bo.content.ContentConfigCategoryIdApiBO;
//import com.dbj.classpal.books.client.bo.content.ContentConfigCategoryIdsApiBO;
//import com.dbj.classpal.books.client.bo.content.ContentConfigQueryCategoryApiBO;
//import com.dbj.classpal.books.client.dto.config.ContentConfigCategoryQueryApiDTO;
//import com.dbj.classpal.books.common.bo.content.ContentCategoryConfigBO;
//import com.dbj.classpal.books.common.bo.content.ContentConfigCategoryIdBO;
//import com.dbj.classpal.books.common.bo.content.ContentConfigCategoryIdsBO;
//import com.dbj.classpal.books.common.bo.content.ContentConfigCategoryQueryBO;
//import com.dbj.classpal.books.common.dto.content.ContentConfigCategoryDTO;
//import com.dbj.classpal.books.service.service.content.ContentConfigService;
//import com.dbj.classpal.framework.commons.converter.PageInfoConverter;
//import com.dbj.classpal.framework.commons.exception.BusinessException;
//import com.dbj.classpal.framework.commons.request.PageInfo;
//import com.dbj.classpal.framework.commons.response.RestResponse;
//import lombok.RequiredArgsConstructor;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.List;
//
//@RestController
//@RequiredArgsConstructor
//public class ContentConfigCategoryApiServiceImpl implements ContentConfigCategoryApi {
//
//    private final ContentConfigService contentConfigService;
//
//    @Override
//    public RestResponse<Integer> create(ContentConfigCategoryApiBO bo) throws BusinessException {
//        ContentCategoryConfigBO serviceBo = BeanUtil.copyProperties(bo, ContentCategoryConfigBO.class);
//        return RestResponse.success(contentConfigService.create(serviceBo));
//    }
//
//    @Override
//    public RestResponse<Void> update(ContentConfigCategoryApiBO bo) throws BusinessException {
//        ContentCategoryConfigBO serviceBo = BeanUtil.copyProperties(bo, ContentCategoryConfigBO.class);
//        contentConfigService.update(serviceBo);
//        return RestResponse.success(null);
//    }
//
//    @Override
//    public RestResponse<Void> batchDelete(ContentConfigCategoryIdsApiBO idsApiBO) {
//        ContentConfigCategoryIdsBO serviceBo = BeanUtil.copyProperties(idsApiBO, ContentConfigCategoryIdsBO.class);
//        contentConfigService.batchDelete(serviceBo);
//        return RestResponse.success(null);
//    }
//
//    @Override
//    public RestResponse<ContentConfigCategoryQueryApiDTO> detail(ContentConfigCategoryIdApiBO idApiBO) {
//        ContentConfigCategoryIdBO serviceBo = BeanUtil.copyProperties(idApiBO, ContentConfigCategoryIdBO.class);
//        return RestResponse.success(BeanUtil.copyProperties(contentConfigService.detail(serviceBo), ContentConfigCategoryQueryApiDTO.class));
//    }
//
//    @Override
//    public RestResponse<Page<ContentConfigCategoryQueryApiDTO>> pageList(PageInfo<ContentConfigQueryCategoryApiBO> queryApiBO) {
//        Page<ContentConfigCategoryDTO> page = contentConfigService.pageList(PageInfoConverter.convertPageInfo(queryApiBO, ContentConfigCategoryQueryBO.class));
//        return RestResponse.success((Page<ContentConfigCategoryQueryApiDTO>) page.convert(vo -> {
//            ContentConfigCategoryQueryApiDTO dto = new ContentConfigCategoryQueryApiDTO();
//            BeanUtil.copyProperties(vo, dto);
//            return dto;
//        }));
//    }
//
//    @Override
//    public RestResponse<List<ContentConfigCategoryQueryApiDTO>> list(ContentConfigQueryCategoryApiBO queryApiBO) {
//        ContentConfigCategoryQueryBO serviceBo = BeanUtil.copyProperties(queryApiBO, ContentConfigCategoryQueryBO.class);
//        return RestResponse.success(BeanUtil.copyToList(contentConfigService.list(serviceBo), ContentConfigCategoryQueryApiDTO.class));
//    }
//
//}