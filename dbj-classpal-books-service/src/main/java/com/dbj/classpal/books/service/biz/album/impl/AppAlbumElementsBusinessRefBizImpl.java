package com.dbj.classpal.books.service.biz.album.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.client.enums.BusinessTypeEnum;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsBusinessRefQueryCommonBO;
import com.dbj.classpal.books.common.dto.album.AppAlbumElementsBusinessRefQueryDTO;
import com.dbj.classpal.books.service.biz.album.IAppAlbumElementsBusinessRefBiz;
import com.dbj.classpal.books.service.entity.album.AppAlbumElementsBusinessRef;
import com.dbj.classpal.books.service.mapper.album.AppAlbumElementsBusinessRefMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppAlbumElementsBusinessRefBizImpl
 * Date:     2025-04-15 10:27:37
 * Description: 表名： ,描述： 表
 */
@Service
public class AppAlbumElementsBusinessRefBizImpl extends ServiceImpl<AppAlbumElementsBusinessRefMapper, AppAlbumElementsBusinessRef> implements IAppAlbumElementsBusinessRefBiz {

    @Override
    public Integer checkMenuRefCount(Integer id) {
        return baseMapper.checkMenuRefCount(id);
    }

    @Override
    public List<AppAlbumElementsBusinessRefQueryDTO> getRefBusinessList(AppAlbumElementsBusinessRefQueryCommonBO bo) {
        return baseMapper.refBusinessList(bo);
    }

    @Override
    public Boolean removeAlbumElementsBusinessRef(Set<Integer> businessIds, BusinessTypeEnum typeEnum) {
        if(CollUtil.isEmpty(businessIds) || typeEnum == null) {
            return false;
        }
        return remove(new LambdaQueryWrapper<AppAlbumElementsBusinessRef>()
                .in(AppAlbumElementsBusinessRef::getBusinessId, businessIds)
                .eq(AppAlbumElementsBusinessRef::getBusinessType, typeEnum.getCode())
        );
    }
}
