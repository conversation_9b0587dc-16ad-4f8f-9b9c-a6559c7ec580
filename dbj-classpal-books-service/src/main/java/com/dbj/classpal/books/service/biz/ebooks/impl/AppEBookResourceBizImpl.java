package com.dbj.classpal.books.service.biz.ebooks.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookResourceBatchQueryBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookResourceQueryBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookResourceSaveBO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookResourceDTO;
import com.dbj.classpal.books.common.enums.ResourceTypeEnum;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookResourceBiz;
import com.dbj.classpal.books.service.entity.product.AppEBook;
import com.dbj.classpal.books.service.entity.product.AppEBookResource;
import com.dbj.classpal.books.service.entity.product.AppEBookshelfBookRef;
import com.dbj.classpal.books.service.entity.product.AppEBookstoreShelfRef;
import com.dbj.classpal.books.service.mapper.ebooks.AppEBookMapper;
import com.dbj.classpal.books.service.mapper.ebooks.AppEBookResourceMapper;
import com.dbj.classpal.books.service.mapper.ebooks.AppEBookshelfBookRefMapper;
import com.dbj.classpal.books.service.mapper.ebooks.AppEBookstoreShelfRefMapper;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import com.dbj.classpal.framework.pdf.enums.PdfWaterMarkBusinessTypeEnum;
import io.swagger.models.auth.In;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 单书资源 业务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-10
 */
@Slf4j
@Service
public class AppEBookResourceBizImpl extends ServiceImpl<AppEBookResourceMapper, AppEBookResource> implements IAppEBookResourceBiz {
    @Resource
    private AppEBookMapper eBookMapper;

    @Resource
    private AppEBookResourceMapper eBookResourceMapper;

    @Resource
    private AppEBookshelfBookRefMapper eBookshelfBookRefMapper;

    @Resource
    private AppEBookstoreShelfRefMapper eBookstoreShelfRefMapper;

    @Override
    public Page<AppEBookResourceDTO> page(PageInfo<AppEBookResourceQueryBO> pageRequest) throws BusinessException {
        AppEBookResourceQueryBO query = pageRequest.getData();
        AppEBook appEBook = null;
        if(query.getBookId() != null){
            appEBook = eBookMapper.selectById(query.getBookId());
        }
        LambdaQueryWrapper<AppEBookResource> wrapper = new LambdaQueryWrapper<AppEBookResource>()
                .eq(Objects.nonNull(appEBook), AppEBookResource::getResourceKey, Objects.requireNonNull(appEBook).getFileMd5())
                .eq(Objects.nonNull(query.getResourceType()), AppEBookResource::getResourceType, query.getResourceType())
                .eq(Objects.nonNull(query.getBusinessType()), AppEBookResource::getBusinessType, query.getBusinessType())
                .eq(AppEBookResource::getIsDeleted, YesOrNoEnum.NO.getCode())
                .orderByAsc(AppEBookResource::getResourceType)
                .orderByAsc(AppEBookResource::getPageNum)
                .orderByAsc(AppEBookResource::getSortNum);
        
        Page<AppEBookResource> page = page(pageRequest.getPage(), wrapper);

        return (Page<AppEBookResourceDTO>) page.convert(resource -> {
            AppEBookResourceDTO dto = new AppEBookResourceDTO();
            BeanUtil.copyProperties(resource, dto);
            
            // 设置资源类型描述
            ResourceTypeEnum resourceTypeEnum = ResourceTypeEnum.fromCode(resource.getResourceType());
            if (resourceTypeEnum != null) {
                dto.setResourceTypeDesc(resourceTypeEnum.getDesc());
            }
            
            // 设置业务类型描述
            PdfWaterMarkBusinessTypeEnum businessTypeEnum = PdfWaterMarkBusinessTypeEnum.getByCode(resource.getBusinessType());
            if (businessTypeEnum != null) {
                dto.setBusinessTypeDesc(businessTypeEnum.getDesc());
            }
            
            return dto;
        });
    }

    @Override
    public List<AppEBookResourceDTO> listByBookId(Integer bookId) {
        if (bookId == null) {
            return new ArrayList<>();
        }
        AppEBook appEBook = eBookMapper.selectById(bookId);
        LambdaQueryWrapper<AppEBookResource> wrapper = new LambdaQueryWrapper<AppEBookResource>()
                .eq(AppEBookResource::getResourceKey, appEBook.getFileMd5())
                .eq(AppEBookResource::getIsDeleted, YesOrNoEnum.NO.getCode())
                .orderByAsc(AppEBookResource::getResourceType)
                .orderByAsc(AppEBookResource::getPageNum)
                .orderByAsc(AppEBookResource::getSortNum);
        
        List<AppEBookResource> resources = list(wrapper);
        
        return resources.stream().map(resource -> {
            AppEBookResourceDTO dto = new AppEBookResourceDTO();
            BeanUtil.copyProperties(resource, dto);
            
            // 设置资源类型描述
            ResourceTypeEnum resourceTypeEnum = ResourceTypeEnum.fromCode(resource.getResourceType());
            if (resourceTypeEnum != null) {
                dto.setResourceTypeDesc(resourceTypeEnum.getDesc());
            }
            
            // 设置业务类型描述
            PdfWaterMarkBusinessTypeEnum businessTypeEnum = PdfWaterMarkBusinessTypeEnum.getByCode(resource.getBusinessType());
            if (businessTypeEnum != null) {
                dto.setBusinessTypeDesc(businessTypeEnum.getDesc());
            }
            
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<AppEBookResourceDTO> listByBookIdAndType(Integer bookId, List<Integer> resourceTypes) {
        if (bookId == null || resourceTypes == null) {
            return new ArrayList<>();
        }
        AppEBook appEBook = eBookMapper.selectById(bookId);
        LambdaQueryWrapper<AppEBookResource> wrapper = new LambdaQueryWrapper<AppEBookResource>()
                .eq(AppEBookResource::getResourceKey, appEBook.getFileMd5())
                .in(AppEBookResource::getResourceType, resourceTypes)
                .eq(AppEBookResource::getIsDeleted, YesOrNoEnum.NO.getCode())
                .orderByAsc(AppEBookResource::getPageNum)
                .orderByAsc(AppEBookResource::getSortNum);
        
        List<AppEBookResource> resources = list(wrapper);
        
        return resources.stream().map(resource -> {
            AppEBookResourceDTO dto = new AppEBookResourceDTO();
            BeanUtil.copyProperties(resource, dto);
            
            // 设置资源类型描述
            ResourceTypeEnum resourceTypeEnum = ResourceTypeEnum.fromCode(resource.getResourceType());
            if (resourceTypeEnum != null) {
                dto.setResourceTypeDesc(resourceTypeEnum.getDesc());
            }
            
            // 设置业务类型描述
            PdfWaterMarkBusinessTypeEnum businessTypeEnum = PdfWaterMarkBusinessTypeEnum.getByCode(resource.getBusinessType());
            if (businessTypeEnum != null) {
                dto.setBusinessTypeDesc(businessTypeEnum.getDesc());
            }
            
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<AppEBookResourceDTO> listByBookIdsAndType(List<Integer> bookIds, Integer resourceType) {
        if (CollectionUtils.isEmpty(bookIds) || resourceType == null) {
            return new ArrayList<>();
        }

        // 批量查询书籍的MD5
        List<AppEBook> books = eBookMapper.selectByIds(bookIds);
        if (CollectionUtils.isEmpty(books)) {
            return new ArrayList<>();
        }

        // 提取MD5列表
        List<String> md5List = books.stream()
                .map(AppEBook::getFileMd5)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(md5List)) {
            return new ArrayList<>();
        }

        // 批量查询资源
        LambdaQueryWrapper<AppEBookResource> wrapper = new LambdaQueryWrapper<AppEBookResource>()
                .in(AppEBookResource::getResourceKey, md5List)
                .eq(AppEBookResource::getResourceType, resourceType)
                .eq(AppEBookResource::getIsDeleted, YesOrNoEnum.NO.getCode())
                .orderByAsc(AppEBookResource::getPageNum)
                .orderByAsc(AppEBookResource::getSortNum);

        List<AppEBookResource> resources = list(wrapper);

        // 创建MD5到BookId的映射
        Map<String, Integer> md5ToBookIdMap = books.stream()
                .collect(Collectors.toMap(AppEBook::getFileMd5, AppEBook::getId, (existing, replacement) -> existing));

        return resources.stream().map(resource -> {
            AppEBookResourceDTO dto = new AppEBookResourceDTO();
            BeanUtil.copyProperties(resource, dto);

            dto.setResourceId(md5ToBookIdMap.get(resource.getResourceKey()));

            // 设置资源类型描述
            ResourceTypeEnum resourceTypeEnum = ResourceTypeEnum.fromCode(resource.getResourceType());
            if (resourceTypeEnum != null) {
                dto.setResourceTypeDesc(resourceTypeEnum.getDesc());
            }

            // 设置业务类型描述
            PdfWaterMarkBusinessTypeEnum businessTypeEnum = PdfWaterMarkBusinessTypeEnum.getByCode(resource.getBusinessType());
            if (businessTypeEnum != null) {
                dto.setBusinessTypeDesc(businessTypeEnum.getDesc());
            }

            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(AppEBookResourceSaveBO saveBO) throws BusinessException {
        if (saveBO == null || saveBO.getResourceId() == null || saveBO.getResourceType() == null || saveBO.getResourceUrl() == null) {
            throw new BusinessException("参数不完整");
        }
        
        AppEBookResource resource = new AppEBookResource();
        BeanUtil.copyProperties(saveBO, resource);
        
        // 保存资源
        this.save(resource);
        
        return resource.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(List<AppEBookResourceSaveBO> saveBOList) throws BusinessException {
        if (CollectionUtils.isEmpty(saveBOList)) {
            return true;
        }
        
        List<AppEBookResource> resources = saveBOList.stream().map(saveBO -> {
            AppEBookResource resource = new AppEBookResource();
            BeanUtil.copyProperties(saveBO, resource);
            return resource;
        }).collect(Collectors.toList());
        
        // 批量保存资源
        return this.saveBatch(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Integer id) throws BusinessException {
        if (id == null) {
            return false;
        }
        
        return this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBookId(Integer bookId) {
        if (bookId == null) {
            return false;
        }

        LambdaQueryWrapper<AppEBookResource> wrapper = new LambdaQueryWrapper<AppEBookResource>()
                .eq(AppEBookResource::getResourceId, bookId);
        
        return this.remove(wrapper);
    }


    @Override
    public Page<AppEBookResourceDTO> pageImages(PageInfo<AppEBookResourceBatchQueryBO> pageRequest) {
        if (pageRequest == null || pageRequest.getData() == null || CollectionUtils.isEmpty(pageRequest.getData().getBookIds())) {
            return new Page<>();
        }
        AppEBookResourceBatchQueryBO query = pageRequest.getData();
        List<AppEBook> books = eBookMapper.selectByIds(query.getBookIds());
        if (CollectionUtils.isEmpty(books)) {
            return new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        }

        List<String> md5sWithWatermark = new ArrayList<>();
        List<String> md5sWithoutWatermark = new ArrayList<>();

        for (AppEBook book : books) {
            if (book.getWatermarkId() != null && book.getWatermarkId() > YesOrNoEnum.NO.getCode()) {
                if (book.getFileMd5() != null) {
                    md5sWithWatermark.add(book.getFileMd5());
                }
            } else {
                if (book.getFileMd5() != null) {
                    md5sWithoutWatermark.add(book.getFileMd5());
                }
            }
        }

        if (md5sWithWatermark.isEmpty() && md5sWithoutWatermark.isEmpty()) {
            return new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        }
        Page<AppEBookResource> page = queryResourcesIntelligently(pageRequest, md5sWithWatermark, md5sWithoutWatermark);

        if (CollUtil.isEmpty(page.getRecords())) {
            return new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        }

        return convertToDtoPage(page);
    }

    /**
     * - 对于有水印的MD5，查询 business_type = ONLY_ADD_WATER_MARK_AND_SLICING_IMAGE
     * - 对于无水印的MD5，查询 business_type = ONLY_SLICING_IMAGE
     * - 同时，解决了 ONLY_FULL_GROUP_BY 问题，并按 (page_num, resource_type) 去重。
     *
     * @param pageRequest          分页请求
     * @param md5sWithWatermark    有水印的MD5列表
     * @param md5sWithoutWatermark 无水印的MD5列表
     * @return 分页结果
     */
    private Page<AppEBookResource> queryResourcesIntelligently(PageInfo<?> pageRequest, List<String> md5sWithWatermark, List<String> md5sWithoutWatermark) {

        QueryWrapper<AppEBookResource> idQueryWrapper = new QueryWrapper<AppEBookResource>()
                .select("MIN(id) as id")
                .in("resource_type", List.of(ResourceTypeEnum.PAGE_IMAGE.getCode(), ResourceTypeEnum.COVER_IMAGE.getCode()))
                .and(wrapper -> {
                    if (CollUtil.isNotEmpty(md5sWithWatermark)) {
                        wrapper.nested(w -> w.in("resource_key", md5sWithWatermark)
                                .eq("business_type", PdfWaterMarkBusinessTypeEnum.ONLY_ADD_WATER_MARK_AND_SLICING_IMAGE.getCode()));
                    }

                    if (CollUtil.isNotEmpty(md5sWithWatermark) && CollUtil.isNotEmpty(md5sWithoutWatermark)) {
                        wrapper.or();
                    }

                    if (CollUtil.isNotEmpty(md5sWithoutWatermark)) {
                        wrapper.nested(w -> w.in("resource_key", md5sWithoutWatermark)
                                .eq("business_type", PdfWaterMarkBusinessTypeEnum.ONLY_SLICING_IMAGE.getCode()));
                    }
                })
                .groupBy("page_num", "resource_type");

        List<Object> idObjects = baseMapper.selectObjs(idQueryWrapper);
        if (CollUtil.isEmpty(idObjects)) {
            return new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0);
        }

        List<Long> distinctIds = idObjects.stream().map(id -> Long.valueOf(id.toString())).collect(Collectors.toList());

        LambdaQueryWrapper<AppEBookResource> finalQueryWrapper = new LambdaQueryWrapper<AppEBookResource>()
                .in(AppEBookResource::getId, distinctIds)
                .orderByAsc(AppEBookResource::getPageNum)
                .orderByAsc(AppEBookResource::getResourceType);

        Page<AppEBookResource> page = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), false);
        page = this.page(page, finalQueryWrapper);
        page.setTotal(distinctIds.size());

        return page;
    }

    /**
     * 将 Page<AppEBookResource> 转换为 Page<AppEBookResourceDTO>
     * @param page 原始分页对象
     * @return DTO分页对象
     */
    private Page<AppEBookResourceDTO> convertToDtoPage(Page<AppEBookResource> page) {
        return (Page<AppEBookResourceDTO>) page.convert(resource -> {
            AppEBookResourceDTO dto = new AppEBookResourceDTO();
            BeanUtil.copyProperties(resource, dto);

            // 设置资源类型描述
            ResourceTypeEnum resourceTypeEnum = ResourceTypeEnum.fromCode(resource.getResourceType());
            if (resourceTypeEnum != null) {
                dto.setResourceTypeDesc(resourceTypeEnum.getDesc());
            }

            // 设置业务类型描述
            PdfWaterMarkBusinessTypeEnum businessTypeEnum = PdfWaterMarkBusinessTypeEnum.getByCode(resource.getBusinessType());
            if (businessTypeEnum != null) {
                dto.setBusinessTypeDesc(businessTypeEnum.getDesc());
            }

            return dto;
        });
    }


    @Override
    public Map<Integer, List<AppEBookResourceDTO>> batchQueryByBooks(AppEBookResourceBatchQueryBO queryBO) {
        if (queryBO == null || CollectionUtils.isEmpty(queryBO.getBookIds())) {
            return Map.of();
        }

        // 根据书籍ID列表查询对应的文件MD5
        List<AppEBook> books = eBookMapper.selectByIds(queryBO.getBookIds());
        if (CollectionUtils.isEmpty(books)) {
            return Map.of();
        }

        // 提取文件MD5列表
        List<String> md5List = books.stream()
                .map(AppEBook::getFileMd5)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(md5List)) {
            return Map.of();
        }

        // 构建查询条件
        LambdaQueryWrapper<AppEBookResource> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(AppEBookResource::getResourceKey, md5List)
                .orderByAsc(AppEBookResource::getPageNum, AppEBookResource::getSortNum);

        if (queryBO.getResourceType() != null) {
            wrapper.eq(AppEBookResource::getResourceType, queryBO.getResourceType());
        }
        if (queryBO.getBusinessType() != null) {
            wrapper.eq(AppEBookResource::getBusinessType, queryBO.getBusinessType());
        }

        // 查询资源列表
        List<AppEBookResource> resources = this.list(wrapper);
        if (CollectionUtils.isEmpty(resources)) {
            return Map.of();
        }

        // 创建MD5到书籍ID的映射
        Map<String, Integer> md5ToBookIdMap = books.stream()
                .filter(book -> book.getFileMd5() != null)
                .collect(Collectors.toMap(AppEBook::getFileMd5, AppEBook::getId, (existing, replacement) -> existing));

        // 转换为DTO并按书籍ID分组
        return resources.stream()
                .map(this::convertToDTO)
                .filter(dto -> md5ToBookIdMap.containsKey(getResourceKeyFromDTO(dto)))
                .collect(Collectors.groupingBy(dto -> md5ToBookIdMap.get(getResourceKeyFromDTO(dto))));
    }

    @Override
    public Map<Integer, List<AppEBookResourceDTO>> batchQueryByShelves(AppEBookResourceBatchQueryBO queryBO) throws BusinessException {
        log.info("按书架批量查询资源，书架数量：{}", queryBO.getShelfIds() != null ? queryBO.getShelfIds().size() : 0);

        if (CollectionUtils.isEmpty(queryBO.getShelfIds())) {
            return new HashMap<>();
        }

        try {
            // 1. 查询书架下的所有书籍ID
            List<Integer> allBookIds = new ArrayList<>();
            for (Integer shelfId : queryBO.getShelfIds()) {
                List<Integer> bookIds = eBookshelfBookRefMapper.selectList(
                        Wrappers.<AppEBookshelfBookRef>lambdaQuery()
                                .eq(AppEBookshelfBookRef::getShelfId, shelfId)
                                .select(AppEBookshelfBookRef::getBookId)
                ).stream().map(AppEBookshelfBookRef::getBookId).toList();
                allBookIds.addAll(bookIds);
            }

            if (allBookIds.isEmpty()) {
                return new HashMap<>();
            }

            // 2. 构建书籍查询参数
            AppEBookResourceBatchQueryBO bookQueryBO = new AppEBookResourceBatchQueryBO();
            bookQueryBO.setBookIds(allBookIds);
            bookQueryBO.setResourceType(queryBO.getResourceType());
            bookQueryBO.setBusinessType(queryBO.getBusinessType());

            // 3. 调用按书籍查询的方法
            return batchQueryByBooks(bookQueryBO);

        } catch (Exception e) {
            log.error("按书架批量查询资源失败", e);
            throw new BusinessException("按书架批量查询资源失败：" + e.getMessage());
        }
    }

    @Override
    public Page<AppEBookResourceDTO> pageStoreResources(PageInfo<AppEBookResourceBatchQueryBO> pageRequest) throws BusinessException {
        log.info("分页查询书城资源，书城ID：{}", pageRequest.getData().getStoreId());

        if (pageRequest.getData().getStoreId() == null) {
            throw new BusinessException("书城ID不能为空");
        }

        try {
            // 1. 查询书城下的所有书籍ID
            List<Integer> allBookIds = new ArrayList<>();

            // 先查询书城下的书架
            List<Integer> shelfIds = eBookstoreShelfRefMapper.selectList(
                    Wrappers.<AppEBookstoreShelfRef>lambdaQuery()
                            .eq(AppEBookstoreShelfRef::getStoreId, pageRequest.getData().getStoreId())
                            .select(AppEBookstoreShelfRef::getShelfId)
            ).stream().map(AppEBookstoreShelfRef::getShelfId).toList();

            // 再查询书架下的书籍
            if (!shelfIds.isEmpty()) {
                for (Integer shelfId : shelfIds) {
                    List<Integer> bookIds = eBookshelfBookRefMapper.selectList(
                            Wrappers.<AppEBookshelfBookRef>lambdaQuery()
                                    .eq(AppEBookshelfBookRef::getShelfId, shelfId)
                                    .select(AppEBookshelfBookRef::getBookId)
                    ).stream().map(AppEBookshelfBookRef::getBookId).toList();
                    allBookIds.addAll(bookIds);
                }
            }

            if (allBookIds.isEmpty()) {
                return new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
            }

            // 2. 构建分页查询条件
            Page<AppEBookResource> page = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

            LambdaQueryWrapper<AppEBookResource> queryWrapper = Wrappers.lambdaQuery();

            // 根据书籍MD5查询资源
            List<String> md5List = eBookMapper.selectByIds(allBookIds).stream()
                    .map(AppEBook::getFileMd5)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (md5List.isEmpty()) {
                return new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
            }

            queryWrapper.in(AppEBookResource::getResourceKey, md5List);

            // 添加资源类型过滤
            if (pageRequest.getData().getResourceType() != null) {
                queryWrapper.eq(AppEBookResource::getResourceType, pageRequest.getData().getResourceType());
            }

            // 添加业务类型过滤
            if (pageRequest.getData().getBusinessType() != null) {
                queryWrapper.eq(AppEBookResource::getBusinessType, pageRequest.getData().getBusinessType());
            }

            // 3. 执行分页查询
            Page<AppEBookResource> resourcePage = eBookResourceMapper.selectPage(page, queryWrapper);

            // 4. 转换结果
            return (Page<AppEBookResourceDTO>) resourcePage.convert(this::convertToDTO);

        } catch (Exception e) {
            log.error("分页查询书城资源失败", e);
            throw new BusinessException("分页查询书城资源失败：" + e.getMessage());
        }
    }

    /**
     * 转换为DTO
     */
    private AppEBookResourceDTO convertToDTO(AppEBookResource resource) {
        AppEBookResourceDTO dto = new AppEBookResourceDTO();
        BeanUtil.copyProperties(resource, dto);

        // 设置资源类型描述
        ResourceTypeEnum resourceTypeEnum = ResourceTypeEnum.fromCode(resource.getResourceType());
        if (resourceTypeEnum != null) {
            dto.setResourceTypeDesc(resourceTypeEnum.getDesc());
        }

        // 设置业务类型描述
        PdfWaterMarkBusinessTypeEnum businessTypeEnum = PdfWaterMarkBusinessTypeEnum.getByCode(resource.getBusinessType());
        if (businessTypeEnum != null) {
            dto.setBusinessTypeDesc(businessTypeEnum.getDesc());
        }

        return dto;
    }

    /**
     * 从DTO中获取资源键（MD5）
     */
    private String getResourceKeyFromDTO(AppEBookResourceDTO dto) {
        return dto.getResourceKey();
    }
}