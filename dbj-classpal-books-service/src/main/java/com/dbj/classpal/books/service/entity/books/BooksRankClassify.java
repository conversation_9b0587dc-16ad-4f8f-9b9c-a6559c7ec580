package com.dbj.classpal.books.service.entity.books;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 图书配置-图书内容分类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("books_rank_classify")
@Tag(name="BooksRankClassify对象", description="图书配置-图书内容分类")
public class BooksRankClassify extends BizEntity implements Serializable {

    @Schema(description = "类型名称")
    private String name;

    @Schema(description = "书内码 bookCodes")
    private String type;

    @Schema(description = "册数id")
    private Integer rankId;

    @Schema(description = "权重用于排序")
    private Integer weight;

    @Schema(description = "是否启用 1-是 0-否")
    private Integer status;


}
