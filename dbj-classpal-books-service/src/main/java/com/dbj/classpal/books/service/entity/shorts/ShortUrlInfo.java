package com.dbj.classpal.books.service.entity.shorts;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 短链接信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("short_url_info")
@Tag(name="ShortUrlInfo", description="短链接信息表")
public class ShortUrlInfo extends BizEntity implements Serializable {



    @Schema(description = "短码")
    private String shortCode;

    @Schema(description = "长链接")
    private String longUrl;
}
