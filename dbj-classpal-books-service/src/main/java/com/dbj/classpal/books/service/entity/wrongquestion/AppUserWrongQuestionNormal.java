package com.dbj.classpal.books.service.entity.wrongquestion;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户普通题错题本
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_user_wrong_question_normal")
@Schema(description = "用户普通题错题本")
public class AppUserWrongQuestionNormal extends BizEntity implements Serializable {


    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private Integer appUserId;

    /**
     * 问题id
     */
    @Schema(description = "问题id")
    private Integer questionId;

    /**
     * 题目内容
     */
    @Schema(description = "题目内容")
    private String questionContent;

    /**
     * 题目类型（单选/多选/判断等）
     */
    @Schema(description = "题目类型（单选/多选/判断等）")
    private String questionType;

    /**
     * 学科ID
     */
    @Schema(description = "学科ID")
    private Integer subjectId;

    /**
     * 学科名称
     */
    @Schema(description = "学科名称")
    private String subjectName;

    /**
     * 来源类型（1:试卷,2:练习,3:题库等）
     */
    @Schema(description = "来源类型（1:试卷,2:练习,3:题库等）")
    private Integer sourceType;

    /**
     * 来源ID
     */
    @Schema(description = "来源ID")
    private Integer sourceId;

    /**
     * 用户错误答案
     */
    @Schema(description = "用户错误答案")
    private String userAnswer;

    /**
     * 用户选择的答案ID（多个英文逗号隔开）
     */
    @Schema(description = "用户选择的答案ID（多个英文逗号隔开）")
    private String answerIds;
    /**
     * 唯一标识一次错题事件
     */
    @Schema(description = "唯一标识一次错题事件")
    private String stateId;
} 