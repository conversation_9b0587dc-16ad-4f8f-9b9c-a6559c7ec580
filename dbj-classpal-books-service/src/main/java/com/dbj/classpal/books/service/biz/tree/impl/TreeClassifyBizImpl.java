package com.dbj.classpal.books.service.biz.tree.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.enums.tree.TreeClassifyEnum;
import com.dbj.classpal.books.service.biz.tree.ITreeClassifyBiz;
import com.dbj.classpal.books.service.entity.tree.TreeClassify;
import com.dbj.classpal.books.service.mapper.tree.TreeClassifyMapper;
import org.springframework.stereotype.Service;

import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Deque;
import java.util.List;


/**
 * <p>
 * 树形目录 业务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Service
public class TreeClassifyBizImpl extends ServiceImpl<TreeClassifyMapper, TreeClassify> implements ITreeClassifyBiz {

    @Override
    public List<Integer> getSubTreeIds(Integer nodeId, boolean includeSelf) {
        List<Integer> result = new ArrayList<>();
        Deque<Integer> queue = new ArrayDeque<>();

        if (includeSelf) {
            result.add(nodeId);
        }

        queue.offer(nodeId);

        while (!queue.isEmpty()) {
            Integer currentId = queue.poll();
            List<TreeClassify> children = this.list(Wrappers.<TreeClassify>lambdaQuery()
                    .eq(TreeClassify::getParentId, currentId));
            for (TreeClassify child : children) {
                result.add(child.getId());
                queue.offer(child.getId());
            }
        }

        return result;
    }

    @Override
    public TreeClassify getRootNode(TreeClassifyEnum ancientPoem) {
        return this.getOne(Wrappers.<TreeClassify>lambdaQuery()
                .eq(TreeClassify::getType, ancientPoem.getType())
                .eq(TreeClassify::getParentId, 0));
    }
}
