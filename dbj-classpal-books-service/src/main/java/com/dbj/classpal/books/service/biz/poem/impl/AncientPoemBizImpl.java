package com.dbj.classpal.books.service.biz.poem.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.client.bo.poem.AncientPoemPageBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemDTO;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemBiz;
import com.dbj.classpal.books.service.entity.poem.AncientPoem;
import com.dbj.classpal.books.service.mapper.poem.AncientPoemMapper;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 古诗文 业务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Service
public class AncientPoemBizImpl extends ServiceImpl<AncientPoemMapper, AncientPoem> implements IAncientPoemBiz {

    @Override
    public Page<AncientPoemDTO> getAncientPoemPage(Page<Object> page, AncientPoemPageBO bo) {
        return this.baseMapper.getAncientPoemPage(page, bo);
    }
}
