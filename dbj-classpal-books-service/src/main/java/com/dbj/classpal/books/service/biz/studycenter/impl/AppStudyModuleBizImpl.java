package com.dbj.classpal.books.service.biz.studycenter.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.admin.client.dto.sys.dict.SysDictItemApiDTO;
import com.dbj.classpal.books.common.bo.studycenter.AppStudyModuleCreateBO;
import com.dbj.classpal.books.common.bo.studycenter.AppStudyModuleQueryPageBO;
import com.dbj.classpal.books.common.bo.studycenter.AppStudyModuleUpdateBO;
import com.dbj.classpal.books.common.bo.studycenter.StudyCenterModuleListQueryBO;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.books.common.dto.question.QuestionCountDTO;
import com.dbj.classpal.books.common.dto.studycenter.*;
import com.dbj.classpal.books.common.enums.BusinessTypeEnum;
import com.dbj.classpal.books.service.biz.studycenter.IAppStudyModuleBiz;
import com.dbj.classpal.books.service.entity.config.BasicConfig;
import com.dbj.classpal.books.service.entity.question.QuestionCategory;
import com.dbj.classpal.books.service.entity.question.QuestionCategoryBusinessRef;
import com.dbj.classpal.books.service.entity.studycenter.AppStudyModule;
import com.dbj.classpal.books.service.entity.studycenter.AppStudyModuleQuestionExt;
import com.dbj.classpal.books.service.entity.studycenter.AppStudyModuleResourceRel;
import com.dbj.classpal.books.service.mapper.config.BasicConfigMapper;
import com.dbj.classpal.books.service.mapper.question.QuestionCategoryBusinessRefMapper;
import com.dbj.classpal.books.service.mapper.question.QuestionCategoryMapper;
import com.dbj.classpal.books.service.mapper.question.QuestionMapper;
import com.dbj.classpal.books.service.mapper.studycenter.AppStudyModuleMapper;
import com.dbj.classpal.books.service.mapper.studycenter.AppStudyModuleQuestionExtMapper;
import com.dbj.classpal.books.service.mapper.studycenter.AppStudyModuleResourceRelMapper;
import com.dbj.classpal.books.service.remote.sys.SysDictItemRemoteService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import jakarta.annotation.Resource;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AppStudyModuleBizImpl extends ServiceImpl<AppStudyModuleMapper, AppStudyModule> implements IAppStudyModuleBiz {
    @Resource
    private AppStudyModuleMapper appStudyModuleMapper;
    @Resource
    private AppStudyModuleQuestionExtMapper questionExtMapper;
    @Resource
    private AppStudyModuleResourceRelMapper resourceRelMapper;
    @Resource
    private BasicConfigMapper configMapper;
    @Resource
    private QuestionCategoryBusinessRefMapper businessRefMapper;
    @Resource
    private QuestionCategoryMapper questionCategoryMapper;
    @Resource
    private QuestionMapper questionMapper;
    @Resource
    private SysDictItemRemoteService dictItemRemoteService;
    private static final String GARAGE_DICT_CODE = "grages";
    private static final String DEFAULT_GARDE = "firstGrade";
    private static final String ALL_GARDE = "ALL";

    @Override
    public AppStudyModule create(AppStudyModuleCreateBO bo) throws BusinessException {
        AppStudyModule entity = BeanUtil.copyProperties(bo, AppStudyModule.class);
        appStudyModuleMapper.insert(entity);
        return entity;
    }

    @Override
    public Boolean update(AppStudyModuleUpdateBO bo) throws BusinessException {
        AppStudyModule entity = BeanUtil.copyProperties(bo, AppStudyModule.class);
        int result = appStudyModuleMapper.updateById(entity);
        return result > YesOrNoEnum.NO.getCode();
    }

    @Override
    public Boolean delete(List<Integer> ids) throws BusinessException {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException(AppErrorCode.ID_LIST_NOT_EMPTY_CODE, AppErrorCode.ID_LIST_NOT_EMPTY_MSG);
        }
        int result = appStudyModuleMapper.deleteByIds(ids);
        return result > YesOrNoEnum.NO.getCode();
    }

    @Override
    public Page<AppStudyModuleListDTO> page(PageInfo<AppStudyModuleQueryPageBO> pageInfo) throws BusinessException {
        AppStudyModuleQueryPageBO query = pageInfo.getData();
        LambdaQueryWrapper<AppStudyModule> wrapper = new LambdaQueryWrapper<AppStudyModule>()
                .like(StringUtil.isNotBlank(query.getTitle()), AppStudyModule::getTitle, query.getTitle())
                .eq(Objects.nonNull(query.getModuleType()), AppStudyModule::getModuleType, query.getModuleType())
                .eq(Objects.nonNull(query.getBelongCategoryId()), AppStudyModule::getBelongCategoryId, query.getBelongCategoryId())
                .eq(Objects.nonNull(query.getIsVisible()), AppStudyModule::getIsVisible, query.getIsVisible())
                .eq(Objects.nonNull(query.getPublishStatus()), AppStudyModule::getPublishStatus, query.getPublishStatus())
                .orderByDesc(AppStudyModule::getSortNum, AppStudyModule::getCreateTime);
        String grades = query.getApplicableGrades();
        if (StringUtil.isNotBlank(grades)) {
            if (ALL_GARDE.equalsIgnoreCase(grades)) {
                wrapper.eq(AppStudyModule::getApplicableGrades, ALL_GARDE);
            } else {
                String[] gradeArr = grades.split(",");
                wrapper.and(w -> {
                    for (String grade : gradeArr) {
                        w.or().apply("find_in_set({0}, applicable_grades)", grade);
                    }
                    w.or().eq(AppStudyModule::getApplicableGrades, ALL_GARDE);
                });
            }
        }

        Page<AppStudyModule> page = page(pageInfo.getPage(), wrapper);
        Map<String, List<SysDictItemApiDTO>> dictMap = dictItemRemoteService.findAll();

        Map<String, SysDictItemApiDTO> itemDictMap = dictMap.get(GARAGE_DICT_CODE).stream()
                .sorted(Comparator.comparing(SysDictItemApiDTO::getSort).reversed())
                .collect(Collectors.toMap(
                        SysDictItemApiDTO::getItemValue,
                        Function.identity(),
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ));

        return (Page<AppStudyModuleListDTO>) page.convert(module -> {
            String applicableGrades = module.getApplicableGrades();
            AppStudyModuleListDTO dto = new AppStudyModuleListDTO();
            dto.setBelongCategory(configMapper.selectById(module.getBelongCategoryId()).getName());
            BeanUtil.copyProperties(module, dto);
            StringBuilder itemNames = new StringBuilder();
            if (StringUtil.isNotBlank(applicableGrades) && !ALL_GARDE.equalsIgnoreCase(applicableGrades)) {
                String[] gradeArr = applicableGrades.split(",");
                for (String grade : gradeArr) {
                    if (itemDictMap.containsKey(grade)) {
                        if (!itemNames.isEmpty()) {
                            itemNames.append(",");
                        }
                        itemNames.append(itemDictMap.get(grade).getItemName());
                    }
                }
                dto.setApplicableGrades(itemNames.toString());
            }else if (ALL_GARDE.equalsIgnoreCase(applicableGrades)){
                for (Map.Entry<String,SysDictItemApiDTO> entry : itemDictMap.entrySet()) {
                    if (!itemNames.isEmpty()) {
                        itemNames.append(",");
                    }
                    itemNames.append(entry.getValue().getItemName());
                }
                dto.setApplicableGrades(itemNames.toString());
            }
            return dto;
        });
    }

    @Override
    public AppStudyModuleDetailDTO detail(Integer id) throws BusinessException {
        if (id == null) {
            throw new BusinessException(AppErrorCode.ID_NOT_NULL_CODE, AppErrorCode.ID_NOT_NULL_MSG);
        }
        AppStudyModule entity = appStudyModuleMapper.selectById(id);
        AppStudyModuleDetailDTO result = BeanUtil.copyProperties(entity, AppStudyModuleDetailDTO.class);
        // 获取问题扩展信息
        Map<Integer, AppStudyModuleQuestionExtDetailDTO> questionExtMap = fetchQuestionExtInfo(Collections.singletonList(id));
        if (questionExtMap.containsKey(id)) {
            result.setQuestionExt(questionExtMap.get(id));
        }

        // 获取资源关联信息
        Map<Integer, List<AppStudyModuleResourceRelDetailDTO>> resourceRelMap = fetchResourceRelInfo(Collections.singletonList(id));
        if (resourceRelMap.containsKey(id)) {
            result.setResourceList(resourceRelMap.get(id));
        }

        log.info("查询学习模块详情 返回: {}", result);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchPublish(List<Integer> ids) throws BusinessException {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException(AppErrorCode.ID_LIST_NOT_EMPTY_CODE, AppErrorCode.ID_LIST_NOT_EMPTY_MSG);
        }
        return lambdaUpdate()
                .in(AppStudyModule::getId,ids)
                .set(AppStudyModule::getPublishStatus, YesOrNoEnum.YES.getCode()).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUnpublish(List<Integer> ids) throws BusinessException {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException(AppErrorCode.ID_LIST_NOT_EMPTY_CODE, AppErrorCode.ID_LIST_NOT_EMPTY_MSG);
        }
        return lambdaUpdate()
                .in(AppStudyModule::getId,ids)
                .set(AppStudyModule::getPublishStatus, YesOrNoEnum.NO.getCode()).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchShow(List<Integer> ids) throws BusinessException {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException(AppErrorCode.ID_LIST_NOT_EMPTY_CODE, AppErrorCode.ID_LIST_NOT_EMPTY_MSG);
        }
        return lambdaUpdate()
                .in(AppStudyModule::getId,ids)
                .set(AppStudyModule::getIsVisible, YesOrNoEnum.YES.getCode()).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchHide(List<Integer> ids) throws BusinessException {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException(AppErrorCode.ID_LIST_NOT_EMPTY_CODE, AppErrorCode.ID_LIST_NOT_EMPTY_MSG);
        }
        return lambdaUpdate()
                .in(AppStudyModule::getId,ids)
                .set(AppStudyModule::getIsVisible, YesOrNoEnum.NO.getCode()).update();
    }

    @Override
    public List<StudyCenterCategoryDTO> listHomeWithRelations(StudyCenterModuleListQueryBO queryBO) throws BusinessException {
        if(StringUtils.isEmpty(queryBO.getApplicableGrades())){
            queryBO.setApplicableGrades(DEFAULT_GARDE);
        }
        LambdaQueryWrapper<AppStudyModule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(queryBO.getModuleType()), AppStudyModule::getModuleType, queryBO.getModuleType());
        String applicableGrades = queryBO.getApplicableGrades();
        if (StringUtil.isNotBlank(applicableGrades)) {
            String trimmedGradesInput = applicableGrades.trim();
            if (ALL_GARDE.equalsIgnoreCase(trimmedGradesInput)) {
                wrapper.eq(AppStudyModule::getApplicableGrades, ALL_GARDE);
            } else {
                String[] gradeArr = trimmedGradesInput.split(",");
                wrapper.and(w -> {
                    for (String grade : gradeArr) {
                        String trimmedGrade = grade.trim();
                        if (StringUtils.isNotBlank(trimmedGrade)) {
                            w.or().apply("FIND_IN_SET({0}, applicable_grades)", trimmedGrade);
                        }
                    }
                    w.or().eq(AppStudyModule::getApplicableGrades, ALL_GARDE);
                });
            }
        }else{
            wrapper.and(w -> w.eq(AppStudyModule::getApplicableGrades, ALL_GARDE)
                    .or()
                    .eq(AppStudyModule::getApplicableGrades, DEFAULT_GARDE)
            );
        }


        // 排序和限制
        wrapper.orderByDesc(AppStudyModule::getSortNum, AppStudyModule::getCreateTime);
        if (queryBO.getLimit() != null) {
            wrapper.last("limit " + queryBO.getLimit());
        }

        List<AppStudyModule> modules = this.list(wrapper);
        if (modules.isEmpty()) {
            return Collections.emptyList();
        }

        List<Integer> moduleIds = modules.stream().map(AppStudyModule::getId).collect(Collectors.toList());

        // 获取问题扩展信息
        Map<Integer, AppStudyModuleQuestionExtDetailDTO> questionExtMap = fetchQuestionExtInfo(moduleIds);

        // 获取资源关联信息
        Map<Integer, List<AppStudyModuleResourceRelDetailDTO>> resourceRelMap = fetchResourceRelInfo(moduleIds);

        // 按照belongCategoryId分组
        Map<Integer, List<AppStudyModule>> modulesByCategory = modules.stream()
                .collect(Collectors.groupingBy(AppStudyModule::getBelongCategoryId, Collectors.toList()));

        // 获取所有分类ID
        Set<Integer> categoryIds = modulesByCategory.keySet();

        // 查询分类信息，并按sortNum和id降序排序
        List<BasicConfig> sortedCategories = fetchSortedCategories(categoryIds);

        // 组装结果，按照sortedCategories的顺序
        List<StudyCenterCategoryDTO> result = new ArrayList<>();
        for (BasicConfig category : sortedCategories) {
            Integer categoryId = category.getId();
            // 检查是否有属于此分类的模块
            if (!modulesByCategory.containsKey(categoryId)) {
                continue;
            }

            List<AppStudyModule> categoryModules = modulesByCategory.get(categoryId);
            StudyCenterCategoryDTO categoryDTO = new StudyCenterCategoryDTO();
            categoryDTO.setBeLongCategoryId(categoryId);
            categoryDTO.setBeLongCategoryName(category.getName());
            categoryDTO.setBeLongCategoryIcon(category.getIcon());
            categoryDTO.setBeLongCategoryBackGround(category.getBackground());
            categoryDTO.setBeLongCategoryBackGroundName(category.getBackgroundName());

            // 转换模块列表
            List<StudyCenterModuleListDTO> moduleDTOList = categoryModules.stream().map(module -> {
                StudyCenterModuleListDTO dto = new StudyCenterModuleListDTO();
                BeanUtil.copyProperties(module, dto);

                // 设置问题扩展信息
                if (questionExtMap.containsKey(module.getId())) {
                    dto.setQuestionExt(questionExtMap.get(module.getId()));
                }

                // 设置资源关联信息
                if (resourceRelMap.containsKey(module.getId())) {
                    dto.setResourceList(resourceRelMap.get(module.getId()));
                }

                return dto;
            }).collect(Collectors.toList());

            categoryDTO.setModuleList(moduleDTOList);
            result.add(categoryDTO);
        }

        return result;
    }

    /**
     * 获取并排序分类信息
     *
     * @param categoryIds 分类ID集合
     * @return 按sortNum和id降序排序的分类列表
     */
    private List<BasicConfig> fetchSortedCategories(Set<Integer> categoryIds) {
        if (categoryIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 查询分类信息
        List<BasicConfig> categories = configMapper.selectByIds(categoryIds);

        // 按sortNum降序，如果sortNum相同则按id降序
        categories.sort(Comparator.comparing(BasicConfig::getSortNum, Comparator.nullsLast(Comparator.reverseOrder()))
                .thenComparing(BasicConfig::getId, Comparator.reverseOrder()));

        return categories;
    }



    /**
     * 获取并处理问题扩展信息
     *
     * @param moduleIds 学习模块ID列表
     * @return 模块ID到问题扩展信息的映射
     */
    private Map<Integer, AppStudyModuleQuestionExtDetailDTO> fetchQuestionExtInfo(List<Integer> moduleIds) {
        if (CollectionUtils.isEmpty(moduleIds)) {
            return Collections.emptyMap();
        }

        // 查询问题扩展信息
        List<AppStudyModuleQuestionExt> questionExtList = questionExtMapper.selectList(new LambdaQueryWrapper<AppStudyModuleQuestionExt>()
                .in(AppStudyModuleQuestionExt::getModuleId, moduleIds));
        Map<Integer, AppStudyModuleQuestionExt> questionExtMap = questionExtList.stream().collect(Collectors.toMap(AppStudyModuleQuestionExt::getModuleId, e -> e, (a, b) -> a));

        // 如果没有扩展信息，直接返回空映射
        if (questionExtMap.isEmpty()) {
            return Collections.emptyMap();
        }

        // 查询关联的问题分类数据
        List<Integer> questionExtIds = questionExtList.stream().map(AppStudyModuleQuestionExt::getId).collect(Collectors.toList());
        List<QuestionCategoryBusinessRef> allCategoryRefs = businessRefMapper.selectList(new LambdaQueryWrapper<QuestionCategoryBusinessRef>()
                .in(QuestionCategoryBusinessRef::getBusinessId, questionExtIds)
                .eq(QuestionCategoryBusinessRef::getBusinessType, BusinessTypeEnum.STUDY_CENTER_QUESTION_BUSINESS.getCode())
                .orderByAsc(QuestionCategoryBusinessRef::getId)
                .orderByAsc(QuestionCategoryBusinessRef::getSortNum));

        if (CollectionUtils.isEmpty(allCategoryRefs)) {
            // 如果没有关联的问题分类，仅返回基本的扩展信息
            return questionExtMap.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> BeanUtil.copyProperties(entry.getValue(), AppStudyModuleQuestionExtDetailDTO.class)
                    ));
        }

        // 按模块ID分组
        Map<Integer, List<QuestionCategoryBusinessRef>> categoryRefMap = allCategoryRefs.stream()
                .collect(Collectors.groupingBy(QuestionCategoryBusinessRef::getBusinessId));

        // 收集所有问题分类ID
        List<Integer> allQuestionCategoryIds = allCategoryRefs.stream()
                .map(QuestionCategoryBusinessRef::getQuestionCategoryId)
                .collect(Collectors.toList());

        // 查询问题分类信息
        Map<Integer, String> questionCategoryMap = Collections.emptyMap();
        List<QuestionCategory> questionCategoryList = questionCategoryMapper.selectByIds(allQuestionCategoryIds);
        if (CollectionUtils.isNotEmpty(questionCategoryList)) {
            questionCategoryMap = questionCategoryList.stream()
                    .collect(Collectors.toMap(QuestionCategory::getId, QuestionCategory::getName));
        }

        // 查询问题数量统计
        Map<Integer, Integer> questionCountMap = Collections.emptyMap();
        List<QuestionCountDTO> questionCountDTOList = questionMapper.countByCategoryIds(allQuestionCategoryIds);
        if (CollectionUtils.isNotEmpty(questionCountDTOList)) {
            questionCountMap = questionCountDTOList.stream()
                    .collect(Collectors.toMap(QuestionCountDTO::getQuestionCategoryId, QuestionCountDTO::getNum));
        }

        // 构建最终结果
        Map<Integer, String> finalQuestionCategoryMap = questionCategoryMap;
        Map<Integer, Integer> finalQuestionCountMap = questionCountMap;

        return moduleIds.stream()
                .filter(questionExtMap::containsKey)
                .collect(Collectors.toMap(
                        moduleId -> moduleId,
                        moduleId -> {
                            AppStudyModuleQuestionExt ext = questionExtMap.get(moduleId);
                            AppStudyModuleQuestionExtDetailDTO extDTO = BeanUtil.copyProperties(ext, AppStudyModuleQuestionExtDetailDTO.class);
                            extDTO.setQuestionExtId(ext.getId());
                            List<QuestionCategoryBusinessRef> refs = categoryRefMap.getOrDefault(ext.getId(), Collections.emptyList());
                            if (CollectionUtils.isNotEmpty(refs)) {
                                List<AppStudyModuleQuestionRefDTO> refDTOList = BeanUtil.copyToList(refs, AppStudyModuleQuestionRefDTO.class);

                                for (AppStudyModuleQuestionRefDTO refDTO : refDTOList) {
                                    refDTO.setId(refDTO.getQuestionCategoryId());
                                    refDTO.setName(finalQuestionCategoryMap.get(refDTO.getQuestionCategoryId()));
                                    refDTO.setCurrentQuestionCount(finalQuestionCountMap.getOrDefault(refDTO.getQuestionCategoryId(), 0));
                                }

                                extDTO.setQuestion(refDTOList);
                            }

                            return extDTO;
                        }
                ));
    }

    /**
     * 获取并处理资源关联信息
     *
     * @param moduleIds 学习模块ID列表
     * @return 模块ID到资源关联信息列表的映射
     */
    private Map<Integer, List<AppStudyModuleResourceRelDetailDTO>> fetchResourceRelInfo(List<Integer> moduleIds) {
        if (CollectionUtils.isEmpty(moduleIds)) {
            return Collections.emptyMap();
        }

        // 查询资源关联信息
        List<AppStudyModuleResourceRel> allResourceRels = resourceRelMapper.selectList(new LambdaQueryWrapper<AppStudyModuleResourceRel>()
                .in(AppStudyModuleResourceRel::getModuleId, moduleIds));
        if (CollectionUtils.isEmpty(allResourceRels)) {
            return Collections.emptyMap();
        }

        // 按模块ID分组并转换为DTO
        return allResourceRels.stream()
                .collect(Collectors.groupingBy(
                        AppStudyModuleResourceRel::getModuleId,
                        Collectors.mapping(
                                rel -> {
                                    AppStudyModuleResourceRelDetailDTO dto = new AppStudyModuleResourceRelDetailDTO();
                                    dto.setModuleId(rel.getModuleId());
                                    dto.setResourceId(rel.getResourceId());
                                    dto.setResourceType(rel.getResourceType());
                                    dto.setResourceName(rel.getResourceName());
                                    dto.setResourceIcon(rel.getResourceIcon());
                                    return dto;
                                },
                                Collectors.toList()
                        )
                ));
    }
} 