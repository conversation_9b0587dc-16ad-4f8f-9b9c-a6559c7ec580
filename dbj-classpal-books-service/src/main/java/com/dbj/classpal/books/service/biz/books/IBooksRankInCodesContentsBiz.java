package com.dbj.classpal.books.service.biz.books;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsPageBO;
import com.dbj.classpal.books.client.dto.books.BooksRankInCodesContentsPageDTO;
import com.dbj.classpal.books.client.dto.books.app.BooksRankInCodesContentsCountDTO;
import com.dbj.classpal.books.client.dto.books.app.BooksRankInCodesContentsRankCountDTO;
import com.dbj.classpal.books.client.dto.books.app.BooksRankInCodesContentsTreeAppDTO;
import com.dbj.classpal.books.service.entity.books.BooksRankInCodesContents;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.framework.commons.request.PageInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 图书书内码分类表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
public interface IBooksRankInCodesContentsBiz extends IService<BooksRankInCodesContents> {

    /**
     * 分页查询页面信息
     * @param pageInfo
     * @return
     */
    Page<BooksRankInCodesContentsPageDTO> page(PageInfo<BooksRankInCodesContentsPageBO> pageInfo);

    /**
     * 获取图书书内码数量
     * @param bookIds
     * @return
     */
    List<BooksRankInCodesContentsCountDTO> listCount(List<Integer> bookIds);



    List<BooksRankInCodesContentsTreeAppDTO> getTree(Integer id);

    /**
     * rankIds
     * @param rankIds
     * @return
     */
    List<BooksRankInCodesContentsRankCountDTO> listRankCount(List<Integer> rankIds);

}
