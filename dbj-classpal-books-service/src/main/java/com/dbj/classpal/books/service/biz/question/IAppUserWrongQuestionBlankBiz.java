package com.dbj.classpal.books.service.biz.question;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.common.bo.question.AddBlankWrongQuestionBO;
import com.dbj.classpal.books.service.entity.wrongquestion.AppUserWrongQuestionBlank;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;
import java.util.Set;

/**
 * 用户完形填空错题本业务接口
 */
public interface IAppUserWrongQuestionBlankBiz extends IService<AppUserWrongQuestionBlank> {

    /**
     * 批量添加完形填空错题（使用BO对象）
     *
     * @param boList 错题BO信息列表
     * @throws BusinessException 业务异常
     */
    void batchAddWrongQuestions(List<AddBlankWrongQuestionBO> boList) throws BusinessException;
    
    /**
     * 删除完形填空错题（软删除）
     *
     * @param appUserId 用户ID
     * @param ids       ID
     * @throws BusinessException 业务异常
     */
    void removeWrongQuestion(Integer appUserId, List<Integer> ids) throws BusinessException;
    
    /**
     * 获取用户完形填空错题列表
     *
     * @param appUserId 用户ID
     * @param subjectId 学科ID（可选）
     * @return 错题列表
     * @throws BusinessException 业务异常
     */
    List<AppUserWrongQuestionBlank> listUserWrongQuestions(Integer appUserId, Integer subjectId) throws BusinessException;
    
    /**
     * 根据题目ID获取所有空位的错题
     *
     * @param appUserId 用户ID
     * @param questionId 题目ID
     * @return 错题列表
     * @throws BusinessException 业务异常
     */
    List<AppUserWrongQuestionBlank> listQuestionBlanks(Integer appUserId, Integer questionId) throws BusinessException;


    List<AppUserWrongQuestionBlank> queryBlankWrongQuestionsWithJoin(Integer appUserId,
                                                                       Integer subjectId,
                                                                       Set<Integer> questionIds,
                                                                       Integer type);
} 