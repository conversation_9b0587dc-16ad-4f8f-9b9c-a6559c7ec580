package com.dbj.classpal.books.service.entity.pointreading;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 点读热点媒体快照表
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("point_reading_hotspot_media")
@Schema(description = "点读热点媒体快照表")
public class PointReadingHotspotMedia extends BizEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    @Schema(description = "热点ID")
    private Integer hotspotId;

    @Schema(description = "媒体来源：10-TTS合成 20-素材中心")
    private Integer mediaSource;

    @Schema(description = "媒体文件ID")
    private String mediaId;

    @Schema(description = "媒体文件URL（快照数据）")
    private String mediaUrl;

    @Schema(description = "媒体文件名（快照数据）")
    private String mediaName;

    @Schema(description = "媒体类型：10-音频 20-视频")
    private Integer mediaType;

    @Schema(description = "排序号")
    private Integer sortNum;

    @Schema(description = "快照时间")
    private LocalDateTime snapshotTime;

    @Schema(description = "状态：0-禁用 1-启用")
    private Integer status;


    @Schema(description = "版本号")
    private Integer version;
}
