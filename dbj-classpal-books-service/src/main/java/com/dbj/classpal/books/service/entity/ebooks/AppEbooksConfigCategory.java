package com.dbj.classpal.books.service.entity.ebooks;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_ebooks_config_category")
@Tag(name="电子样书-样书配置-图书分类", description="电子样书-样书配置-图书分类")
public class AppEbooksConfigCategory extends BizEntity {

    @TableField("parent_id")
    @Schema(name = "父级id")
    private Integer parentId;

    @TableField("name")
    @Schema(name = "名称")
    private String name;

    @TableField("sort")
    @Schema(name = "权重")
    private Integer sort;

    @TableField("status")
    @Schema(name = "是否启用 1-是 0-否")
    private Integer status;


}
