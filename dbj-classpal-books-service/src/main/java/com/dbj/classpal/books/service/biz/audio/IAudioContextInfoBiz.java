package com.dbj.classpal.books.service.biz.audio;

import com.dbj.classpal.books.common.dto.audio.AudioHintRefNumDTO;
import com.dbj.classpal.books.service.entity.audio.AudioContextInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 音频文本详情 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
public interface IAudioContextInfoBiz extends IService<AudioContextInfo> {

    List<AudioHintRefNumDTO> statRefNum(List<Integer> audioHintMusicIds);
}
