package com.dbj.classpal.books.service.entity.product;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * <p>
 * 商品销售配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("product_sales_config")
@Schema(name="ProductSalesConfig对象", description="商品销售配置表")
public class ProductSalesConfig extends BizEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "商品ID")
    private Integer productId;

    @Schema(description = "商品类型：BOOK-图书 VIDEO_ALBUM-视频专辑 AUDIO_ALBUM-音频专辑 POINT_READING-点读书")
    private String productType;

    @Schema(description = "试学体验个数")
    private Integer trialCount;

    @Schema(description = "售卖方式开关")
    private Boolean salesModeStatus;

    @Schema(description = "销售模式：0-免费 1-收费")
    private Boolean salesMode;

    @Schema(description = "销售价格(元)")
    private BigDecimal salesPrice;

    @Schema(description = "原价(元)")
    private BigDecimal originalPrice;

    @Schema(description = "上架状态：0-下架 1-上架")
    private Boolean listingStatus;

    @Schema(description = "是否隐藏：0-显示 1-隐藏")
    private Boolean isHidden;

    @Schema(description = "状态：0-停用 1-正常")
    private Boolean status;

}
