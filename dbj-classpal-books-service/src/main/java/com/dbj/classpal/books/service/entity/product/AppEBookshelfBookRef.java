package com.dbj.classpal.books.service.entity.product;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 书架-单书关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_ebookshelf_book_ref")
@Tag(name="AppEBookshelfBookRef", description="书架-单书关联表")
public class AppEBookshelfBookRef extends BizEntity implements Serializable {

    @Schema(description = "书架ID")
    private Integer shelfId;

    @Schema(description = "单书ID")
    private Integer bookId;

    @Schema(description = "排序序号")
    private Integer sortNum;
} 