package com.dbj.classpal.books.service.biz.studycenter;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.common.bo.studycenter.StudyCenterModuleListQueryBO;
import com.dbj.classpal.books.common.bo.studycenter.StudyCenterModuleStudyBO;
import com.dbj.classpal.books.common.dto.studycenter.AppStudyModuleProgressListDTO;
import com.dbj.classpal.books.service.entity.studycenter.AppStudyModuleProgress;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;

public interface IAppStudyModuleProgressBiz extends IService<AppStudyModuleProgress> {
    Boolean delete(List<Integer> ids) throws BusinessException;
    Boolean recordStudy(StudyCenterModuleStudyBO bo) throws BusinessException;
    List<AppStudyModuleProgressListDTO> listRecentStudy(StudyCenterModuleListQueryBO queryBO) throws BusinessException;
    Boolean deleteStudyRecord(StudyCenterModuleStudyBO bo) throws BusinessException;
}