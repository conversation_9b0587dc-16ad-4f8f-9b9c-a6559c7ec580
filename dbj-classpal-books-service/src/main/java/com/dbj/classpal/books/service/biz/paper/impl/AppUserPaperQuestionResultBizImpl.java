package com.dbj.classpal.books.service.biz.paper.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.bo.paper.QueryErrorResultsBO;
import com.dbj.classpal.books.common.bo.paper.QueryQuestionResultBO;
import com.dbj.classpal.books.common.bo.paper.SaveQuestionResultBO;
import com.dbj.classpal.books.common.dto.paper.QuestionResultDTO;
import com.dbj.classpal.books.service.biz.paper.IAppUserPaperQuestionResultBiz;
import com.dbj.classpal.books.service.entity.paper.AppUserPaperQuestionResult;
import com.dbj.classpal.books.service.mapper.books.BooksRankInCodesContentsQuestionMapper;
import com.dbj.classpal.books.service.mapper.paper.AppUserPaperInfoMapper;
import com.dbj.classpal.books.service.mapper.paper.AppUserPaperQuestionResultMapper;
import com.dbj.classpal.books.service.mapper.question.QuestionBlankAreaMapper;
import com.dbj.classpal.books.service.mapper.question.QuestionMapper;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户试卷题目结果业务实现类
 */
@Slf4j
@Service
public class AppUserPaperQuestionResultBizImpl extends ServiceImpl<AppUserPaperQuestionResultMapper, AppUserPaperQuestionResult> implements IAppUserPaperQuestionResultBiz {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveQuestionResult(SaveQuestionResultBO saveBO) throws BusinessException {
        AppUserPaperQuestionResult result = new AppUserPaperQuestionResult();
        BeanUtil.copyProperties(saveBO, result);
        return save(result);
    }

    @Override
    public List<QuestionResultDTO> getQuestionResults(QueryQuestionResultBO queryBO) throws BusinessException {
        List<AppUserPaperQuestionResult> results = list(new LambdaQueryWrapper<AppUserPaperQuestionResult>()
                .eq(AppUserPaperQuestionResult::getPaperId, queryBO.getPaperId())
                .eq(AppUserPaperQuestionResult::getAppUserId, queryBO.getAppUserId()));

        return results.stream()
                .map(result -> {
                    QuestionResultDTO dto = new QuestionResultDTO();
                    BeanUtil.copyProperties(result, dto);
                    dto.setUserAnswerIds(result.getAnswerIds());
                    return dto;
                })
                .collect(Collectors.toList());
    }
    
    @Override
    public List<AppUserPaperQuestionResult> batchGetErrorResults(QueryErrorResultsBO queryBO) {
        if (queryBO == null || CollectionUtils.isEmpty(queryBO.getQuestionIds())) {
            return Collections.emptyList();
        }
        
        LambdaQueryWrapper<AppUserPaperQuestionResult> queryWrapper = new LambdaQueryWrapper<AppUserPaperQuestionResult>()
                .eq(AppUserPaperQuestionResult::getAppUserId, queryBO.getAppUserId())
                .in(AppUserPaperQuestionResult::getQuestionId, queryBO.getQuestionIds())
                .eq(AppUserPaperQuestionResult::getResult, YesOrNoEnum.NO.getCode());
        
        if (queryBO.getSourceType() != null) {
            queryWrapper.eq(AppUserPaperQuestionResult::getBusinessType, queryBO.getSourceType());
        }
        
        if (queryBO.getSourceId() != null) {
            queryWrapper.eq(AppUserPaperQuestionResult::getBusinessId, queryBO.getSourceId());
        }
        
        return this.list(queryWrapper);
    }
} 