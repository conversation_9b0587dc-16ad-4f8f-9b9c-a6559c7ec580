package com.dbj.classpal.books.service.api.client.ebooks;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.ebooks.AppEBookResourceApi;
import com.dbj.classpal.books.client.bo.ebooks.AppEBookResourceBatchQueryApiBO;
import com.dbj.classpal.books.client.dto.ebooks.AppEBookResourceApiDTO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookResourceBatchQueryBO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookResourceDTO;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookBiz;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookResourceBiz;
import com.dbj.classpal.framework.commons.converter.PageInfoConverter;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 单书资源API实现类
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@RestController
public class AppEBookResourceApiImpl implements AppEBookResourceApi {

    @Resource
    private IAppEBookResourceBiz eBookResourceBiz;

    @Resource
    private IAppEBookBiz eBookBiz;

    @Override
    public RestResponse<Map<Integer, List<AppEBookResourceApiDTO>>> batchQueryByBooks(AppEBookResourceBatchQueryApiBO queryBO) throws BusinessException {
        log.info("批量查询书籍资源 入参：{}", JSON.toJSONString(queryBO));

        try {
            // 转换参数
            AppEBookResourceBatchQueryBO serviceQueryBO = BeanUtil.copyProperties(queryBO, AppEBookResourceBatchQueryBO.class);

            // 调用业务层
            Map<Integer, List<AppEBookResourceDTO>> serviceResult = eBookResourceBiz.batchQueryByBooks(serviceQueryBO);

            // 转换结果
            Map<Integer, List<AppEBookResourceApiDTO>> apiResult = convertResourceMap(serviceResult);

            log.info("批量查询书籍资源 返回记录数：{}", apiResult.size());
            return RestResponse.success(apiResult);
        } catch (Exception e) {
            log.error("批量查询书籍资源失败", e);
            throw new BusinessException("批量查询书籍资源失败");
        }
    }

    @Override
    public RestResponse<Map<Integer, List<AppEBookResourceApiDTO>>> batchQueryByShelves(AppEBookResourceBatchQueryApiBO queryBO) throws BusinessException {
        log.info("批量查询书架资源 入参：{}", JSON.toJSONString(queryBO));

        try {
            // 转换参数
            AppEBookResourceBatchQueryBO serviceQueryBO = BeanUtil.copyProperties(queryBO, AppEBookResourceBatchQueryBO.class);

            // 调用业务层
            Map<Integer, List<AppEBookResourceDTO>> serviceResult = eBookResourceBiz.batchQueryByShelves(serviceQueryBO);

            // 转换结果
            Map<Integer, List<AppEBookResourceApiDTO>> apiResult = convertResourceMap(serviceResult);

            log.info("批量查询书架资源 返回记录数：{}", apiResult.size());
            return RestResponse.success(apiResult);
        } catch (Exception e) {
            log.error("批量查询书架资源失败", e);
            throw new BusinessException("批量查询书架资源失败");
        }
    }

    @Override
    public RestResponse<Page<AppEBookResourceApiDTO>> pageStoreResources(PageInfo<AppEBookResourceBatchQueryApiBO> pageRequest) throws BusinessException {
        log.info("分页查询书城资源 入参：{}", JSON.toJSONString(pageRequest));

        try {
            // 转换参数
            PageInfo<AppEBookResourceBatchQueryBO> servicePageRequest = PageInfoConverter.convertPageInfo(pageRequest, AppEBookResourceBatchQueryBO.class);

            // 调用业务层
            Page<AppEBookResourceDTO> servicePage = eBookResourceBiz.pageStoreResources(servicePageRequest);

            // 转换结果
            Page<AppEBookResourceApiDTO> apiPage = (Page<AppEBookResourceApiDTO>) servicePage.convert(dto -> {
                AppEBookResourceApiDTO apiDTO = new AppEBookResourceApiDTO();
                BeanUtil.copyProperties(dto, apiDTO);
                return apiDTO;
            });

            log.info("分页查询书城资源 返回记录数：{}", apiPage.getRecords().size());
            return RestResponse.success(apiPage);
        } catch (Exception e) {
            log.error("分页查询书城资源失败", e);
            throw new BusinessException("分页查询书城资源失败");
        }
    }

    @Override
    public RestResponse<Map<Integer, List<AppEBookResourceApiDTO>>> batchQueryResourcesByBookIds(List<Integer> bookIds) throws BusinessException {
        log.info("批量查询指定书籍资源 入参：{}", JSON.toJSONString(bookIds));

        try {
            if (CollectionUtils.isEmpty(bookIds)) {
                return RestResponse.success(new HashMap<>());
            }

            // 调用业务层
            Map<Integer, List<AppEBookResourceDTO>> serviceResult = eBookBiz.batchQueryResources(bookIds, null);

            // 转换结果
            Map<Integer, List<AppEBookResourceApiDTO>> apiResult = convertResourceMap(serviceResult);

            log.info("批量查询指定书籍资源 返回记录数：{}", apiResult.size());
            return RestResponse.success(apiResult);
        } catch (Exception e) {
            log.error("批量查询指定书籍资源失败", e);
            throw new BusinessException("批量查询指定书籍资源失败");
        }
    }

    @Override
    public RestResponse<Map<Integer, List<AppEBookResourceApiDTO>>> batchQueryTypedResourcesByBookIds(AppEBookResourceBatchQueryApiBO queryBO) throws BusinessException {
        log.info("批量查询指定书籍特定类型资源 入参：{}", JSON.toJSONString(queryBO));

        try {
            if (CollectionUtils.isEmpty(queryBO.getBookIds())) {
                return RestResponse.success(new HashMap<>());
            }

            // 调用业务层
            Map<Integer, List<AppEBookResourceDTO>> serviceResult = eBookBiz.batchQueryResources(queryBO.getBookIds(), queryBO.getResourceType());

            // 转换结果
            Map<Integer, List<AppEBookResourceApiDTO>> apiResult = convertResourceMap(serviceResult);

            log.info("批量查询指定书籍特定类型资源 返回记录数：{}", apiResult.size());
            return RestResponse.success(apiResult);
        } catch (Exception e) {
            log.error("批量查询指定书籍特定类型资源失败", e);
            throw new BusinessException("批量查询指定书籍特定类型资源失败");
        }
    }

    /**
     * 转换资源映射
     */
    private Map<Integer, List<AppEBookResourceApiDTO>> convertResourceMap(Map<Integer, List<AppEBookResourceDTO>> serviceResult) {
        if (serviceResult == null || serviceResult.isEmpty()) {
            return new HashMap<>();
        }

        return serviceResult.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream()
                                .map(dto -> {
                                    AppEBookResourceApiDTO apiDTO = new AppEBookResourceApiDTO();
                                    BeanUtil.copyProperties(dto, apiDTO);
                                    return apiDTO;
                                })
                                .collect(Collectors.toList())
                ));
    }
}
