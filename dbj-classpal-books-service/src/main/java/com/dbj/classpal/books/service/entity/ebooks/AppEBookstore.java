package com.dbj.classpal.books.service.entity.ebooks;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 书城表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_ebookstore")
@Tag(name="AppEBookstore", description="书城表")
public class AppEBookstore extends BizEntity implements Serializable {

    @Schema(description = "书城名称")
    private String storeTitle;

    @Schema(description = "封面URL")
    private String coverUrl;

    @Schema(description = "封面名称")
    private String coverUrlName;
    
    @Schema(description = "简介")
    private String blurb;

    @Schema(description = "启用状态：0-禁用，1-启用")
    private Integer launchStatus;

    @Schema(description = "允许下载：0-否，1-是")
    private Integer allowDownload;

    @Schema(description = "排序序号")
    private Integer sortNum;

    @Schema(description = "分享链接")
    private String shareUrl;

    @Schema(description = "分享二维码")
    private String shareQrCode;

    @Schema(description = "分享状态：0-未分享，1-已分享")
    private Integer shareStatus;

    @Schema(description = "分享时间")
    private LocalDateTime shareTime;

}