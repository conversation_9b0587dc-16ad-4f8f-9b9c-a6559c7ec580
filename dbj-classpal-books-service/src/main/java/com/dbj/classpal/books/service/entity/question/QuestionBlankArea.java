package com.dbj.classpal.books.service.entity.question;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("question_blank_area")
@Tag(name="完形填空区域", description="完形填空区域")
@AllArgsConstructor
@NoArgsConstructor
public class QuestionBlankArea extends BizEntity implements Serializable {


    /**
     * 题目id
     */
    @Schema(description = "题目id")
    private Integer questionId;

    /**
     * 空位序号
     */
    @Schema(description = "空位序号")
    private Integer blankIndex;
    /**
     * 正确答案ID（多个英文逗号隔开）
     */
    @Schema(description = "正确答案ID（多个英文逗号隔开）")
    private String answerIds;

    @TableField(exist = false)
    private List<QuestionAnswer> answers;
    /**
     * 是否启用 1-是 0-否
     */
    @Schema(description = "是否启用 1-是 0-否")
    private Integer status;
} 