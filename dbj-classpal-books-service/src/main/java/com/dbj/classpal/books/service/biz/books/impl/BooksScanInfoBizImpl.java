package com.dbj.classpal.books.service.biz.books.impl;

import com.dbj.classpal.books.client.dto.books.BooksScanCountDTO;
import com.dbj.classpal.books.service.entity.books.BooksScanInfo;
import com.dbj.classpal.books.service.mapper.books.BooksScanInfoMapper;
import com.dbj.classpal.books.service.biz.books.IBooksScanInfoBiz;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 图书配置-图书内容分类 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Service
public class BooksScanInfoBizImpl extends ServiceImpl<BooksScanInfoMapper, BooksScanInfo> implements IBooksScanInfoBiz {

    @Override
    public List<BooksScanCountDTO> bookScanCount(List<Integer> bookIds) {
        return baseMapper.bookScanCount(bookIds);
    }
}
