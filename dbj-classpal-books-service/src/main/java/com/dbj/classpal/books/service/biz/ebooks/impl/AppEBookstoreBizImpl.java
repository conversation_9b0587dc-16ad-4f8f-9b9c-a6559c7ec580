package com.dbj.classpal.books.service.biz.ebooks.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookstoreQueryBO;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookDTO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookshelfDTO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookstoreDTO;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookBiz;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookshelfBiz;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookshelfBookRefBiz;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookstoreBiz;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookstoreShelfRefBiz;
import com.dbj.classpal.books.service.entity.ebooks.AppEBookshelf;
import com.dbj.classpal.books.service.entity.ebooks.AppEBookstore;
import com.dbj.classpal.books.service.mapper.ebooks.AppEBookstoreMapper;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 书城 业务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
@Slf4j
@Service
public class AppEBookstoreBizImpl extends ServiceImpl<AppEBookstoreMapper, AppEBookstore> implements IAppEBookstoreBiz {

    @Resource
    private IAppEBookstoreShelfRefBiz eBookstoreShelfRefService;
    
    @Resource
    private IAppEBookshelfBiz eBookshelfBiz;

    @Resource
    private IAppEBookshelfBookRefBiz eBookshelfBookRefService;

    @Resource
    private IAppEBookBiz eBookBiz;


    @Override
    public Page<AppEBookstoreDTO> pageBookstore(Page<AppEBookstore> page, AppEBookstoreQueryBO queryBO) throws BusinessException {
        // 构建查询条件
        LambdaQueryWrapper<AppEBookstore> queryWrapper = new LambdaQueryWrapper<>();
        
        if (queryBO != null) {
            // 书城名称模糊查询
            if (StringUtils.isNotBlank(queryBO.getStoreTitle())) {
                queryWrapper.like(AppEBookstore::getStoreTitle, queryBO.getStoreTitle());
            }
            
            // 是否隐藏查询
            if (queryBO.getAllowDownload() != null) {
                queryWrapper.eq(AppEBookstore::getAllowDownload, queryBO.getAllowDownload());
            }
            
            // 上下架状态查询
            if (queryBO.getLaunchStatus() != null) {
                queryWrapper.eq(AppEBookstore::getLaunchStatus, queryBO.getLaunchStatus());
            }

        }

        // 默认按排序序号和ID降序排序
        queryWrapper.orderByDesc(AppEBookstore::getSortNum)
                .orderByDesc(AppEBookstore::getSortNum,AppEBookstore::getCreateTime);

        // 执行分页查询
        Page<AppEBookstore> resultPage = this.page(page, queryWrapper);

        return (Page<AppEBookstoreDTO>) resultPage.convert(store -> {
            AppEBookstoreDTO dto = new AppEBookstoreDTO();
            BeanUtils.copyProperties(store, dto);
            // 统计书架数量
            int shelfCount;
            try {
                shelfCount = eBookstoreShelfRefService.countShelves(store.getId());
            } catch (BusinessException e) {
                throw new RuntimeException(e);
            }
            dto.setShelfCount(shelfCount);

            // 计算总书籍数量
            int totalBookCount = calculateTotalBookCount(store.getId());
            dto.setTotalBookCount(totalBookCount);
            return dto;
        });
    }

    @Override
    public AppEBookstoreDTO getBookstoreDetail(Integer id) throws BusinessException {
        if (id == null) {
            throw new BusinessException(AppErrorCode.BOOKSTORE_ID_NOT_NULL_CODE, AppErrorCode.BOOKSTORE_ID_NOT_NULL_MSG);
        }

        // 查询书城信息
        AppEBookstore store = this.getById(id);
        if (store == null) {
            throw new BusinessException(AppErrorCode.BOOKSTORE_NOT_EXIST_CODE, AppErrorCode.BOOKSTORE_NOT_EXIST_MSG);
        }

        // 转换为DTO
        AppEBookstoreDTO dto = new AppEBookstoreDTO();
        BeanUtils.copyProperties(store, dto);

        // 查询书城下的书架ID列表
        List<Integer> shelfIds = eBookstoreShelfRefService.listShelfIds(List.of(id));

        List<AppEBookshelfDTO> shelfDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(shelfIds)) {
            shelfDTOList = batchGetBookshelfDetails(shelfIds);
        }

        dto.setShelves(shelfDTOList);
        dto.setShelfCount(shelfDTOList.size());

        // 计算总书籍数量（优化：从已查询的书架数据中统计）
        int totalBookCount = shelfDTOList.stream()
                .mapToInt(shelf -> shelf.getBookCount() != null ? shelf.getBookCount() : 0)
                .sum();
        dto.setTotalBookCount(totalBookCount);

        return dto;
    }

    /**
     * 批量查询书架详情（按输入顺序返回）
     */
    private List<AppEBookshelfDTO> batchGetBookshelfDetails(List<Integer> shelfIds) {
        if (CollectionUtils.isEmpty(shelfIds)) {
            return new ArrayList<>();
        }

        try {
            // 批量查询书架基本信息并转换为Map
            List<AppEBookshelf> shelves = eBookshelfBiz.listByIds(shelfIds);
            if (CollectionUtils.isEmpty(shelves)) {
                return new ArrayList<>();
            }

            Map<Integer, AppEBookshelf> shelfMap = shelves.stream()
                    .collect(Collectors.toMap(AppEBookshelf::getId, shelf -> shelf));

            // 批量查询书架-书籍关联关系
            Map<Integer, List<Integer>> shelfBookIdsMap = batchQueryShelfBookIds(shelfIds);

            // 收集所有书籍ID
            Set<Integer> allBookIds = shelfBookIdsMap.values().stream()
                    .flatMap(List::stream)
                    .collect(Collectors.toSet());

            // 批量查询书籍详情（不包含资源信息）
            Map<Integer, AppEBookDTO> bookMap = new HashMap<>();
            if (!allBookIds.isEmpty()) {
                List<AppEBookDTO> books = eBookBiz.getDetailList(new ArrayList<>(allBookIds), false);
                bookMap = books.stream()
                        .collect(Collectors.toMap(AppEBookDTO::getId, book -> book));
            }

            // 按照输入的 shelfIds 顺序组装书架DTO
            List<AppEBookshelfDTO> result = new ArrayList<>();
            for (Integer shelfId : shelfIds) {
                AppEBookshelf shelf = shelfMap.get(shelfId);
                if (shelf != null) {
                    AppEBookshelfDTO dto = new AppEBookshelfDTO();
                    BeanUtils.copyProperties(shelf, dto);

                    // 设置书架下的书籍列表（保持书籍在书架中的顺序）
                    List<Integer> bookIds = shelfBookIdsMap.get(shelf.getId());
                    if (CollectionUtils.isNotEmpty(bookIds)) {
                        List<AppEBookDTO> shelfBooks = bookIds.stream()
                                .map(bookMap::get)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                        dto.setBooks(shelfBooks);
                        dto.setBookCount(shelfBooks.size());
                    } else {
                        dto.setBooks(new ArrayList<>());
                        dto.setBookCount(0);
                    }

                    result.add(dto);
                }
            }

            return result;
        } catch (Exception e) {
            log.error("批量查询书架详情失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 批量查询书架-书籍关联关系
     */
    private Map<Integer, List<Integer>> batchQueryShelfBookIds(List<Integer> shelfIds) {
        try {
            return eBookshelfBookRefService.batchListBookIds(shelfIds);
        } catch (Exception e) {
            log.error("批量查询书架-书籍关联关系失败", e);
            return new HashMap<>();
        }
    }

    @Override
    public boolean setBookstoreCover(Integer id, Integer shelfId, String picUrl) throws BusinessException {
        if (id == null) {
            throw new BusinessException(AppErrorCode.BOOKSTORE_ID_NOT_NULL_CODE, AppErrorCode.BOOKSTORE_ID_NOT_NULL_MSG);
        }
        
        // 查询书城信息
        AppEBookstore store = this.getById(id);
        if (store == null) {
            throw new BusinessException(AppErrorCode.BOOKSTORE_NOT_EXIST_CODE, AppErrorCode.BOOKSTORE_NOT_EXIST_MSG);
        }
        
        // 如果直接提供了封面URL，则直接使用
        if (StringUtils.isNotBlank(picUrl)) {
            store.setCoverUrl(picUrl);
        } 
        // 否则根据书架ID获取封面
        else if (shelfId != null) {
            AppEBookshelf shelf = eBookshelfBiz.getById(shelfId);
            if (shelf == null) {
                throw new BusinessException(AppErrorCode.BOOKSHELF_NOT_EXIST_CODE, AppErrorCode.BOOKSHELF_NOT_EXIST_MSG);
            }
            
            if (StringUtils.isNotBlank(shelf.getCoverUrl())) {
                store.setCoverUrl(shelf.getCoverUrl());
            } else {
                throw new BusinessException(AppErrorCode.BOOKSHELF_COVER_URL_EMPTY_CODE, AppErrorCode.BOOKSHELF_COVER_URL_EMPTY_MSG);
            }
        } else {
            throw new BusinessException(AppErrorCode.COVER_URL_BOOKID_EMPTY_CODE, AppErrorCode.COVER_URL_BOOKID_EMPTY_MSG);
        }
        
        return this.updateById(store);
    }

    @Override
    public boolean addShelvesToStore(Integer storeId, List<Integer> shelfIds) throws BusinessException {
        if (storeId == null || CollectionUtils.isEmpty(shelfIds)) {
            throw new BusinessException(AppErrorCode.PARAMS_NOT_NULL_CODE, AppErrorCode.PARAMS_NOT_NULL_MSG);
        }
        
        // 检查书城是否存在
        if (this.existsStore(storeId)) {
            throw new BusinessException(AppErrorCode.BOOKSTORE_NOT_EXIST_CODE, AppErrorCode.BOOKSTORE_NOT_EXIST_MSG);
        }

        List<AppEBookshelf> appEBookshelves = eBookshelfBiz.getBaseMapper().selectByIds(shelfIds);
        if (CollectionUtils.isEmpty(appEBookshelves)) {
            throw new BusinessException(AppErrorCode.BOOKSHELF_NOT_EXIST_CODE, AppErrorCode.BOOKSHELF_NOT_EXIST_MSG);
        }
        List<Integer> existIds =  eBookstoreShelfRefService.listShelfIds(shelfIds);
        List<Integer> alreadyExistIds = shelfIds.stream()
                .filter(existIds::contains)
                .toList();

        if (!alreadyExistIds.isEmpty()) {
            throw new BusinessException(AppErrorCode.BOOKSHELF_ALREADY_EXIST_CODE, AppErrorCode.BOOKSHELF_ALREADY_EXIST_MSG);
        }
        // 添加关联关系
        return eBookstoreShelfRefService.saveBatch(storeId, shelfIds);
    }

    @Override
    public boolean removeShelvesFromStore(Integer storeId, List<Integer> shelfIds) throws BusinessException {
        if (storeId == null) {
            throw new BusinessException(AppErrorCode.BOOKSTORE_ID_NOT_NULL_CODE, AppErrorCode.BOOKSTORE_ID_NOT_NULL_MSG);
        }
        
        // 检查书城是否存在
        if (this.existsStore(storeId)) {
            throw new BusinessException(AppErrorCode.BOOKSTORE_NOT_EXIST_CODE, AppErrorCode.BOOKSTORE_NOT_EXIST_MSG);
        }
        
        // 删除关联关系
        return eBookstoreShelfRefService.removeBatch(List.of(storeId), shelfIds);
    }

    @Override
    public boolean sortShelvesInStore(Integer storeId, Map<Integer, Integer> shelfSortMap) throws BusinessException {
        if (storeId == null || shelfSortMap == null || shelfSortMap.isEmpty()) {
            throw new BusinessException(AppErrorCode.PARAMS_NOT_NULL_CODE, AppErrorCode.PARAMS_NOT_NULL_MSG);
        }
        
        // 检查书城是否存在
        if (this.existsStore(storeId)) {
            throw new BusinessException(AppErrorCode.BOOKSTORE_NOT_EXIST_CODE, AppErrorCode.BOOKSTORE_NOT_EXIST_MSG);
        }
        
        // 更新排序
        return eBookstoreShelfRefService.updateSort(storeId, shelfSortMap);
    }

    
    @Override
    public AppEBookstoreDTO getBookstoreStatistics(Integer id) throws BusinessException {
        if (id == null) {
            throw new BusinessException(AppErrorCode.BOOKSTORE_ID_NOT_NULL_CODE, AppErrorCode.BOOKSTORE_ID_NOT_NULL_MSG);
        }
        
        // 查询书城信息
        AppEBookstore store = this.getById(id);
        if (store == null) {
            throw new BusinessException(AppErrorCode.BOOKSTORE_NOT_EXIST_CODE, AppErrorCode.BOOKSTORE_NOT_EXIST_MSG);
        }
        
        AppEBookstoreDTO dto = new AppEBookstoreDTO();
        BeanUtils.copyProperties(store, dto);
        
        // 查询书城下的书架ID列表
        List<Integer> shelfIds = eBookstoreShelfRefService.listShelfIds(List.of(id));
        int shelfCount = shelfIds.size();
        dto.setShelfCount(shelfCount);
        
        // 计算各书架的书籍数量及总数
        int totalBookCount = 0;
        List<AppEBookshelfDTO> shelfStatsList = new ArrayList<>();
        
        for (Integer shelfId : shelfIds) {
            try {
                AppEBookshelfDTO shelfDTO = new AppEBookshelfDTO();
                AppEBookshelf shelf = eBookshelfBiz.getById(shelfId);
                if (shelf != null) {
                    BeanUtils.copyProperties(shelf, shelfDTO);
                    
                    // 统计书架下的书籍数量
                    int bookCount = eBookshelfBookRefService.countBooks(shelfId);
                    shelfDTO.setBookCount(bookCount);
                    totalBookCount += bookCount;
                    
                    shelfStatsList.add(shelfDTO);
                }
            } catch (Exception e) {
                log.error("获取书架统计信息失败，shelfId={}", shelfId, e);
            }
        }
        
        dto.setTotalBookCount(totalBookCount);
        dto.setShelves(shelfStatsList);
        
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean allowDownloadBatch(List<Integer> ids) throws BusinessException {
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }

        // 批量修改允许下载状态
        List<AppEBookstore> updateList = ids.stream().map(id -> {
            AppEBookstore bookstore = new AppEBookstore();
            bookstore.setId(id);
            bookstore.setAllowDownload(YesOrNoEnum.YES.getCode());
            return bookstore;
        }).collect(Collectors.toList());

        return this.updateBatchById(updateList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableDownloadBatch(List<Integer> ids) throws BusinessException {
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }
        List<AppEBookstore> updateList = ids.stream().map(id -> {
            AppEBookstore bookstore = new AppEBookstore();
            bookstore.setId(id);
            bookstore.setAllowDownload(YesOrNoEnum.NO.getCode());
            return bookstore;
        }).collect(Collectors.toList());

        return this.updateBatchById(updateList);
    }

    
    /**
     * 检查书城是否存在
     * 
     * @param id 书城ID
     * @return 是否存在
     */
    private boolean existsStore(Integer id) {
        if (id == null) {
            return true;
        }
        return this.getById(id) == null;
    }
    
    /**
     * 计算书城下所有书架的总书籍数量
     * 
     * @param storeId 书城ID
     * @return 总书籍数量
     */
    private int calculateTotalBookCount(Integer storeId) {
        int totalBookCount = 0;
        try {
            // 获取书城下的所有书架ID
            List<Integer> shelfIds = eBookshelfBookRefService.listShelfIds(storeId);
            
            // 累加每个书架的书籍数量
            for (Integer shelfId : shelfIds) {
                totalBookCount += eBookshelfBookRefService.countBooks(shelfId);
            }
        } catch (Exception e) {
            log.error("计算书城总书籍数量失败，storeId={}", storeId, e);
        }
        return totalBookCount;
    }
}