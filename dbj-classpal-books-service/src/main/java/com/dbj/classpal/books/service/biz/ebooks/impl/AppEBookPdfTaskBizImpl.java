package com.dbj.classpal.books.service.biz.ebooks.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.enums.PdfTaskStatusEnum;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookPdfTaskBiz;
import com.dbj.classpal.books.service.entity.ebooks.AppEBookPdfTask;
import com.dbj.classpal.books.service.mapper.ebooks.AppEBookPdfTaskMapper;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * PDF处理任务业务实现类
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Slf4j
@Service
public class AppEBookPdfTaskBizImpl extends ServiceImpl<AppEBookPdfTaskMapper, AppEBookPdfTask> implements IAppEBookPdfTaskBiz {

    @Override
    public String createTask(Integer businessId, String businessKey, Integer waterMarkBusinessType, String pdfUrl, String pdfName) throws BusinessException {
        if (StringUtils.isBlank(pdfUrl)) {
            throw new BusinessException("PDF文件URL不能为空");
        }

        String taskId = UUID.randomUUID().toString().replace("-","");

        AppEBookPdfTask task = new AppEBookPdfTask();
        task.setTaskId(taskId);
        task.setBusinessId(businessId);
        task.setBusinessKey(businessKey);
        task.setWaterMarkBusinessType(waterMarkBusinessType);
        task.setPdfUrl(pdfUrl);
        task.setPdfName(pdfName);
        task.setStatus(PdfTaskStatusEnum.PROCESSING.getCode());

        boolean saved = this.save(task);
        if (!saved) {
            throw new BusinessException("创建PDF处理任务失败");
        }

        log.info("创建PDF处理任务成功，taskId: {}, businessId: {}", taskId, businessId);
        return taskId;
    }

    @Override
    public AppEBookPdfTask getTaskByTaskId(String taskId) throws BusinessException {
        if (StringUtils.isBlank(taskId)) {
            throw new BusinessException("任务ID不能为空");
        }

        LambdaQueryWrapper<AppEBookPdfTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppEBookPdfTask::getTaskId, taskId);

        AppEBookPdfTask task = this.getOne(queryWrapper);
        if (task == null) {
            throw new BusinessException("PDF处理任务不存在");
        }

        return task;
    }

    @Override
    public List<AppEBookPdfTask> queryTaskByBusinessKey(String BusinessKey) throws BusinessException {
        return this.getBaseMapper().selectList(new LambdaQueryWrapper<AppEBookPdfTask>()
                .eq(AppEBookPdfTask::getBusinessKey, BusinessKey)
                .orderByAsc(AppEBookPdfTask::getStatus));
    }

    @Override
    public void updateTaskSuccess(String taskId, String coverUrl, String resultJson) throws BusinessException {
        AppEBookPdfTask task = getTaskByTaskId(taskId);
        
        task.setStatus(PdfTaskStatusEnum.SUCCESS.getCode());
        task.setCoverUrl(coverUrl);
        task.setResultJson(resultJson);

        boolean updated = this.updateById(task);
        if (!updated) {
            throw new BusinessException("更新PDF处理任务状态失败");
        }

        log.info("更新PDF处理任务为成功状态，taskId: {}, coverUrl: {}", taskId, coverUrl);
    }

    @Override
    public void updateTaskFailed(String taskId, String errorMsg) throws BusinessException {
        AppEBookPdfTask task = getTaskByTaskId(taskId);
        
        task.setStatus(PdfTaskStatusEnum.FAILED.getCode());
        task.setErrorMsg(errorMsg);

        boolean updated = this.updateById(task);
        if (!updated) {
            throw new BusinessException("更新PDF处理任务状态失败");
        }

        log.info("更新PDF处理任务为失败状态，taskId: {}, errorMsg: {}", taskId, errorMsg);
    }

    @Override
    public int cleanupTimeoutTasks(int timeoutMinutes) throws BusinessException {
        try {
            // 计算超时时间点
            LocalDateTime timeoutTime = LocalDateTime.now().minusMinutes(timeoutMinutes);

            // 查询超时的处理中任务
            LambdaQueryWrapper<AppEBookPdfTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AppEBookPdfTask::getStatus, PdfTaskStatusEnum.PROCESSING.getCode())
                    .lt(AppEBookPdfTask::getCreateTime, timeoutTime);

            List<AppEBookPdfTask> timeoutTasks = this.list(queryWrapper);

            if (timeoutTasks.isEmpty()) {
                log.info("没有发现超时的PDF处理任务");
                return 0;
            }

            // 批量更新为失败状态
            int cleanupCount = 0;
            for (AppEBookPdfTask task : timeoutTasks) {
                try {
                    String errorMsg = String.format("任务超时自动清理，超时时间：%d分钟", timeoutMinutes);
                    updateTaskFailed(task.getTaskId(), errorMsg);
                    cleanupCount++;
                } catch (Exception e) {
                    log.error("清理超时任务失败，taskId: {}, 错误: {}", task.getTaskId(), e.getMessage());
                }
            }

            log.info("清理超时PDF处理任务完成，总数: {}, 成功: {}", timeoutTasks.size(), cleanupCount);
            return cleanupCount;

        } catch (Exception e) {
            log.error("清理超时PDF处理任务异常", e);
            throw new BusinessException("清理超时任务失败：" + e.getMessage());
        }
    }

    @Override
    public AppEBookPdfTask getLatestTaskByBusinessKey(Integer userId, String businessKey, Integer waterMarkBusinessType) throws BusinessException {
        if (StringUtils.isBlank(businessKey)) {
            return null;
        }

        LambdaQueryWrapper<AppEBookPdfTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppEBookPdfTask::getBusinessKey, businessKey)
                .eq(AppEBookPdfTask::getCreateBy, userId);

        // 如果指定了水印类型，则按水印类型过滤
        if (waterMarkBusinessType != null) {
            queryWrapper.eq(AppEBookPdfTask::getWaterMarkBusinessType, waterMarkBusinessType);
        }

        queryWrapper.orderByDesc(AppEBookPdfTask::getCreateTime)
                .last("limit 1");

        List<AppEBookPdfTask> tasks = this.list(queryWrapper);
        return tasks.isEmpty() ? null : tasks.get(0);
    }
}
