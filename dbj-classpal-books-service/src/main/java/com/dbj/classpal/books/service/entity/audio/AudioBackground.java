package com.dbj.classpal.books.service.entity.audio;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 音频背景音
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("audio_background")
@ApiModel(value="AudioBackground对象", description="音频背景音")
public class AudioBackground extends BizEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "音频名称")
    private String materialName;

    @ApiModelProperty(value = "资源路径")
    private String materialPath;

    @ApiModelProperty(value = "资源大小")
    private Double materialSize;

    @ApiModelProperty(value = "资源时长(s)")
    private Integer materialDuration;

    @ApiModelProperty(value = "类型：1 预置背景音 2 自定义")
    private Integer type;

    @ApiModelProperty(value = "排序")
    private Integer orderNum;



}
