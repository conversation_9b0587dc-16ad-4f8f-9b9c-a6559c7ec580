package com.dbj.classpal.books.service.biz.poem;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.poem.app.AncientPoemReciteAppUserAssessmentScorePageBO;
import com.dbj.classpal.books.client.dto.poem.app.AncientPoemReciteAppUserAssessmentScorePageDTO;
import com.dbj.classpal.books.service.entity.poem.AncientPoemReciteAppUserAssessmentScores;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;

/**
 * <p>
 * 古诗背诵评测得分 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
public interface IAncientPoemReciteAppUserAssessmentScoresBiz extends IService<AncientPoemReciteAppUserAssessmentScores> {


    Page<AncientPoemReciteAppUserAssessmentScorePageDTO> pageAncientPoemReciteAppUserAssessmentScore(PageInfo<AncientPoemReciteAppUserAssessmentScorePageBO> pageInfo);
}
