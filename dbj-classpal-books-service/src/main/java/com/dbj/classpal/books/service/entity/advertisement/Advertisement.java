package com.dbj.classpal.books.service.entity.advertisement;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 广告信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("advertisement")
@Tag(name="广告信息表", description="广告信息表")
public class Advertisement extends BizEntity implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "广告类型字典项的值")
    private String type;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "跳转类型 0-无跳转 1-链接 2-小程序 3-基础图文")
    private Integer redirectType;

//    @Schema(description = "跳转标识")
//    private String redirectFlag;

    @Schema(description = "跳转地址")
    private String redirectUri;

    @Schema(description = "外部小程序appid")
    private String redirectAppId;

//    @Schema(description = "落地页面id")
//    private String advertisingLandingId;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "富文本内容")
    private String content;

    @Schema(description = "展示次数类型 1-仅一次 2-每次 3-每天一次")
    private Integer showType;

    @Schema(description = "排序（越大越靠前）")
    private Integer sort;

    @Schema(description = "是否启用 1-是 0-否")
    private Boolean status;

    @Schema(description = "封面路径")
    private String coverUrl;

    @Schema(description = "启用时间")
    private LocalDateTime activationTime;
}
