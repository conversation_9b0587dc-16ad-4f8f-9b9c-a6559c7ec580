package com.dbj.classpal.books.service.entity.evaluation;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.models.auth.In;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppUserPaperEvaluation
 * Date:     2025-05-16 11:23:51
 * Description: 表名： ,描述： 表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("app_user_paper_evaluation")
@Tag(name="内容管理-评测-评测项表", description="内容管理-评测-评测项表")
public class AppUserPaperEvaluation extends BizEntity {

    @TableField("app_user_id")
    @Schema(name = "app用户id")
    private Integer appUserId;

    @TableField("app_evaluation_id")
    @Schema(name = "评测表id")
    private Integer appEvaluationId;

    @TableField("grade_id")
    @Schema(name = "学员年级id")
    private String gradeId;

    @TableField("grade_name")
    @Schema(name = "学员年级")
    private String gradeName;

    @TableField("region")
    @Schema(name = "地区")
    private String region;

    @TableField("evaluation")
    @Schema(name = "综合评价")
    private String evaluation;

    @TableField("is_generated")
    @Schema(name = "是否生成评测报告 0否 1是")
    private Integer isGenerated;

    @TableField("generated_time")
    @Schema(name = "评测报告生成时间")
    private LocalDateTime generatedTime;

}
