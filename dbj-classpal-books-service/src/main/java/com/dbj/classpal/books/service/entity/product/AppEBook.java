package com.dbj.classpal.books.service.entity.product;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 单书表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_ebook")
@Tag(name="AppEBook", description="单书表")
public class AppEBook extends BizEntity implements Serializable {

    @Schema(description = "样书标题")
    private String bookTitle;

    @Schema(description = "书籍编码")
    private String bookCode;

    @Schema(description = "样书分类ID集合，逗号分隔")
    private String categoryIds;

    @Schema(description = "封面URLId")
    private Integer coverUrlId;

    @Schema(description = "封面URL")
    private String coverUrl;

    @Schema(description = "封面名称")
    private String coverUrlName;

    @Schema(description = "简介")
    private String blurb;

    @Schema(description = "文件ID")
    private Integer fileId;

    @Schema(description = "文件URL")
    private String fileUrl;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "文件大小")
    private BigDecimal fileSize;

    @Schema(description = "文件md5")
    private String fileMd5;

    @Schema(description = "文件状态：0-待处理，1-处理中，2-已完成，3-处理失败")
    private Integer fileStatus;

    @Schema(description = "文件错误信息")
    private String fileErrMsg;

    @Schema(description = "水印模板ID")
    private Integer watermarkId;

    @Schema(description = "学科ID")
    private Integer subjectId;
    
    @Schema(description = "阶段ID")
    private Integer stageId;
    
    @Schema(description = "教材版本ID")
    private Integer textbookVersionId;

    @Schema(description = "适用年级，ALL表示全部，部分年级用逗号分隔")
    private String applicableGrades;

    @Schema(description = "启用状态：0-禁用，1-启用")
    private Integer launchStatus;

    @Schema(description = "允许下载：0-否，1-是")
    private Integer allowDownload;

    @Schema(description = "排序序号")
    private Integer sortNum;

    @Schema(description = "分享链接")
    private String shareUrl;

    @Schema(description = "分享二维码")
    private String shareQrCode;

    @Schema(description = "分享状态：0-未分享，1-已分享")
    private Integer shareStatus;

    @Schema(description = "分享时间")
    private LocalDateTime shareTime;

}