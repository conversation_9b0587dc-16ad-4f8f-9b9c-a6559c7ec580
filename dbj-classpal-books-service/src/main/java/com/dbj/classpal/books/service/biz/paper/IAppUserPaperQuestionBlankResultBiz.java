package com.dbj.classpal.books.service.biz.paper;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.common.bo.paper.CheckBlankAnsweredBO;
import com.dbj.classpal.books.common.bo.paper.QueryBlankResultBO;
import com.dbj.classpal.books.common.bo.paper.QueryErrorBlankResultsBO;
import com.dbj.classpal.books.common.bo.paper.SaveBlankResultBO;
import com.dbj.classpal.books.common.dto.paper.BlankResultDTO;
import com.dbj.classpal.books.service.entity.paper.AppUserPaperQuestionBlankResult;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;

/**
 * 用户试卷题目空位结果业务接口
 */
public interface IAppUserPaperQuestionBlankResultBiz extends IService<AppUserPaperQuestionBlankResult> {
    
    /**
     * 保存空位作答结果
     *
     * @param saveBO 保存空位结果参数
     * @return 是否成功
     */
    boolean saveBlankResult(SaveBlankResultBO saveBO) throws BusinessException;

    /**
     * 获取空位作答结果列表
     *
     * @param queryBO 查询空位结果参数
     * @return 空位结果列表
     */
    List<BlankResultDTO> getBlankResults(QueryBlankResultBO queryBO) throws BusinessException;

    /**
     * 检查所有空位是否已作答
     *
     * @param checkBO 检查空位作答参数
     * @return 是否全部作答完成
     */
    boolean checkAllBlanksAnswered(CheckBlankAnsweredBO checkBO) throws BusinessException;
    
    /**
     * 批量查询用户错误空位答题结果
     *
     * @param queryBO 查询参数
     * @return 错误空位答题结果列表
     */
    List<AppUserPaperQuestionBlankResult> batchGetErrorResults(QueryErrorBlankResultsBO queryBO);
} 