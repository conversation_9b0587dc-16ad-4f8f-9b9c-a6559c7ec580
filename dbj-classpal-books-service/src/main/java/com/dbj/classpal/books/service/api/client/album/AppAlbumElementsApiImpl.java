package com.dbj.classpal.books.service.api.client.album;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.album.AppAlbumElementsApi;
import com.dbj.classpal.books.client.bo.album.*;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.dto.album.AppAlbumElementsQueryApiDTO;
import com.dbj.classpal.books.common.bo.album.*;
import com.dbj.classpal.books.common.bo.common.CommonIdBO;
import com.dbj.classpal.books.common.bo.common.CommonIdsBO;
import com.dbj.classpal.books.common.dto.album.AppAlbumElementsQueryDTO;
import com.dbj.classpal.books.service.biz.album.IAppAlbumElementsBusinessRefBiz;
import com.dbj.classpal.books.service.entity.album.AppAlbumElements;
import com.dbj.classpal.books.service.entity.album.AppAlbumElementsBusinessRef;
import com.dbj.classpal.books.service.entity.material.AppMaterialBusinessRef;
import com.dbj.classpal.books.service.service.album.IAppAlbumElementsService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumApiImpl
 * Date:     2025-04-15 11:25:28
 * Description: 表名： ,描述： 表
 */
@RestController
public class AppAlbumElementsApiImpl implements AppAlbumElementsApi {
    @Resource
    private IAppAlbumElementsService elementsService;
    @Resource
    private IAppAlbumElementsBusinessRefBiz elementsBusinessRefBiz;

    @Override
    public RestResponse<List<AppAlbumElementsQueryApiDTO>> getAppAlbumElementsList(AppAlbumElementsQueryApiBO bo) throws BusinessException {
        AppAlbumElementsQueryBO queryBO = new AppAlbumElementsQueryBO();
        BeanUtil.copyProperties(bo, queryBO);
        return RestResponse.success(elementsService.getAppAlbumElementsList(queryBO).stream().map(d -> {
            AppAlbumElementsQueryApiDTO queryApiDTO = new AppAlbumElementsQueryApiDTO();
            BeanUtil.copyProperties(d, queryApiDTO);
            return queryApiDTO;
        }).collect(Collectors.toList()));
    }

    @Override
    public RestResponse<Page<AppAlbumElementsQueryApiDTO>> pageAlbumElements(PageInfo<AppAlbumElementsQueryApiBO> bo) throws BusinessException {
        // 1. 转换查询条件
        AppAlbumElementsQueryBO queryBO = new AppAlbumElementsQueryBO();
        BeanUtil.copyProperties(bo.getData(), queryBO);

        PageInfo<AppAlbumElementsQueryBO> servicePageRequest = new PageInfo<>();
        servicePageRequest.setPageNum(bo.getPageNum());
        servicePageRequest.setPageSize(bo.getPageSize());
        servicePageRequest.setData(queryBO);

        // 2. 调用Service
        Page<AppAlbumElementsQueryDTO> page = elementsService.pageInfo(servicePageRequest);

        Set<Integer> idSet = page.getRecords().stream().map(d -> d.getId()).collect(Collectors.toSet());
        Map<Integer, Integer> refMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(idSet)){
            refMap = elementsBusinessRefBiz.lambdaQuery().in(AppAlbumElementsBusinessRef::getAppAlbumId, idSet).list().stream().collect(Collectors.groupingBy(
                    AppAlbumElementsBusinessRef::getAppAlbumId,
                    Collectors.collectingAndThen(
                            Collectors.counting(),
                            Long::intValue
                    )
            ));
        }

        // 3. 转换返回结果
        Map<Integer, Integer> finalRefMap = refMap;
        return RestResponse.success((Page<AppAlbumElementsQueryApiDTO>) page.convert(vo -> {
            AppAlbumElementsQueryApiDTO dto = new AppAlbumElementsQueryApiDTO();
            BeanUtil.copyProperties(vo, dto);
            if (finalRefMap.containsKey(dto.getId())){
                dto.setIsRef(true);
            }else{
                dto.setIsRef(false);
            }
            return dto;
        }));
    }

    @Override
    public RestResponse<AppAlbumElementsQueryApiDTO> getAppAlbumElement(CommonIdApiBO bo) {
        CommonIdBO commonIdBO = new CommonIdBO();
        BeanUtil.copyProperties(bo, commonIdBO);
        AppAlbumElementsQueryDTO appAlbumElement = elementsService.getAppAlbumElement(commonIdBO);
        AppAlbumElementsQueryApiDTO appAlbumElementsQueryApiDTO = new AppAlbumElementsQueryApiDTO();
        BeanUtil.copyProperties(appAlbumElement, appAlbumElementsQueryApiDTO);
        return RestResponse.success(appAlbumElementsQueryApiDTO);
    }

    @Override
    public RestResponse<Boolean> saveAlbumElements(AppAlbumElementsSaveApiBO bo) throws BusinessException {
        AppAlbumElementsSaveBO appAlbumElementsSaveBO = new AppAlbumElementsSaveBO();
        BeanUtil.copyProperties(bo, appAlbumElementsSaveBO);
        if (!elementsService.saveAppAlbumElements(appAlbumElementsSaveBO)){
            throw new BusinessException(APP_ALBUM_ELEMENTS_SAVE_FAIL_CODE,APP_ALBUM_ELEMENTS_SAVE_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> updateAlbumElements(AppAlbumElementsUpdateApiBO bo) throws BusinessException {
        AppAlbumElementsUpdateBO appAlbumElementsUpdateBO = new AppAlbumElementsUpdateBO();
        BeanUtil.copyProperties(bo, appAlbumElementsUpdateBO);
        if (!elementsService.updateAppAlbumElement(appAlbumElementsUpdateBO)){
            throw new BusinessException(APP_ALBUM_ELEMENTS_UPDATE_FAIL_CODE,APP_ALBUM_ELEMENTS_UPDATE_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> deleteAppAlbumElements(CommonIdsApiBO bo) throws BusinessException {
        CommonIdsBO idsBO = new CommonIdsBO();
        BeanUtil.copyProperties(bo, idsBO);
        if (!elementsService.deleteAppAlbumElements(idsBO)){
            throw new BusinessException(APP_ALBUM_ELEMENTS_DELETE_FAIL_CODE,APP_ALBUM_ELEMENTS_DELETE_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> updateAppAlbumElementTitle(AppAlbumElementsUpdateTitleApiBO bo) throws BusinessException {
        AppAlbumElementsUpdateBO appAlbumElementsUpdateBO = new AppAlbumElementsUpdateBO();
        BeanUtil.copyProperties(bo, appAlbumElementsUpdateBO);
        if (!elementsService.updateAppAlbumElementTitle(appAlbumElementsUpdateBO)){
            throw new BusinessException(APP_ALBUM_ELEMENTS_UPDATE_FAIL_CODE,APP_ALBUM_ELEMENTS_UPDATE_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> updateAppAlbumElementRemark(AppAlbumElementsUpdateRemarkApiBO bo) throws BusinessException {
        AppAlbumElementsUpdateBO appAlbumElementsUpdateBO = new AppAlbumElementsUpdateBO();
        BeanUtil.copyProperties(bo, appAlbumElementsUpdateBO);
        if (!elementsService.updateAppAlbumElementRemark(appAlbumElementsUpdateBO)){
            throw new BusinessException(APP_ALBUM_ELEMENTS_UPDATE_FAIL_CODE,APP_ALBUM_ELEMENTS_UPDATE_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> updateAppAlbumElementVisible(AppAlbumElementsUpdateVisibleApiBO bo) throws BusinessException {
        AppAlbumElementsUpdateVisibleBO updateBO = new AppAlbumElementsUpdateVisibleBO();
        BeanUtil.copyProperties(bo, updateBO);
        if (!elementsService.updateAppAlbumElementVisible(updateBO)){
            throw new BusinessException(APP_ALBUM_ELEMENTS_UPDATE_FAIL_CODE,APP_ALBUM_ELEMENTS_UPDATE_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> updateAppAlbumElementStatus(AppAlbumElementsUpdateStatusApiBO bo) throws BusinessException {
        AppAlbumElementsUpdateStatusBO statusBO = new AppAlbumElementsUpdateStatusBO();
        BeanUtil.copyProperties(bo, statusBO);
        if (!elementsService.updateAppAlbumElementStatus(statusBO)){
            throw new BusinessException(APP_ALBUM_ELEMENTS_UPDATE_FAIL_CODE,APP_ALBUM_ELEMENTS_UPDATE_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> updateAppAlbumElementCover(AppAlbumElementsUpdateCoverApiBO bo) throws BusinessException {
        AppAlbumElementsUpdateBO updateBO = new AppAlbumElementsUpdateBO();
        BeanUtil.copyProperties(bo, updateBO);
        if (!elementsService.updateAppAlbumElementCover(updateBO)){
            throw new BusinessException(APP_ALBUM_ELEMENTS_UPDATE_FAIL_CODE,APP_ALBUM_ELEMENTS_UPDATE_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    public PageInfo<AppAlbumElementsQueryBO> convertToBO(PageInfo<AppAlbumElementsQueryApiBO> bo) {
        PageInfo<AppAlbumElementsQueryBO> vo = new PageInfo<AppAlbumElementsQueryBO>();
        BeanUtil.copyProperties(bo, vo);
        return vo;
    }
}
