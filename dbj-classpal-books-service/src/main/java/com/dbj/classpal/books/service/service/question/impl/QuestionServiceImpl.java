package com.dbj.classpal.books.service.service.question.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.question.*;
import com.dbj.classpal.books.common.dto.question.*;
import com.dbj.classpal.books.common.enums.BusinessTypeEnum;
import com.dbj.classpal.books.common.enums.question.QuestionTypeEnum;
import com.dbj.classpal.books.service.biz.config.BasicConfigBiz;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBusinessRefBiz;
import com.dbj.classpal.books.service.biz.question.*;
import com.dbj.classpal.books.service.entity.config.BasicConfig;
import com.dbj.classpal.books.service.entity.material.AppMaterial;
import com.dbj.classpal.books.service.entity.material.AppMaterialBusinessRef;
import com.dbj.classpal.books.service.entity.question.Question;
import com.dbj.classpal.books.service.entity.question.QuestionAnswer;
import com.dbj.classpal.books.service.entity.question.QuestionBlankArea;
import com.dbj.classpal.books.service.entity.question.QuestionCategory;
import com.dbj.classpal.books.service.mapper.material.AppMaterialMapper;
import com.dbj.classpal.books.service.service.question.QuestionService;
import com.dbj.classpal.books.service.validator.QuestionValidator;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

@Service
@RequiredArgsConstructor
public class QuestionServiceImpl implements QuestionService {

    private final QuestionBiz questionBiz;
    private final QuestionCategoryBiz questionCategoryBiz;
    private final QuestionAnswerBiz questionAnswerBiz;
    private final QuestionBlankAreaBiz questionBlankAreaBiz;
    private final BasicConfigBiz contentConfigBiz;
    private final IQuestionCategoryBusinessRefBiz questionCategoryBusinessRefBiz;
    private final IAppMaterialBusinessRefBiz materialBusinessRefBiz;
    private final AppMaterialMapper materialMapper;
    /**
     * 根节点的父ID
     */
    public static final Integer DEFAULT_FATHER_ID = 1;
    @Override
    public QuestionInfoDTO getQuestion(QuestionIdBO idBO) throws BusinessException {
        Question question = questionBiz.getQuestionById(idBO.getId());
        if (question == null) {
            return null;
        }
        List<QuestionAnswer> answers = questionAnswerBiz.getAnswersByQuestionId(idBO.getId());
        question.setAnswers(answers);
        return convertToDTO(question);
    }

    @SneakyThrows
    @Override
    public List<QuestionInfoDTO> getQuestionList(QuestionCategoryIdQueryBO idBO) {
        List<Question> questions = questionBiz.getQuestionListByCategoryId(idBO.getCategoryId());
        return questions.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    /**
     * 保存单个答案并返回保存后的实体
     *
     * @param answerBO 答案BO对象
     * @param questionId 问题ID
     * @param blankAreaId 空位区域ID，可为null
     * @return 保存后的答案实体
     */
    public QuestionAnswer saveAnswer(QuestionAnswerBO answerBO, Integer questionId, Integer blankAreaId) {
        QuestionAnswer answer = new QuestionAnswer();
        BeanUtil.copyProperties(answerBO, answer);
        answer.setId(null);
        answer.setQuestionId(questionId);

        if (blankAreaId != null) {
            answer.setBlankAreaId(blankAreaId);
        }

        // 保存答案
        questionAnswerBiz.save(answer);

        return answer;
    }

    /**
     * 处理答案的媒体文件引用
     *
     * @param answerBO 答案BO对象
     * @param answerId 保存后的答案ID
     */
    public void processAnswerMediaReferences(QuestionAnswerBO answerBO, Integer answerId) {
        if (!CollectionUtils.isEmpty(answerBO.getMediaUrl())) {
            answerBO.getMediaUrl().forEach(mediaBO -> {
                AppMaterialBusinessRef ref = new AppMaterialBusinessRef();
                ref.setAppMaterialId(mediaBO.getMaterialId());
                ref.setBusinessId(answerId);
                ref.setBusinessType(BusinessTypeEnum.ANSWER_MEDIA_BUSINESS.getCode());
                ref.setBusinessName(BusinessTypeEnum.ANSWER_MEDIA_BUSINESS.getType());
                ref.setOrderNum(mediaBO.getSortNum());
                materialBusinessRefBiz.save(ref);
            });
        }
    }

    public void updateAnswerMediaReferences(QuestionAnswerBO answerBO, Integer answerId) {
        materialBusinessRefBiz.remove(new LambdaQueryWrapper<AppMaterialBusinessRef>()
                .eq(AppMaterialBusinessRef::getBusinessId, answerId)
                .eq(AppMaterialBusinessRef::getIsDeleted, YesOrNoEnum.NO.getCode())
                .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.ANSWER_MEDIA_BUSINESS.getCode()));
        if (!CollectionUtils.isEmpty(answerBO.getMediaUrl())) {
            answerBO.getMediaUrl().forEach(mediaBO -> {
                AppMaterialBusinessRef ref = new AppMaterialBusinessRef();
                ref.setAppMaterialId(mediaBO.getMaterialId());
                ref.setBusinessId(answerId);
                ref.setBusinessType(BusinessTypeEnum.ANSWER_MEDIA_BUSINESS.getCode());
                ref.setBusinessName(BusinessTypeEnum.ANSWER_MEDIA_BUSINESS.getType());
                ref.setOrderNum(mediaBO.getSortNum());
                materialBusinessRefBiz.save(ref);
            });
        }
    }

    /**
     * 处理普通题型的所有答案
     *
     * @param questionBO 问题BO对象
     * @param questionId 问题ID
     * @param answerIds 保存正确答案ID的集合
     */
    public void processNormalAnswers(QuestionBO questionBO, Integer questionId, Set<String> answerIds) {
        if (questionBO.getAnswers() != null) {
            // 循环处理每个答案
            for (QuestionAnswerBO answerBO : questionBO.getAnswers()) {
                // 保存答案
                QuestionAnswer answer = saveAnswer(answerBO, questionId, null);

                // 收集正确答案ID
                if (Objects.equals(answerBO.getIsAnswer(), YesOrNoEnum.YES.getCode())) {
                    answerIds.add(answer.getId().toString());
                }

                // 处理媒体文件引用
                processAnswerMediaReferences(answerBO, answer.getId());
            }
        }
    }

    /**
     * 处理完形填空题中单个空位区域的所有答案
     *
     * @param areaBO 空位区域BO对象
     * @param area 保存后的空位区域实体
     * @param questionId 问题ID
     */
    public void processBlankAreaAnswers(QuestionBlankAreaBO areaBO, QuestionBlankArea area, Integer questionId) {
        if (!CollectionUtils.isEmpty(areaBO.getAnswers())) {
            // 收集正确答案ID
            Set<String> blankAnswerIds = new LinkedHashSet<>();

            // 循环处理每个空位区域的答案
            for (QuestionAnswerBO answerBO : areaBO.getAnswers()) {
                // 保存答案
                QuestionAnswer answer = saveAnswer(answerBO, questionId, area.getId());

                // 收集正确答案ID
                if (Objects.equals(answerBO.getIsAnswer(), YesOrNoEnum.YES.getCode())) {
                    blankAnswerIds.add(answer.getId().toString());
                }

                // 处理媒体文件引用
                processAnswerMediaReferences(answerBO, answer.getId());
            }

            // 更新空位的正确答案ID
            if (!blankAnswerIds.isEmpty()) {
                area.setAnswerIds(String.join(",", blankAnswerIds));
                questionBlankAreaBiz.updateById(area);
            }
        }
    }

    /**
     * 处理完形填空题的所有空位区域
     *
     * @param questionBO 问题BO对象
     * @param questionId 问题ID
     */
    public void processBlankAreas(QuestionBO questionBO, Integer questionId) {
        if (Objects.equals(questionBO.getType(), QuestionTypeEnum.CLOZE.getValue()) && questionBO.getBlankAreas() != null) {
            // 保存空位区域
            for (QuestionBlankAreaBO areaBO : questionBO.getBlankAreas()) {
                QuestionBlankArea area = new QuestionBlankArea();
                BeanUtil.copyProperties(areaBO, area);
                area.setId(null);
                area.setQuestionId(questionId);

                // 保存空位区域
                questionBlankAreaBiz.save(area);

                // 处理空位区域答案
                processBlankAreaAnswers(areaBO, area, questionId);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createQuestion(QuestionBO questionBO) throws BusinessException {
        QuestionValidator.validate(questionBO, questionBO.getAnswers());
        Question question = convertToDomain(questionBO);
        if (question.getAnalyzes() != null && question.getAnalyzes().trim().isEmpty()) {
            question.setAnalyzes(null);
        }
        if(question.getQuestionCategoryId()!= null){
            QuestionCategory questionCategory =  questionCategoryBiz.getById(question.getQuestionCategoryId());
            if(questionCategory == null){
                throw new BusinessException(APP_QUESTION_CATEGORY_NOT_EXIST_CODE,APP_QUESTION_CATEGORY_NOT_EXIST_MSG);
            }
            if(questionCategory.getIsDefault()){
                throw new BusinessException(APP_QUESTION_CATEGORY_NOT_ROOT_CODE,APP_QUESTION_CATEGORY_NOT_ROOT_MSG);
            }
        }
        Integer questionId = questionBiz.createQuestion(question);
        BasicConfig questionSubject = contentConfigBiz.getBaseMapper().selectById(question.getSubjectId());
        Set<String> answerIds = new LinkedHashSet<>();
        question.setSubjectId(questionSubject.getId());

        // 处理问题的媒体文件引用
        if (!CollectionUtils.isEmpty(questionBO.getMediaUrl())) {
            questionBO.getMediaUrl().forEach(mediaBO -> {
                AppMaterialBusinessRef ref = new AppMaterialBusinessRef();
                ref.setAppMaterialId(mediaBO.getMaterialId());
                ref.setBusinessId(questionId);
                ref.setBusinessType(BusinessTypeEnum.QUESTION_MEDIA_BUSINESS.getCode());
                ref.setBusinessName(BusinessTypeEnum.QUESTION_MEDIA_BUSINESS.getType());
                ref.setOrderNum(mediaBO.getSortNum());
                materialBusinessRefBiz.save(ref);
            });
        }

        // 处理问题的识别辅助引用
        if (!CollectionUtils.isEmpty(questionBO.getAidedRecognitionUrl())) {
            questionBO.getAidedRecognitionUrl().forEach(recognitionBO -> {
                AppMaterialBusinessRef ref = new AppMaterialBusinessRef();
                ref.setAppMaterialId(recognitionBO.getMaterialId());
                ref.setBusinessId(questionId);
                ref.setBusinessType(BusinessTypeEnum.QUESTION_RECOGNITION_BUSINESS.getCode());
                ref.setBusinessName(BusinessTypeEnum.QUESTION_RECOGNITION_BUSINESS.getType());
                ref.setOrderNum(recognitionBO.getSortNum());
                materialBusinessRefBiz.save(ref);
            });
        }

        // 处理普通题型的答案
        processNormalAnswers(questionBO, questionId, answerIds);

        // 设置问题的正确答案
        question.setAnswer(String.join(",", answerIds));

        // 处理完形填空题
        processBlankAreas(questionBO, questionId);

        // 更新问题
        questionBiz.updateQuestion(question);
        return questionId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateQuestion(QuestionBO questionBO) throws BusinessException {
        QuestionValidator.validate(questionBO, questionBO.getAnswers());
        Question question = convertToDomain(questionBO);
        Set<String> answerIds = new LinkedHashSet<>();

        // 处理问题媒体文件引用
        materialBusinessRefBiz.remove(new LambdaQueryWrapper<AppMaterialBusinessRef>()
                .eq(AppMaterialBusinessRef::getBusinessId, questionBO.getId())
                .eq(AppMaterialBusinessRef::getIsDeleted, YesOrNoEnum.NO.getCode())
                .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.QUESTION_MEDIA_BUSINESS.getCode()));
        if (!CollectionUtils.isEmpty(questionBO.getMediaUrl())) {
            questionBO.getMediaUrl().forEach(mediaBO -> {
                AppMaterialBusinessRef ref = new AppMaterialBusinessRef();
                ref.setAppMaterialId(mediaBO.getMaterialId());
                ref.setBusinessId(questionBO.getId());
                ref.setBusinessType(BusinessTypeEnum.QUESTION_MEDIA_BUSINESS.getCode());
                ref.setBusinessName(BusinessTypeEnum.QUESTION_MEDIA_BUSINESS.getType());
                ref.setOrderNum(mediaBO.getSortNum());
                materialBusinessRefBiz.save(ref);
            });
        }

        // 处理问题识别辅助引用
        materialBusinessRefBiz.remove(new LambdaQueryWrapper<AppMaterialBusinessRef>()
                .eq(AppMaterialBusinessRef::getBusinessId, questionBO.getId())
                .eq(AppMaterialBusinessRef::getIsDeleted, YesOrNoEnum.NO.getCode())
                .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.QUESTION_RECOGNITION_BUSINESS.getCode()));
        if (!CollectionUtils.isEmpty(questionBO.getAidedRecognitionUrl())) {
            questionBO.getAidedRecognitionUrl().forEach(recognitionBO -> {
                AppMaterialBusinessRef ref = new AppMaterialBusinessRef();
                ref.setAppMaterialId(recognitionBO.getMaterialId());
                ref.setBusinessId(questionBO.getId());
                ref.setBusinessType(BusinessTypeEnum.QUESTION_RECOGNITION_BUSINESS.getCode());
                ref.setBusinessName(BusinessTypeEnum.QUESTION_RECOGNITION_BUSINESS.getType());
                ref.setOrderNum(recognitionBO.getSortNum());
                materialBusinessRefBiz.save(ref);
            });
        }

        // 差异化更新普通题型的答案
        processNormalAnswersDiff(questionBO, questionBO.getId(), answerIds);

        // 设置问题的正确答案
        question.setAnswer(String.join(",", answerIds));

        // 差异化更新完形填空题
        if (Objects.equals(questionBO.getType(), QuestionTypeEnum.CLOZE.getValue()) && questionBO.getBlankAreas() != null) {
            processBlankAreasDiff(questionBO, questionBO.getId());
        }

        // 更新问题
        questionBiz.updateQuestion(question);
    }

    /**
     * 差异化更新普通题目答案
     * @param questionBO 题目BO
     * @param questionId 题目ID
     * @param answerIds 正确答案ID集合
     */
    private void processNormalAnswersDiff(QuestionBO questionBO, Integer questionId, Set<String> answerIds) {
        if (CollectionUtils.isEmpty(questionBO.getAnswers())) {
            return;
        }
        List<QuestionAnswer> existingAnswers = questionAnswerBiz.getAnswersByQuestionId(questionId);
        Map<Integer, QuestionAnswer> existingAnswerMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(existingAnswers)) {
            for (QuestionAnswer answer : existingAnswers) {
                existingAnswerMap.put(answer.getId(), answer);
            }
        }
        Set<Integer> frontendAnswerIds = new LinkedHashSet<>();
        for (QuestionAnswerBO answerBO : questionBO.getAnswers()) {
            QuestionAnswer answer = new QuestionAnswer();
            BeanUtil.copyProperties(answerBO, answer);
            answer.setQuestionId(questionId);

            if (answerBO.getId() != null) {
                frontendAnswerIds.add(answerBO.getId());
                if (existingAnswerMap.containsKey(answerBO.getId())) {
                    // 更新
                    questionAnswerBiz.updateById(answer);
                    updateAnswerMediaReferences(answerBO, answerBO.getId());
                } else {
                    answer.setId(null);
                    questionAnswerBiz.save(answer);
                    updateAnswerMediaReferences(answerBO, answer.getId());
                }
            } else {
                // 新增
                questionAnswerBiz.save(answer);
                updateAnswerMediaReferences(answerBO, answer.getId());
            }
            if (YesOrNoEnum.YES.getCode().equals(answerBO.getIsAnswer())) {
                answerIds.add(String.valueOf(answer.getId() != null ? answer.getId() : answerBO.getId()));
            }
        }
        for (Integer id : existingAnswerMap.keySet()) {
            if (!frontendAnswerIds.contains(id)) {
                questionAnswerBiz.removeById(id);
                updateAnswerMediaReferences(new QuestionAnswerBO(), id);
            }
        }
    }

    /**
     * 差异化更新完形填空区域
     * @param questionBO 题目BO
     * @param questionId 题目ID
     */
    private void processBlankAreasDiff(QuestionBO questionBO, Integer questionId) throws BusinessException {
        if (CollectionUtils.isEmpty(questionBO.getBlankAreas())) {
            return;
        }

        // 获取数据库中的现有空位区域
        List<QuestionBlankArea> existingBlankAreas = questionBlankAreaBiz.getBlankAreasByQuestionId(questionId);
        Map<Integer, QuestionBlankArea> existingBlankAreaMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(existingBlankAreas)) {
            for (QuestionBlankArea area : existingBlankAreas) {
                existingBlankAreaMap.put(area.getId(), area);
            }
        }

        // 记录前端传来的空位区域ID
        Set<Integer> frontendBlankAreaIds = new LinkedHashSet<>();

        // 处理前端传来的空位区域
        for (QuestionBlankAreaBO areaBO : questionBO.getBlankAreas()) {
            QuestionBlankArea blankArea = new QuestionBlankArea();
            BeanUtil.copyProperties(areaBO, blankArea);
            blankArea.setQuestionId(questionId);

            if (areaBO.getId() != null) {
                // 前端传来ID不为空，说明是更新
                frontendBlankAreaIds.add(areaBO.getId());
                if (existingBlankAreaMap.containsKey(areaBO.getId())) {
                    // 数据库中存在此ID，进行更新
                    questionBlankAreaBiz.updateById(blankArea);

                    // 处理该空位区域的答案
                    processBlankAreaAnswersDiff(areaBO, areaBO.getId());
                } else {
                    // 数据库中不存在此ID，说明前端ID可能有问题，执行新增
                    blankArea.setId(null);
                    questionBlankAreaBiz.save(blankArea);

                    // 处理新增的空位区域答案
                    if (!CollectionUtils.isEmpty(areaBO.getAnswers())) {
                        List<QuestionAnswer> answers = new ArrayList<>();
                        for (QuestionAnswerBO answerBO : areaBO.getAnswers()) {
                            QuestionAnswer answer = new QuestionAnswer();
                            BeanUtil.copyProperties(answerBO, answer);
                            answer.setQuestionId(questionId);
                            answer.setBlankAreaId(blankArea.getId());
                            answers.add(answer);
                        }
                        questionAnswerBiz.saveBatch(answers);

                        // 设置空位区域的答案IDs
                        List<String> answerIds = answers.stream()
                                .map(answer -> String.valueOf(answer.getId()))
                                .collect(Collectors.toList());
                        blankArea.setAnswerIds(String.join(",", answerIds));
                        questionBlankAreaBiz.updateById(blankArea);
                    }
                }
            } else {
                // 前端传来ID为空，说明是新增
                questionBlankAreaBiz.save(blankArea);

                // 处理新增的空位区域答案
                if (!CollectionUtils.isEmpty(areaBO.getAnswers())) {
                    List<QuestionAnswer> answers = new ArrayList<>();
                    for (QuestionAnswerBO answerBO : areaBO.getAnswers()) {
                        QuestionAnswer answer = new QuestionAnswer();
                        BeanUtil.copyProperties(answerBO, answer);
                        answer.setQuestionId(questionId);
                        answer.setBlankAreaId(blankArea.getId());
                        answers.add(answer);
                    }
                    questionAnswerBiz.saveBatch(answers);

                    // 设置空位区域的答案IDs
                    List<String> answerIds = answers.stream()
                            .map(answer -> String.valueOf(answer.getId()))
                            .collect(Collectors.toList());
                    blankArea.setAnswerIds(String.join(",", answerIds));
                    questionBlankAreaBiz.updateById(blankArea);
                }
            }
        }

        // 删除数据库中有但前端未传的空位区域
        for (Integer id : existingBlankAreaMap.keySet()) {
            if (!frontendBlankAreaIds.contains(id)) {
                // 删除该空位区域的所有答案
                questionAnswerBiz.deleteByBlankAreaId(Collections.singletonList(id));
                // 删除空位区域
                questionBlankAreaBiz.removeById(id);
            }
        }
    }

    /**
     * 差异化更新空位区域的答案
     * @param areaBO 空位区域BO
     * @param blankAreaId 空位区域ID
     */
    private void processBlankAreaAnswersDiff(QuestionBlankAreaBO areaBO, Integer blankAreaId) throws BusinessException {
        if (CollectionUtils.isEmpty(areaBO.getAnswers())) {
            questionAnswerBiz.deleteByBlankAreaId(Collections.singletonList(blankAreaId));
            return;
        }
        List<QuestionAnswer> existingAnswers = questionAnswerBiz.getAnswersByBlankAreaId(areaBO.getQuestionId(), blankAreaId);
        Map<Integer, QuestionAnswer> existingAnswerMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(existingAnswers)) {
            for (QuestionAnswer answer : existingAnswers) {
                existingAnswerMap.put(answer.getId(), answer);
            }
        }
        Set<Integer> frontendAnswerIds = new LinkedHashSet<>();
        List<String> answerIds = new ArrayList<>();
        for (QuestionAnswerBO answerBO : areaBO.getAnswers()) {
            QuestionAnswer answer = new QuestionAnswer();
            BeanUtil.copyProperties(answerBO, answer);
            answer.setBlankAreaId(blankAreaId);

            if (answerBO.getId() != null) {
                frontendAnswerIds.add(answerBO.getId());
                if (existingAnswerMap.containsKey(answerBO.getId())) {
                    questionAnswerBiz.updateById(answer);
                    updateAnswerMediaReferences(answerBO, answerBO.getId());
                    answerIds.add(String.valueOf(answerBO.getId()));
                } else {
                    answer.setId(null);
                    questionAnswerBiz.save(answer);
                    updateAnswerMediaReferences(answerBO, answer.getId());
                    answerIds.add(String.valueOf(answer.getId()));
                }
            } else {
                questionAnswerBiz.save(answer);
                updateAnswerMediaReferences(answerBO, answer.getId());
                answerIds.add(String.valueOf(answer.getId()));
            }
        }
        for (Integer id : existingAnswerMap.keySet()) {
            if (!frontendAnswerIds.contains(id)) {
                questionAnswerBiz.removeById(id);
                updateAnswerMediaReferences(new QuestionAnswerBO(), id);
            }
        }
        QuestionBlankArea blankArea = new QuestionBlankArea();
        blankArea.setId(blankAreaId);
        blankArea.setAnswerIds(String.join(",", answerIds));
        questionBlankAreaBiz.updateById(blankArea);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteQuestion(QuestionIdsBO idsBO) throws BusinessException {
        if (Objects.isNull(idsBO)) {
            return;
        }
        List<Question> questions = questionBiz.getBaseMapper().selectByIds(idsBO.getIds());
        if(CollectionUtils.isEmpty(questions)){
            return;
        }
        boolean hasBusinessRefs = questionCategoryBusinessRefBiz.hasBusinessRefs(questions.stream().map(Question::getQuestionCategoryId).collect(Collectors.toList()));
        if(hasBusinessRefs){
            throw new BusinessException(APP_QUESTION_HAS_REF_CODE,APP_QUESTION_HAS_REF_MSG);
        }
        materialBusinessRefBiz.remove(new LambdaQueryWrapper<AppMaterialBusinessRef>()
                .in(AppMaterialBusinessRef::getBusinessId, idsBO.getIds())
                .eq(AppMaterialBusinessRef::getIsDeleted,YesOrNoEnum.NO.getCode())
                .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.QUESTION_MEDIA_BUSINESS.getCode()));

        materialBusinessRefBiz.remove(new LambdaQueryWrapper<AppMaterialBusinessRef>()
                .in(AppMaterialBusinessRef::getBusinessId, idsBO.getIds())
                .eq(AppMaterialBusinessRef::getIsDeleted,YesOrNoEnum.NO.getCode())
                .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.QUESTION_RECOGNITION_BUSINESS.getCode()));
        for (Integer boId : idsBO.getIds()) {
            List<QuestionAnswer>  answers =  questionAnswerBiz.getAnswersByQuestionId(boId);
            materialBusinessRefBiz.remove(new LambdaQueryWrapper<AppMaterialBusinessRef>()
                    .in(AppMaterialBusinessRef::getBusinessId, answers.stream().map(QuestionAnswer::getId).collect(Collectors.toList()))
                    .eq(AppMaterialBusinessRef::getIsDeleted,YesOrNoEnum.NO.getCode())
                    .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.ANSWER_MEDIA_BUSINESS.getCode()));
        }
        questionAnswerBiz.deleteByQuestionId(idsBO.getIds());
        questionBlankAreaBiz.deleteByQuestionId(idsBO.getIds());
        questionBiz.batchDeleteQuestion(idsBO.getIds());
    }

    @Override
    public List<String> getQuestionAnswer(QuestionIdBO idBO) {
        Question question = questionBiz.getQuestionById(idBO.getId());
        if (question == null) {
            return null;
        }
        if (Objects.equals(question.getType(), QuestionTypeEnum.CLOZE.getValue())) {
            List<QuestionBlankArea> areas = questionBlankAreaBiz.getBlankAreasByQuestionId(idBO.getId());
            return areas.stream()
                    .map(QuestionBlankArea::getAnswerIds)
                    .collect(Collectors.toList());
        }

        return Arrays.asList(question.getAnswer().split(","));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchCopyQuestion(QuestionCopyBO copyBO) throws BusinessException {
        List<Question> sourceQuestions = questionBiz.listByIds(copyBO.getQuestionIds());
        
        for (Question sourceQuestion : sourceQuestions) {
            Question newQuestion = new Question();
            BeanUtil.copyProperties(sourceQuestion, newQuestion);
            newQuestion.setId(null);
            newQuestion.setQuestionCategoryId(copyBO.getTargetCategoryId());
            questionBiz.createQuestion(newQuestion);
            
            List<QuestionAnswer> sourceAnswers = questionAnswerBiz.getAnswersByQuestionId(sourceQuestion.getId());
            if (!sourceAnswers.isEmpty()) {
                Set<String> answerIds = new LinkedHashSet<>();
                List<QuestionAnswer> newAnswers = sourceAnswers.stream()
                        .map(sourceAnswer -> {
                            QuestionAnswer newAnswer = new QuestionAnswer();
                            BeanUtil.copyProperties(sourceAnswer, newAnswer);
                            newAnswer.setId(null);
                            newAnswer.setQuestionId(newQuestion.getId());
                            questionAnswerBiz.save(newAnswer);
                            List<AppMaterialBusinessRef> refList = materialBusinessRefBiz.getBaseMapper().selectList(new LambdaQueryWrapper<AppMaterialBusinessRef>()
                                    .eq(AppMaterialBusinessRef::getBusinessId, sourceAnswer.getId())
                                    .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.ANSWER_MEDIA_BUSINESS.getCode()));
                            if (!CollectionUtils.isEmpty(refList)) {
                                refList.forEach(mediaBO -> {
                                    AppMaterialBusinessRef ref = new AppMaterialBusinessRef();
                                    ref.setAppMaterialId(mediaBO.getAppMaterialId());
                                    ref.setBusinessId(newAnswer.getId());
                                    ref.setBusinessType(BusinessTypeEnum.ANSWER_MEDIA_BUSINESS.getCode());
                                    ref.setBusinessName(BusinessTypeEnum.ANSWER_MEDIA_BUSINESS.getType());
                                    ref.setOrderNum(mediaBO.getOrderNum());
                                    materialBusinessRefBiz.save(ref);
                                });
                            }
                            return newAnswer;
                        })
                        .toList();
                for (QuestionAnswer newAnswer : newAnswers) {
                    if (Objects.equals(newAnswer.getIsAnswer(), YesOrNoEnum.YES.getCode())) {
                        answerIds.add(newAnswer.getId().toString());
                    }
                }
                newQuestion.setAnswer(String.join(",", answerIds));
                questionBiz.updateById(newQuestion);
            }
            
            if (Objects.equals(sourceQuestion.getType(), QuestionTypeEnum.CLOZE.getValue())) {
                List<QuestionBlankArea> sourceBlankAreas = questionBlankAreaBiz.getBlankAreasByQuestionId(sourceQuestion.getId());
                if (!sourceBlankAreas.isEmpty()) {
                    for (QuestionBlankArea sourceArea : sourceBlankAreas) {
                        QuestionBlankArea newArea = new QuestionBlankArea();
                        BeanUtil.copyProperties(sourceArea, newArea);
                        newArea.setId(null);
                        newArea.setQuestionId(newQuestion.getId());
                        questionBlankAreaBiz.saveBlankAreas(List.of(newArea));

                        // 复制该空位区域的答案列表
                        List<QuestionAnswer> sourceBlankAnswers = questionAnswerBiz.getAnswersByBlankAreaId(sourceQuestion.getId(), sourceArea.getId());
                        if (!CollectionUtils.isEmpty(sourceBlankAnswers)) {
                            Set<String> answerIds = new LinkedHashSet<>();
                            List<QuestionAnswer> newBlankAnswers = sourceBlankAnswers.stream()
                                    .map(sourceAnswer -> {
                                        QuestionAnswer newAnswer = new QuestionAnswer();
                                        BeanUtil.copyProperties(sourceAnswer, newAnswer);
                                        newAnswer.setId(null);
                                        newAnswer.setQuestionId(newQuestion.getId());
                                        newAnswer.setBlankAreaId(newArea.getId());
                                        return newAnswer;
                                    })
                                    .collect(Collectors.toList());
                            questionAnswerBiz.saveAnswers(newBlankAnswers);
                            for (QuestionAnswer newBlankAnswer : newBlankAnswers) {
                                if (Objects.equals(newBlankAnswer.getIsAnswer(), YesOrNoEnum.YES.getCode())) {
                                    answerIds.add(newBlankAnswer.getId().toString());
                                }
                            }
                            newArea.setAnswerIds(String.join(",", answerIds));
                        }
                        questionBlankAreaBiz.updateById(newArea);
                    }
                }
            }

            List<AppMaterialBusinessRef> sourceMediaRefs = materialBusinessRefBiz.list(
                    new LambdaQueryWrapper<AppMaterialBusinessRef>()
                            .eq(AppMaterialBusinessRef::getBusinessId, sourceQuestion.getId())
                            .eq(AppMaterialBusinessRef::getIsDeleted,YesOrNoEnum.NO.getCode())
                            .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.QUESTION_MEDIA_BUSINESS.getCode())
            );
            if (!CollectionUtils.isEmpty(sourceMediaRefs)) {
                sourceMediaRefs.forEach(sourceRef -> {
                    AppMaterialBusinessRef newRef = new AppMaterialBusinessRef();
                    BeanUtil.copyProperties(sourceRef, newRef);
                    newRef.setId(null);
                    newRef.setBusinessId(newQuestion.getId());
                    materialBusinessRefBiz.save(newRef);
                });
            }

            List<AppMaterialBusinessRef> sourceRecognitionRefs = materialBusinessRefBiz.list(
                    new LambdaQueryWrapper<AppMaterialBusinessRef>()
                            .eq(AppMaterialBusinessRef::getBusinessId, sourceQuestion.getId())
                            .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.QUESTION_RECOGNITION_BUSINESS.getCode())
            );
            if (!CollectionUtils.isEmpty(sourceRecognitionRefs)) {
                sourceRecognitionRefs.forEach(sourceRef -> {
                    AppMaterialBusinessRef newRef = new AppMaterialBusinessRef();
                    BeanUtil.copyProperties(sourceRef, newRef);
                    newRef.setId(null);
                    newRef.setBusinessId(newQuestion.getId());
                    materialBusinessRefBiz.save(newRef);
                });
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchMoveQuestion(QuestionMoveBO moveBO) {
        questionBiz.batchUpdateQuestionCategory(moveBO.getQuestionIds(), moveBO.getTargetCategoryId());
    }

    @Override
    public QuestionBlankContentDTO getQuestionBlankContent(QuestionIdBO idBO) throws BusinessException {
        Question question = questionBiz.getQuestionById(idBO.getId());
        if (question == null || !Objects.equals(question.getType(), QuestionTypeEnum.CLOZE.getValue())) {
            return null;
        }

        QuestionBlankContentDTO dto = new QuestionBlankContentDTO();
        dto.setQuestionId(idBO.getId());

        List<QuestionBlankArea> blankAreas = questionBlankAreaBiz.getBlankAreasByQuestionId(idBO.getId());
        if (CollectionUtils.isEmpty(blankAreas)) {
            return dto;
        }
        Map<String, String> answerMap = new HashMap<>();
        Map<String, List<String>> optionsMap = new HashMap<>();
        List<QuestionBlankAreaDTO> blankAreaList = new ArrayList<>();
        for (QuestionBlankArea area : blankAreas) {
                QuestionBlankAreaDTO areaDTO = new QuestionBlankAreaDTO();
            BeanUtil.copyProperties(area, areaDTO);
                blankAreaList.add(areaDTO);
                answerMap.put(String.valueOf(area.getBlankIndex()), area.getAnswerIds());
            List<QuestionAnswer> answers = questionAnswerBiz.getAnswersByBlankAreaId(area.getQuestionId(), area.getId());
            List<String> options = answers.stream()
                        .map(QuestionAnswer::getOptionContent)
                        .collect(Collectors.toList());
                optionsMap.put(String.valueOf(area.getBlankIndex()), options);
        }

        dto.setBlankAnswerMap(answerMap);
        dto.setBlankOptionsMap(optionsMap);
        dto.setBlankAreaList(blankAreaList);

        return dto;
    }

    @SneakyThrows
    @Override
    public Page<QuestionInfoDTO> pageList(PageInfo<QuestionPageBO> pageBO) {
        Question condition = new Question();
        BeanUtil.copyProperties(pageBO.getData(), condition);
        condition.setQuestionCategoryId(pageBO.getData().getCategoryId() == null ? DEFAULT_FATHER_ID: pageBO.getData().getCategoryId());
        Page<Question> page = questionBiz.pageList(
            new Page<>(pageBO.getPageNum(), pageBO.getPageSize()),
            condition
        );

        return (Page<QuestionInfoDTO>) page.convert(this::convertToDTO);
    }

    private QuestionInfoDTO convertToDTO(Question question) {
        if (question == null) {
            return null;
        }
        BasicConfig questionSubject = contentConfigBiz.getBaseMapper().selectById(question.getSubjectId());

        QuestionInfoDTO dto = new QuestionInfoDTO();
        BeanUtil.copyProperties(question, dto);

        dto.setType(QuestionTypeEnum.getByValue(question.getType()).getValue());
        dto.setSubjectId(questionSubject.getId());
        dto.setSubjectName(questionSubject.getName());

        List<AppMaterialBusinessRef> mediaRefs = materialBusinessRefBiz.list(
                new LambdaQueryWrapper<AppMaterialBusinessRef>()
                        .eq(AppMaterialBusinessRef::getBusinessId, question.getId())
                        .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.QUESTION_MEDIA_BUSINESS.getCode())
                        .eq(AppMaterialBusinessRef::getIsDeleted,YesOrNoEnum.NO.getCode())
                        .orderByAsc(AppMaterialBusinessRef::getOrderNum)
        );
        if (!CollectionUtils.isEmpty(mediaRefs)) {
            List<QuestionMediaDTO> mediaList = mediaRefs.stream()
                    .map(ref -> {
                        QuestionMediaDTO media = new QuestionMediaDTO();
                        media.setMaterialId(ref.getAppMaterialId());
                        media.setSortNum(ref.getOrderNum());
                        AppMaterial material = materialMapper.selectById(ref.getAppMaterialId());
                        if(material !=null) {
                            media.setMaterialPath(material.getMaterialPath());
                            media.setMaterialName(material.getMaterialName());
                        }
                        return media;
                    })
                    .collect(Collectors.toList());
            dto.setMediaUrl(mediaList);
        }

        List<AppMaterialBusinessRef> recognitionRefs = materialBusinessRefBiz.list(
                new LambdaQueryWrapper<AppMaterialBusinessRef>()
                        .eq(AppMaterialBusinessRef::getBusinessId, question.getId())
                        .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.QUESTION_RECOGNITION_BUSINESS.getCode())
                        .eq(AppMaterialBusinessRef::getIsDeleted,YesOrNoEnum.NO.getCode())
                        .orderByAsc(AppMaterialBusinessRef::getOrderNum)
        );
        if (!CollectionUtils.isEmpty(recognitionRefs)) {
            List<QuestionRecognitionDTO> recognitionList = recognitionRefs.stream()
                    .map(ref -> {
                        QuestionRecognitionDTO recognition = new QuestionRecognitionDTO();
                        recognition.setMaterialId(ref.getAppMaterialId());
                        recognition.setSortNum(ref.getOrderNum());
                        AppMaterial material = materialMapper.selectById(ref.getAppMaterialId());
                        if(material !=null) {
                            recognition.setMaterialPath(material.getMaterialPath());
                            recognition.setMaterialName(material.getMaterialName());
                        }
                        return recognition;
                    })
                    .collect(Collectors.toList());
            dto.setAidedRecognitionUrl(recognitionList);
        }

        List<QuestionAnswer> answers = questionAnswerBiz.getAnswersByQuestionId(question.getId());
        if (!CollectionUtils.isEmpty(answers)) {
            List<QuestionAnswerDTO> answerDTOs = answers.stream()
                    .map(answer -> {
                        QuestionAnswerDTO answerDTO = new QuestionAnswerDTO();
                        BeanUtil.copyProperties(answer, answerDTO);
                        List<AppMaterialBusinessRef> answerMediaRefs = materialBusinessRefBiz.list(
                                new LambdaQueryWrapper<AppMaterialBusinessRef>()
                                        .eq(AppMaterialBusinessRef::getBusinessId, answer.getId())
                                        .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.ANSWER_MEDIA_BUSINESS.getCode())
                                        .eq(AppMaterialBusinessRef::getIsDeleted,YesOrNoEnum.NO.getCode())
                                        .orderByAsc(AppMaterialBusinessRef::getOrderNum)
                        );
                        if (!CollectionUtils.isEmpty(answerMediaRefs)) {
                            List<QuestionMediaDTO> answerMediaList = answerMediaRefs.stream()
                                    .map(ref -> {
                                        QuestionMediaDTO media = new QuestionMediaDTO();
                                        media.setMaterialId(ref.getAppMaterialId());
                                        media.setSortNum(ref.getOrderNum());
                                        AppMaterial material = materialMapper.selectById(ref.getAppMaterialId());
                                        if(material !=null){
                                            media.setMaterialPath(material.getMaterialPath());
                                            media.setMaterialName(material.getMaterialName());
                                        }
                                        return media;
                                    })
                                    .collect(Collectors.toList());
                            answerDTO.setMediaUrl(answerMediaList);
                        }
                        return answerDTO;
                    })
                    .collect(Collectors.toList());
            dto.setAnswers(answerDTOs);
        }

        // 如果是完形填空题，获取并转换空位区域列表
        if (Objects.equals(question.getType(), QuestionTypeEnum.CLOZE.getValue())) {
            List<QuestionBlankArea> blankAreas = questionBlankAreaBiz.getBlankAreasByQuestionId(question.getId());
            if (!CollectionUtils.isEmpty(blankAreas)) {
                List<QuestionBlankAreaDTO> blankAreaDTOs = new ArrayList<>();
                for (QuestionBlankArea area : blankAreas) {
                        QuestionBlankAreaDTO areaDTO = new QuestionBlankAreaDTO();
                    BeanUtil.copyProperties(area, areaDTO);
                    
                    // 获取并转换该空位区域的答案列表
                    List<QuestionAnswer> blankAnswers = questionAnswerBiz.getAnswersByBlankAreaId(area.getQuestionId(), area.getId());
                    if (!CollectionUtils.isEmpty(blankAnswers)) {
                        List<QuestionAnswerDTO> blankAnswerDTOs = blankAnswers.stream()
                                .map(answer -> {
                                    QuestionAnswerDTO answerDTO = new QuestionAnswerDTO();
                                    BeanUtil.copyProperties(answer, answerDTO);
                                    return answerDTO;
                    })
                    .collect(Collectors.toList());
                        areaDTO.setAnswers(blankAnswerDTOs);
                    }
                    blankAreaDTOs.add(areaDTO);
                }
            dto.setBlankAreas(blankAreaDTOs);
            }
        }

        return dto;
    }

    private Question convertToDomain(QuestionBO questionBO) {
        if (questionBO == null) {
            return null;
        }

        Question question = new Question();
        BeanUtil.copyProperties(questionBO, question);

        question.setType(questionBO.getType());

        if (questionBO.getAnswers() != null) {
            List<QuestionAnswer> answers = questionBO.getAnswers().stream()
                    .map(answerDTO -> {
                        QuestionAnswer answer = new QuestionAnswer();
                        BeanUtil.copyProperties(answerDTO, answer);
                        return answer;
                    })
                    .collect(Collectors.toList());
            question.setAnswers(answers);
        }

        if (questionBO.getBlankAreas() != null) {
            List<QuestionBlankArea> blankAreas = questionBO.getBlankAreas().stream()
                    .map(areaDTO -> {
                        QuestionBlankArea area = new QuestionBlankArea();
                        BeanUtil.copyProperties(areaDTO, area);
                        if (!CollectionUtils.isEmpty(areaDTO.getAnswers())) {
                            List<QuestionAnswer> blankAnswers = areaDTO.getAnswers().stream()
                                    .map(answerDTO -> {
                                        QuestionAnswer answer = new QuestionAnswer();
                                        BeanUtil.copyProperties(answerDTO, answer);
                                        return answer;
                                    })
                                    .collect(Collectors.toList());
                            area.setAnswers(blankAnswers);
                        }
                        return area;
                    })
                    .collect(Collectors.toList());
            question.setBlankAreas(blankAreas);
        }

        return question;
    }
} 