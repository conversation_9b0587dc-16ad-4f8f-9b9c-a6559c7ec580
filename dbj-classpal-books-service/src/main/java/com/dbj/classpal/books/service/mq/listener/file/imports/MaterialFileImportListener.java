package com.dbj.classpal.books.service.mq.listener.file.imports;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.sdk.service.mts20140618.models.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportQueryApiBO;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportUpdateApiBO;
import com.dbj.classpal.admin.client.dto.file.importfile.ExcelFileImportQueryApiDTO;
import com.dbj.classpal.books.common.bo.file.ExcelFileEntity;
import com.dbj.classpal.books.common.constant.QueueConstant;
import com.dbj.classpal.books.common.dto.file.MaterialFileConversionDTO;
import com.dbj.classpal.books.common.enums.FileTypeEnum;
import com.dbj.classpal.books.common.enums.MaterialTypeEnum;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBiz;
import com.dbj.classpal.books.service.entity.material.AppMaterial;
import com.dbj.classpal.books.service.remote.file.imports.MaterialFileImportRemoteService;
import com.dbj.classpal.books.service.service.material.IAppMaterialService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.mq.annotation.ExtractHeader;
import com.dbj.classpal.framework.mq.pool.DbjRabbitTemplate;
import com.dbj.classpal.framework.oss.config.MnsConfig;
import com.dbj.classpal.framework.oss.config.OssConfig;
import com.dbj.classpal.framework.oss.constant.MtsExchangeConstant;
import com.dbj.classpal.framework.oss.constant.MtsQueueConstant;
import com.dbj.classpal.framework.oss.constant.MtsRoutingKeyConstant;
import com.dbj.classpal.framework.oss.enums.MnsEnvEnum;
import com.dbj.classpal.framework.oss.enums.MtsAnalysisStatesEnum;
import com.dbj.classpal.framework.oss.enums.MtsTransCodeStatesEnum;
import com.dbj.classpal.framework.oss.utils.MtsUtil;
import com.dbj.classpal.framework.utils.enums.FileStatusEnum;
import com.dbj.classpal.framework.utils.util.PinYinUtil;
import com.google.gson.Gson;
import com.rabbitmq.client.Channel;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.core.task.TaskRejectedException;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ExcelFileListener
 * @date 2023-10-27 10:42
 **/
@Component
@Slf4j
public class MaterialFileImportListener {

    @Resource
    private MaterialFileImportRemoteService service;

    @Resource
    private IAppMaterialService appMaterialService;
    @Resource
    private OssConfig ossConfig;
    @Resource
    private MtsUtil mtsUtil;
    @Resource
    private MnsConfig mnsConfig;
    @Resource
    private IAppMaterialBiz business;
    @Resource
    private DbjRabbitTemplate dbjRabbitTemplate;


    //上传文件，提交分析模板任务，并通知查询转码结果
    @ExtractHeader
    @RabbitListener(queues = {QueueConstant.CLASSPAL_FILE_SERVICE_QUEUE_BOOKS_MATERIAL})
    public void materialFileHandler(ExcelFileEntity excelFileEntity, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag, Message message) throws IOException, BusinessException {
        try {
            ExcelFileImportQueryApiBO queryApiBO = new ExcelFileImportQueryApiBO();
            BeanUtil.copyProperties(excelFileEntity, queryApiBO);
            ExcelFileImportQueryApiDTO fileDomain = null;
            log.info("文件上传接收到消息:{}", excelFileEntity);
            if (excelFileEntity == null || excelFileEntity.getId() == null) {
                // 直接确认消息进行下一条的处理
                channel.basicAck(tag, false);  // 手动确认
                return;
            }
            fileDomain = service.getExcelFileImportById(queryApiBO);
            log.info("查询文件信息{}", JSON.toJSONString(fileDomain));
            if (fileDomain == null || StringUtils.isEmpty(fileDomain.getFileUrl()) || !Objects.equals(fileDomain.getStatus(), FileStatusEnum.PENDING_PROCESSING.getCode())) {
                return;
            }
            String fileUrl = fileDomain.getFileUrl();
            String fileName = fileUrl.replace(ossConfig.getUrlPrefix(),"").replace(ossConfig.getCdn(),"");
            boolean isSupported = FileTypeEnum.isSupportedType(fileName);
            //如果不支持类型,则改变文件上传状态
            if (!isSupported) {
                throw new BusinessException(FILE_TYPE_IMPORT_FAIL_CODE, FILE_TYPE_IMPORT_FAIL_MSG);
            }
            ExcelFileImportUpdateApiBO subFileDomain = new ExcelFileImportUpdateApiBO();
            subFileDomain.setId(fileDomain.getId());
            subFileDomain.setHandleStartTime(LocalDateTime.now());
            subFileDomain.setStatus(FileStatusEnum.PROCESSING.getCode());
            log.info("更改的文件信息:{}", JSONObject.toJSONString(subFileDomain));
            service.updateExcelFileImportById(subFileDomain);
            MaterialTypeEnum materialType = FileTypeEnum.getMaterialType(fileName);
            //如果不支持类型,则改变文件上传状态
            if (materialType == null) {
                throw new BusinessException(FILE_TYPE_IMPORT_FAIL_CODE, FILE_TYPE_IMPORT_FAIL_MSG);
            }
            log.info("文件类型:{}", JSONObject.toJSONString(materialType.getDesc()));
            //如果未找到保存的路径信息，处理失败
            String paramJson = fileDomain.getParamJson();
            if (StringUtils.isEmpty(paramJson)) {
                throw new BusinessException(FILE_DIRECTORY_NOT_EXIST_CODE, FILE_DIRECTORY_NOT_EXIST_MSG);
            }
            Gson gson = new Gson();
            //转换成单文件接收对象
            MaterialFileConversionDTO dto = gson.fromJson(paramJson, MaterialFileConversionDTO.class);
            String extension = fileName.substring(fileName.lastIndexOf(".") + 1);
            //如果文件夹id不为空，执行以下代码
            if (dto.getMaterialId() == null) {
                throw new BusinessException(FILE_DIRECTORY_NOT_EXIST_CODE, FILE_DIRECTORY_NOT_EXIST_MSG);
            }
            Integer parentId = dto.getMaterialId();
            //查询当前文件夹目录是否存在
            AppMaterial byId = business.getById(parentId);
            //不存在，处理失败
            if (byId == null) {
                throw new BusinessException(FILE_DIRECTORY_NOT_EXIST_CODE, FILE_DIRECTORY_NOT_EXIST_MSG);
            }
            String dirPath = dto.getDirPath();
            if (StringUtils.isNotEmpty(dirPath)) {
                String[] pathArr = dirPath.split("/");
                boolean rootNotExist = false;
                List<String> mkdirList = new ArrayList<>();
                for (int i = 0; i < pathArr.length; i++) {
                    List<AppMaterial> list = business.lambdaQuery().orderByDesc(AppMaterial::getOrderNum).eq(AppMaterial::getParentId, parentId).eq(AppMaterial::getMaterialType, MaterialTypeEnum.FILE_TYPE_DIR.getCode()).eq(AppMaterial::getMaterialName, pathArr[i]).list();
                    //判断是否存在文件夹，如果存在遍历查询上级文件夹id
                    if (CollectionUtils.isNotEmpty(list)) {
                        AppMaterial appMaterial = list.get(0);
                        parentId = appMaterial.getId();
                        continue;
                    }
                    //判断是否第一个文件夹就不存在
                    if (i == 0) {
                        rootNotExist = true;
                        break;
                    } else {
                        mkdirList.add(pathArr[i]);
                    }
                }

                if (rootNotExist) {
                    for (String path : pathArr) {
                        AppMaterial appMaterial = new AppMaterial();
                        appMaterial.setParentId(parentId);
                        appMaterial.setMaterialName(path);
                        appMaterial.setMaterialType(MaterialTypeEnum.FILE_TYPE_DIR.getCode());
                        appMaterial.setOrderNum(appMaterialService.createSort(parentId));
                        business.save(appMaterial);
                        parentId = appMaterial.getId();
                    }
                } else {
                    for (String path : mkdirList) {
                        AppMaterial appMaterial = new AppMaterial();
                        appMaterial.setParentId(parentId);
                        appMaterial.setMaterialName(path);
                        appMaterial.setMaterialType(MaterialTypeEnum.FILE_TYPE_DIR.getCode());
                        appMaterial.setOrderNum(appMaterialService.createSort(parentId));
                        business.save(appMaterial);
                        parentId = appMaterial.getId();
                    }
                }
            }
            excelFileEntity.setParentId(parentId);
            dto.setParentId(parentId);
            subFileDomain.setParamJson(JSON.toJSONString(dto));
            //如果不为视频或音频需要转码的类型,则直接保存文件
            if (!(materialType.getCode().equals(MaterialTypeEnum.FILE_TYPE_VIDEO.getCode())) && !(materialType.getCode().equals(MaterialTypeEnum.FILE_TYPE_AUDIO.getCode()))) {
                LambdaQueryWrapper<AppMaterial> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(AppMaterial::getParentId, parentId);
                queryWrapper.orderByDesc(AppMaterial::getOrderNum);
                //查询当前文件夹下其他文件（查询最大的排序号）
                List<AppMaterial> broList = business.list(queryWrapper);
                AppMaterial appMaterial = new AppMaterial();
                appMaterial.setParentId(parentId);
                appMaterial.setMaterialName(dto.getFileName());
                appMaterial.setMaterialType(Objects.requireNonNull(FileTypeEnum.getMaterialType(fileDomain.getFileUrl())).getCode());
                appMaterial.setMaterialExtension(extension);
                appMaterial.setMaterialPath(fileDomain.getFileUrl());
                appMaterial.setMaterialOriginUrl(fileDomain.getFileUrl());
                if (dto.getMd5() != null) {
                    appMaterial.setMaterialMd5(dto.getMd5());
                }
                if (dto.getSize() != null) {
                    appMaterial.setMaterialSize(dto.getSize());
                }
                //给新增的资源赋予排序号
                if (CollectionUtils.isNotEmpty(broList)) {
                    AppMaterial orderNum = broList.get(0);
                    appMaterial.setOrderNum(orderNum.getOrderNum() + 1);
                }
                //新增素材资源数据
                business.save(appMaterial);
                //成功后修改上传文件状态等相关信息
                subFileDomain.setStatus(FileStatusEnum.PROCESSED.getCode());
                subFileDomain.setHandleEndTime(LocalDateTime.now());
                subFileDomain.setProcessedUrl(fileDomain.getFileUrl());
                ExcelFileImportUpdateApiBO updateApiBO = new ExcelFileImportUpdateApiBO();
                BeanUtil.copyProperties(subFileDomain, updateApiBO);
                service.updateExcelFileImportById(updateApiBO);
                return;
            }
            //如果为音频文件,提交mts音频转码任务
            if (materialType.getCode().equals(MaterialTypeEnum.FILE_TYPE_AUDIO.getCode())) {
                log.info("文件类型:{}", JSONObject.toJSONString(materialType.getDesc()));
                log.info("音频文件提交转码任务:{}", JSONObject.toJSONString(LocalDateTime.now()));
                SubmitJobsResponse response = mtsUtil.translateAudio(fileName).get();
                if (response == null) {
                    throw new BusinessException(FILE_CONVERSION_ERROR_CODE, FILE_CONVERSION_ERROR_MSG);
                }
                if (response.getStatusCode() != 200) {
                    throw new BusinessException(FILE_CONVERSION_ERROR_CODE, FILE_CONVERSION_ERROR_MSG);
                }
                String jobId = response.getBody().getJobResultList().getJobResult().get(0).getJob().getJobId();
                subFileDomain.setTransSubmitJobId(jobId);
                subFileDomain.setHandleEndTime(LocalDateTime.now());
                service.updateExcelFileImportById(subFileDomain);
                log.info("音频文件结束提交转码任务:{}", JSONObject.toJSONString(LocalDateTime.now()));
                excelFileEntity.setTransCodeJobId(jobId);
                dbjRabbitTemplate.sendExchangeEntityMessage(excelFileEntity, MtsExchangeConstant.MTS_TASK_EXCHANGE, MtsRoutingKeyConstant.MTS_QUERY_JOB_ROUTING_KEY);
                return;
            }
            //如果为视频文件,因为需要适配系统智能预制模板,所以需要先“分析”，后根据分析提供的推荐模板进行转码
            if (materialType.getCode().equals(MaterialTypeEnum.FILE_TYPE_VIDEO.getCode())) {
                log.info("文件类型:{}", JSONObject.toJSONString(materialType.getDesc()));
                log.info("视频文件开始提交分析转码模板任务:{}", JSONObject.toJSONString(LocalDateTime.now()));
                SubmitAnalysisJobResponse response = mtsUtil.subAnalysisVideoTemplate(fileName).get();
                if (response == null) {
                    throw new BusinessException(FILE_ANALYSIS_ERROR_CODE, FILE_ANALYSIS_ERROR_MSG);
                }
                if (response.getStatusCode() != 200) {
                    throw new BusinessException(response.getBody().getAnalysisJob().getMessage());
                }
                String state = response.getBody().getAnalysisJob().getState();
                if (state.equals(MtsAnalysisStatesEnum.STATES_FAIL.getState())) {
                    throw new BusinessException(response.getBody().getAnalysisJob().getMessage());
                }
                subFileDomain.setAnalysisSubmitJobId(response.getBody().getAnalysisJob().getId());
                subFileDomain.setHandleEndTime(LocalDateTime.now());
                service.updateExcelFileImportById(subFileDomain);
                excelFileEntity.setAnalysisJobId(response.getBody().getAnalysisJob().getId());
                excelFileEntity.setFileName(fileName);
                log.info("视频文件结束提交分析转码模板任务:{}", JSONObject.toJSONString(LocalDateTime.now()));
                if (!(mnsConfig.getEnv().equals(MnsEnvEnum.ENV_PRO.getDev()))) {
                    log.info("视频文件发送mq通知查询分析转码模板结果:{}", JSONObject.toJSONString(LocalDateTime.now()));
                    dbjRabbitTemplate.sendExchangeEntityMessage(excelFileEntity, MtsExchangeConstant.MTS_TASK_EXCHANGE, MtsRoutingKeyConstant.MTS_QUERY_ANALYSIS_JOB_ROUTING_KEY);
                }
            }

        } catch (Exception e) {
            log.error(e.getMessage());
            ExcelFileImportUpdateApiBO subFileDomain = new ExcelFileImportUpdateApiBO();
            subFileDomain.setId(excelFileEntity.getId());
            subFileDomain.setHandleEndTime(LocalDateTime.now());
            subFileDomain.setStatus(FileStatusEnum.PROCESSING_FAILED_SYS.getCode());
            subFileDomain.setErrorMsg(e.getMessage());
            service.updateExcelFileImportById(subFileDomain);
        } finally {
            channel.basicAck(tag, false);  // 拒绝并重新入队
        }
    }


    //查询分析模板任务结果，返回最优模板id并通知转码
    @ExtractHeader
    @RabbitListener(queues = {MtsQueueConstant.MTS_QUERY_ANALYSIS_JOB_QUEUE})
    public void analysisMaterial(ExcelFileEntity excelFileEntity, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag, Message message) throws IOException, BusinessException {
        try {
            log.info("接收到通知查询分析模板结果消息:{}", excelFileEntity);
            if (excelFileEntity == null || excelFileEntity.getId() == null) {
                // 直接确认消息进行下一条的处理
                channel.basicAck(tag, false);  // 手动确认
                return;
            }
            log.info("开始查询分析模板结果消息:{}", JSONObject.toJSONString(LocalDateTime.now()));
            CompletableFuture<QueryAnalysisJobListResponse> analysisVideoTemplate = mtsUtil.getAnalysisVideoTemplate(excelFileEntity.getAnalysisJobId());
            QueryAnalysisJobListResponse response = analysisVideoTemplate.get();
            ExcelFileImportUpdateApiBO subFileDomain = new ExcelFileImportUpdateApiBO();
            subFileDomain.setId(excelFileEntity.getId());
            String state = response.getBody().getAnalysisJobList().getAnalysisJob().get(0).getState();
            Long percent = response.getBody().getAnalysisJobList().getAnalysisJob().get(0).getPercent();
            log.info("查询的分析结果状态:{}", state);
            log.info("查询的分析结果进度:{}", percent);
            if (state.equals(MtsAnalysisStatesEnum.STATES_FAIL.getState())) {
                throw new BusinessException(FILE_ANALYSIS_ERROR_CODE, FILE_ANALYSIS_ERROR_MSG);
            }
            if (!state.equals(MtsAnalysisStatesEnum.STATES_SUCCESS.getState())) {
                log.info("未完成分析任务重新入队:{}", JSONObject.toJSONString(LocalDateTime.now()));
                throw new TaskRejectedException("未完成分析任务重新入队");
            }
            List<QueryAnalysisJobListResponseBody.Template> templates = response.getBody().getAnalysisJobList().getAnalysisJob().get(0).getTemplateList().getTemplate().stream().filter(d -> d.getName().contains("M3U8")).sorted(Comparator.comparing(QueryAnalysisJobListResponseBody.Template::getId).reversed()).collect(Collectors.toList());
            if (templates.isEmpty()) {
                throw new CompletionException(new IllegalStateException("无可用模板"));
            }
            subFileDomain.setHandleEndTime(LocalDateTime.now());
            subFileDomain.setAnalysisQueryJobId(response.getBody().getAnalysisJobList().getAnalysisJob().get(0).getId());
            service.updateExcelFileImportById(subFileDomain);
            excelFileEntity.setTemplateId(templates.get(0).getId());
            log.info("已完成分析,发送本地mq通知转码任务:{}", JSONObject.toJSONString(LocalDateTime.now()));
            dbjRabbitTemplate.sendExchangeEntityMessage(excelFileEntity, MtsExchangeConstant.MTS_TASK_EXCHANGE, MtsRoutingKeyConstant.MTS_SUBMIT_JOB_ROUTING_KEY);
            channel.basicAck(tag, false);
        } catch (TaskRejectedException e) {
            channel.basicReject(tag, true);
        } catch (Exception e) {
            log.error(e.getMessage());
            ExcelFileImportUpdateApiBO subFileDomain = new ExcelFileImportUpdateApiBO();
            subFileDomain.setId(excelFileEntity.getId());
            subFileDomain.setHandleEndTime(LocalDateTime.now());
            subFileDomain.setStatus(FileStatusEnum.PROCESSING_FAILED_SYS.getCode());
            subFileDomain.setErrorMsg(e.getMessage());
            service.updateExcelFileImportById(subFileDomain);
            channel.basicAck(tag, false);
        }
    }

    //转码任务
    @ExtractHeader
    @RabbitListener(queues = {MtsQueueConstant.MTS_SUBMIT_JOB_QUEUE})
    public void transcodeMaterial(ExcelFileEntity excelFileEntity, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag, Message message) throws IOException, BusinessException {
        try {
            log.info("接收到通知视频转码消息:{}", excelFileEntity);
            if (excelFileEntity == null || excelFileEntity.getId() == null) {
                // 直接确认消息进行下一条的处理
                channel.basicAck(tag, false);  // 手动确认
                return;
            }
            log.info("开始视频转码:{}", JSONObject.toJSONString(LocalDateTime.now()));
            CompletableFuture<SubmitJobsResponse> future = mtsUtil.submitTranscodeJob(excelFileEntity.getTemplateId(), excelFileEntity.getFileName());
            SubmitJobsResponse response = future.get();
            if (response == null) {
                throw new BusinessException(FILE_CONVERSION_ERROR_CODE, FILE_CONVERSION_ERROR_MSG);
            }
            if (response.getStatusCode() != 200) {
                throw new BusinessException(FILE_CONVERSION_ERROR_CODE, FILE_CONVERSION_ERROR_MSG);
            }
            Boolean success = response.getBody().getJobResultList().getJobResult().get(0).getSuccess();
            if (!success) {
                throw new BusinessException(FILE_CONVERSION_ERROR_CODE, response.getBody().getJobResultList().getJobResult().get(0).getMessage());
            }
            String jobId = response.getBody().getJobResultList().getJobResult().get(0).getJob().getJobId();
            ExcelFileImportUpdateApiBO subFileDomain = new ExcelFileImportUpdateApiBO();
            subFileDomain.setId(excelFileEntity.getId());
            subFileDomain.setHandleEndTime(LocalDateTime.now());
            subFileDomain.setTransSubmitJobId(jobId);
            service.updateExcelFileImportById(subFileDomain);
            excelFileEntity.setTransCodeJobId(jobId);
            if (!(mnsConfig.getEnv().equals(MnsEnvEnum.ENV_PRO.getDev()))) {
                log.info("发送本地mq通知查询视频转码结果消息:{}", JSONObject.toJSONString(LocalDateTime.now()));
                dbjRabbitTemplate.sendExchangeEntityMessage(excelFileEntity, MtsExchangeConstant.MTS_TASK_EXCHANGE, MtsRoutingKeyConstant.MTS_QUERY_JOB_ROUTING_KEY);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            ExcelFileImportUpdateApiBO subFileDomain = new ExcelFileImportUpdateApiBO();
            subFileDomain.setId(excelFileEntity.getId());
            subFileDomain.setHandleEndTime(LocalDateTime.now());
            subFileDomain.setStatus(FileStatusEnum.PROCESSING_FAILED_SYS.getCode());
            subFileDomain.setErrorMsg(e.getMessage());
            service.updateExcelFileImportById(subFileDomain);
        } finally {
            channel.basicAck(tag, false);  // 拒绝并重新入队
        }
    }

    //查询转码结果任务
    @ExtractHeader
    @RabbitListener(queues = {MtsQueueConstant.MTS_QUERY_JOB_QUEUE})
    @Transactional(rollbackFor = Exception.class)
    public void queryTransCodeAndSaveMaterial(ExcelFileEntity excelFileEntity, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag, Message message) throws IOException, BusinessException {
        try {
            log.info("接收到通知查询视频转码结果消息:{}", excelFileEntity);
            if (excelFileEntity == null || excelFileEntity.getId() == null) {
                // 直接确认消息进行下一条的处理
                channel.basicAck(tag, false);  // 手动确认
                return;
            }
            log.info("开始查询视频转码结果:{}", JSONObject.toJSONString(LocalDateTime.now()));
            QueryJobListResponse response = mtsUtil.queryJobListResponse(excelFileEntity.getTransCodeJobId()).get();
            QueryJobListResponseBody.Job job = response.getBody().getJobList().getJob().get(0);
            String jobId = job.getJobId();
            String state = job.getState();
            Long percent = job.getPercent();
            log.info("查询的转码结果状态:{}", state);
            log.info("查询的转码结果进度:{}", percent);
            ExcelFileImportUpdateApiBO subFileDomain = new ExcelFileImportUpdateApiBO();
            subFileDomain.setId(excelFileEntity.getId());
            if (!state.equals(MtsTransCodeStatesEnum.STATES_TRANSCODE_SUCCESS.getState())) {
                if (state.equals(MtsTransCodeStatesEnum.STATES_TRANSCODE_FAIL.getState())) {
                    throw new BusinessException(FILE_CONVERSION_ERROR_MSG);
                }
                log.info("未完成转码任务重新入队:{}", JSONObject.toJSONString(LocalDateTime.now()));
                throw new TaskRejectedException("未完成转码任务重新入队");
            }
            subFileDomain.setHandleEndTime(LocalDateTime.now());
            subFileDomain.setTransQueryJobId(job.getJobId());
            service.updateExcelFileImportById(subFileDomain);
            log.info("已完成查询转码任务,开始生成素材中心数据:{}", JSONObject.toJSONString(LocalDateTime.now()));
            //转码任务响应内容
            QueryJobListResponseBody.OutputFile outputFile = job.getOutput().getOutputFile();
            //转码后文件全名（含后缀）
            String fileFullName = outputFile.getObject();
            //转码文件oss全路径
            String resultUrl = ossConfig.getCdn() + fileFullName;

            ExcelFileImportQueryApiBO excelFileImportQueryApiBO = new ExcelFileImportQueryApiBO();
            excelFileImportQueryApiBO.setId(excelFileEntity.getId());
            ExcelFileImportQueryApiDTO file = service.getExcelFileImportById(excelFileImportQueryApiBO);
            if (ObjectUtil.isNotEmpty(file)) {
                AppMaterial byId = business.getById(excelFileEntity.getParentId());
                //不存在，处理失败并跳过
                if (byId == null) {
                    throw new BusinessException(FILE_DIRECTORY_NOT_EXIST_MSG);
                }
                //上传时保存的文件路径
                String paramJson = file.getParamJson();
                //如果未找到保存的路径信息，处理失败并跳过
                if (StringUtils.isEmpty(paramJson)) {
                    throw new BusinessException(FILE_DIRECTORY_NOT_EXIST_MSG);
                }
                Integer parentId = excelFileEntity.getParentId();
                Gson gson = new Gson();
                //转换成单文件接收对象
                MaterialFileConversionDTO dto = gson.fromJson(paramJson, MaterialFileConversionDTO.class);
                AppMaterial appMaterial = new AppMaterial();
                appMaterial.setParentId(parentId);
                appMaterial.setMaterialName(dto.getFileName());
                MaterialTypeEnum materialType = FileTypeEnum.getMaterialType(file.getFileUrl());
                if (materialType != null) {
                    appMaterial.setMaterialType(materialType.getCode());
                }
                appMaterial.setMaterialPath(resultUrl);
                appMaterial.setMaterialOriginUrl(file.getFileUrl());
                appMaterial.setMaterialMd5(dto.getMd5());
                //获取计算oss转码后文件大小（kb）
                BigDecimal size = new BigDecimal(job.getOutput().getProperties().getFileSize());
                BigDecimal change = new BigDecimal(1024);
                BigDecimal resultSize = size.divide(change, 2, RoundingMode.HALF_UP);
                double materialSize = resultSize.doubleValue();
                appMaterial.setMaterialSize(materialSize);
                appMaterial.setMaterialDuration(Integer.valueOf(job.getOutput().getProperties().getDuration()));
                appMaterial.setJobId(jobId);
                appMaterial.setFirstPinYin(PinYinUtil.getFirstCharFirstLetter(dto.getFileName()));
                appMaterial.setPinYin(PinYinUtil.convertChineseToPinyin(dto.getFileName()));
                //给新增的资源赋予排序号
                appMaterial.setOrderNum(appMaterialService.createSort(parentId));
                int saveCount = business.lambdaQuery().eq(AppMaterial::getJobId, jobId).count().intValue();
                if (saveCount <= 0) {
                    //新增素材资源数据
                    if (business.save(appMaterial)) {
                        //成功后修改上传文件状态等相关信息
                        file.setStatus(FileStatusEnum.PROCESSED.getCode());
                        file.setHandleEndTime(LocalDateTime.now());
                        file.setProcessedUrl(resultUrl);
                        ExcelFileImportUpdateApiBO updateApiBO = new ExcelFileImportUpdateApiBO();
                        BeanUtil.copyProperties(file, updateApiBO);
                        service.updateExcelFileImportById(updateApiBO);
                        log.info("生成素材中心数据完成:{}", JSONObject.toJSONString(LocalDateTime.now()));
                    }
                }
                channel.basicAck(tag, false);
            }
        } catch (TaskRejectedException e) {
            channel.basicReject(tag, true);
        } catch (Exception e) {
            log.error(e.getMessage());
            ExcelFileImportUpdateApiBO updateApiBO = new ExcelFileImportUpdateApiBO();
            updateApiBO.setId(excelFileEntity.getId());
            updateApiBO.setErrorMsg(e.getMessage());
            updateApiBO.setStatus(FileStatusEnum.PROCESSING_FAILED_SYS.getCode());
            updateApiBO.setHandleEndTime(LocalDateTime.now());
            service.updateExcelFileImportById(updateApiBO);
            channel.basicAck(tag, false);
        }
    }


}
