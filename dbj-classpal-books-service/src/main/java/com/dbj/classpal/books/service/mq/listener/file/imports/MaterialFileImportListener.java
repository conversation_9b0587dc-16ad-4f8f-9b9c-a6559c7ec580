package com.dbj.classpal.books.service.mq.listener.file.imports;

import com.dbj.classpal.books.common.bo.file.ExcelFileEntity;
import com.dbj.classpal.books.common.constant.QueueConstant;
import com.dbj.classpal.books.service.service.material.IAppMaterialService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.mq.annotation.ExtractHeader;
import com.dbj.classpal.framework.oss.constant.MtsQueueConstant;
import com.rabbitmq.client.Channel;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.core.task.TaskRejectedException;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ExcelFileListener
 * @date 2023-10-27 10:42
 **/
@Component
@Slf4j
public class MaterialFileImportListener {

    @Resource
    private IAppMaterialService appMaterialService;


    //上传文件，提交分析模板任务，并通知查询转码结果
    @ExtractHeader
    @RabbitListener(queues = {QueueConstant.CLASSPAL_FILE_SERVICE_QUEUE_BOOKS_MATERIAL})
    public void materialFileHandler(ExcelFileEntity excelFileEntity, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag, Message message) throws IOException {
        try{
            log.info("文件上传接收到消息:{}", excelFileEntity);
            if (excelFileEntity == null || excelFileEntity.getId() == null) {
                // 直接确认消息进行下一条的处理
                channel.basicAck(tag, false);  // 手动确认
                return;
            }
            appMaterialService.getMaterialFileHandler(excelFileEntity);
            channel.basicAck(tag, false);  // 拒绝并重新入队
        }catch (Exception e){
            log.error(e.getMessage());
            channel.basicAck(tag, false);  // 拒绝并重新入队
        }

    }

    //查询分析模板任务结果，返回最优模板id并通知转码
    @ExtractHeader
    @RabbitListener(queues = {MtsQueueConstant.MTS_QUERY_ANALYSIS_JOB_QUEUE})
    public void analysisMaterial(ExcelFileEntity excelFileEntity, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag, Message message) throws IOException {
        try {
            log.info("接收到通知查询分析模板结果消息:{}", excelFileEntity);
            if (excelFileEntity == null || excelFileEntity.getId() == null) {
                // 直接确认消息进行下一条的处理
                channel.basicAck(tag, false);  // 手动确认
                return;
            }
            appMaterialService.getAnalysisMaterial(excelFileEntity);
            channel.basicAck(tag, false);
        } catch (Exception e) {
            if (e instanceof TaskRejectedException) {
                channel.basicReject(tag, true);
            }else{
                log.error(e.getMessage());
                channel.basicAck(tag, false);
            }
        }
    }

    //转码任务
    @ExtractHeader
    @RabbitListener(queues = {MtsQueueConstant.MTS_SUBMIT_JOB_QUEUE})
    public void transcodeMaterial(ExcelFileEntity excelFileEntity, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag, Message message) throws IOException, BusinessException {
        try {
            log.info("接收到通知视频转码消息:{}", excelFileEntity);
            if (excelFileEntity == null || excelFileEntity.getId() == null) {
                // 直接确认消息进行下一条的处理
                channel.basicAck(tag, false);  // 手动确认
                return;
            }
            appMaterialService.getTranscodeMaterial(excelFileEntity);
            channel.basicAck(tag, false);  // 拒绝并重新入队
        } catch (Exception e) {
            log.error(e.getMessage());
            channel.basicAck(tag, false);  // 拒绝并重新入队
        }
    }

    //查询转码结果任务
    @ExtractHeader
    @RabbitListener(queues = {MtsQueueConstant.MTS_QUERY_JOB_QUEUE})
    public void queryTransCodeAndSaveMaterial(ExcelFileEntity excelFileEntity, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag, Message message) throws IOException, BusinessException {
        try {
            log.info("接收到通知查询视频转码结果消息:{}", excelFileEntity);
            if (excelFileEntity == null || excelFileEntity.getId() == null) {
                // 直接确认消息进行下一条的处理
                channel.basicAck(tag, false);  // 手动确认
                return;
            }
            appMaterialService.getQueryTransCodeAndSaveMaterial(excelFileEntity);
            channel.basicAck(tag, false);
        } catch (Exception e) {
            if (e instanceof TaskRejectedException) {
                channel.basicReject(tag, true);
            } else {
                log.error(e.getMessage());
                channel.basicAck(tag, false);
            }
        }
    }
}
