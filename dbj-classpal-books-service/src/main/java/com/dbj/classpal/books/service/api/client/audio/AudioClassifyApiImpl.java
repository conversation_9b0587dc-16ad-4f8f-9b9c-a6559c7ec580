package com.dbj.classpal.books.service.api.client.audio;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.dbj.classpal.books.client.api.audio.AudioClassifyApi;
import com.dbj.classpal.books.client.bo.audio.AudioClassifyAddBO;
import com.dbj.classpal.books.client.bo.audio.AudioClassifyDelBO;
import com.dbj.classpal.books.client.bo.audio.AudioClassifyMoveBO;
import com.dbj.classpal.books.client.bo.audio.AudioClassifyQueryBO;
import com.dbj.classpal.books.client.dto.audio.AudioClassifyDTO;
import com.dbj.classpal.books.common.enums.audio.AudioIntroStatus;
import com.dbj.classpal.books.service.biz.audio.IAudioClassifyBiz;
import com.dbj.classpal.books.service.biz.audio.IAudioIntroBiz;
import com.dbj.classpal.books.service.entity.audio.AudioClassify;
import com.dbj.classpal.books.service.entity.audio.AudioIntro;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.commons.utils.Assert;
import com.dbj.classpal.framework.utils.util.TreeUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/6/27 17:14
 */
@RestController
@RequiredArgsConstructor
public class AudioClassifyApiImpl implements AudioClassifyApi {

    private final IAudioClassifyBiz audioClassifyBiz;
    private final IAudioIntroBiz audioIntroBiz;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RestResponse<Boolean> save(AudioClassifyAddBO bo) throws BusinessException {
        if (bo.getId() == null) {
            Assert.notNull(bo.getParentId(), "父级id不能为空");
        }
        AudioClassify classify = BeanUtil.copyProperties(bo, AudioClassify.class);
        return RestResponse.success(audioClassifyBiz.saveOrUpdate(classify));
    }

    @Override
    public RestResponse<List<AudioClassifyDTO>> list(AudioClassifyQueryBO bo) throws BusinessException {
        List<AudioClassify> list = audioClassifyBiz.lambdaQuery()
                .like(StrUtil.isNotEmpty(bo.getName()), AudioClassify::getClassifyName, bo.getName())
                .orderByDesc(AudioClassify::getIsRoot)
                .orderByDesc(AudioClassify::getDefaultType)
                .orderByAsc(AudioClassify::getCreateTime)
                .list();
        if (CollUtil.isEmpty(list)) {
            return RestResponse.success(List.of());
        }
        List<Integer> ids = list.stream().map(AudioClassify::getId).toList();
        List<AudioIntro> introList = audioIntroBiz.lambdaQuery()
                .in(AudioIntro::getAudioClassifyId, ids)
                .list();
        Map<Integer, Long> categoryCountMap = introList.stream().collect(Collectors.groupingBy(
                AudioIntro::getAudioClassifyId, Collectors.mapping(AudioIntro::getId, Collectors.counting())));
        List<AudioClassifyDTO> classifyList = TreeUtil.convertTree(BeanUtil.copyToList(list, AudioClassifyDTO.class));
        statChildrenCount(classifyList, categoryCountMap);
        return RestResponse.success(classifyList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RestResponse<Boolean> remove(AudioClassifyDelBO bo) throws BusinessException {
        Set<Integer> children = audioClassifyBiz.getChildren(bo.getIds());
        Assert.isFalse(audioIntroBiz.lambdaQuery()
                .in(AudioIntro::getAudioClassifyId, children)
                .exists(), "分类下存在数据，无法删除");
        return RestResponse.success(audioClassifyBiz.removeBatchByIds(bo.getIds()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RestResponse<Boolean> move(AudioClassifyMoveBO bo) throws BusinessException {
        boolean exists = audioIntroBiz.lambdaQuery()
                .in(AudioIntro::getAudioClassifyId, bo.getIds())
                .eq(AudioIntro::getStatus, AudioIntroStatus.COMPOUNDING.getValue())
                .exists();
        Assert.isFalse(exists, "存在合成中的任务，请取消后重试");
        Set<Integer> parentIds = audioClassifyBiz.getParentIds(bo.getParentId());
        Assert.isTrue(CollUtil.isEmpty(parentIds) || parentIds.stream().noneMatch(id -> bo.getIds().contains(id)), "无法移动到子节点");
        return RestResponse.success(audioClassifyBiz.lambdaUpdate()
                .set(AudioClassify::getParentId, bo.getParentId())
                .in(AudioClassify::getId, bo.getIds())
                .update());
    }

    private void statChildrenCount(List<AudioClassifyDTO> classifyList, Map<Integer, Long> categoryCountMap) {
        for (AudioClassifyDTO classify : classifyList) {
            List<AudioClassifyDTO> children = classify.getChildren();
            int count = 0;
            if (CollUtil.isNotEmpty(children)) {
                statChildrenCount(children, categoryCountMap);
                count = children.stream().mapToInt(AudioClassifyDTO::getCount).sum();
            }
            Long c = categoryCountMap.getOrDefault(classify.getId(), 0L);
            classify.setCount(c.intValue() + count);
        }
    }
}
