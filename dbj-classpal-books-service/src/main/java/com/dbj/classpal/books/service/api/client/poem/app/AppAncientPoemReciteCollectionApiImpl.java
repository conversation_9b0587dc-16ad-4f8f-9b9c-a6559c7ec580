package com.dbj.classpal.books.service.api.client.poem.app;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.poem.app.AppAncientPoemReciteCollectionApi;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionBO;
import com.dbj.classpal.books.client.bo.poem.app.AppAncientPoemReciteCollectionPageBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemReciteCollectionDTO;
import com.dbj.classpal.books.client.dto.poem.app.AppAncientPoemReciteCollectionPageDTO;
import com.dbj.classpal.books.common.dto.poem.AncientPoemBusinessRefCountDTO;
import com.dbj.classpal.books.common.enums.poem.PoemBusinessTypeEnum;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemBusinessRefBiz;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemReciteCollectionBiz;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className AppAncientPoemReciteCollectionApiImpl
 * @description
 * @date 2025-05-28 10:07
 **/
@RestController
public class AppAncientPoemReciteCollectionApiImpl  implements AppAncientPoemReciteCollectionApi {
    @Resource
    private IAncientPoemReciteCollectionBiz ancientPoemReciteCollectionBiz;
    @Resource
    private IAncientPoemBusinessRefBiz ancientBusinessRefBiz;
    @Override
    public RestResponse<Page<AppAncientPoemReciteCollectionPageDTO>> pageAncientPoemReciteCollection(PageInfo<AppAncientPoemReciteCollectionPageBO> pageInfo) {
        Page<AppAncientPoemReciteCollectionPageDTO> page = ancientPoemReciteCollectionBiz.pageAncientPoemReciteCollection(pageInfo);
        List<AppAncientPoemReciteCollectionPageDTO> ancientPoemReciteCollectionDTOList = page.getRecords();
        if(CollectionUtils.isNotEmpty(ancientPoemReciteCollectionDTOList)){
            List<Integer> ids = ancientPoemReciteCollectionDTOList.stream().map(AppAncientPoemReciteCollectionPageDTO::getId).collect(Collectors.toList());
            List<AncientPoemBusinessRefCountDTO> ancientPoemBusinessRefCountDTOList = ancientBusinessRefBiz.getCount(ids, PoemBusinessTypeEnum.ANCIENT_POEM_RECITE_BUSINESS.getCode());
            if(CollectionUtils.isNotEmpty(ancientPoemBusinessRefCountDTOList)){
                Map<Integer,Integer> poemNumMap = ancientPoemBusinessRefCountDTOList.stream().collect(Collectors.toMap(AncientPoemBusinessRefCountDTO::getBusinessId,AncientPoemBusinessRefCountDTO::getPoemNum));
                ancientPoemReciteCollectionDTOList.forEach(
                        ancientPoemReciteCollectionPageDTO -> ancientPoemReciteCollectionPageDTO.setPoemNum(poemNumMap.getOrDefault(ancientPoemReciteCollectionPageDTO.getId(),0))
                );

            }
        }
        return RestResponse.success(page);
    }
}
