package com.dbj.classpal.books.service.api.client.ebooks;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.ebooks.AppEBookApi;
import com.dbj.classpal.books.client.bo.ebooks.*;
import com.dbj.classpal.books.client.bo.share.GetShareInfoApiBO;
import com.dbj.classpal.books.client.bo.share.RemoveShareUrlApiBO;
import com.dbj.classpal.books.client.dto.ebooks.*;
import com.dbj.classpal.books.client.dto.ebooks.H5LandingPageApiDTO;
import com.dbj.classpal.books.client.dto.ebooks.ShareUrlResultApiDTO;
import com.dbj.classpal.books.common.bo.ebooks.*;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookAsyncProcessDTO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookDTO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookResourceDTO;
import com.dbj.classpal.books.common.dto.ebooks.PdfTaskStatusDTO;
import com.dbj.classpal.books.common.dto.ebooks.H5LandingPageDTO;
import com.dbj.classpal.books.common.dto.ebooks.ShareUrlResultDTO;
import com.dbj.classpal.books.common.enums.ResourceTypeEnum;
import com.dbj.classpal.books.service.biz.shorts.IShortUrlInfoBiz;
import com.dbj.classpal.books.service.service.ebooks.IAppEBookResourceService;
import com.dbj.classpal.books.service.service.ebooks.IAppEBookService;
import com.dbj.classpal.books.service.service.ebooks.IH5LandingPageService;
import com.dbj.classpal.framework.commons.converter.PageInfoConverter;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 单书API接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Slf4j
@RestController
public class AppEBookApiImpl implements AppEBookApi {

    // 静态常量定义
    private static final Integer MAX_BATCH_QUERY_SIZE = 100;

    @Resource
    private IAppEBookService eBookService;

    @Resource
    private IAppEBookResourceService eBookResourceService;

    @Resource
    private IH5LandingPageService h5LandingPageService;

    @Resource
    private IShortUrlInfoBiz shortUrlInfoBiz;

    @Resource
    private HttpServletResponse response;

    @Override
    public RestResponse<Page<AppEBookApiDTO>> page(PageInfo<AppEBookQueryApiBO> pageRequest) throws BusinessException {
        log.info("分页查询单书列表 入参：{}", JSON.toJSONString(pageRequest));

        PageInfo<AppEBookQueryBO> pageBO = PageInfoConverter.convertPageInfo(pageRequest, AppEBookQueryBO.class);

        Page<AppEBookDTO> page = eBookService.page(pageBO);

        log.info("分页查询单书列表 返回记录：{}", page.getRecords());
        return RestResponse.success((Page<AppEBookApiDTO>) page.convert(vo -> {
            AppEBookApiDTO dto = new AppEBookApiDTO();
            BeanUtil.copyProperties(vo, dto);
            return dto;
        }));
    }

    @Override
    public RestResponse<AppEBookApiDTO> detail(AppEBookIdApiBO idBO) throws BusinessException {
        log.info("查询单书详情 入参：{}", JSON.toJSONString(idBO));
        AppEBookDTO dto = eBookService.detail(idBO.getId());
        AppEBookApiDTO apiDTO = BeanUtil.copyProperties(dto, AppEBookApiDTO.class);
        log.info("查询单书详情 结果：{}", JSON.toJSONString(apiDTO));
        return RestResponse.success(apiDTO);
    }

    @Override
    public RestResponse<Integer> save(AppEBookSaveApiBO saveBO) throws BusinessException {
        log.info("新增单书 入参：{}", JSON.toJSONString(saveBO));
        AppEBookSaveBO serviceSaveBO = BeanUtil.copyProperties(saveBO, AppEBookSaveBO.class);
        Integer id = eBookService.save(serviceSaveBO);
        log.info("新增单书 结果：{}", id);
        return RestResponse.success(id);
    }

    @Override
    public RestResponse<Boolean> update(AppEBookUpdateApiBO updateBO) throws BusinessException {
        log.info("更新单书 入参：{}", JSON.toJSONString(updateBO));
        AppEBookUpdateBO serviceUpdateBO = BeanUtil.copyProperties(updateBO, AppEBookUpdateBO.class);
        boolean result = eBookService.update(serviceUpdateBO);
        log.info("更新单书 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> delete(AppEBookIdApiBO idBO) throws BusinessException {
        log.info("删除单书 入参：{}", JSON.toJSONString(idBO));
        boolean result = eBookService.delete(idBO.getId());
        log.info("删除单书 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> deleteBatch(AppEBookIdsApiBO idsBO) throws BusinessException {
        log.info("批量删除单书 入参：{}", JSON.toJSONString(idsBO));
        boolean result = eBookService.deleteBatch(idsBO.getIds());
        log.info("批量删除单书 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> enableBatch(AppEBookIdsApiBO idsBO) throws BusinessException {
        log.info("批量启用单书 入参：{}", JSON.toJSONString(idsBO));
        boolean result = eBookService.enableBatch(idsBO.getIds());
        log.info("批量启用单书 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> disableBatch(AppEBookIdsApiBO idsBO) throws BusinessException {
        log.info("批量禁用单书 入参：{}", JSON.toJSONString(idsBO));
        boolean result = eBookService.disableBatch(idsBO.getIds());
        log.info("批量禁用单书 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> allowDownloadBatch(AppEBookIdsApiBO idsBO) throws BusinessException {
        log.info("批量允许下载单书 入参：{}", JSON.toJSONString(idsBO));
        boolean result = eBookService.allowDownloadBatch(idsBO.getIds());
        log.info("批量允许下载单书 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> disableDownloadBatch(AppEBookIdsApiBO idsBO) throws BusinessException {
        log.info("批量关闭下载单书 入参：{}", JSON.toJSONString(idsBO));
        boolean result = eBookService.disableDownloadBatch(idsBO.getIds());
        log.info("批量关闭下载单书 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> updateFile(AppEBookUpdateFileApiBO updateFileBO) throws BusinessException {
        log.info("更换单书文件 入参：{}", JSON.toJSONString(updateFileBO));
        AppEBookUpdateFileBO serviceBo = BeanUtil.copyProperties(updateFileBO, AppEBookUpdateFileBO.class);
        boolean result = eBookService.updateFile(serviceBo);
        log.info("更换单书文件 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> updateWatermark(AppEBookUpdateWatermarkApiBO updateWatermarkBO) throws BusinessException {
        log.info("更换单书水印 入参：{}", JSON.toJSONString(updateWatermarkBO));
        boolean result = eBookService.updateWatermark(updateWatermarkBO.getId(), updateWatermarkBO.getWatermarkId());
        log.info("更换单书水印 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<String> coverUrl(AppEBookFileApiBO fileApiBO) throws BusinessException {
        log.info("解析文件 入参：{}", JSON.toJSONString(fileApiBO));
        AppEBookFileBO serviceBo = BeanUtil.copyProperties(fileApiBO,AppEBookFileBO.class);
        String shareUrl = eBookService.coverUrl(serviceBo);
        log.info("解析文件 结果：{}", shareUrl);
        return RestResponse.success(shareUrl);
    }

    @Override
    public RestResponse<Page<AppEBookApiDTO>> pageForH5(PageInfo<AppEBookH5QueryApiBO> pageRequest) throws BusinessException {
        log.info("H5分页查询单书列表 入参：{}", JSON.toJSONString(pageRequest));

        PageInfo<AppEBookH5QueryBO> pageBO = PageInfoConverter.convertPageInfo(pageRequest, AppEBookH5QueryBO.class);

        Page<AppEBookDTO> page = eBookService.pageForH5(pageBO);

        Page<AppEBookApiDTO> resultPage = (Page<AppEBookApiDTO>) page.convert(dto -> {
            AppEBookApiDTO apiDTO = new AppEBookApiDTO();
            BeanUtil.copyProperties(dto, apiDTO);
            return apiDTO;
        });

        log.info("H5分页查询单书列表 返回记录：{}", JSON.toJSONString(resultPage.getRecords()));
        return RestResponse.success(resultPage);
    }


    @Override
    public RestResponse<Page<AppEBookPageImageApiDTO>> getPageImages(PageInfo<AppEBookPageImagesQueryApiBO> pageRequest) throws BusinessException {
        log.info("分页查询单书图片资源 入参：{}", JSON.toJSONString(pageRequest));
        
        // 转换请求参数
        AppEBookResourceBatchQueryBO queryBO = new AppEBookResourceBatchQueryBO();
        queryBO.setBookIds(pageRequest.getData().getBookIds());
        queryBO.setResourceType(ResourceTypeEnum.PAGE_IMAGE.getCode());
        
        PageInfo<AppEBookResourceBatchQueryBO> resourcePageRequest = new PageInfo<>();
        resourcePageRequest.setPageNum(pageRequest.getPageNum());
        resourcePageRequest.setPageSize(pageRequest.getPageSize());
        resourcePageRequest.setData(queryBO);
        
        // 调用服务层方法
        Page<AppEBookResourceDTO> page = eBookResourceService.pageImages(resourcePageRequest);
        
        // 转换返回结果
        Page<AppEBookPageImageApiDTO> resultPage = (Page<AppEBookPageImageApiDTO>) page.convert(dto -> {
            AppEBookPageImageApiDTO apiDTO = new AppEBookPageImageApiDTO();
            apiDTO.setId(dto.getId());
            apiDTO.setResourceId(dto.getResourceId());
            apiDTO.setPageNum(dto.getPageNum());
            apiDTO.setResourceUrl(dto.getResourceUrl());
            apiDTO.setFileName(dto.getFileName());
            apiDTO.setTotalPages((int) page.getTotal());
            
            // 计算上一页页码
            if (dto.getPageNum() > 1) {
                apiDTO.setPrevPageNum(dto.getPageNum() - 1);
            }
            
            // 计算下一页页码
            if (dto.getPageNum() < page.getTotal()) {
                apiDTO.setNextPageNum(dto.getPageNum() + 1);
            }
            
            return apiDTO;
        });
        
        log.info("分页查询单书图片资源 返回记录：{}", JSON.toJSONString(resultPage.getRecords()));
        return RestResponse.success(resultPage);
    }

    @Override
    public RestResponse<AppEBookAsyncProcessApiDTO> asyncCoverUrl(AppEBookAsyncProcessApiBO request) throws BusinessException {
        log.info("异步处理PDF文件，参数：{}", JSON.toJSONString(request));

        try {
            // 转换请求参数
            AppEBookAsyncProcessBO serviceRequest = BeanUtil.copyProperties(request, AppEBookAsyncProcessBO.class);

            // 调用服务层
            AppEBookAsyncProcessDTO serviceResponse = eBookService.asyncProcess(serviceRequest);

            // 转换响应数据
            AppEBookAsyncProcessApiDTO apiResponse = BeanUtil.copyProperties(serviceResponse, AppEBookAsyncProcessApiDTO.class);

            log.info("异步处理PDF文件成功，taskId：{}", apiResponse.getTaskId());
            return RestResponse.success(apiResponse);

        } catch (Exception e) {
            log.error("异步处理PDF文件失败：{}", e.getMessage(), e);
            throw new BusinessException("异步处理PDF文件失败：" + e.getMessage());
        }
    }

    @Override
    public RestResponse<PdfTaskStatusApiDTO> getTaskStatus(AppEBookAsyncProcessTaskApiBO taskApiBO) throws BusinessException {
        log.info("查询PDF处理任务状态，taskId：{}", taskApiBO);

        try {
            // 调用服务层
            PdfTaskStatusDTO serviceResponse = eBookService.getTaskStatus(taskApiBO.getTaskId());

            // 转换响应数据
            PdfTaskStatusApiDTO apiResponse = BeanUtil.copyProperties(serviceResponse, PdfTaskStatusApiDTO.class);

            log.info("查询PDF处理任务状态成功，taskId：{}，status：{}", taskApiBO, apiResponse.getStatus());
            return RestResponse.success(apiResponse);

        } catch (Exception e) {
            log.error("查询PDF处理任务状态失败，taskId：{}，错误：{}", taskApiBO, e.getMessage(), e);
            throw new BusinessException("查询PDF处理任务状态失败：" + e.getMessage());
        }
    }

    @Override
    public RestResponse<AppEBookAsyncProcessApiDTO> reprocess(AppEBookReprocessApiBO request) throws BusinessException {
        log.info("重新处理PDF文件，参数：{}", JSON.toJSONString(request));

        try {
            // 转换请求参数
            AppEBookReprocessBO serviceRequest = BeanUtil.copyProperties(request, AppEBookReprocessBO.class);

            // 调用服务层
            AppEBookAsyncProcessDTO serviceResponse = eBookService.reprocess(serviceRequest);

            // 转换响应数据
            AppEBookAsyncProcessApiDTO apiResponse = BeanUtil.copyProperties(serviceResponse, AppEBookAsyncProcessApiDTO.class);

            log.info("重新处理PDF文件成功，新taskId：{}", apiResponse.getTaskId());
            return RestResponse.success(apiResponse);

        } catch (Exception e) {
            log.error("重新处理PDF文件失败：{}", e.getMessage(), e);
            throw new BusinessException("重新处理PDF文件失败：" + e.getMessage());
        }
    }

    @Override
    public RestResponse<H5LandingPageApiDTO> getLandingPageData(PageInfo<H5LandingPageApiBO> pageRequest) throws BusinessException {
        log.info("获取H5落地页数据，参数：{}", JSON.toJSONString(pageRequest));

        try {
            // 转换请求参数
            H5LandingPageBO serviceRequest = new H5LandingPageBO();
            if (pageRequest.getData() != null) {
                BeanUtil.copyProperties(pageRequest.getData(), serviceRequest);
            }
            // 设置分页参数
            serviceRequest.setPageNum(pageRequest.getPageNum());
            serviceRequest.setPageSize(pageRequest.getPageSize());

            // 调用服务层
            H5LandingPageDTO serviceResponse = h5LandingPageService.getLandingPageData(serviceRequest);

            // 转换响应数据（需要手动转换书架分页列表）
            H5LandingPageApiDTO apiResponse = new H5LandingPageApiDTO();
            BeanUtil.copyProperties(serviceResponse, apiResponse);

            // 手动转换书架分页列表（从AppEBookshelfDTO转换为AppEBookshelfH5ApiDTO）
            if (serviceResponse.getBookshelves() != null) {
                Page<AppEBookshelfLandingApiDTO> h5BookshelvesPage = (Page<AppEBookshelfLandingApiDTO>) serviceResponse.getBookshelves().convert(dto ->
                    BeanUtil.copyProperties(dto, AppEBookshelfLandingApiDTO.class)
                );
                apiResponse.setBookshelves(h5BookshelvesPage);
            }

            log.info("获取H5落地页数据成功，类型：{}", serviceResponse.getType());
            return RestResponse.success(apiResponse);

        } catch (Exception e) {
            log.error("获取H5落地页数据失败：{}", e.getMessage(), e);
            throw new BusinessException("获取H5落地页数据失败：" + e.getMessage());
        }
    }


    @Override
    public RestResponse<String> redirect(String shortCode) throws BusinessException {
        log.info("处理短链跳转，短链码：{}", shortCode);

        try {
            // 1. 验证短链码格式
            if (StringUtils.isEmpty(shortCode) || shortCode.length() != 8) {
                throw new BusinessException(AppErrorCode.SHORT_INVALID_CODE, "短链码格式无效");
            }

            // 2. 获取长链接
            String longUrl = shortUrlInfoBiz.getLongUrl(shortCode);
            if (StringUtils.isEmpty(longUrl)) {
                throw new BusinessException(AppErrorCode.SHORT_INVALID_CODE, "短链码不存在或已过期");
            }

            log.info("短链码：{} 对应的长链接：{}", shortCode, longUrl);

            // 3. 执行重定向
            response.sendRedirect(longUrl);

            return RestResponse.success(longUrl);

        } catch (IOException e) {
            log.error("重定向失败，短链码：{}，错误：{}", shortCode, e.getMessage(), e);
            throw new BusinessException(AppErrorCode.SHORT_INVALID_CODE, "重定向失败");
        } catch (Exception e) {
            log.error("处理短链跳转异常，短链码：{}，错误：{}", shortCode, e.getMessage(), e);
            throw new BusinessException(AppErrorCode.SHORT_INVALID_CODE, "跳转处理异常");
        }
    }

    @Override
    public RestResponse<List<PdfTaskStatusApiDTO>> batchGetTaskStatus(List<String> taskIds) throws BusinessException {
        log.info("批量查询PDF处理任务状态，任务数量：{}", taskIds != null ? taskIds.size() : 0);

        try {
            // 参数验证
            if (taskIds == null || taskIds.isEmpty()) {
                throw new BusinessException("任务ID列表不能为空");
            }

            if (taskIds.size() > MAX_BATCH_QUERY_SIZE) {
                throw new BusinessException("批量查询任务数量不能超过" + MAX_BATCH_QUERY_SIZE + "个");
            }

            // 调用服务层
            List<PdfTaskStatusDTO> serviceResponse = eBookService.batchGetTaskStatus(taskIds);

            // 转换响应数据
            List<PdfTaskStatusApiDTO> apiResponse = serviceResponse.stream()
                    .map(dto -> BeanUtil.copyProperties(dto, PdfTaskStatusApiDTO.class))
                    .collect(Collectors.toList());

            log.info("批量查询PDF处理任务状态成功，查询数量：{}，返回数量：{}", taskIds.size(), apiResponse.size());
            return RestResponse.success(apiResponse);

        } catch (Exception e) {
            log.error("批量查询PDF处理任务状态失败，错误：{}", e.getMessage(), e);
            throw new BusinessException("批量查询PDF处理任务状态失败：" + e.getMessage());
        }
    }

    @Override
    public RestResponse<ShareUrlResultApiDTO> getShareInfo(GetShareInfoApiBO request) throws BusinessException {
        log.info("获取分享信息，参数：{}", JSON.toJSONString(request));

        try {
            // 转换请求参数
            GetShareInfoBO serviceRequest = BeanUtil.copyProperties(request, GetShareInfoBO.class);

            // 调用服务层
            ShareUrlResultDTO serviceResponse = eBookService.getShareInfo(serviceRequest);

            // 转换响应数据
            ShareUrlResultApiDTO apiResponse = BeanUtil.copyProperties(serviceResponse, ShareUrlResultApiDTO.class);

            log.info("获取分享信息成功，业务类型：{}，业务ID：{}", request.getBusinessType(), request.getBusinessId());
            return RestResponse.success(apiResponse);

        } catch (Exception e) {
            log.error("获取分享信息失败，错误：{}", e.getMessage(), e);
            throw new BusinessException("获取分享信息失败：" + e.getMessage());
        }
    }

    @Override
    public RestResponse<Boolean> removeShareUrl(RemoveShareUrlApiBO request) throws BusinessException {
        log.info("删除分享链接，参数：{}", JSON.toJSONString(request));

        try {
            // 转换请求参数
            RemoveShareUrlBO serviceRequest = BeanUtil.copyProperties(request, RemoveShareUrlBO.class);

            // 调用服务层
            eBookService.removeShareUrl(serviceRequest);

            log.info("删除分享链接成功，业务类型：{}，业务ID：{}", request.getBusinessType(), request.getBusinessId());
            return RestResponse.success(Boolean.TRUE);

        } catch (Exception e) {
            log.error("删除分享链接失败，错误：{}", e.getMessage(), e);
            throw new BusinessException("删除分享链接失败：" + e.getMessage());
        }
    }

    @Override
    public RestResponse<Map<Integer, List<AppEBookResourceApiDTO>>> batchQueryResources(List<Integer> bookIds) throws BusinessException {
        log.info("批量查询书籍资源信息 入参：{}", JSON.toJSONString(bookIds));

        try {
            if (CollectionUtils.isEmpty(bookIds)) {
                return RestResponse.success(Map.of());
            }

            // 调用业务层
            Map<Integer, List<AppEBookResourceDTO>> serviceResult = eBookService.batchQueryResources(bookIds, null);

            // 转换结果
            Map<Integer, List<AppEBookResourceApiDTO>> apiResult = serviceResult.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> entry.getValue().stream()
                                    .map(dto -> {
                                        AppEBookResourceApiDTO apiDTO = new AppEBookResourceApiDTO();
                                        BeanUtil.copyProperties(dto, apiDTO);
                                        return apiDTO;
                                    })
                                    .collect(Collectors.toList())
                    ));

            log.info("批量查询书籍资源信息 返回记录数：{}", apiResult.size());
            return RestResponse.success(apiResult);
        } catch (Exception e) {
            log.error("批量查询书籍资源信息失败", e);
            throw new BusinessException("批量查询书籍资源信息失败");
        }
    }
}