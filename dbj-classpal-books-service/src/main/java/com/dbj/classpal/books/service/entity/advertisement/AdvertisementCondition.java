package com.dbj.classpal.books.service.entity.advertisement;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 广告条件表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("advertisement_condition")
@Tag(name="AdvertisementCondition对象", description="广告条件表")
public class AdvertisementCondition extends BizEntity implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "条件编码")
    private String code;

    @Schema(description = "展示名称")
    private String name;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "是否启用 1-是 0-否")
    private Boolean status;
}
