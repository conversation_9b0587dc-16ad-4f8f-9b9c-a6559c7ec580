package com.dbj.classpal.books.service.api.client.books;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.books.client.api.books.AdminBooksRankInCodeContentsQuestionApi;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsQuestionDetailBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsQuestionSavaBO;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsQuestionUpdateBO;
import com.dbj.classpal.books.client.bo.question.QuestionCategoryBusinessRefApiBO;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.books.common.dto.question.QuestionCountDTO;
import com.dbj.classpal.books.client.enums.BusinessTypeEnum;
import com.dbj.classpal.books.common.enums.books.ContentsTypeEnum;
import com.dbj.classpal.books.service.biz.books.IBooksRankInCodesContentsBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInCodesContentsQuestionBiz;
import com.dbj.classpal.books.service.biz.question.IQuestionCategoryBusinessRefBiz;
import com.dbj.classpal.books.service.biz.question.QuestionBiz;
import com.dbj.classpal.books.service.biz.question.QuestionCategoryBiz;
import com.dbj.classpal.books.service.entity.books.BooksRankInCodesContents;
import com.dbj.classpal.books.service.entity.books.BooksRankInCodesContentsQuestion;
import com.dbj.classpal.books.service.entity.question.QuestionCategory;
import com.dbj.classpal.books.service.entity.question.QuestionCategoryBusinessRef;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@RestController
public class AdminBooksRankInCodeContentsQuestionApiImpl implements AdminBooksRankInCodeContentsQuestionApi {

    @Resource
    private IBooksRankInCodesContentsQuestionBiz booksRankInCodesContentsQuestionBiz;
    @Resource
    private IBooksRankInCodesContentsBiz booksRankInCodesContentsBiz;
    @Resource
    private IQuestionCategoryBusinessRefBiz questionCategoryBusinessRefBiz;
    @Resource
    private QuestionCategoryBiz questionCategoryBiz;
    @Resource
    private QuestionBiz questionBiz;

    @Override
    public RestResponse<BooksRankInCodesContentsQuestionDetailBO> details(Integer inCodesContentsId) {
        List<BooksRankInCodesContentsQuestion> bookRankInCodesContentsQuestionList =  booksRankInCodesContentsQuestionBiz.lambdaQuery().eq(BooksRankInCodesContentsQuestion::getInCodesContentsId, inCodesContentsId).list();
        BooksRankInCodesContentsQuestionDetailBO booksRankInCodesContentsQuestionDetailBO = null;
        if(CollectionUtils.isNotEmpty(bookRankInCodesContentsQuestionList)){
            booksRankInCodesContentsQuestionDetailBO = BeanUtil.copyProperties(bookRankInCodesContentsQuestionList.get(0),BooksRankInCodesContentsQuestionDetailBO.class );
            //查询关联数据
            List<QuestionCategoryBusinessRef> questionCategoryBusinessRefList = questionCategoryBusinessRefBiz.lambdaQuery()
                    .eq(QuestionCategoryBusinessRef::getBusinessId,booksRankInCodesContentsQuestionDetailBO.getId())
                    .eq(QuestionCategoryBusinessRef::getBusinessType, BusinessTypeEnum.QUESTION_BUSINESS.getCode())
                    .orderByAsc(QuestionCategoryBusinessRef::getId).orderByAsc(QuestionCategoryBusinessRef::getSortNum).list();
            if(CollectionUtils.isNotEmpty(questionCategoryBusinessRefList)){
                List<Integer> questionCategoryIdList = questionCategoryBusinessRefList.stream().map(QuestionCategoryBusinessRef::getQuestionCategoryId).collect(Collectors.toList());
                List<QuestionCategory> questionCategoryList =   questionCategoryBiz.lambdaQuery().in(QuestionCategory::getId,questionCategoryIdList).list();
                Map<Integer,String> questionCategoryMap = new HashMap<>();
                if(CollectionUtils.isNotEmpty(questionCategoryList)){
                    questionCategoryMap =  questionCategoryList.stream().collect(Collectors.toMap(QuestionCategory::getId,QuestionCategory::getName));

                }
                List<QuestionCountDTO> questionCountDTOList = questionBiz.countByCategoryIds(questionCategoryIdList);
                Map<Integer,Integer> questionCategoryBusinessRefMap = new HashMap<>();
                if(CollectionUtils.isNotEmpty(questionCountDTOList)){
                    questionCategoryBusinessRefMap =  questionCountDTOList.stream().collect(Collectors.toMap(QuestionCountDTO::getQuestionCategoryId,QuestionCountDTO::getNum));
                }
                List<QuestionCategoryBusinessRefApiBO> questionCategoryBusinessRefApiBO =  BeanUtil.copyToList(questionCategoryBusinessRefList,QuestionCategoryBusinessRefApiBO.class);

                for(QuestionCategoryBusinessRefApiBO questionCategoryBusinessRefApi : questionCategoryBusinessRefApiBO){
                    questionCategoryBusinessRefApi.setQuestionCategoryName(questionCategoryMap.get(questionCategoryBusinessRefApi.getQuestionCategoryId()));
                    questionCategoryBusinessRefApi.setQuestionNum(questionCategoryBusinessRefMap.getOrDefault(questionCategoryBusinessRefApi.getQuestionCategoryId(),0));
                }

                booksRankInCodesContentsQuestionDetailBO.setQuestionCategoryBusinessRefApiBOList(questionCategoryBusinessRefApiBO);
            }
        }
        return RestResponse.success(booksRankInCodesContentsQuestionDetailBO);
    }

    @Override
    public RestResponse<Boolean> save(BooksRankInCodesContentsQuestionSavaBO saveBO) throws BusinessException {
        //新增数据
        BooksRankInCodesContents booksRankInCodesContents = booksRankInCodesContentsBiz.getById(saveBO.getInCodesContentsId());
        if(booksRankInCodesContents == null){
            throw new BusinessException(AppErrorCode.BOOK_INFO_RANK_CONTENTS_NOT_EXIST_CODE,AppErrorCode.BOOK_INFO_RANK_CONTENTS_NOT_EXIST_CODE);
        }
        if(!booksRankInCodesContents.getType().equals(ContentsTypeEnum.QUESTION.getCode())){
            throw new BusinessException(AppErrorCode.BOOK_INFO_RANK_CONTENTS_TYPE_CODE,AppErrorCode.BOOK_INFO_RANK_CONTENTS_TYPE_MSG);
        }
        //判断当前是否已经添加过如果添加过则不允许添加
        List<BooksRankInCodesContentsQuestion> booksRankInCodesContentsQuestionList =  booksRankInCodesContentsQuestionBiz.lambdaQuery().eq(BooksRankInCodesContentsQuestion::getInCodesContentsId,saveBO.getInCodesContentsId()).list();
        if(CollectionUtils.isNotEmpty(booksRankInCodesContentsQuestionList)){
            throw new BusinessException(AppErrorCode.BOOK_INFO_RANK_CONTENTS_QUESTION_ERROR_CODE,AppErrorCode.BOOK_INFO_RANK_CONTENTS_QUESTION_ERROR_MSG);
        }


        BooksRankInCodesContentsQuestion booksRankInCodesContentsQuestion = BeanUtil.copyProperties(saveBO, BooksRankInCodesContentsQuestion.class);
        booksRankInCodesContentsQuestion.setRankId(booksRankInCodesContents.getRankId());
        booksRankInCodesContentsQuestion.setBooksId(booksRankInCodesContents.getBooksId());
        booksRankInCodesContentsQuestion.setRankClassifyId(booksRankInCodesContents.getRankClassifyId());


        booksRankInCodesContentsQuestionBiz.save(booksRankInCodesContentsQuestion);
        if(CollectionUtils.isNotEmpty(saveBO.getQuestionCategoryBusinessRefApiBOList())){
            List<QuestionCategoryBusinessRef> questionCategoryBusinessRefList = BeanUtil.copyToList(saveBO.getQuestionCategoryBusinessRefApiBOList(),QuestionCategoryBusinessRef.class);
            questionCategoryBusinessRefList.forEach(a -> {
                a.setBusinessId(booksRankInCodesContentsQuestion.getId());
                a.setBusinessType(BusinessTypeEnum.QUESTION_BUSINESS.getCode());
            });
            questionCategoryBusinessRefBiz.saveBatch(questionCategoryBusinessRefList);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> update(BooksRankInCodesContentsQuestionUpdateBO updateBO) throws BusinessException {
        //新增数据
        BooksRankInCodesContentsQuestion booksRankInCodesContentsQuestion = BeanUtil.copyProperties(updateBO, BooksRankInCodesContentsQuestion.class);
        booksRankInCodesContentsQuestionBiz.updateById(booksRankInCodesContentsQuestion);
        questionCategoryBusinessRefBiz.lambdaUpdate().eq(QuestionCategoryBusinessRef::getBusinessId,updateBO.getId()).eq(QuestionCategoryBusinessRef::getBusinessType, BusinessTypeEnum.QUESTION_BUSINESS.getCode()).remove();
        if(CollectionUtils.isNotEmpty(updateBO.getQuestionCategoryBusinessRefApiBOList())){
            List<QuestionCategoryBusinessRef> questionCategoryBusinessRefList = BeanUtil.copyToList(updateBO.getQuestionCategoryBusinessRefApiBOList(),QuestionCategoryBusinessRef.class);
            questionCategoryBusinessRefList.forEach(a -> {
                a.setBusinessId(booksRankInCodesContentsQuestion.getId());
                a.setBusinessType(BusinessTypeEnum.QUESTION_BUSINESS.getCode());
            });
            questionCategoryBusinessRefBiz.saveBatch(questionCategoryBusinessRefList);
        }
        return RestResponse.success(true);
    }
}
