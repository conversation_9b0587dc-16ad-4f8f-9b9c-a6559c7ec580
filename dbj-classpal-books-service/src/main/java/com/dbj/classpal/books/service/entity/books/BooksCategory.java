package com.dbj.classpal.books.service.entity.books;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 产品分类配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("books_category")
@Tag(name="BooksCategory对象", description="产品分类配置表")
public class BooksCategory extends BizEntity implements Serializable {


    @Schema(description = "父级id")
    @TableField("parent_id")
    private Integer parentId;

    @Schema(description = "名称")
    @TableField("name")
    private String name;

    @Schema(description = "排序")
    @TableField("sort")
    private Integer sort;

    @Schema(description = "是否启用 1-是 0-否")
    @TableField("status")
    private Integer status;


}
