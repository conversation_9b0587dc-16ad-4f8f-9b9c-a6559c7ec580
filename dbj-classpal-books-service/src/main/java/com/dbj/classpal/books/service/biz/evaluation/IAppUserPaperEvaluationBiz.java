package com.dbj.classpal.books.service.biz.evaluation;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.common.bo.evaluation.AdminUserPaperEvaluationQueryBO;
import com.dbj.classpal.books.common.bo.evaluation.app.AppUserPaperEvaluationSaveBO;
import com.dbj.classpal.books.common.bo.paper.QueryPaperQuestionsBO;
import com.dbj.classpal.books.common.bo.paper.SubmitPaperBO;
import com.dbj.classpal.books.common.dto.evaluation.app.AppUserPaperEvaluationSaveDTO;
import com.dbj.classpal.books.common.dto.paper.PaperQuestionDTO;
import com.dbj.classpal.books.service.entity.evaluation.AppUserPaperEvaluation;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppEvaluationBiz
 * Date:     2025-04-08 16:26:13
 * Description: 表名： ,描述： 表
 */
public interface IAppUserPaperEvaluationBiz extends IService<AppUserPaperEvaluation> {

    /**
     * 分页查询评测模板列表
     */
    Page<AppUserPaperEvaluation> pageInfo(PageInfo<AdminUserPaperEvaluationQueryBO> page);

    /**
     * 用户新增评测报告,如果存在该评测表未生成的报告记录，则忽略新增
     * @param bo
     * @return
     * @throws BusinessException
     */
    AppUserPaperEvaluationSaveDTO saveEvaluationReport(AppUserPaperEvaluationSaveBO bo) throws BusinessException;
}
