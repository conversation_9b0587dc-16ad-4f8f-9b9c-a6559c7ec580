package com.dbj.classpal.books.service.entity.poem;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 古诗文关联业务关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ancient_poem_business_ref")
@Tag(name="古诗文关联业务关系表", description="古诗文关联业务关系表")
public class AncientPoemBusinessRef extends BizEntity implements Serializable {



    @Schema(description = "古诗文id")
    private Integer ancientPoemId;

    @Schema(description = "业务id")
    private Integer businessId;

    @Schema(description = "业务名称")
    private String businessName;

    @Schema(description = "业务类型 1-古诗背诵")
    private Integer businessType;
    @Schema(description = "跳转类型 system othen")
    private String jumpType;

    @Schema(description = "排序")
    private Integer sort;
}
