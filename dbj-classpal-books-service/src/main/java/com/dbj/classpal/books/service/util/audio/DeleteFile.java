package com.dbj.classpal.books.service.util.audio;

import com.dbj.classpal.books.common.constant.AudioConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;

public class DeleteFile {
    private static final Logger log = LoggerFactory.getLogger(DeleteFile.class);
    public static void main(String[] args) {
        String fileName = "bgm_temp_1750296964807myxgfkgt.wav";
        String tempDir = AudioConstants.AUDIO_INPUT_DIR + File.separator;
//        deleteTempFiles(tempDir + fileName);
        deleteDir(AudioConstants.AUDIO_OUTPUT_DIR);
    }

    public static void deleteTempFiles(String downloadDirPath) {
        try {
            // 输出的文件路径
            File file = new File(downloadDirPath);
            // 如果文件已经存在，则删除它
            if (file.exists()) {
                boolean deleted = file.delete();
                if (!deleted) {
                    System.out.println("无法删除已存在的文件: {}"+  file.getAbsolutePath());
                }
            }
        } catch (Exception e) {
            System.out.println("删除文件异常" + e);
        }
    }

    /**
     * 删除目录
     * @param downloadDirPath
     */
    public static void deleteDir(String downloadDirPath) {
        File downloadDir = new File(downloadDirPath);
        if (!downloadDir.exists() || !downloadDir.isDirectory()) {
            log.error("下载目录不存在或不是目录，无需清理");
            return;
        }

        File[] files = downloadDir.listFiles();
        if (files == null) {
            log.error("无法列出下载目录中的文件");
            return;
        }
        int deletedCount = 0;
        int failedCount = 0;

        for (File file : files) {
            if ( file.isFile()) {
                if (file.delete()) {
                    deletedCount++;
                } else {
                    failedCount++;
                    log.error("删除失败: " + file.getAbsolutePath());
                }
            }
        }

        log.error("临时文件清理完成 - 已删除: " + deletedCount + " 个，失败: " + failedCount + " 个");
    }
}
