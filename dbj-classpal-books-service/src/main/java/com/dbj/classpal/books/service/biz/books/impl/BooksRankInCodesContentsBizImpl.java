package com.dbj.classpal.books.service.biz.books.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsPageBO;
import com.dbj.classpal.books.client.dto.books.BooksRankInCodesContentsPageDTO;
import com.dbj.classpal.books.client.dto.books.app.BooksRankInCodesContentsCountDTO;
import com.dbj.classpal.books.client.dto.books.app.BooksRankInCodesContentsRankCountDTO;
import com.dbj.classpal.books.client.dto.books.app.BooksRankInCodesContentsTreeAppDTO;
import com.dbj.classpal.books.common.enums.books.ContentsTypeEnum;
import com.dbj.classpal.books.service.biz.books.IBooksRankClassifyBiz;
import com.dbj.classpal.books.service.biz.books.IBooksUserRankInCodeStudyLogBiz;
import com.dbj.classpal.books.service.biz.books.IBooksUserRankStudyLogBiz;
import com.dbj.classpal.books.service.entity.books.BooksRankClassify;
import com.dbj.classpal.books.service.entity.books.BooksRankInCodesContents;
import com.dbj.classpal.books.service.entity.books.BooksUserRankInCodeStudyLog;
import com.dbj.classpal.books.service.entity.books.BooksUserRankStudyLog;
import com.dbj.classpal.books.service.mapper.books.BooksRankInCodesContentsMapper;
import com.dbj.classpal.books.service.biz.books.IBooksRankInCodesContentsBiz;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import com.dbj.classpal.framework.utils.util.ContextAppUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 图书书内码分类表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class BooksRankInCodesContentsBizImpl extends ServiceImpl<BooksRankInCodesContentsMapper, BooksRankInCodesContents> implements IBooksRankInCodesContentsBiz {
    @Resource
    private IBooksUserRankInCodeStudyLogBiz booksUserRankInCodeStudyLogBiz;
    @Resource
    private IBooksUserRankStudyLogBiz booksUserRankStudyLogBiz;
    @Resource
    private IBooksRankClassifyBiz booksRankClassifyBiz;
    @Override
    public Page<BooksRankInCodesContentsPageDTO> page(PageInfo<BooksRankInCodesContentsPageBO> pageInfo) {
        return baseMapper.page(pageInfo.getPage(), pageInfo.getData());
    }

    @Override
    public List<BooksRankInCodesContentsCountDTO> listCount(List<Integer> bookIds) {
        return baseMapper.listCount(bookIds);
    }

    @Override
    public List<BooksRankInCodesContentsTreeAppDTO> getTree(Integer rankClassifyId) {
        //查询当前书内码所有数据 然后组装树形结构
        BooksRankClassify BooksRankClassify = booksRankClassifyBiz.getById(rankClassifyId);

        List<BooksRankInCodesContents> booksRankInCodesContentsList = this.lambdaQuery()
                .eq(BooksRankInCodesContents::getRankClassifyId,rankClassifyId).orderByAsc(BooksRankInCodesContents::getContentsIndex)
                .orderByAsc(BooksRankInCodesContents::getCreateTime).list();
        List<BooksRankInCodesContentsTreeAppDTO> rootList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(booksRankInCodesContentsList)){
            List<BooksRankInCodesContentsTreeAppDTO> booksRankInCodesContentsTreeDTOList = BeanUtil.copyToList(booksRankInCodesContentsList,BooksRankInCodesContentsTreeAppDTO.class);
            //获取顶级目录
            rootList =  booksRankInCodesContentsTreeDTOList.stream().filter(booksRankInCodesContents -> booksRankInCodesContents.getFatherId() == 0).collect(Collectors.toList());
            Integer appUserId = ContextAppUtil.getAppUserIdInt();
            Integer contestId = null;
            //如果是登录用户则需要查询对应的学习记录
            if(appUserId != null){
                //需要保存当前册数的学习记录
                // 新增用的最近查看图书的记录
                BooksUserRankStudyLog booksUserRankStudyLog =  booksUserRankStudyLogBiz.lambdaQuery().eq(BooksUserRankStudyLog::getAppUserId, ContextAppUtil.getAppUserIdInt())
                        .eq(BooksUserRankStudyLog::getRankId, BooksRankClassify.getRankId())
                        .orderByDesc(BooksUserRankStudyLog::getIsLastStudy).orderByDesc(BooksUserRankStudyLog::getLastStudyTime).last("LIMIT 1").one();
                //不等于空进行修改，等于空则需要新增
                if(booksUserRankStudyLog != null){
                    booksUserRankStudyLogBiz.lambdaUpdate().eq(BooksUserRankStudyLog::getId,booksUserRankStudyLog.getId())
                            .set(BooksUserRankStudyLog::getIsLastStudy, YesOrNoEnum.YES.getCode())
                            .set(BooksUserRankStudyLog::getLastStudyTime, LocalDateTime.now())
                            .update();
                }else {
                    BooksUserRankStudyLog rankStudyLog = new BooksUserRankStudyLog();
                    rankStudyLog.setAppUserId(appUserId);
                    rankStudyLog.setRankId(BooksRankClassify.getRankId());
                    rankStudyLog.setBooksId(booksRankInCodesContentsList.get(0).getBooksId());
                    rankStudyLog.setIsLastStudy(YesOrNoEnum.YES.getCode());
                    rankStudyLog.setLastStudyTime(LocalDateTime.now());
                    booksUserRankStudyLogBiz.save(rankStudyLog);
                }





                List<Integer> contentsIdList = rootList.stream().filter(booksRankInCodesContents -> !StringUtils.equals(ContentsTypeEnum.DIRECTORY.getCode(),booksRankInCodesContents.getType())).map(booksRankInCodesContents -> booksRankInCodesContents.getId()).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(contentsIdList)){
                    BooksUserRankInCodeStudyLog booksUserRankInCodeStudyLog = booksUserRankInCodeStudyLogBiz.lambdaQuery().eq(BooksUserRankInCodeStudyLog::getAppUserId,appUserId)
                            .eq(BooksUserRankInCodeStudyLog::getRankClassifyId,rankClassifyId)
                            .orderByDesc(BooksUserRankInCodeStudyLog::getIsLastStudy)
                            .orderByDesc(BooksUserRankInCodeStudyLog::getLastStudyTime)
                            .orderByDesc(BooksUserRankInCodeStudyLog::getCreateTime).last("LIMIT 1").one();
                    if(booksUserRankInCodeStudyLog != null){
                        contestId = booksUserRankInCodeStudyLog.getInCodesContentsId();
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(rootList)){
                for(BooksRankInCodesContentsTreeAppDTO booksRankInCodesContentsTreeAppDTO : rootList){
                    buildTree(booksRankInCodesContentsTreeAppDTO,booksRankInCodesContentsTreeDTOList,contestId);
                }
            }
        }
        return rootList;
    }

    @Override
    public List<BooksRankInCodesContentsRankCountDTO> listRankCount(List<Integer> rankIds) {
        return baseMapper.listRankCount(rankIds);
    }




    private void buildTree(BooksRankInCodesContentsTreeAppDTO booksRankInCodesContentsTreeAppDTO,List<BooksRankInCodesContentsTreeAppDTO> booksRankInCodesContentsTreeDTOList,Integer contestId){
        Integer id = booksRankInCodesContentsTreeAppDTO.getId();
        boolean isLastStudy = false;
        if(Objects.equals(id,contestId)){
            isLastStudy = true;
        }
        booksRankInCodesContentsTreeAppDTO.setIsLastStudy(isLastStudy);
        List<BooksRankInCodesContentsTreeAppDTO> children = new ArrayList<>();
        for(BooksRankInCodesContentsTreeAppDTO inCodeInfoDTO : booksRankInCodesContentsTreeDTOList){
            if(Objects.equals(id,inCodeInfoDTO.getFatherId())){
                buildTree(inCodeInfoDTO,booksRankInCodesContentsTreeDTOList,contestId);
                children.add(inCodeInfoDTO);
            }
        }
        booksRankInCodesContentsTreeAppDTO.setChildren(children);
    }
}
