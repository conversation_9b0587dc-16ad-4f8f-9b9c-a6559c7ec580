package com.dbj.classpal.books.service.api.client.books.app;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.books.client.api.books.app.AppBooksRankStudyTimeLogApi;
import com.dbj.classpal.books.client.bo.books.app.BooksRankStudyTimeLogBO;
import com.dbj.classpal.books.service.biz.books.IBooksInfoBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInCodesContentsBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInfoBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankStudyTimeLogBiz;
import com.dbj.classpal.books.service.entity.books.BooksRankInCodesContents;
import com.dbj.classpal.books.service.entity.books.BooksRankInfo;
import com.dbj.classpal.books.service.entity.books.BooksRankStudyTimeLog;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.utils.util.ContextAppUtil;
import com.dbj.classpal.framework.utils.util.ContextUtil;
import com.google.common.base.Functions;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@RestController
public class AppBooksRankStudyTimeLogApiImpl implements AppBooksRankStudyTimeLogApi {


    @Resource
    private IBooksRankStudyTimeLogBiz booksRankStudyTimeLogBiz;
    @Resource
    private IBooksRankInCodesContentsBiz bookRankInCodesContentsBiz;

    @Override
    public RestResponse<Boolean> report(@RequestBody List<BooksRankStudyTimeLogBO> booksRankStudyTimeLogBOList) throws BusinessException {
        //需要把对应的图书id查询出来
        List<Integer> inCodesContentsIdIdList = booksRankStudyTimeLogBOList.stream().map(BooksRankStudyTimeLogBO::getInCodesContentsId).collect(Collectors.toList());
        List<BooksRankInCodesContents> booksRankInCodesContentsList = bookRankInCodesContentsBiz.listByIds(inCodesContentsIdIdList);
        if(CollectionUtils.isEmpty(booksRankInCodesContentsList)){
            return RestResponse.success(false);
        }
        Map<Integer,BooksRankInCodesContents> bookIdMap = booksRankInCodesContentsList.stream().collect(Collectors.toMap(BooksRankInCodesContents::getId, Function.identity()));
        List<BooksRankStudyTimeLog> booksRankStudyTimeLogList = new ArrayList<>();

        for(BooksRankStudyTimeLogBO bookRankStudyTimeLogBO : booksRankStudyTimeLogBOList){
            BooksRankStudyTimeLog bizRankStudyTimeLogBO = new BooksRankStudyTimeLog();
            BeanUtil.copyProperties(bookRankStudyTimeLogBO,bizRankStudyTimeLogBO);
            bizRankStudyTimeLogBO.setAppUserId(ContextAppUtil.getAppUserIdInt());
            BooksRankInCodesContents bizRankInCodesContents = bookIdMap.get(bookRankStudyTimeLogBO.getInCodesContentsId());
            if(bizRankInCodesContents != null){
                bizRankStudyTimeLogBO.setBookId(bizRankInCodesContents.getBooksId());
                bizRankStudyTimeLogBO.setRankClassifyId(bizRankInCodesContents.getRankClassifyId());
                bizRankStudyTimeLogBO.setRankId(bizRankInCodesContents.getRankId());
            }
            bizRankStudyTimeLogBO.setInCodesContentsId(bookRankStudyTimeLogBO.getInCodesContentsId());

            booksRankStudyTimeLogList.add(bizRankStudyTimeLogBO);
        }
        booksRankStudyTimeLogBiz.saveBatch(booksRankStudyTimeLogList);
        return RestResponse.success(true);
    }
}
