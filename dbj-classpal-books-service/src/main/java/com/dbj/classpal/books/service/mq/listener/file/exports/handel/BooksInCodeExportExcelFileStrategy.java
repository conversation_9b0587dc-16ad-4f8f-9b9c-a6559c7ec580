package com.dbj.classpal.books.service.mq.listener.file.exports.handel;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.books.BooksRankInCodesContentsPageBO;
import com.dbj.classpal.books.client.dto.books.BooksRankInCodesContentsPageDTO;
import com.dbj.classpal.books.common.bo.books.BooksInCodeExportBO;
import com.dbj.classpal.books.service.biz.books.IBooksRankInCodesContentsBiz;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.utils.bo.SysFileExportExcelBO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/19 13:56:37
 */
@Service("booksInCodeExportExcelFileStrategy")
@Slf4j
public class BooksInCodeExportExcelFileStrategy extends BooksExportExcelFileStrategy<BooksInCodeExportBO> {


    @Resource
    private IBooksRankInCodesContentsBiz booksRankInCodesContentsBiz;

    @Override
    public void handlerExportExcelFile(SysFileExportExcelBO fileDomain) throws Exception {

        //字段数据不多，所以这里直接查询全部，后续如果数据较多，则需要分页处理
        try {
            List<BooksInCodeExportBO> sysDictExportBOList = new ArrayList<>();
            PageInfo<BooksRankInCodesContentsPageBO> pageInfo = new PageInfo<>();
            pageInfo.setIsPage(false);
            String paramJson = fileDomain.getParamJson();
            BooksRankInCodesContentsPageBO booksRankInCodesContentsPageBO = new BooksRankInCodesContentsPageBO();
            if(StringUtils.isNotEmpty(paramJson)){
                booksRankInCodesContentsPageBO = BeanUtil.copyProperties(JSONUtil.parse(fileDomain.getParamJson()), BooksRankInCodesContentsPageBO.class);
            }
            pageInfo.setData(booksRankInCodesContentsPageBO);
            Page<BooksRankInCodesContentsPageDTO> page = booksRankInCodesContentsBiz.page(pageInfo);
            List<BooksRankInCodesContentsPageDTO> booksRankInCodesContentsPageDTOList = page.getRecords();
            if(CollectionUtils.isEmpty(booksRankInCodesContentsPageDTOList)){
                fileDomain.setErrorMsg("导出数据不能为空");
                handleProcessingFailedSys(fileDomain);
                return;
            }
            sysDictExportBOList = BeanUtil.copyToList(booksRankInCodesContentsPageDTOList, BooksInCodeExportBO.class);

            updateFileProcessed(sysDictExportBOList, fileDomain);
        }catch (Exception e){
            fileDomain.setErrorMsg(e.getMessage());
            handleProcessingFailedSys(fileDomain);
        }

    }

}
