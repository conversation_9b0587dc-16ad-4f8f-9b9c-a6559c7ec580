package com.dbj.classpal.books.service.api.client.printer.service;


import com.dbj.business.external.client.model.request.ProductInfoIdRequest;
import com.dbj.business.external.client.model.request.ProductInfoRequest;
import com.dbj.business.external.client.model.response.BasicConfigProductCategoryResponse;
import com.dbj.business.external.client.model.response.ProductInfoDetailResponse;
import com.dbj.business.external.client.model.response.ProductInfoResponse;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;

public interface PrinterRemoteService {
    /**
     * 获取图书分类
     * @return
     */
    List<BasicConfigProductCategoryResponse> getProductCategoryTreeList() throws BusinessException;
    /**
     * 根据产品名称或者编码获取列表
     * @param request
     * @return
     */
    List<ProductInfoResponse> queryProductListByCodeOrName(ProductInfoRequest request) throws BusinessException;

    /**
     * 根据产品ID获取详情
     * @param bo
     * @return
     */
    ProductInfoDetailResponse queryProductInfoById(ProductInfoIdRequest bo) throws BusinessException;
}