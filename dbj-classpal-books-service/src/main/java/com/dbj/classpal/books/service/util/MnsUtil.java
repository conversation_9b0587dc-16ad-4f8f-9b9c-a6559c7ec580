package com.dbj.classpal.books.service.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.mns.client.CloudAccount;
import com.aliyun.mns.client.CloudQueue;
import com.aliyun.mns.client.MNSClient;
import com.aliyun.mns.common.ServiceException;
import com.aliyun.mns.common.ServiceHandlingRequiredException;
import com.aliyun.mns.model.Message;
import com.aliyun.oss.ClientException;
import com.aliyun.sdk.service.mts20140618.models.QueryAnalysisJobListResponse;
import com.aliyun.sdk.service.mts20140618.models.QueryAnalysisJobListResponseBody;
import com.aliyun.sdk.service.mts20140618.models.QueryJobListResponse;
import com.aliyun.sdk.service.mts20140618.models.QueryJobListResponseBody;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportQueryApiBO;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportUpdateApiBO;
import com.dbj.classpal.admin.client.dto.file.importfile.ExcelFileImportQueryApiDTO;
import com.dbj.classpal.books.common.bo.file.ExcelFileEntity;
import com.dbj.classpal.books.common.dto.file.MaterialFileConversionDTO;
import com.dbj.classpal.books.common.dto.mns.MnsMessageBodyDTO;
import com.dbj.classpal.books.common.enums.FileTypeEnum;
import com.dbj.classpal.books.common.enums.MaterialTypeEnum;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBiz;
import com.dbj.classpal.books.service.entity.material.AppMaterial;
import com.dbj.classpal.books.service.remote.file.imports.MaterialFileImportRemoteService;
import com.dbj.classpal.books.service.service.material.IAppMaterialService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.mq.pool.DbjRabbitTemplate;
import com.dbj.classpal.framework.oss.config.MnsConfig;
import com.dbj.classpal.framework.oss.config.OssConfig;
import com.dbj.classpal.framework.oss.constant.MtsExchangeConstant;
import com.dbj.classpal.framework.oss.constant.MtsRoutingKeyConstant;
import com.dbj.classpal.framework.oss.enums.MnsMessageTypeEnum;
import com.dbj.classpal.framework.oss.enums.MtsAnalysisStatesEnum;
import com.dbj.classpal.framework.oss.enums.MtsTransCodeStatesEnum;
import com.dbj.classpal.framework.oss.utils.MtsUtil;
import com.dbj.classpal.framework.utils.enums.FileStatusEnum;
import com.dbj.classpal.framework.utils.util.ContextCommandHolder;
import com.dbj.classpal.framework.utils.util.ContextHolder;
import com.dbj.classpal.framework.utils.util.ContextUtil;
import com.google.gson.Gson;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;
import static com.dbj.classpal.books.common.constant.AppErrorCode.FILE_DIRECTORY_NOT_EXIST_MSG;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: MnsUtil
 * Date:     2025-04-29 15:46:19
 * Description: 表名： ,描述： 表
 */
@Slf4j
@Data
@Component
public class MnsUtil{
    @Resource
    private MnsConfig mnsConfig;
    @Resource
    private OssConfig ossConfig;
    @Resource
    private CloudAccount mnsAccount;
    @Resource
    private DbjRabbitTemplate dbjRabbitTemplate;
    @Resource
    private MtsUtil mtsUtil;
    @Resource
    private MaterialFileImportRemoteService materialFileImportRemoteService;
    @Resource
    private IAppMaterialBiz materialBiz;
    @Resource
    private IAppMaterialService appMaterialService;


    @Async
    public void getMnsClient(){
        log.info("                          ___                                \n" +
                "                       .-'   `'.                             \n" +
                "                      /         \\                            \n" +
                "                      |         ;                            \n" +
                "                      |         |           ___.--,          \n" +
                "          _.._        |0) ~ (0) |    _.---'`__.-( (_.        \n" +
                "      __.--'`_.. '.__.\\    '--. \\_.-' ,.--'`     `\"\"`        \n" +
                "     ( ,.--'`   ',__ /./;   ;, '.__.'`    __                 \n" +
                "     _`) )  .---.__.' / |   |\\   \\__..--\"\"  \"\"\"--.,_         \n" +
                "    `---' .'.''-._.-'`_./  /\\ '.  \\ _.-~~~````~~~-._`-.__.'  \n" +
                "          | |  .' _.-' |  |  \\  \\  '.               `~---`   \n" +
                "          \\ \\/ .'     \\  \\   '. '-._)                        \n" +
                "           \\/ /        \\  \\    `=.__`~-.                     \n" +
                "           / /\\         `) )    / / `\"\".`\\                   \n" +
                "   ,   _.-'.'\\ \\        / /    ( (     / /                   \n" +
                "     `--~`   ) )    .-'.'      '.'.  | (                     \n" +
                "            (/`    ( (`          ) )  '-;                    \n" +
                "             `      '-;         (-'                          ");
        if (mnsAccount != null) {
            MNSClient client = mnsAccount.getMNSClient();
            CloudQueue queue = client.getQueueRef(mnsConfig.getQueueName());
            // 其他初始化逻辑
            loopReceive(queue,client);
            client.close();
        }
    }

    public void loopReceive(CloudQueue queue, MNSClient client) {
        while (true) {
            // 循环执行
            try {
                // 基础: 单次拉取
                // singleReceive(queue);

                // 推荐: 使用的 长轮询批量拉取模型
                longPollingBatchReceive(queue);
            } catch (ClientException ce) {
                log.info("Something wrong with the network connection between client and MNS service."
                        + "Please check your network and DNS availablity.");
                // 客户端异常，默认为抖动，触发下次重试
            } catch (ServiceException se) {
                if (se.getErrorCode().equals("QueueNotExist")) {
                    log.info("Queue is not exist.Please create queue before use");
                    client.close();
                    return;
                } else if (se.getErrorCode().equals("TimeExpired")) {
                    log.info("The request is time expired. Please check your local machine timeclock");
                    return;
                }
                // 其他的服务端异常，默认为抖动，触发下次重试
            } catch (Exception e) {
                log.info("Unknown exception happened!e:"+e.getMessage());
                // 其他异常，默认为抖动，触发下次重试
            }

        }
    }

    public void longPollingBatchReceive(CloudQueue queue){
        log.info("=============start longPollingBatchReceive=============");
        // 一次性拉取 最多 xx 条消息
        int batchSize = 15;
        // 长轮询时间为 xx s
        int waitSeconds = 30;
        try{
            List<Message> messages = queue.batchPopMessage(batchSize, waitSeconds);
            if (messages != null && messages.size() > 0) {
                for (Message message : messages) {
                    printMsgAndDelete(queue,message);
                }
            }
        }catch (Exception e) {
            log.error(e.getMessage());
        }
        log.info("=============end longPollingBatchReceive=============");

    }


    public void printMsgAndDelete(CloudQueue queue, Message popMsg) throws ServiceHandlingRequiredException, ExecutionException, InterruptedException, BusinessException {
        if (popMsg != null) {
            String messageBody = popMsg.getMessageBody();
            log.info("message body: {}",messageBody);
            queue.deleteMessage(popMsg.getReceiptHandle());
            Gson gson = new Gson();
            MnsMessageBodyDTO mnsMessageBodyDTO = gson.fromJson(messageBody, MnsMessageBodyDTO.class);
            if (ObjectUtil.isNotEmpty(mnsMessageBodyDTO)){
                String type = mnsMessageBodyDTO.getType();
                String state = mnsMessageBodyDTO.getState();
                String jobId = mnsMessageBodyDTO.getJobId();
                //如果获取的是分析结果消息通知
                if (type.equals(MnsMessageTypeEnum.MESSAGE_TYPE_ANALYSIS.getType())){
                    ExcelFileImportQueryApiBO queryApiBO = new ExcelFileImportQueryApiBO();
                    queryApiBO.setAnalysisSubmitJobId(jobId);
                    if (state.equals("Success")) {
                        ExcelFileEntity excelFileEntity = new ExcelFileEntity();
                        log.info("分析模板成功，调用本地查询分析结果mq:{}", JSONObject.toJSONString(LocalDateTime.now()));
                        ExcelFileImportQueryApiDTO byAnalysisJobId = materialFileImportRemoteService.getByAnalysisJobId(queryApiBO);
                        ContextCommandHolder.set(ContextUtil.TENANT_ID_HEADER,byAnalysisJobId.getTenantId());
                        if (ObjectUtil.isNotEmpty(byAnalysisJobId)) {
                            //转换成单文件接收对象
                            String fileUrl = byAnalysisJobId.getFileUrl();
                            String fileName = fileUrl.replace(ossConfig.getUrlPrefix(),"").replace(ossConfig.getCdn(),"");
                            excelFileEntity.setId(byAnalysisJobId.getId());
                            excelFileEntity.setFileName(fileName);
                            excelFileEntity.setAnalysisJobId(jobId);
                            dbjRabbitTemplate.sendExchangeEntityMessage(excelFileEntity, MtsExchangeConstant.MTS_TASK_EXCHANGE, MtsRoutingKeyConstant.MTS_QUERY_ANALYSIS_JOB_ROUTING_KEY);
                        }
                    }
                }
                //如果获取的是转码结果
                if (type.equals(MnsMessageTypeEnum.MESSAGE_TYPE_TRANSCODE.getType())) {
                    ExcelFileImportQueryApiBO queryApiBO = new ExcelFileImportQueryApiBO();
                    queryApiBO.setTransSubmitJobId(jobId);
                    if (state.equals("Success")) {
                        ExcelFileEntity excelFileEntity = new ExcelFileEntity();
                        log.info("转码成功，调用本地查询转码结果mq:{}", JSONObject.toJSONString(LocalDateTime.now()));
                        ExcelFileImportQueryApiDTO file = materialFileImportRemoteService.getByTransCodeJobId(queryApiBO);
                        ContextCommandHolder.set(ContextUtil.TENANT_ID_HEADER,file.getTenantId());
                        if (ObjectUtil.isNotEmpty(file)) {
                            //转换成单文件接收对象
                            MaterialFileConversionDTO dto = gson.fromJson(file.getParamJson(), MaterialFileConversionDTO.class);
                            String fileUrl = file.getFileUrl();
                            String fileName = fileUrl.replace(ossConfig.getUrlPrefix(),"").replace(ossConfig.getCdn(),"");
                            excelFileEntity.setId(file.getId());
                            excelFileEntity.setFileName(fileName);
                            excelFileEntity.setTransCodeJobId(file.getTransSubmitJobId());
                            excelFileEntity.setParentId(dto.getParentId());
                            dbjRabbitTemplate.sendExchangeEntityMessage(excelFileEntity, MtsExchangeConstant.MTS_TASK_EXCHANGE, MtsRoutingKeyConstant.MTS_QUERY_JOB_ROUTING_KEY);
                        }
                    }
                }
            }
        }
    }
}
