package com.dbj.classpal.books.service.util;

import cn.hutool.core.util.ObjectUtil;
import com.aliyun.mns.client.CloudAccount;
import com.aliyun.mns.client.CloudQueue;
import com.aliyun.mns.client.MNSClient;
import com.aliyun.mns.common.ServiceException;
import com.aliyun.mns.model.Message;
import com.aliyun.oss.ClientException;
import com.dbj.classpal.books.common.dto.mns.MnsMessageBodyDTO;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBiz;
import com.dbj.classpal.books.service.remote.file.imports.MaterialFileImportRemoteService;
import com.dbj.classpal.books.service.service.material.IAppMaterialService;
import com.dbj.classpal.framework.mq.pool.DbjRabbitTemplate;
import com.dbj.classpal.framework.oss.config.MnsConfig;
import com.dbj.classpal.framework.oss.config.OssConfig;
import com.dbj.classpal.framework.oss.utils.MtsUtil;
import com.google.gson.Gson;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: MnsUtil
 * Date:     2025-04-29 15:46:19
 * Description: 表名： ,描述： 表
 */
@Slf4j
@Data
@Component
public class MnsUtil{
    @Resource
    private MnsConfig mnsConfig;
    @Resource
    private OssConfig ossConfig;
    @Resource
    private CloudAccount mnsAccount;
    @Resource
    private DbjRabbitTemplate dbjRabbitTemplate;
    @Resource
    private MtsUtil mtsUtil;
    @Resource
    private MaterialFileImportRemoteService materialFileImportRemoteService;
    @Resource
    private IAppMaterialBiz materialBiz;
    @Resource
    private IAppMaterialService appMaterialService;


    @Async
    public void getMnsClient(){
        log.info("                          ___                                \n" +
                "                       .-'   `'.                             \n" +
                "                      /         \\                            \n" +
                "                      |         ;                            \n" +
                "                      |         |           ___.--,          \n" +
                "          _.._        |0) ~ (0) |    _.---'`__.-( (_.        \n" +
                "      __.--'`_.. '.__.\\    '--. \\_.-' ,.--'`     `\"\"`        \n" +
                "     ( ,.--'`   ',__ /./;   ;, '.__.'`    __                 \n" +
                "     _`) )  .---.__.' / |   |\\   \\__..--\"\"  \"\"\"--.,_         \n" +
                "    `---' .'.''-._.-'`_./  /\\ '.  \\ _.-~~~````~~~-._`-.__.'  \n" +
                "          | |  .' _.-' |  |  \\  \\  '.               `~---`   \n" +
                "          \\ \\/ .'     \\  \\   '. '-._)                        \n" +
                "           \\/ /        \\  \\    `=.__`~-.                     \n" +
                "           / /\\         `) )    / / `\"\".`\\                   \n" +
                "   ,   _.-'.'\\ \\        / /    ( (     / /                   \n" +
                "     `--~`   ) )    .-'.'      '.'.  | (                     \n" +
                "            (/`    ( (`          ) )  '-;                    \n" +
                "             `      '-;         (-'                          ");
        if (mnsAccount != null) {
            MNSClient client = mnsAccount.getMNSClient();
            CloudQueue queue = client.getQueueRef(mnsConfig.getQueueName());
            // 其他初始化逻辑
            loopReceive(queue,client);
            client.close();
        }
    }

    public void loopReceive(CloudQueue queue, MNSClient client) {
        while (true) {
            // 循环执行
            try {
                // 基础: 单次拉取
                // singleReceive(queue);

                // 推荐: 使用的 长轮询批量拉取模型
                longPollingBatchReceive(queue);
            } catch (ClientException ce) {
                log.info("Something wrong with the network connection between client and MNS service."
                        + "Please check your network and DNS availablity.");
                // 客户端异常，默认为抖动，触发下次重试
            } catch (ServiceException se) {
                if (se.getErrorCode().equals("QueueNotExist")) {
                    log.info("Queue is not exist.Please create queue before use");
                    client.close();
                    return;
                } else if (se.getErrorCode().equals("TimeExpired")) {
                    log.info("The request is time expired. Please check your local machine timeclock");
                    return;
                }
                // 其他的服务端异常，默认为抖动，触发下次重试
            } catch (Exception e) {
                log.info("Unknown exception happened!e:"+e.getMessage());
                // 其他异常，默认为抖动，触发下次重试
            }

        }
    }

    public void longPollingBatchReceive(CloudQueue queue){
        log.info("=============start longPollingBatchReceive=============");
        // 一次性拉取 最多 xx 条消息
        int batchSize = 15;
        // 长轮询时间为 xx s
        int waitSeconds = 30;
        try{
            List<Message> messages = queue.batchPopMessage(batchSize, waitSeconds);
            if (messages != null && messages.size() > 0) {
                for (Message message : messages) {
                    printMsgAndDelete(queue,message);
                }
            }
        }catch (Exception e) {
            log.error(e.getMessage());
        }
        log.info("=============end longPollingBatchReceive=============");

    }

    public void printMsgAndDelete(CloudQueue queue, Message popMsg) throws Exception{
        if (popMsg != null) {
            String messageBody = popMsg.getMessageBody();
            log.info("message body: {}",messageBody);
            queue.deleteMessage(popMsg.getReceiptHandle());
            Gson gson = new Gson();
            MnsMessageBodyDTO mnsMessageBodyDTO = gson.fromJson(messageBody, MnsMessageBodyDTO.class);
            if (ObjectUtil.isNotEmpty(mnsMessageBodyDTO)){
                appMaterialService.getProEnvMnsMaterialHandler(mnsMessageBodyDTO);
            }
        }
    }
}
