package com.dbj.classpal.books.service.biz.audio.impl;

import com.dbj.classpal.books.service.entity.audio.AudioEmotionClassifyConfig;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.service.biz.audio.IAudioEmotionClassifyConfigBiz;
import com.dbj.classpal.books.service.mapper.audio.AudioEmotionClassifyConfigMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 多情感分类配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Service
public class AudioEmotionClassifyConfigBizImpl extends ServiceImpl<AudioEmotionClassifyConfigMapper, AudioEmotionClassifyConfig> implements IAudioEmotionClassifyConfigBiz {

}
