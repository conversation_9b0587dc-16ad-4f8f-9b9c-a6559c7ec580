package com.dbj.classpal.books.service.util.audio;

import javax.sound.sampled.*;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.UUID;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.dbj.classpal.books.client.dto.audio.AudioDetailsDTO;
import com.dbj.classpal.books.common.constant.AudioConstants;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import ws.schild.jave.*;
import ws.schild.jave.info.AudioInfo;
import ws.schild.jave.info.MultimediaInfo;

@Slf4j
public class AudioDurationUtils {
    /**
     * 获取WAV格式音频时长
     * @param file
     * @return
     */
    public static double getAudioDurationWav(File file) {
        try {
            AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(file);
            AudioFormat format = audioInputStream.getFormat();
            long frames = audioInputStream.getFrameLength();

            // 计算时长（秒）
            double durationInSeconds = (frames + 0.0) / format.getFrameRate();
            audioInputStream.close();

            return durationInSeconds;
        } catch (UnsupportedAudioFileException | IOException e) {
            log.error(e.getMessage());
            return -1; // 错误返回-1
        }
    }

    /**
     * 校验音频格式是否满足阿里云提示音格式要求
     * @param urlList
     * @return
     */
    public static void validateAudioFormat(List<String> urlList)  throws BusinessException {
        if (CollectionUtil.isEmpty(urlList)) {
            throw new BusinessException("音频文件参数不能为空！");
        }
        // 格式（阿里云提示音仅支持.WAV）
        boolean isWav = urlList.stream().anyMatch(url -> !isWavFormat(url));
        if (isWav) {
            throw new BusinessException("音频文件格式必须是.WAV格式！");
        }
        File dir  = new File(AudioConstants.AUDIO_INPUT_DIR);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        for (String url : urlList) {
            File file = downloadFileToDir(url, AudioConstants.AUDIO_INPUT_DIR);
            log.info("[校验音频格式] - 下载音频文件：{}, 存储路径：{}", url, AudioConstants.AUDIO_INPUT_DIR);
            if (file == null) {
                log.error("[校验音频格式] - 下载音频文件为空：{}", JSON.toJSONString(file));
                throw new BusinessException("无效音频文件！");
            }
            AudioDetailsDTO dto = getAudioDurationMp3(file);
            if (dto == null) {
                log.error("[校验音频格式] - 解析文件信息为空：{}", JSON.toJSONString(dto));
                throw new BusinessException("无效音频文件！");
            }
            // 赫兹
            if (dto.getSamplingRate() > AudioConstants.ALIYUN_AUDIO_SAMPLING_RATE) {
                throw new BusinessException("音频采样率不能超过16kHz！");
            }
            // 声道
            if (dto.getChannelType() != AudioConstants.ALIYUN_SINGLE_CHANNEL) {
                throw new BusinessException("音频声道仅支持单声道！");
            }
            // 位深度
            if (dto.getBitDepth() != AudioConstants.ALIYUN_BIT_DEEP) {
                throw new BusinessException("音频位深度仅支持16位！");
            }
            // 文件大小
            if (dto.getFileSizeInMB() > AudioConstants.ALIYUN_FILE_SIZE_LIMIT) {
                throw new BusinessException("音频文件大小不能超过2MB！");
            }
        }
    }

    private static boolean isWavFormat(String url) {
        // 转为小写后判断是否以 .wav 结尾
        return !StringUtils.isBlank(url) && url.toLowerCase().endsWith(AudioConstants.BGM_FORMAT_WAV);
    }

    /**
     * 获取MP3格式音频时长
     * @param audioFile
     * @return
     */
    public static AudioDetailsDTO getAudioDurationMp3(File audioFile) {
        try {
            // 获取文件大小
            long fileSizeInBytes = audioFile.length();
            double fileSizeInKB = fileSizeInBytes / 1024.0;
            double fileSizeInMB = fileSizeInKB / 1024.0;

            // 获取音频信息
            MultimediaObject multimediaObject = new MultimediaObject(audioFile);
            MultimediaInfo info = multimediaObject.getInfo();
            AudioInfo audioInfo = info.getAudio();

            // 输出结果
            System.out.printf("文件路径: %s%n", audioFile.getAbsolutePath());
            System.out.printf("文件大小: %.2f KB (%.2f MB)%n", fileSizeInKB, fileSizeInMB);

            // 新增：计算小时、分钟和秒
            long durationInMillis = info.getDuration();
            double durationInSeconds = durationInMillis / 1000.0;
            int hours = (int) (durationInSeconds / 3600);
            int minutes = (int) (durationInSeconds / 60);
            int seconds = (int) (durationInSeconds % 60);

            // 判断声道数（单声道/双声道）
            int channels = info.getAudio().getChannels(); // 获取声道数
            String channelType = channels == 1 ? "单声道" : (channels == 2 ? "双声道" : channels + "声道");

            // 新增：获取位深度（不同库的获取方式可能不同）
            int bitDepth = 0;
            int bitRate = audioInfo.getBitRate();
            int samplingRate = audioInfo.getSamplingRate();
            if (bitRate > 0 && samplingRate > 0) {
                bitDepth = bitRate / samplingRate; // 位深度 = 比特率 / (采样率 × 声道数)
            }

            System.out.printf("音频时长:  %d小时%d分%d秒 (%.2f秒)%n", hours, minutes, seconds, durationInSeconds);
            System.out.printf("格式: %s%n", info.getFormat());
            System.out.printf("比特率: %d kbps%n", info.getAudio().getBitRate() / 1000);
            System.out.printf("采样率: %d Hz%n", info.getAudio().getSamplingRate());
            System.out.println("音频毫秒：" + durationInMillis);
            System.out.printf("声道: %s（%d声道）%n", channelType, channels);
            System.out.printf("位深度: %d bit%n", bitDepth);
            return new AudioDetailsDTO(fileSizeInKB, fileSizeInMB, hours, minutes, seconds, durationInMillis, info.getAudio().getSamplingRate(), channels, bitDepth);

        } catch (EncoderException e) {
            log.error("获取音频详情失败: {}",  e.getMessage());
            return null;
        }
    }

    /**
     * 下载文件并保存到指定目录
     * @param fileUrl 文件URL
     * @param dirPath 保存目录路径
     * @return 保存后的文件对象
     * @throws IOException
     */
    public static File downloadFileToDir(String fileUrl, String dirPath) {
        try {
            URL url = new URL(fileUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");

            if (connection.getResponseCode() != HttpURLConnection.HTTP_OK) {
                throw new IOException("下载失败，HTTP响应码: " + connection.getResponseCode());
            }

            // 生成指定目录下的文件名（使用原始文件名或自定义命名）
//            String fileName = fileUrl.substring(fileUrl.lastIndexOf('/') + 1);
//            if (fileName.isEmpty() || fileName.contains("?")) {
//                fileName = AudioConstants.HINT_MUSIC_PREFIX + UUID.randomUUID().toString().replace("-", "") + AudioConstants.BGM_FORMAT_WAV; // 处理无文件名或带参数的URL
//            }
            File targetFile = new File(dirPath, AudioConstants.HINT_MUSIC_PREFIX + UUID.randomUUID().toString().replace("-", "") + AudioConstants.BGM_FORMAT_WAV) ;

            // 下载文件到指定目录
            try (InputStream in = connection.getInputStream();
                 FileOutputStream out = new FileOutputStream(targetFile)) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
            }
            return targetFile;
        } catch (Exception e) {
            log.error("下载文件失败：{}", e.getMessage());
            return null;
        }

    }

    public static void main(String[] args) throws BusinessException {
//        File wavFile = new File("E:\\audio\\output\\output_with_online_loop_bgm.wav");
//        File mp3File = new File("C:\\Users\\<USER>\\Downloads\\whistle.wav");
//        double duration = getAudioDurationWav(wavFile);
//        if (duration >= 0) {
//            System.out.printf("音频时长: %.2f 秒%n", duration);
//        } else {
//            System.out.println("无法获取音频时长");
//        }
        String url = "https://nls.alicdn.com/sound-event/blow.wav";
        File mp3File = downloadFileToDir(url, AudioConstants.AUDIO_INPUT_DIR);
//        if (mp3File != null) {
            getAudioDurationMp3(mp3File);
//        }

//        validateAudioFormat(Arrays.asList("https://cdn.xiaoliuban.com/printer/1750296029275hotupklf.mp3"));



    }
}
