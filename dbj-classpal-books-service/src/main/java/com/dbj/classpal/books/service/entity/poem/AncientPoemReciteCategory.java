package com.dbj.classpal.books.service.entity.poem;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 古诗文背诵分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ancient_poem_recite_category")
@Tag(name="AncientPoemReciteCategory对象", description="古诗文背诵分类表")
public class AncientPoemReciteCategory extends BizEntity implements Serializable {




    @Schema(description = "类型 system 系统 other 其他 系统类型不让更改")
    @TableField("type")
    private String type;

    @Schema(description = "分类名称")
    @TableField("name")
    private String name;

    @Schema(description = "排序权重")
    @TableField("sort")
    private Integer sort;



}
