package com.dbj.classpal.books.service.mapper.ebooks;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.service.entity.product.AppEBookstore;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 书城表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Mapper
public interface AppEBookstoreMapper extends BaseMapper<AppEBookstore> {

    /**
     * 分页查询书城列表
     *
     * @param page 分页对象
     * @param storeTitle 书城名称
     * @param isHide 是否隐藏
     * @param launchStatus 上下架状态
     * @return 分页结果
     */
    Page<AppEBookstore> pageBookstore(Page<AppEBookstore> page,
                                      @Param("storeTitle") String storeTitle,
                                      @Param("isHide") Integer isHide,
                                      @Param("launchStatus") Integer launchStatus);
} 