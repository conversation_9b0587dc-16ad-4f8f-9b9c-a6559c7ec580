package com.dbj.classpal.books.service.api.client.books.app;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.books.client.api.books.app.AppUserBooksApi;
import com.dbj.classpal.books.client.bo.books.app.BooksUserShelfBO;
import com.dbj.classpal.books.client.dto.books.app.BooksRankInCodesContentsCountDTO;
import com.dbj.classpal.books.client.dto.books.app.BooksUserLastStudyDTO;
import com.dbj.classpal.books.client.dto.books.app.BooksUserRankDTO;
import com.dbj.classpal.books.client.dto.books.app.BooksUserShelfDTO;
import com.dbj.classpal.books.service.biz.books.IBooksRankInCodesContentsBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInfoBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankStudyTimeLogBiz;
import com.dbj.classpal.books.service.biz.books.IBooksUserRankInCodeStudyLogBiz;
import com.dbj.classpal.books.service.biz.books.IBooksUserRankStudyLogBiz;
import com.dbj.classpal.books.service.biz.books.IBooksUserRefBiz;
import com.dbj.classpal.books.service.entity.books.BooksRankInfo;
import com.dbj.classpal.books.service.entity.books.BooksUserRankInCodeStudyLog;
import com.dbj.classpal.books.service.entity.books.BooksUserRankStudyLog;
import com.dbj.classpal.books.service.entity.books.BooksUserRef;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.utils.util.ContextAppUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@RestController
public class AppUserBooksApiImpl implements AppUserBooksApi {

    @Resource
    private IBooksUserRefBiz booksUserRefBiz;
    @Resource
    private IBooksRankStudyTimeLogBiz booksRankStudyTimeLogBiz;
    @Resource
    private IBooksUserRankStudyLogBiz booksUserRankStudyLogBiz;
    @Resource
    private IBooksRankInfoBiz booksRankInfoBiz;
    @Resource
    private IBooksRankInCodesContentsBiz booksRankInCodesContentsBiz;
    @Resource
    private IBooksUserRankInCodeStudyLogBiz booksUserRankInCodeStudyLogBiz;
    @Override
    public RestResponse<List<BooksUserShelfDTO>> booksUserShellist(BooksUserShelfBO booksUserShelfBO) throws BusinessException {
        //查询用户书架
        List<BooksUserShelfDTO> booksUserShelfDTOList = booksUserRefBiz.booksUserShellist(booksUserShelfBO);
        if(CollectionUtils.isNotEmpty(booksUserShelfDTOList)){
            //查询册书集合
            List<Integer> booksIdSet = booksUserShelfDTOList.stream().map(BooksUserShelfDTO::getBooksId).collect(Collectors.toList());
            List<BooksRankInfo> booksRankInfoList = booksRankInfoBiz.lambdaQuery().in(BooksRankInfo::getBookId, booksIdSet).list();
            Map<Integer, List<BooksRankInfo>> booksRankInfoMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(booksRankInfoList)){
                booksRankInfoMap = booksRankInfoList.stream().collect(Collectors.groupingBy(BooksRankInfo::getBookId));
            }


            List<BooksRankInCodesContentsCountDTO> booksRankInCodesContentsCountDTOS = booksRankInCodesContentsBiz.listCount(booksIdSet);
            Map<Integer,Long>  booksRankInCodesContentsCountMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(booksRankInCodesContentsCountDTOS)){
                booksRankInCodesContentsCountMap = booksRankInCodesContentsCountDTOS.stream().collect(Collectors.toMap(BooksRankInCodesContentsCountDTO::getBooksId,BooksRankInCodesContentsCountDTO::getContentsNum, (v1, v2) -> v1));
            }


            for(BooksUserShelfDTO booksUserShelfDTO : booksUserShelfDTOList){
                Integer booksId = booksUserShelfDTO.getBooksId();
                List<BooksRankInfo> rankInfos = booksRankInfoMap.get(booksId);
                if(CollectionUtils.isNotEmpty(rankInfos)){
                    List<BooksUserRankDTO> booksUserRankDTOList = BeanUtil.copyToList(rankInfos, BooksUserRankDTO.class);
                    booksUserShelfDTO.setBooksUserRankDTOList(booksUserRankDTOList);
                }
                booksUserShelfDTO.setContentsNum(booksRankInCodesContentsCountMap.getOrDefault(booksId,0L));
            }
        }
        return RestResponse.success(booksUserShelfDTOList);
    }

    @Override
    public RestResponse<BooksUserLastStudyDTO> lastStudy() throws BusinessException {
        //查询最近学习记录以及时长
        BooksUserRankStudyLog booksUserRankStudyLog =  booksUserRankStudyLogBiz.lambdaQuery().eq(BooksUserRankStudyLog::getAppUserId,ContextAppUtil.getAppUserIdInt())
                .orderByDesc(BooksUserRankStudyLog::getIsLastStudy).orderByDesc(BooksUserRankStudyLog::getLastStudyTime).last("LIMIT 1").one();
        //最近学习记录
        BooksUserLastStudyDTO booksUserLastStudyDTO = new BooksUserLastStudyDTO();
        Long booksNum = booksUserRefBiz.lambdaQuery().eq(BooksUserRef::getAppUserId,ContextAppUtil.getAppUserIdInt()).count();
        booksUserLastStudyDTO.setBooksNum(booksNum);
        if(booksUserRankStudyLog != null){
            Integer rankId = booksUserRankStudyLog.getRankId();
            BooksRankInfo rankInfo = booksRankInfoBiz.getById(rankId);
            if(rankInfo != null){
                Long lastStudyTime = booksRankStudyTimeLogBiz.getLastStudyTime(rankId,ContextAppUtil.getAppUserIdInt());
                booksUserLastStudyDTO.setBooksId(rankInfo.getBookId());
                booksUserLastStudyDTO.setRankId(rankInfo.getId());
                booksUserLastStudyDTO.setRankName(rankInfo.getProductItemName());
                booksUserLastStudyDTO.setLastStudyTime(lastStudyTime);
                booksUserLastStudyDTO.setPicUrl(rankInfo.getPicUrl());
            }
        }else{
            BooksUserRef booksUserRef = booksUserRefBiz.lambdaQuery().eq(BooksUserRef::getAppUserId,ContextAppUtil.getAppUserIdInt())
                    .orderByDesc(BooksUserRef::getIsLastStudy).orderByDesc(BooksUserRef::getLastStudyTime).orderByDesc(BooksUserRef::getCreateTime).last("LIMIT 1").one();
            if(booksUserRef != null){
                //查询第一本册书
                BooksRankInfo rankInfo = booksRankInfoBiz.lambdaQuery().eq(BooksRankInfo::getBookId,booksUserRef.getBooksId())
                        .orderByDesc(BooksRankInfo::getSerialNo).orderByDesc(BooksRankInfo::getCreateTime).last("LIMIT 1").one();
                if(rankInfo != null){
                    Integer rankId = rankInfo.getId();
                    Long lastStudyTime = booksRankStudyTimeLogBiz.getLastStudyTime(rankId,ContextAppUtil.getAppUserIdInt());
                    booksUserLastStudyDTO.setBooksId(rankInfo.getBookId());
                    booksUserLastStudyDTO.setRankId(rankInfo.getId());
                    booksUserLastStudyDTO.setRankName(rankInfo.getProductItemName());
                    booksUserLastStudyDTO.setLastStudyTime(lastStudyTime);
                    booksUserLastStudyDTO.setPicUrl(rankInfo.getPicUrl());
                }
            }
        }
        return RestResponse.success(booksUserLastStudyDTO);
    }

    @Override
    @Transactional( rollbackFor = Exception.class)
    public RestResponse<Boolean> del(Integer bookId) throws BusinessException {
        List<BooksUserRef> booksUserRefList = booksUserRefBiz.lambdaQuery().eq(BooksUserRef::getBooksId,bookId).eq(BooksUserRef::getAppUserId, ContextAppUtil.getAppUserIdInt()).list();
        if(CollectionUtils.isNotEmpty(booksUserRefList)){
            List<Integer> ids = booksUserRefList.stream().map(BooksUserRef::getId).collect(Collectors.toList());
            booksUserRefBiz.removeBatchByIds(ids);
            booksUserRankStudyLogBiz.lambdaUpdate().eq(BooksUserRankStudyLog::getBooksId,bookId).eq(BooksUserRankStudyLog::getAppUserId, ContextAppUtil.getAppUserIdInt()).remove();
            booksUserRankInCodeStudyLogBiz.lambdaUpdate().eq(BooksUserRankInCodeStudyLog::getBooksId,bookId).eq(BooksUserRankInCodeStudyLog::getAppUserId, ContextAppUtil.getAppUserIdInt()).remove();
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> batchSave(List<Integer> bookIds) throws BusinessException {
        //批量添加图书到书架
        List<BooksUserRef> booksUserRefList = booksUserRefBiz.lambdaQuery().in(BooksUserRef::getId,bookIds).eq(BooksUserRef::getAppUserId, ContextAppUtil.getAppUserIdInt()).list();
        Set<Integer> booksUserRefMap = new HashSet<>();
        if(CollectionUtils.isNotEmpty(booksUserRefList)){
            booksUserRefMap = booksUserRefList.stream().map(BooksUserRef::getBooksId).collect(Collectors.toSet());
        }
        List<BooksUserRef> booksUserRefs = new ArrayList<>();
        for(Integer bookId : bookIds){
            if(!booksUserRefMap.contains(bookId)){
                BooksUserRef booksUserRef = new BooksUserRef();
                booksUserRef.setBooksId(bookId);
                booksUserRef.setAppUserId(ContextAppUtil.getAppUserIdInt());
                booksUserRefs.add(booksUserRef);
            }
        }
        if(CollectionUtils.isNotEmpty(booksUserRefs)){
            booksUserRefBiz.saveBatch(booksUserRefs);
        }
        return RestResponse.success(true);
    }
}
