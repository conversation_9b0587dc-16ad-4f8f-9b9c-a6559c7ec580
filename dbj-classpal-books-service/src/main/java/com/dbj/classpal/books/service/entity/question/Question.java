package com.dbj.classpal.books.service.entity.question;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("question")
@Tag(name="题目", description="题目")
@AllArgsConstructor
@NoArgsConstructor
public class Question extends BizEntity implements Serializable {



    /**
     * 分类id
     */
    private Integer questionCategoryId;

    /**
     * 题目
     */
    private String title;

    /**
     * 媒体文件类型 1-文本 2-图片,3-音频,4-视频
     */
    private Integer mediaType;
    /**
     * 学科ID
     */
    private Integer subjectId;

    /**
     * 题目类型 0-单选题 1-多选题 2-判断题 3-填空题 4-排序题，5-完形填空
     */
    private Integer type;

    /**
     * 选项类型 1-文本 2-图片,3-音频,4-视频
     */
    private Integer optionType;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 选择题答案id（多个英文逗号隔开）
     */
    private String answer;

    /**
     * 解析
     */
    private String analyzes;

    /**
     * 是否启用 1-是 0-否
     */
    private Integer status;

    /**
     * 答案列表
     */
    @TableField(exist = false)
    private List<QuestionAnswer> answers;

    /**
     * 完形填空区域列表
     */
    @TableField(exist = false)
    private List<QuestionBlankArea> blankAreas;
} 