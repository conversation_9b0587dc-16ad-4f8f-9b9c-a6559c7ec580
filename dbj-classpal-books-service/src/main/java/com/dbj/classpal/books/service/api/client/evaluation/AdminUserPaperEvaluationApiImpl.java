package com.dbj.classpal.books.service.api.client.evaluation;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.app.client.dto.user.CurrentUserApiDTO;
import com.dbj.classpal.books.client.api.evaluation.AdminUserPaperEvaluationApi;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminUserPaperEvaluationQueryApiBO;
import com.dbj.classpal.books.client.dto.evaluation.AdminEvaluationReportQueryApiDTO;
import com.dbj.classpal.books.client.dto.evaluation.AdminUserPaperEvaluationAnalysisQueryApiDTO;
import com.dbj.classpal.books.client.dto.evaluation.AdminUserPaperEvaluationQueryPageApiDTO;
import com.dbj.classpal.books.common.bo.evaluation.AdminUserPaperEvaluationQueryBO;
import com.dbj.classpal.books.common.dto.evaluation.AdminUserPaperEvaluationQueryPageDTO;
import com.dbj.classpal.books.service.biz.evaluation.IAppEvaluationBiz;
import com.dbj.classpal.books.service.biz.evaluation.IAppEvaluationNodeBiz;
import com.dbj.classpal.books.service.biz.evaluation.IAppUserPaperEvaluationAnalysisBiz;
import com.dbj.classpal.books.service.biz.evaluation.IAppUserPaperEvaluationBiz;
import com.dbj.classpal.books.service.entity.evaluation.AppEvaluation;
import com.dbj.classpal.books.service.entity.evaluation.AppEvaluationNode;
import com.dbj.classpal.books.service.entity.evaluation.AppUserPaperEvaluation;
import com.dbj.classpal.books.service.entity.evaluation.AppUserPaperEvaluationAnalysis;
import com.dbj.classpal.books.service.remote.sys.SysAppUserRemoteService;
import com.dbj.classpal.books.service.service.evaluation.IAppUserPaperEvaluationService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApiImpl
 * Date:     2025-04-10 11:53:30
 * Description: 表名： ,描述： 表
 */
@RestController
public class AdminUserPaperEvaluationApiImpl implements AdminUserPaperEvaluationApi {

    @Resource
    private IAppUserPaperEvaluationService appUserPaperEvaluationService;
    @Resource
    private IAppUserPaperEvaluationBiz appUserPaperEvaluationBiz;
    @Resource
    private IAppEvaluationNodeBiz appEvaluationNodeBiz;
    @Resource
    private IAppEvaluationBiz appEvaluationBiz;
    @Resource
    private IAppUserPaperEvaluationAnalysisBiz appUserPaperEvaluationAnalysisBiz;
    @Resource
    private SysAppUserRemoteService appUserRemoteService;

    @Override
    public RestResponse<Page<AdminUserPaperEvaluationQueryPageApiDTO>> pageInfo(PageInfo<AdminUserPaperEvaluationQueryApiBO> pageRequest) throws BusinessException {
        // 1. 转换查询条件
        AdminUserPaperEvaluationQueryBO queryBO = new AdminUserPaperEvaluationQueryBO();
        BeanUtil.copyProperties(pageRequest.getData(), queryBO);

        PageInfo<AdminUserPaperEvaluationQueryBO> servicePageRequest = new PageInfo<>();
        servicePageRequest.setPageNum(pageRequest.getPageNum());
        servicePageRequest.setPageSize(pageRequest.getPageSize());
        servicePageRequest.setData(queryBO);

        Page<AdminUserPaperEvaluationQueryPageDTO> page = appUserPaperEvaluationService.pageInfo(servicePageRequest);
        return RestResponse.success((Page<AdminUserPaperEvaluationQueryPageApiDTO>) page.convert(vo -> {
            AdminUserPaperEvaluationQueryPageApiDTO dto = new AdminUserPaperEvaluationQueryPageApiDTO();
            BeanUtil.copyProperties(vo, dto);
            return dto;
        }));
    }

    @Override
    public RestResponse<AdminEvaluationReportQueryApiDTO> getEvaluationReport(CommonIdApiBO bo) throws BusinessException {
        AppUserPaperEvaluation evaluation = appUserPaperEvaluationBiz.getById(bo.getId());
        if (ObjectUtils.isEmpty(evaluation)) {
            throw new BusinessException(APP_EVALUATION_REPORT_NOT_EXIST_CODE,APP_EVALUATION_REPORT_NOT_EXIST_MSG);
        }
        List<Integer> userIdList = new ArrayList<>();
        userIdList.add(evaluation.getAppUserId());
        List<CurrentUserApiDTO> currentUserApiDTOList = appUserRemoteService.listById(userIdList);
        if (CollectionUtils.isEmpty(currentUserApiDTOList)) {
            throw new BusinessException(OPENAPI_GET_USER_INFO_FAIL_CODE,OPENAPI_GET_USER_INFO_FAIL_MSG);
        }
        CurrentUserApiDTO currentUser = currentUserApiDTOList.get(0);
        AdminEvaluationReportQueryApiDTO reportQueryApiDTO = new AdminEvaluationReportQueryApiDTO();
        reportQueryApiDTO.setId(evaluation.getId());
        reportQueryApiDTO.setUserId(evaluation.getAppUserId());
        reportQueryApiDTO.setUid(currentUser.getUid());
        reportQueryApiDTO.setUserName(currentUser.getNickname());
        reportQueryApiDTO.setGradeName(evaluation.getGradeName());
        reportQueryApiDTO.setAvatar(currentUser.getAvatar());
        reportQueryApiDTO.setEvaluation(evaluation.getEvaluation());
        reportQueryApiDTO.setGeneratedTime(evaluation.getGeneratedTime());
        AppEvaluation appEvaluation = appEvaluationBiz.getById(evaluation.getAppEvaluationId());
        if (!ObjectUtils.isEmpty(appEvaluation)) {
            reportQueryApiDTO.setEvaluationName(appEvaluation.getEvaluationName());
        }
        List<AdminUserPaperEvaluationAnalysisQueryApiDTO> analysisQueryApiDTOList = new ArrayList<>();
        List<AppUserPaperEvaluationAnalysis> appUserPaperEvaluationAnalysisList = appUserPaperEvaluationAnalysisBiz.lambdaQuery().eq(AppUserPaperEvaluationAnalysis::getAppUserPaperEvaluationId, bo.getId()).list();
        Map<Integer,Integer>orderMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(appUserPaperEvaluationAnalysisList)) {
            analysisQueryApiDTOList = BeanUtil.copyToList(appUserPaperEvaluationAnalysisList, AdminUserPaperEvaluationAnalysisQueryApiDTO.class);
            Set<Integer> nodeIdSet = analysisQueryApiDTOList.stream().map(AdminUserPaperEvaluationAnalysisQueryApiDTO::getAppEvaluationNodeId).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(nodeIdSet)) {
                orderMap = appEvaluationNodeBiz.listByIds(nodeIdSet).stream().filter(d -> d.getAppEvaluationOrder() != null).collect(Collectors.toMap(AppEvaluationNode::getId,AppEvaluationNode::getAppEvaluationOrder));
            }
        }
        Map<Integer, Integer> finalOrderMap = orderMap;
        analysisQueryApiDTOList = analysisQueryApiDTOList.stream().map(d -> {
            if (finalOrderMap.containsKey(d.getAppEvaluationNodeId())){
                d.setOrderNum(finalOrderMap.get(d.getAppEvaluationNodeId()));
            }
            return d;
        }).collect(Collectors.toList());

        analysisQueryApiDTOList.sort(Comparator.comparing(AdminUserPaperEvaluationAnalysisQueryApiDTO::getOrderNum,Comparator.nullsLast(Comparator.naturalOrder())));
        reportQueryApiDTO.setAnalysisQueryApiDTOList(analysisQueryApiDTOList);
        return RestResponse.success(reportQueryApiDTO);
    }
}
