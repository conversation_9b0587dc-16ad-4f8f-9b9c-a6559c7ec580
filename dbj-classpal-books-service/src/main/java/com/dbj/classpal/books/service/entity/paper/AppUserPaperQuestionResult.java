package com.dbj.classpal.books.service.entity.paper;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户试卷题目结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_user_paper_question_result")
@Schema(description = "用户试卷题目结果")
public class AppUserPaperQuestionResult extends BizEntity implements Serializable {


    /**
     * 业务类型
     */
    @Schema(description = "业务类型")
    private Integer businessType;

    /**
     * 业务id
     */
    @Schema(description = "业务id")
    private Integer businessId;

    /**
     * 试卷id
     */
    @Schema(description = "试卷id")
    private Integer paperId;

    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private Integer appUserId;

    /**
     * 问题id
     */
    @Schema(description = "问题id")
    private Integer questionId;

    /**
     * 用户选择的答案ID（多个英文逗号隔开）
     */
    @Schema(description = "用户选择的答案ID（多个英文逗号隔开）")
    private String answerIds;

    /**
     * 用户答案
     */
    @Schema(description = "用户答案")
    private String userAnswer;

    /**
     * 结果 1 正确 0 错误
     */
    @Schema(description = "结果 1 正确 0 错误")
    private Integer result;

    /**
     * 题目排序
     */
    @Schema(description = "题目排序")
    private Integer questionSort;
} 