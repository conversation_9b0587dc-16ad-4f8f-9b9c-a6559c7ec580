package com.dbj.classpal.books.service.biz.audio.impl;

import com.dbj.classpal.books.client.dto.audio.AudioClassifyPathDTO;
import com.dbj.classpal.books.service.biz.audio.IAudioClassifyBiz;
import com.dbj.classpal.books.service.entity.audio.AudioClassify;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.service.mapper.audio.AudioClassifyMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 音频分类 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Service
public class AudioClassifyBizImpl extends ServiceImpl<AudioClassifyMapper, AudioClassify> implements IAudioClassifyBiz {

    @Override
    public List<AudioClassifyPathDTO> getPathList(List<Integer> audioIntroIds) {
        return baseMapper.getPathList(audioIntroIds);
    }

    @Override
    public Set<Integer> getChildren(Set<Integer> ids) {
        return baseMapper.getChildren(ids);
    }

    @Override
    public Set<Integer> getParentIds(Integer id) {
        return baseMapper.getParentIds(id);
    }
}
