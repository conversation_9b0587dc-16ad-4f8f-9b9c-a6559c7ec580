package com.dbj.classpal.books.service.biz.tree;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.common.enums.tree.TreeClassifyEnum;
import com.dbj.classpal.books.service.entity.tree.TreeClassify;

import java.util.List;

/**
 * <p>
 * 树形分类菜单 业务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
public interface ITreeClassifyBiz extends IService<TreeClassify> {
    /**
     * 获取子树id集合
     *
     * @param id
     * @param isIncludeSelf
     * @return
     */
    List<Integer> getSubTreeIds(Integer id, boolean isIncludeSelf);

    /**
     * 获取根节点
     *
     * @param ancientPoem
     * @return
     */
    TreeClassify getRootNode(TreeClassifyEnum ancientPoem);
}
