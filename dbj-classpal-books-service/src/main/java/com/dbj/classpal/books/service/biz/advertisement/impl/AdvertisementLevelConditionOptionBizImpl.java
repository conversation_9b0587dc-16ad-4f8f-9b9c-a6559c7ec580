package com.dbj.classpal.books.service.biz.advertisement.impl;

import com.dbj.classpal.books.client.dto.advertisement.AdvertisementLevelConditionOptionDTO;
import com.dbj.classpal.books.service.biz.advertisement.IAdvertisementLevelConditionOptionBiz;
import com.dbj.classpal.books.service.entity.advertisement.AdvertisementLevelConditionOption;
import com.dbj.classpal.books.service.mapper.advertisement.AdvertisementLevelConditionOptionMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 广告层级条件选项关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Service
public class AdvertisementLevelConditionOptionBizImpl extends ServiceImpl<AdvertisementLevelConditionOptionMapper, AdvertisementLevelConditionOption> implements IAdvertisementLevelConditionOptionBiz {

    @Resource
    private AdvertisementLevelConditionOptionMapper advertisementLevelConditionOptionMapper;

    @Override
    public List<AdvertisementLevelConditionOptionDTO> getByAdvertisementLevelConditionIds(Collection<Integer> advertisementLevelConditionIds) {
        return advertisementLevelConditionOptionMapper.getByAdvertisementLevelConditionIds(advertisementLevelConditionIds);
    }
}
