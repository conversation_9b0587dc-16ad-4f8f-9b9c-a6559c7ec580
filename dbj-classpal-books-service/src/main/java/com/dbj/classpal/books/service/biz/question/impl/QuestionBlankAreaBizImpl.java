package com.dbj.classpal.books.service.biz.question.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.service.biz.question.QuestionBlankAreaBiz;
import com.dbj.classpal.books.service.entity.question.QuestionBlankArea;
import com.dbj.classpal.books.service.mapper.question.QuestionBlankAreaMapper;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_QUESTION_ID_NOT_NULL_CODE;
import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_QUESTION_ID_NOT_NULL_MSG;

/**
 * 完形填空业务实现类
 */
@Slf4j
@Service
public class QuestionBlankAreaBizImpl extends ServiceImpl<QuestionBlankAreaMapper, QuestionBlankArea> 
    implements QuestionBlankAreaBiz {

    @Override
    public List<QuestionBlankArea> getBlankAreas(Integer questionId) throws BusinessException {
        if (questionId == null) {
            throw new BusinessException(APP_QUESTION_ID_NOT_NULL_CODE,APP_QUESTION_ID_NOT_NULL_MSG);
        }

        List<QuestionBlankArea> blankAreas = this.baseMapper.selectList(
                new LambdaQueryWrapper<QuestionBlankArea>()
                        .eq(QuestionBlankArea::getQuestionId, questionId)
                        .orderByAsc(QuestionBlankArea::getBlankIndex)
        );

        return CollectionUtils.isEmpty(blankAreas) ? Collections.emptyList() : blankAreas;
    }

    @Override
    public List<QuestionBlankArea> getBlankAreasByQuestionId(Integer questionId) {
        return this.lambdaQuery()
                .eq(QuestionBlankArea::getQuestionId, questionId)
                .orderByAsc(QuestionBlankArea::getBlankIndex)
                .list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBlankAreas(List<QuestionBlankArea> blankAreas) {
        this.saveBatch(blankAreas);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBlankAreas(Integer questionId, List<QuestionBlankArea> blankAreas) {
        this.deleteByQuestionId(List.of(questionId));
        blankAreas.forEach(area ->
                area.setQuestionId(questionId));
        this.saveBatch(blankAreas);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByQuestionId(List<Integer> questionIds) {
        this.lambdaUpdate()
                .in(QuestionBlankArea::getQuestionId, questionIds)
                .remove();
    }
} 