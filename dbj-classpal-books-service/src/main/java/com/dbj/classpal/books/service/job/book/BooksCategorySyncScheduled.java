package com.dbj.classpal.books.service.job.book;

import com.dbj.classpal.books.service.biz.books.IBooksCategoryBiz;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className BooksCategorySyncScheduled
 * @description
 * @date 2025-04-28 14:31
 **/
@Slf4j
@Component
public class BooksCategorySyncScheduled {



    @Resource
    private IBooksCategoryBiz booksCategoryBiz;

    @XxlJob("booksCategorySyncScheduled")
    public ReturnT<String> syncBooksCategory() throws BusinessException {
        log.info("开始同步图书分类");

        booksCategoryBiz.sync();
        return ReturnT.SUCCESS;
    }



}
