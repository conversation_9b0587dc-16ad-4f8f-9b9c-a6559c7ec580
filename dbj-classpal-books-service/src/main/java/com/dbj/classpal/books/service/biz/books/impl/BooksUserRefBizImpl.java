package com.dbj.classpal.books.service.biz.books.impl;

import com.dbj.classpal.books.client.bo.books.app.BooksUserShelfBO;
import com.dbj.classpal.books.client.dto.books.BooksUserCountDTO;
import com.dbj.classpal.books.client.dto.books.app.BooksUserShelfDTO;
import com.dbj.classpal.books.service.entity.books.BooksUserRef;
import com.dbj.classpal.books.service.mapper.books.BooksUserRefMapper;
import com.dbj.classpal.books.service.biz.books.IBooksUserRefBiz;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.framework.utils.util.ContextAppUtil;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 产品分类配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Service
public class BooksUserRefBizImpl extends ServiceImpl<BooksUserRefMapper, BooksUserRef> implements IBooksUserRefBiz {

    @Override
    public List<BooksUserShelfDTO> booksUserShellist(BooksUserShelfBO booksUserShelfBO) {
        return baseMapper.booksUserShellist(booksUserShelfBO,ContextAppUtil.getAppUserIdInt());
    }

    @Override
    public List<BooksUserCountDTO> booksUserCount(List<Integer> bookIds) {
        return baseMapper.booksUserCount(bookIds);
    }

}
