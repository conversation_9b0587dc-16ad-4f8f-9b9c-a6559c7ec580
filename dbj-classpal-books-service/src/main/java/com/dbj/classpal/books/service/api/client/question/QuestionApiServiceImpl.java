package com.dbj.classpal.books.service.api.client.question;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.question.QuestionApi;
import com.dbj.classpal.books.client.bo.question.*;
import com.dbj.classpal.books.client.dto.question.*;
import com.dbj.classpal.books.common.bo.question.*;
import com.dbj.classpal.books.common.dto.question.QuestionInfoDTO;
import com.dbj.classpal.books.common.enums.question.QuestionTypeEnum;
import com.dbj.classpal.books.service.service.question.QuestionService;
import com.dbj.classpal.framework.commons.converter.PageInfoConverter;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@RestController
public class QuestionApiServiceImpl implements QuestionApi {

    private final QuestionService questionService;



    @Override
    public RestResponse<QuestionApiDTO> getQuestion(QuestionIdApiBO idApiBO) throws BusinessException {
        QuestionIdBO idBO = BeanUtil.copyProperties(idApiBO, QuestionIdBO.class);
        QuestionInfoDTO questionInfoDTO = questionService.getQuestion(idBO);
        if (questionInfoDTO == null) {
            return RestResponse.success(null);
        }
        return RestResponse.success(convertToApiDTO(questionInfoDTO));
    }

    @Override
    public RestResponse<List<QuestionApiDTO>> getQuestionList(@Valid QuestionCategoryIdQueryApiBO idApiBO) {
        QuestionCategoryIdQueryBO idBO = BeanUtil.copyProperties(idApiBO, QuestionCategoryIdQueryBO.class);
        List<QuestionInfoDTO> questionInfoDTOS = questionService.getQuestionList(idBO);
        List<QuestionApiDTO> apiDTOs = questionInfoDTOS.stream()
                .map(this::convertToApiDTO)
                .collect(Collectors.toList());
        return RestResponse.success(apiDTOs);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<Integer> createQuestion(@Valid QuestionSaveApiBO apiBO) throws BusinessException {
        QuestionBO questionBO = convertToServiceBO(apiBO);
        Integer id = questionService.createQuestion(questionBO);
        return RestResponse.success(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<Void> updateQuestion(@Valid QuestionEditApiBO apiBO) throws BusinessException {
        QuestionBO questionBO = convertToServiceBO(apiBO);
        questionService.updateQuestion(questionBO);
        return RestResponse.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<Void> batchDeleteQuestion(QuestionIdsApiBO idsApiBO) throws BusinessException {
        QuestionIdsBO idsBO = BeanUtil.copyProperties(idsApiBO, QuestionIdsBO.class);
        questionService.batchDeleteQuestion(idsBO);
        return RestResponse.success(null);
    }

    @Override
    public RestResponse<List<String>> getQuestionAnswer(QuestionIdApiBO idApiBO) {
        QuestionIdBO idBO = BeanUtil.copyProperties(idApiBO, QuestionIdBO.class);
        List<String> answers = questionService.getQuestionAnswer(idBO);
        return RestResponse.success(answers);
    }

    @Override
    public RestResponse<QuestionBlankContentApiDTO> getQuestionBlankContent(QuestionIdApiBO idApiBO) {
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<Void> batchCopyQuestion(QuestionCopyApiBO copyApiBO) throws BusinessException {
        QuestionCopyBO copyBO = BeanUtil.copyProperties(copyApiBO, QuestionCopyBO.class);
        questionService.batchCopyQuestion(copyBO);
        return RestResponse.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<Void> batchMoveQuestion(QuestionMoveApiBO moveApiBO) {
        QuestionMoveBO moveBO = BeanUtil.copyProperties(moveApiBO, QuestionMoveBO.class);
        questionService.batchMoveQuestion(moveBO);
        return RestResponse.success(null);
    }

    @Override
    public RestResponse<Page<QuestionApiDTO>> pageList(PageInfo<QuestionPageApiBO> pageApiBO) {
        PageInfo<QuestionPageBO> pageBO = PageInfoConverter.convertPageInfo(pageApiBO, QuestionPageBO.class);
        
        Page<QuestionInfoDTO> page = questionService.pageList(pageBO);
        
        return RestResponse.success((Page<QuestionApiDTO>) page.convert(vo -> {
            QuestionApiDTO dto = new QuestionApiDTO();
            BeanUtil.copyProperties(vo, dto);
            return dto;
        }));
    }

    private QuestionBO convertToServiceBO(QuestionEditApiBO questionApiBO) {
        if (questionApiBO == null) {
            return null;
        }

        QuestionBO bo = new QuestionBO();
        BeanUtil.copyProperties(questionApiBO, bo);

        if (questionApiBO.getAnswers() != null) {
            List<QuestionAnswerBO> answerBOs = questionApiBO.getAnswers().stream()
                    .map(answerApiBO -> {
                        QuestionAnswerBO answerBO = new QuestionAnswerBO();
                        BeanUtil.copyProperties(answerApiBO, answerBO);
                        return answerBO;
                    })
                    .collect(Collectors.toList());
            bo.setAnswers(answerBOs);
        }

        if (questionApiBO.getBlankAreas() != null) {
            List<QuestionBlankAreaBO> blankAreaBOs = questionApiBO.getBlankAreas().stream()
                    .map(areaApiBO -> {
                        QuestionBlankAreaBO areaBO = new QuestionBlankAreaBO();
                        BeanUtil.copyProperties(areaApiBO, areaBO);
                        return areaBO;
                    })
                    .collect(Collectors.toList());
            bo.setBlankAreas(blankAreaBOs);
        }

        return bo;
    }

    private QuestionBO convertToServiceBO(QuestionSaveApiBO questionApiBO) {
        if (questionApiBO == null) {
            return null;
        }

        QuestionBO bo = new QuestionBO();
        BeanUtil.copyProperties(questionApiBO, bo);

        if (questionApiBO.getAnswers() != null) {
            List<QuestionAnswerBO> answerBOs = questionApiBO.getAnswers().stream()
                    .map(answerApiBO -> {
                        QuestionAnswerBO answerBO = new QuestionAnswerBO();
                        BeanUtil.copyProperties(answerApiBO, answerBO);
                        return answerBO;
                    })
                    .collect(Collectors.toList());
            bo.setAnswers(answerBOs);
        }

        if (questionApiBO.getBlankAreas() != null) {
            List<QuestionBlankAreaBO> blankAreaBOs = questionApiBO.getBlankAreas().stream()
                    .map(areaApiBO -> {
                        QuestionBlankAreaBO areaBO = new QuestionBlankAreaBO();
                        BeanUtil.copyProperties(areaApiBO, areaBO);
                        return areaBO;
                    })
                    .collect(Collectors.toList());
            bo.setBlankAreas(blankAreaBOs);
        }

        return bo;
    }



    private QuestionApiDTO convertToApiDTO(QuestionInfoDTO questionInfoDTO) {
        if (questionInfoDTO == null) {
            return null;
        }

        QuestionApiDTO apiDTO = new QuestionApiDTO();
        BeanUtil.copyProperties(questionInfoDTO, apiDTO);

        apiDTO.setType(QuestionTypeEnum.getByValue(questionInfoDTO.getType()).getValue());

        if (questionInfoDTO.getAnswers() != null) {
            List<QuestionAnswerApiDTO> answerApiDTOs = questionInfoDTO.getAnswers().stream()
                    .map(answerDTO -> {
                        QuestionAnswerApiDTO answerApiDTO = new QuestionAnswerApiDTO();
                        BeanUtil.copyProperties(answerDTO, answerApiDTO);
                        return answerApiDTO;
                    })
                    .collect(Collectors.toList());
            apiDTO.setAnswers(answerApiDTOs);
        }

        if (questionInfoDTO.getBlankAreas() != null) {
            List<QuestionBlankAreaApiDTO> blankAreaApiDTOs = questionInfoDTO.getBlankAreas().stream()
                    .map(areaDTO -> {
                        QuestionBlankAreaApiDTO areaApiDTO = new QuestionBlankAreaApiDTO();
                        BeanUtil.copyProperties(areaDTO, areaApiDTO);
                        return areaApiDTO;
                    })
                    .collect(Collectors.toList());
            apiDTO.setBlankAreas(blankAreaApiDTOs);
        }

        return apiDTO;
    }
} 