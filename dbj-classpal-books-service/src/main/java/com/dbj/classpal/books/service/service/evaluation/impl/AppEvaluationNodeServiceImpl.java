package com.dbj.classpal.books.service.service.evaluation.impl;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminEvaluationNodeEditApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminEvaluationNodeSaveApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminEvaluationNodeSortApiBO;
import com.dbj.classpal.books.common.enums.AlbumMenusOrderEnum;
import com.dbj.classpal.books.common.enums.BusinessTypeEnum;
import com.dbj.classpal.books.service.biz.evaluation.IAppEvaluationNodeBiz;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBiz;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBusinessRefBiz;
import com.dbj.classpal.books.service.biz.question.QuestionBusinessRefBiz;
import com.dbj.classpal.books.service.entity.evaluation.AppEvaluationNode;
import com.dbj.classpal.books.service.entity.material.AppMaterial;
import com.dbj.classpal.books.service.entity.material.AppMaterialBusinessRef;
import com.dbj.classpal.books.service.entity.question.QuestionBusinessRef;
import com.dbj.classpal.books.service.service.evaluation.IAppEvaluationNodeService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import jodd.util.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.ListIterator;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;
import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_MATERIAL_NOT_FOUND_MSG;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationNodeServiceImpl
 * Date:     2025-05-16 15:00:23
 * Description: 表名： ,描述： 表
 */
@Service
public class AppEvaluationNodeServiceImpl implements IAppEvaluationNodeService {
    @Resource
    private IAppEvaluationNodeBiz appEvaluationNodeBiz;
    @Resource
    private QuestionBusinessRefBiz questionBusinessRefBiz;
    @Resource
    private IAppMaterialBiz appMaterialBiz;
    @Resource
    private IAppMaterialBusinessRefBiz appMaterialBusinessRefBiz;

    @Override
    public Boolean saveEvaluationNode(AdminEvaluationNodeSaveApiBO bo) throws BusinessException {
        int count = appEvaluationNodeBiz.lambdaQuery().eq(AppEvaluationNode::getAppEvaluationId, bo.getAppEvaluationId()).count().intValue();
        if (count >= 10){
            throw new BusinessException(APP_EVALUATION_NODE_SAVE_MAX_CODE,APP_EVALUATION_NODE_SAVE_MAX_MSG);
        }
        AppMaterial material = appMaterialBiz.getById(bo.getAppMaterialId());
        AppEvaluationNode appEvaluationNode = new AppEvaluationNode();
        BeanUtil.copyProperties(bo, appEvaluationNode);
        appEvaluationNode.setAppEvaluationNodeName(bo.getAppEvaluationNodeName());
        if (ObjectUtils.isEmpty(material)){
            throw new BusinessException(APP_MATERIAL_NOT_FOUND_CODE,APP_MATERIAL_NOT_FOUND_MSG);
        }
        int sameNameCount = appEvaluationNodeBiz.lambdaQuery().eq(AppEvaluationNode::getAppEvaluationId, bo.getAppEvaluationId()).eq(AppEvaluationNode::getAppEvaluationNodeName, bo.getAppEvaluationNodeName()).count().intValue();
        if (sameNameCount > 0) {
            throw new BusinessException(APP_EVALUATION_NODE_SAME_NAME_CODE,APP_EVALUATION_NODE_SAME_NAME_MSG);
        }
        appEvaluationNode.setAppEvaluationOrder(appEvaluationNodeBiz.getMaxSortNum(bo.getAppEvaluationId())+1);
        String materialPath = material.getMaterialPath();
        String materialName = material.getMaterialName();
        String materialExtension = material.getMaterialExtension();
        appEvaluationNode.setAppEvaluationNodeCover(materialPath);
        if (StringUtil.isNotEmpty(materialExtension)){
            appEvaluationNode.setAppEvaluationNodeCoverName(materialName+"."+materialExtension);
        }else{
            appEvaluationNode.setAppEvaluationNodeCoverName(materialName+"."+materialPath.substring(materialPath.lastIndexOf(".")));
        }
        return appEvaluationNodeBiz.save(appEvaluationNode);
    }

    @Override
    public Boolean editEvaluationNode(AdminEvaluationNodeEditApiBO bo) throws BusinessException {
        AppEvaluationNode evaluationNode = appEvaluationNodeBiz.getById(bo.getId());
        if (ObjectUtils.isEmpty(evaluationNode)){
            throw new BusinessException(APP_EVALUATION_NODE_SORT_FAIL_CODE,APP_EVALUATION_NODE_SORT_FAIL_MSG);
        }
        evaluationNode.setAppEvaluationNodeName(bo.getAppEvaluationNodeName());
        if(bo.getAppMaterialId() != null){
            AppMaterial material = appMaterialBiz.getById(bo.getAppMaterialId());
            if (ObjectUtils.isEmpty(material)){
                throw new BusinessException(APP_MATERIAL_NOT_FOUND_CODE,APP_MATERIAL_NOT_FOUND_MSG);
            }
            String materialPath = material.getMaterialPath();
            String materialName = material.getMaterialName();
            String materialExtension = material.getMaterialExtension();
            evaluationNode.setAppEvaluationNodeCover(materialPath);
            if (StringUtil.isNotEmpty(materialExtension)){
                evaluationNode.setAppEvaluationNodeCoverName(materialName+"."+materialExtension);
            }else{
                evaluationNode.setAppEvaluationNodeCoverName(materialName+"."+materialPath.substring(materialPath.lastIndexOf(".")));
            }
        }
        int sameNameCount = appEvaluationNodeBiz.lambdaQuery()
                .ne(AppEvaluationNode::getId,bo.getId())
                .eq(AppEvaluationNode::getAppEvaluationId, evaluationNode.getAppEvaluationId())
                .eq(AppEvaluationNode::getAppEvaluationNodeName, bo.getAppEvaluationNodeName())
                .count().intValue();
        if (sameNameCount > 0) {
            throw new BusinessException(APP_EVALUATION_NODE_SAME_NAME_CODE,APP_EVALUATION_NODE_SAME_NAME_MSG);
        }
        if (!appEvaluationNodeBiz.updateById(evaluationNode)){
            throw new BusinessException(APP_EVALUATION_NODE_EDIT_FAIL_CODE,APP_EVALUATION_NODE_EDIT_FAIL_MSG);
        }
        return true;
    }

    @Override
    public Boolean reSort(AdminEvaluationNodeSortApiBO bo) throws BusinessException {
        List<AppEvaluationNode> nodeList = new LinkedList<>(appEvaluationNodeBiz.lambdaQuery().eq(AppEvaluationNode::getAppEvaluationId,bo.getId()).list()).stream().sorted(Comparator.comparingInt(AppEvaluationNode::getAppEvaluationOrder)).filter(d -> !(d.getId().equals(bo.getEvaluationNodeId()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nodeList)) {
            throw new BusinessException(APP_EVALUATION_NODE_SORT_FAIL_CODE,APP_EVALUATION_NODE_SORT_FAIL_MSG);
        }
        //查询当前评测项
        AppEvaluationNode node = appEvaluationNodeBiz.getById(bo.getEvaluationNodeId());
        ListIterator<AppEvaluationNode> iterator = nodeList.listIterator();
        while (iterator.hasNext()) {
            AppEvaluationNode current = iterator.next();
            if (current.getId().equals(bo.getAimEvaluationNodeId())) {
                if (bo.getOrder().equals(AlbumMenusOrderEnum.ORDER_BEFORE.getCode())){
                    if (iterator.hasPrevious()){
                        iterator.previous(); // 回退到 "Bob" 前
                        iterator.add(node); // 插入新对象
                    }else{
                        nodeList.add(0,node);
                    }
                }else{
                    if (iterator.hasNext()){
                        iterator.next(); // 回退到 "Bob" 后
                        iterator.add(node); // 插入新对象
                    }else{
                        nodeList.add(node);
                    }
                }
                break;
            }
        }
        //重新排序赋值
        for (int i = 0; i < nodeList.size(); i++) {
            nodeList.get(i).setAppEvaluationOrder(i+1);
        }
        return appEvaluationNodeBiz.updateBatchById(nodeList);
    }

    @Override
    public Boolean delete(CommonIdsApiBO bo) throws BusinessException {
        int refCount = questionBusinessRefBiz.lambdaQuery().in(QuestionBusinessRef::getBusinessId, bo.getIds()).eq(QuestionBusinessRef::getBusinessType, BusinessTypeEnum.EVALUATION_BUSINESS.getCode()).count().intValue();
        if (refCount > 0) {
            throw new BusinessException(APP_EVALUATION_NODE_DELETE_FAIL_REF_CODE,APP_EVALUATION_NODE_DELETE_FAIL_REF_MSG);
        }
        if (!(appEvaluationNodeBiz.removeByIds(bo.getIds()))){
            throw new BusinessException(APP_EVALUATION_NODE_DELETE_FAIL_CODE,APP_EVALUATION_NODE_DELETE_FAIL_MSG);
        }
        return true;
    }
}
