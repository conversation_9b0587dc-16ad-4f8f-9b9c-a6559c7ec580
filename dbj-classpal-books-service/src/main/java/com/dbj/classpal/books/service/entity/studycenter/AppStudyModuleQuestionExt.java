package com.dbj.classpal.books.service.entity.studycenter;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_study_module_question_ext")
@Tag(name = "答题设置", description = "学习中心-答题设置表")
@AllArgsConstructor
@NoArgsConstructor
public class AppStudyModuleQuestionExt extends BizEntity implements Serializable {


    /** 模块ID */
    private Integer moduleId;

    /** 出题方式 */
    private Integer questionMethod;

    /** 题目数量 */
    private Integer questionNum;

    /** 是否启用 1-是 0-否 */
    private Integer status;
} 