package com.dbj.classpal.books.service.api.client.books;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.books.AdminBooksRankInCodeContentsApi;
import com.dbj.classpal.books.client.bo.books.*;
import com.dbj.classpal.books.client.dto.books.BooksRankInCodesContentsDetailDTO;
import com.dbj.classpal.books.client.dto.books.BooksRankInCodesContentsPageDTO;
import com.dbj.classpal.books.client.dto.books.BooksRankInCodesContentsTreeDTO;
import com.dbj.classpal.books.client.dto.books.app.BooksRankInCodesContentsTreeAppDTO;
import com.dbj.classpal.books.common.config.books.BookCodeUrlConfig;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.books.common.enums.BusinessTypeEnum;
import com.dbj.classpal.books.common.enums.books.ContentsTypeEnum;
import com.dbj.classpal.books.service.biz.album.IAppAlbumElementsBusinessRefBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInCodesContentsBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInCodesContentsQuestionBiz;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBusinessRefBiz;
import com.dbj.classpal.books.service.biz.question.IQuestionCategoryBusinessRefBiz;
import com.dbj.classpal.books.service.biz.shorts.IShortUrlInfoBiz;
import com.dbj.classpal.books.service.entity.album.AppAlbumElementsBusinessRef;
import com.dbj.classpal.books.service.entity.books.BooksRankInCodesContents;
import com.dbj.classpal.books.service.entity.books.BooksRankInCodesContentsQuestion;
import com.dbj.classpal.books.service.entity.material.AppMaterialBusinessRef;
import com.dbj.classpal.books.service.entity.question.QuestionCategoryBusinessRef;
import com.dbj.classpal.books.service.util.ShortUrlGenerator;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@RestController
public class AdminBooksRankInCodeContentsApiImpl implements AdminBooksRankInCodeContentsApi {

    @Resource
    private IBooksRankInCodesContentsBiz booksRankInCodesContentsBiz;
    @Resource
    private IBooksRankInCodesContentsQuestionBiz bookRankInCodesContentsQuestion;
    @Resource
    private IQuestionCategoryBusinessRefBiz questionCategoryBusinessRefBiz;
    @Resource
    private IAppAlbumElementsBusinessRefBiz appointmentElementsBusinessRefBiz;
    @Resource
    private IAppMaterialBusinessRefBiz appMaterialBusinessRefBiz;

    @Resource
    private BookCodeUrlConfig bizBookCodeUrlConfig;
    @Resource
    private ShortUrlGenerator shortUrlGenerator;
    @Resource
    private IShortUrlInfoBiz shortUrlInfoBiz;

    @Override
    public RestResponse<List<BooksRankInCodesContentsTreeDTO>> list(BooksRankInCodesContentsTreeBO boardBooksRankClassifyBO) throws BusinessException {
        List<BooksRankInCodesContentsTreeDTO> booksRankInCodesContentsTreeDTOList = new ArrayList<>();
        //查询当前书内码所有数据 然后组装树形结构
        List<BooksRankInCodesContents> booksRankInCodesContentsList = booksRankInCodesContentsBiz.lambdaQuery()
                .eq(BooksRankInCodesContents::getRankClassifyId,boardBooksRankClassifyBO.getRankClassifyId())
                .orderByAsc(BooksRankInCodesContents::getContentsIndex)
                .orderByAsc(BooksRankInCodesContents::getCreateTime).list();

        if(CollectionUtils.isNotEmpty(booksRankInCodesContentsList)){
            //组装树形结构
            booksRankInCodesContentsTreeDTOList = BeanUtil.copyToList(booksRankInCodesContentsList,BooksRankInCodesContentsTreeDTO.class);
        }
        return RestResponse.success(booksRankInCodesContentsTreeDTOList);
    }

    @Override
    public RestResponse<Page<BooksRankInCodesContentsPageDTO>> page(PageInfo<BooksRankInCodesContentsPageBO> pageInfo) throws BusinessException {
        return RestResponse.success(booksRankInCodesContentsBiz.page(pageInfo));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<Integer> save(BooksRankInCodesContentsSaveBO booksRankInCodesContentsSaveBO) throws BusinessException {
        //新增
        BooksRankInCodesContents booksRankInCodesContents = BeanUtil.copyProperties(booksRankInCodesContentsSaveBO, BooksRankInCodesContents.class);
        String h5ShortCode = null;
        String newPrintCode = null;
        BookCodeUrlConfig.InCode incode = bizBookCodeUrlConfig.getIncode();
        String shortUrl =bizBookCodeUrlConfig.getShortUrl();
        if(!StringUtils.equals(booksRankInCodesContents.getType(),ContentsTypeEnum.DIRECTORY.getCode())){
            //生成短链
            h5ShortCode = shortUrlGenerator.generate();
            booksRankInCodesContents.setH5PageUrl(shortUrl + h5ShortCode);
            newPrintCode = shortUrlGenerator.generate();
            booksRankInCodesContents.setNewPrintCodeUrl(shortUrl + newPrintCode);
        }
        booksRankInCodesContentsBiz.save(booksRankInCodesContents);
        if(StringUtils.isNotEmpty(h5ShortCode)){

            String longUrl = MessageFormat.format(incode.getH5PageUrl(),booksRankInCodesContents.getBooksId(), booksRankInCodesContents.getRankId(),booksRankInCodesContents.getRankClassifyId(), booksRankInCodesContents.getId());
            shortUrlInfoBiz.saveShortUrlInfo(h5ShortCode, longUrl);
        }
        if(StringUtils.isNotEmpty(newPrintCode)){
            String longUrl = MessageFormat.format(incode.getNewPrintCodeUrl(),booksRankInCodesContents.getBooksId(), booksRankInCodesContents.getRankId(),booksRankInCodesContents.getRankClassifyId(), booksRankInCodesContents.getId());
            shortUrlInfoBiz.saveShortUrlInfo(newPrintCode, longUrl);
        }

        return RestResponse.success(booksRankInCodesContents.getId());
    }

    @Override
    public RestResponse<Boolean> update(BooksRankInCodesContentsUpdBO booksRankInCodesContentsUpdBO) throws BusinessException {
        BooksRankInCodesContents booksRankInCodesContents = BeanUtil.copyProperties(booksRankInCodesContentsUpdBO, BooksRankInCodesContents.class);
        booksRankInCodesContentsBiz.updateById(booksRankInCodesContents);
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> delete(Integer id) throws BusinessException {
        //如果是目录 查询是否有子目录 如果不是则直接删除
        BooksRankInCodesContents bookRankInCodesContents = booksRankInCodesContentsBiz.getById(id);
        if(bookRankInCodesContents == null){
            throw new BusinessException(AppErrorCode.BOOK_INFO_RANK_CONTENTS_NOT_EXIST_CODE,AppErrorCode.BOOK_INFO_RANK_CONTENTS_NOT_EXIST_MSG);
        }
        List<BooksRankInCodesContents> booksRankInCodesContentsList = booksRankInCodesContentsBiz.lambdaQuery().eq(BooksRankInCodesContents::getFatherId,id).list();
        if(CollectionUtils.isNotEmpty(booksRankInCodesContentsList)){
            throw new BusinessException(AppErrorCode.BOOK_INFO_RANK_CONTENTS_EXIST_CHILDREN_CODE,AppErrorCode.BOOK_INFO_RANK_CONTENTS_NOT_EXIST_CHILDREN_MSG);
        }
        del(bookRankInCodesContents);
        booksRankInCodesContentsBiz.removeById(id);
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> updateType(BooksRankInCodesContentsUpdTypeBO bo) throws BusinessException {
        // 修改类型直接删除目录下的所有内容
        //如果是目录 查询是否有子目录 如果不是则直接删除
        BooksRankInCodesContents bookRankInCodesContents = booksRankInCodesContentsBiz.getById(bo.getId());
        if(bookRankInCodesContents == null){
            throw new BusinessException(AppErrorCode.BOOK_INFO_RANK_CONTENTS_NOT_EXIST_CODE,AppErrorCode.BOOK_INFO_RANK_CONTENTS_NOT_EXIST_MSG);
        }
        if(StringUtils.equals(ContentsTypeEnum.DIRECTORY.getCode(),bookRankInCodesContents.getType())){
            throw new BusinessException(AppErrorCode.BOOK_INFO_RANK_CONTENTS_DIRECTORY_NOT_CHANGE_CODE,AppErrorCode.BOOK_INFO_RANK_CONTENTS_DIRECTORY_NOT_CHANGE_MSG);
        }
        if(StringUtils.equals(bo.getType(),bookRankInCodesContents.getType())){
            throw new BusinessException(AppErrorCode.BOOK_INFO_RANK_CONTENTS_NOT_CHANGE_CODE,AppErrorCode.BOOK_INFO_RANK_CONTENTS_NOT_CHANGE_MSG);
        }
        // 然后修改类型，清空下面的所有数据
        del(bookRankInCodesContents);
        booksRankInCodesContentsBiz.lambdaUpdate().eq(BooksRankInCodesContents::getId,bookRankInCodesContents.getId()).set(BooksRankInCodesContents::getType,bo.getType()).update();
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> forcePromotionUrl(BooksRankInCodeContentsUpdForceApiBO bo) throws BusinessException {
        // 修改类型直接删除目录下的所有内容
        //如果是目录 查询是否有子目录 如果不是则直接删除
        BooksRankInCodesContents bookRankInCodesContents = booksRankInCodesContentsBiz.getById(bo.getId());
        if(bookRankInCodesContents == null){
            throw new BusinessException(AppErrorCode.BOOK_INFO_RANK_CONTENTS_NOT_EXIST_CODE,AppErrorCode.BOOK_INFO_RANK_CONTENTS_NOT_EXIST_MSG);
        }
        booksRankInCodesContentsBiz.lambdaUpdate().eq(BooksRankInCodesContents::getId,bookRankInCodesContents.getId()).set(BooksRankInCodesContents::getForcePromotionUrl,bo.getForcePromotionUrl()).update();
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<BooksRankInCodesContentsDetailDTO> detail(Integer id) throws BusinessException {
        BooksRankInCodesContents bookRankInCodesContents = booksRankInCodesContentsBiz.getById(id);
        if(bookRankInCodesContents == null){
            throw new BusinessException(AppErrorCode.BOOK_INFO_RANK_CONTENTS_NOT_EXIST_CODE,AppErrorCode.BOOK_INFO_RANK_CONTENTS_NOT_EXIST_MSG);
        }
        BooksRankInCodesContentsDetailDTO booksRankInCodesContentsDetailDTO = new BooksRankInCodesContentsDetailDTO();
        BeanUtil.copyProperties(bookRankInCodesContents, booksRankInCodesContentsDetailDTO);
        return RestResponse.success(booksRankInCodesContentsDetailDTO);
    }

    @Override
    public RestResponse<Boolean> move(BooksRankInCodesContentsMoveBO bo) throws BusinessException {
        BooksRankInCodesContents bookRankInCodesContents = booksRankInCodesContentsBiz.getById(bo.getId());
        if(bookRankInCodesContents == null){
            throw new BusinessException(AppErrorCode.BOOK_INFO_RANK_CONTENTS_NOT_EXIST_CODE,AppErrorCode.BOOK_INFO_RANK_CONTENTS_NOT_EXIST_MSG);
        }
        //不相等判断父目录是否存在
        if(!Objects.equals(bookRankInCodesContents.getFatherId(),bo.getMoveFatherId())){
            if(!Objects.equals(bo.getMoveFatherId(),0)){
                BooksRankInCodesContents fatherInCodesContents = booksRankInCodesContentsBiz.getById(bo.getMoveFatherId());
                if(fatherInCodesContents == null){
                    throw new BusinessException(AppErrorCode.BOOK_INFO_RANK_CONTENTS_FATHER_NOT_EXIST_CODE,AppErrorCode.BOOK_INFO_RANK_CONTENTS_FATHER_NOT_EXIST_MSG);
                }
                if(!Objects.equals(ContentsTypeEnum.DIRECTORY.getCode(),fatherInCodesContents.getType())){
                    throw new BusinessException(AppErrorCode.BOOK_INFO_RANK_CONTENTS_FATHER_NOT_DIRECTORY_CODE,AppErrorCode.BOOK_INFO_RANK_CONTENTS_FATHER_NOT_DIRECTORY_MSG);
                }
            }
            booksRankInCodesContentsBiz.lambdaUpdate().eq(BooksRankInCodesContents::getId,bookRankInCodesContents.getId())
                    .set(BooksRankInCodesContents::getFatherId,bo.getMoveFatherId()).update();
        }
        booksRankInCodesContentsBiz.updateBatchById(BeanUtil.copyToList(bo.getSortList(),BooksRankInCodesContents.class));
        return RestResponse.success(true);
    }

    public void del(BooksRankInCodesContents bookRankInCodesContents){

        ContentsTypeEnum contentsTypeEnum = ContentsTypeEnum.getByCode(bookRankInCodesContents.getType());
        switch (contentsTypeEnum) {
            case RESOURCE:
                appMaterialBusinessRefBiz.lambdaUpdate().eq(AppMaterialBusinessRef::getBusinessId,bookRankInCodesContents.getId())
                        .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.BOOKS_RESOURCE_BUSINESS.getCode()).remove();
                break;
            case QUESTION:
                List<BooksRankInCodesContentsQuestion> booksRankInCodesContentsQuestionList = bookRankInCodesContentsQuestion.lambdaQuery().eq(BooksRankInCodesContentsQuestion::getInCodesContentsId,bookRankInCodesContents.getId()).list();
                if(CollectionUtils.isNotEmpty(booksRankInCodesContentsQuestionList)){
                    List<Integer> questionIdList = booksRankInCodesContentsQuestionList.stream().map(BooksRankInCodesContentsQuestion::getId).collect(Collectors.toList());
                    bookRankInCodesContentsQuestion.removeByIds(questionIdList);
                    questionCategoryBusinessRefBiz.lambdaUpdate().in(QuestionCategoryBusinessRef::getBusinessId,questionIdList)
                            .eq(QuestionCategoryBusinessRef::getBusinessType, BusinessTypeEnum.QUESTION_BUSINESS.getCode()).remove();
                }
                break;
            case AUDIO:
                appointmentElementsBusinessRefBiz.lambdaUpdate().eq(AppAlbumElementsBusinessRef::getBusinessId,bookRankInCodesContents.getId())
                        .eq(AppAlbumElementsBusinessRef::getBusinessType, BusinessTypeEnum.BOOKS_AUDIO_BUSINESS.getCode()).remove();
                break;
            case VIDEO:
                appointmentElementsBusinessRefBiz.lambdaUpdate().eq(AppAlbumElementsBusinessRef::getBusinessId,bookRankInCodesContents.getId())
                        .eq(AppAlbumElementsBusinessRef::getBusinessType, BusinessTypeEnum.BOOKS_VIDEO_BUSINESS.getCode()).remove();
                break;
            default:
                break;

        }
    }
}
