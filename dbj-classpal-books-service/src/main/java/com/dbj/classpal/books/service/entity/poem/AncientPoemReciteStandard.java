package com.dbj.classpal.books.service.entity.poem;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 古诗背诵评分标准表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ancient_poem_recite_standard")
@Tag(name="AncientPoemReciteStandard对象", description="古诗背诵评分标准表")
public class AncientPoemReciteStandard implements Serializable {




    @Schema(description = "分数（0-100分制）")
    @TableField("lowest_score")
    private BigDecimal lowestScore;

    @Schema(description = "分数（0-100分制）")
    @TableField("highest_score")
    private BigDecimal highestScore;

    @Schema(description = "最低百分比")
    @TableField("lowest_percentage")
    private BigDecimal lowestPercentage;

    @Schema(description = "评语")
    @TableField("remark")
    private String remark;

    @Schema(description = "最高百分比")
    @TableField("highest_percentage")
    private BigDecimal highestPercentage;



}
