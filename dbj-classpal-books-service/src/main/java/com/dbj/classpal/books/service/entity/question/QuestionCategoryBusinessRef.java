package com.dbj.classpal.books.service.entity.question;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

/**
 * 题目分类业务关联表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("question_category_business_ref")
@Schema(description = "题目分类业务关联")
public class QuestionCategoryBusinessRef extends BizEntity implements Serializable {


    /**
     * 题目分类ID
     */
    @Schema(description = "题目分类ID")
    private Integer questionCategoryId;

    /**
     * 业务ID
     */
    @Schema(description = "业务ID")
    private Integer businessId;

    /**
     * 业务类型（1-音频专辑 2-视频专辑 3-图书）
     */
    @Schema(description = "业务类型（1-音频专辑 2-视频专辑 3-图书）")
    private Integer businessType;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sortNum;
    /**
     * 状态（1-启用 0-禁用）
     */
    @Schema(description = "状态（1-启用 0-禁用）")
    private Integer status;
}
