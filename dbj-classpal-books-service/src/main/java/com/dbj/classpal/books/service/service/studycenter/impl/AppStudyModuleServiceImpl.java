package com.dbj.classpal.books.service.service.studycenter.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsBusinessRefSaveBO;
import com.dbj.classpal.books.common.bo.studycenter.*;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.books.common.dto.studycenter.AppStudyModuleDetailDTO;
import com.dbj.classpal.books.common.dto.studycenter.AppStudyModuleListDTO;
import com.dbj.classpal.books.common.dto.studycenter.AppStudyModuleQuestionResourceBO;
import com.dbj.classpal.books.common.dto.studycenter.StudyCenterCategoryDTO;
import com.dbj.classpal.books.common.enums.studycenter.StudyModuleResourceTypeEnum;
import com.dbj.classpal.books.service.biz.album.IAppAlbumElementsBusinessRefBiz;
import com.dbj.classpal.books.service.biz.studycenter.IAppStudyModuleBiz;
import com.dbj.classpal.books.service.biz.studycenter.IAppStudyModuleQuestionExtBiz;
import com.dbj.classpal.books.service.biz.studycenter.IAppStudyModuleResourceRelBiz;
import com.dbj.classpal.books.service.entity.album.AppAlbumElementsBusinessRef;
import com.dbj.classpal.books.service.entity.studycenter.AppStudyModule;
import com.dbj.classpal.books.service.entity.studycenter.AppStudyModuleResourceRel;
import com.dbj.classpal.books.service.service.studycenter.IAppStudyModuleService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.dbj.classpal.books.common.enums.BusinessTypeEnum.*;

@Slf4j
@Service
public class AppStudyModuleServiceImpl implements IAppStudyModuleService {
    @Resource
    private IAppStudyModuleBiz appStudyModuleBiz;
    @Resource
    private IAppStudyModuleQuestionExtBiz questionExtBiz;
    @Resource
    private IAppStudyModuleResourceRelBiz resourceRelBiz;
    @Resource
    private IAppAlbumElementsBusinessRefBiz appAlbumElementsBusinessRefBiz;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean create(AppStudyModuleCreateBO bo) throws BusinessException {
        log.info("创建学习模块 入参: {}", bo);
        if (bo == null) {
            throw new BusinessException(AppErrorCode.PARAM_NOT_NULL_CODE, AppErrorCode.PARAM_NOT_NULL_MSG);
        }
        validateRequiredFields(bo);
        AppStudyModule module = appStudyModuleBiz.create(bo);
        if (StudyModuleResourceTypeEnum.QUESTION_BANK.getId().equals(bo.getModuleType())){
            if (bo.getQuestionExt() != null) {
                questionExtBiz.create(bo.getQuestionExt(), module.getId());
            }
        } else {
            for (AppStudyModuleQuestionResourceBO resourceBO : bo.getResourceList()) {
                AppStudyModuleResourceRelCreateBO relBO = new AppStudyModuleResourceRelCreateBO();
                relBO.setModuleId(module.getId());
                relBO.setResourceId(resourceBO.getResourceId());
                relBO.setResourceName(resourceBO.getResourceName());
                relBO.setResourceIcon(resourceBO.getResourceIcon());
                relBO.setResourceType(bo.getModuleType());
                if(StudyModuleResourceTypeEnum.AUDIO_ALBUM.getId().equals(bo.getModuleType())
                        || StudyModuleResourceTypeEnum.VIDEO_ALBUM.getId().equals(bo.getModuleType())){
                    AppAlbumElementsBusinessRefSaveBO saveBO = new AppAlbumElementsBusinessRefSaveBO();
                    saveBO.setAppAlbumId(resourceBO.getResourceId());
                    saveBO.setBusinessId(module.getId());
                    if(StudyModuleResourceTypeEnum.AUDIO_ALBUM.getId().equals(bo.getModuleType())){
                        saveBO.setBusinessType(STUDY_CENTER_AUDIO_BUSINESS.getCode());
                    }
                    if(StudyModuleResourceTypeEnum.VIDEO_ALBUM.getId().equals(bo.getModuleType())){
                        saveBO.setBusinessType(STUDY_CENTER_VIDEO_BUSINESS.getCode());
                    }
                    appAlbumElementsBusinessRefBiz.save(BeanUtil.copyProperties(saveBO, AppAlbumElementsBusinessRef.class));
                }
                resourceRelBiz.create(relBO);
            }
        }
        log.info("创建学习模块 返回: {}", module);
        return module!=null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(AppStudyModuleUpdateBO bo) throws BusinessException {
        log.info("修改学习模块 入参: {}", bo);
        if (bo == null) {
            throw new BusinessException(AppErrorCode.PARAM_NOT_NULL_CODE, AppErrorCode.PARAM_NOT_NULL_MSG);
        }
        if (bo.getId() == null) {
            throw new BusinessException(AppErrorCode.ID_NOT_NULL_CODE, AppErrorCode.ID_NOT_NULL_MSG);
        }
        validateRequiredFields(bo);
        Boolean result = appStudyModuleBiz.update(bo);
        if (StudyModuleResourceTypeEnum.QUESTION_BANK.getId().equals(bo.getModuleType())) {
            if (bo.getQuestionExt() != null) {
                questionExtBiz.update(bo.getQuestionExt(), bo.getId());
            }
        } else {
            resourceRelBiz.remove(new LambdaQueryWrapper<AppStudyModuleResourceRel>().eq(AppStudyModuleResourceRel::getModuleId,bo.getId()));
            for (AppStudyModuleQuestionResourceBO resourceBO : bo.getResourceList()) {
                AppStudyModuleResourceRelCreateBO relBO = new AppStudyModuleResourceRelCreateBO();
                relBO.setModuleId(bo.getId());
                relBO.setResourceId(resourceBO.getResourceId());
                relBO.setResourceName(resourceBO.getResourceName());
                relBO.setResourceIcon(resourceBO.getResourceIcon());
                relBO.setResourceType(bo.getModuleType());
                if(StudyModuleResourceTypeEnum.AUDIO_ALBUM.getId().equals(bo.getModuleType())
                        || StudyModuleResourceTypeEnum.VIDEO_ALBUM.getId().equals(bo.getModuleType())){
                    AppAlbumElementsBusinessRefSaveBO saveBO = new AppAlbumElementsBusinessRefSaveBO();
                    saveBO.setAppAlbumId(resourceBO.getResourceId());
                    saveBO.setBusinessId(bo.getId());
                    if(StudyModuleResourceTypeEnum.AUDIO_ALBUM.getId().equals(bo.getModuleType())){
                        saveBO.setBusinessType(STUDY_CENTER_AUDIO_BUSINESS.getCode());
                    }
                    if(StudyModuleResourceTypeEnum.VIDEO_ALBUM.getId().equals(bo.getModuleType())){
                        saveBO.setBusinessType(STUDY_CENTER_VIDEO_BUSINESS.getCode());
                    }
                    appAlbumElementsBusinessRefBiz.save(BeanUtil.copyProperties(saveBO, AppAlbumElementsBusinessRef.class));
                }
                resourceRelBiz.create(relBO);
            }
        }
        log.info("修改学习模块 返回: {}", result);
        return result;
    }

    @Override
    public Boolean delete(List<Integer> ids) throws BusinessException {
        log.info("删除学习模块 入参: {}", ids);
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException(AppErrorCode.ID_LIST_NOT_EMPTY_CODE, AppErrorCode.ID_LIST_NOT_EMPTY_MSG);
        }
        Boolean result = appStudyModuleBiz.delete(ids);
        log.info("删除学习模块 返回: {}", result);
        return result;
    }

    @Override
    public Page<AppStudyModuleListDTO> page(PageInfo<AppStudyModuleQueryPageBO> pageInfo) throws BusinessException {
        log.info("分页查询学习模块 入参: {}", pageInfo);
        Page<AppStudyModuleListDTO> result = appStudyModuleBiz.page(pageInfo);
        log.info("分页查询学习模块 返回: {}", result);
        return result;
    }

    @Override
    public AppStudyModuleDetailDTO detail(Integer id) throws BusinessException {
        log.info("查询学习模块详情 入参: {}", id);
        if (id == null) {
            throw new BusinessException(AppErrorCode.ID_NOT_NULL_CODE, AppErrorCode.ID_NOT_NULL_MSG);
        }
        AppStudyModuleDetailDTO result = appStudyModuleBiz.detail(id);
        log.info("查询学习模块详情 返回: {}", result);
        return result;
    }

    @Override
    public List<StudyCenterCategoryDTO> listHome(StudyCenterModuleListQueryBO queryBO) throws BusinessException {
        log.info("首页所有模块/内容 入参: {}", queryBO);
        if (queryBO == null) {
            return Collections.emptyList();
        }
        return appStudyModuleBiz.listHomeWithRelations(queryBO);
    }



    @Override
    public Boolean batchPublish(List<Integer> ids) throws BusinessException {
        log.info("批量上架 入参: {}", ids);
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException(AppErrorCode.ID_LIST_NOT_EMPTY_CODE, AppErrorCode.ID_LIST_NOT_EMPTY_MSG);
        }
        Boolean result = appStudyModuleBiz.batchPublish(ids);
        log.info("批量上架 返回: {}", result);
        return result;
    }

    @Override
    public Boolean batchUnpublish(List<Integer> ids) throws BusinessException {
        log.info("批量下架 入参: {}", ids);
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException(AppErrorCode.ID_LIST_NOT_EMPTY_CODE, AppErrorCode.ID_LIST_NOT_EMPTY_MSG);
        }
        Boolean result = appStudyModuleBiz.batchUnpublish(ids);
        log.info("批量下架 返回: {}", result);
        return result;
    }

    @Override
    public Boolean batchShow(List<Integer> ids) throws BusinessException {
        log.info("批量显示 入参: {}", ids);
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException(AppErrorCode.ID_LIST_NOT_EMPTY_CODE, AppErrorCode.ID_LIST_NOT_EMPTY_MSG);
        }
        Boolean result = appStudyModuleBiz.batchShow(ids);
        log.info("批量显示 返回: {}", result);
        return result;
    }

    @Override
    public Boolean batchHide(List<Integer> ids) throws BusinessException {
        log.info("批量隐藏 入参: {}", ids);
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException(AppErrorCode.ID_LIST_NOT_EMPTY_CODE, AppErrorCode.ID_LIST_NOT_EMPTY_MSG);
        }
        Boolean result = appStudyModuleBiz.batchHide(ids);
        log.info("批量隐藏 返回: {}", result);
        return result;
    }
    private void validateRequiredFields(AppStudyModuleCreateBO bo)throws BusinessException{
        if (StringUtils.isEmpty(bo.getTitle())) {
            throw new BusinessException(AppErrorCode.APP_MODULE_TITLE_REQUIRED_CODE, AppErrorCode.APP_MODULE_TITLE_REQUIRED_MSG);
        }
        if (StringUtils.isEmpty(bo.getDescription())) {
            throw new BusinessException(AppErrorCode.APP_MODULE_DESC_REQUIRED_CODE, AppErrorCode.APP_MODULE_DESC_REQUIRED_MSG);
        }
        if (Objects.isNull(bo.getBelongCategoryId())) {
            throw new BusinessException(AppErrorCode.APP_MODULE_CATEGORY_REQUIRED_CODE, AppErrorCode.APP_MODULE_CATEGORY_REQUIRED_MSG);
        }
        if (Objects.isNull(bo.getIsVisible())) {
            throw new BusinessException(AppErrorCode.APP_MODULE_VISIBLE_REQUIRED_CODE, AppErrorCode.APP_MODULE_VISIBLE_REQUIRED_MSG);
        }
        if (Objects.isNull(bo.getPublishStatus())) {
            throw new BusinessException(AppErrorCode.APP_MODULE_STATUS_REQUIRED_CODE, AppErrorCode.APP_MODULE_STATUS_REQUIRED_MSG);
        }
        if (bo.getModuleType().equals(StudyModuleResourceTypeEnum.QUESTION_BANK.getId())
                && Objects.isNull(bo.getQuestionExt())) {
            throw new BusinessException(AppErrorCode.APP_QUESTION_EXT_EXIST_CODE, AppErrorCode.APP_QUESTION_EXT_EXIST_MSG);
        }
        if (!bo.getModuleType().equals(StudyModuleResourceTypeEnum.QUESTION_BANK.getId())
                && (CollectionUtils.isEmpty(bo.getResourceList())) || Objects.isNull(bo.getModuleType())) {
            throw new BusinessException(AppErrorCode.APP_RESOURCE_REL_RESOURCE_EXIST_CODE, AppErrorCode.APP_RESOURCE_REL_RESOURCE_EXIST_MSG);
        }
    }
} 