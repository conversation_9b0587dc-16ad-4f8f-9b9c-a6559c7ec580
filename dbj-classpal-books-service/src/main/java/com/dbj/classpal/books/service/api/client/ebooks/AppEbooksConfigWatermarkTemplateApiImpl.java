package com.dbj.classpal.books.service.api.client.ebooks;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.ebooks.AppEbooksConfigWatermarkTemplateApi;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigWatermarkTemplateEditApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigWatermarkTemplateQueryApiBO;
import com.dbj.classpal.books.client.bo.ebooks.AppEbooksConfigWatermarkTemplateSaveApiBO;
import com.dbj.classpal.books.client.dto.ebooks.AppEbooksConfigWatermarkTemplateQueryApiDTO;
import com.dbj.classpal.books.service.biz.ebooks.IAppEbooksConfigWatermarkTemplateBiz;
import com.dbj.classpal.books.service.entity.ebooks.AppEbooksConfigWatermarkTemplate;
import com.dbj.classpal.books.service.service.ebooks.IAppEbooksConfigWatermarkTemplateService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEbooksConfigWatermarkTemplateApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@RestController
public class AppEbooksConfigWatermarkTemplateApiImpl implements AppEbooksConfigWatermarkTemplateApi {

    @Resource
    private IAppEbooksConfigWatermarkTemplateService watermarkTemplateService;


    @Override
    public RestResponse<Page<AppEbooksConfigWatermarkTemplateQueryApiDTO>> pageInfo(PageInfo<AppEbooksConfigWatermarkTemplateQueryApiBO> pageRequest) throws BusinessException{
        return RestResponse.success(watermarkTemplateService.pageInfo(pageRequest));
    }

    @Override
    public RestResponse<List<AppEbooksConfigWatermarkTemplateQueryApiDTO>> getAll() {
        return RestResponse.success(watermarkTemplateService.getAll());
    }

    @Override
    public RestResponse<Boolean> saveEbooksConfigWatermarkTemplate(AppEbooksConfigWatermarkTemplateSaveApiBO bo) throws BusinessException {
        return RestResponse.success(watermarkTemplateService.saveEbooksConfigWatermarkTemplate(bo));
    }

    @Override
    public RestResponse<Boolean> updateEbooksConfigWatermarkTemplate(AppEbooksConfigWatermarkTemplateEditApiBO bo) throws BusinessException{
        return RestResponse.success(watermarkTemplateService.updateEbooksConfigWatermarkTemplate(bo));
    }

    @Override
    public RestResponse<Boolean> deleteEbooksConfigWatermarkTemplate(CommonIdsApiBO bo) throws BusinessException {
        return RestResponse.success(watermarkTemplateService.deleteEbooksConfigWatermarkTemplate(bo));
    }
}
