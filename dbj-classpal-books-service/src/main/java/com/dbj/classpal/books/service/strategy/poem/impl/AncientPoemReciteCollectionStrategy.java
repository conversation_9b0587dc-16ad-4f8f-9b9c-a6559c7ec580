package com.dbj.classpal.books.service.strategy.poem.impl;

import com.dbj.classpal.books.client.dto.poem.AncientPoemRelateDTO;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemReciteCollectionBiz;
import com.dbj.classpal.books.service.entity.poem.AncientPoem;
import com.dbj.classpal.books.service.entity.poem.AncientPoemBusinessRef;
import com.dbj.classpal.books.service.entity.poem.AncientPoemReciteCollection;
import com.dbj.classpal.books.service.strategy.poem.IAncientPoemReleateStrategy;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 古诗背诵合集 引用策略实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Component
public class AncientPoemReciteCollectionStrategy implements IAncientPoemReleateStrategy {

    @Resource
    private IAncientPoemReciteCollectionBiz ancientPoemReciteCollectionBiz;
    @Override
    public List<AncientPoemRelateDTO> getAncientPoemRelate(Integer businessType, List<AncientPoemBusinessRef> ancientPoemBusinessRefList, AncientPoem ancientPoem) {
        if(CollectionUtils.isEmpty(ancientPoemBusinessRefList)){
            return Collections.emptyList();
        }
        List<Integer> businessIds = ancientPoemBusinessRefList.stream().map(ancientPoemBusinessRef -> ancientPoemBusinessRef.getBusinessId()).collect(Collectors.toList()) ;
        Map<Integer, String> businessTypeGroup = ancientPoemBusinessRefList.stream().collect(Collectors.toMap(AncientPoemBusinessRef::getBusinessId, AncientPoemBusinessRef::getJumpType, (key1, key2) -> key1));
        List<AncientPoemReciteCollection> ancientPoemReciteCollections = ancientPoemReciteCollectionBiz.listByIds(businessIds);
        return ancientPoemReciteCollections.stream().map(reciteCollection ->{
                AncientPoemRelateDTO ancientPoemRelateDTO = new AncientPoemRelateDTO();
                ancientPoemRelateDTO.setAncientPoemId(ancientPoem.getId());
                ancientPoemRelateDTO.setAncientPoemTitle(ancientPoem.getTitle());
                ancientPoemRelateDTO .setBusinessType(businessType);
                ancientPoemRelateDTO .setBusinessId(reciteCollection.getId());
                ancientPoemRelateDTO .setSourceName(reciteCollection.getTitle());
                ancientPoemRelateDTO .setOther(Map.of("categoryId", reciteCollection.getCategoryId(),
                                    "collectionId", reciteCollection.getId()
                        , "categoryType",businessTypeGroup.get(reciteCollection.getId())));
                return ancientPoemRelateDTO;
                }
        ).collect(Collectors.toList());
    }
}
