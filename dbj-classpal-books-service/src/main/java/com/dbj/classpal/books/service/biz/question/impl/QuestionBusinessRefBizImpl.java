package com.dbj.classpal.books.service.biz.question.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.bo.question.QuestionBusinessRefPageBO;
import com.dbj.classpal.books.common.dto.question.QuestionBusinessRefDTO;
import com.dbj.classpal.books.service.biz.question.QuestionBiz;
import com.dbj.classpal.books.service.biz.question.QuestionBusinessRefBiz;
import com.dbj.classpal.books.service.entity.question.Question;
import com.dbj.classpal.books.service.entity.question.QuestionBusinessRef;
import com.dbj.classpal.books.service.mapper.question.QuestionBusinessRefMapper;
import com.dbj.classpal.books.service.mapper.question.QuestionMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 题目业务实现类
 */
@Slf4j
@Service
public class QuestionBusinessRefBizImpl extends ServiceImpl<QuestionBusinessRefMapper, QuestionBusinessRef> implements QuestionBusinessRefBiz {

    @Resource
    private QuestionBusinessRefMapper questionBusinessRefMapper;

    @Override
    public Page<QuestionBusinessRefDTO> pageList(Page<QuestionBusinessRefDTO> page, QuestionBusinessRefPageBO condition) {
        return questionBusinessRefMapper.pageList(page,condition);
    }

    @Override
    public Integer getMaxSortNum(QuestionBusinessRefPageBO questionBusinessRefPageBO) {
        return questionBusinessRefMapper.getMaxSortNum(questionBusinessRefPageBO);
    }
}