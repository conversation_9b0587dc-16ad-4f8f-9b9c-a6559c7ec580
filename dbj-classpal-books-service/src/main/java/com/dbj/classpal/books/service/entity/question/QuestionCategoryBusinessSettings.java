package com.dbj.classpal.books.service.entity.question;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 题库业务设置统一管理表
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("question_category_business_settings")
@Tag(name = "QuestionCategoryBusinessSettings", description = "题库业务设置统一管理表")
@AllArgsConstructor
@NoArgsConstructor
public class QuestionCategoryBusinessSettings extends BizEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务ID
     */
    @Schema(description = "业务ID")
    private Integer businessId;

    /**
     * 业务类型（1-学习模块答题 2-图书书内码答题 3-点读答题）
     */
    @Schema(description = "业务类型（1-学习模块答题 2-图书书内码答题 3-点读答题）")
    private Integer businessType;

    /**
     * 出题方式
     */
    @Schema(description = "出题方式")
    private Integer questionMethod;

    /**
     * 题目数量
     */
    @Schema(description = "题目数量")
    private Integer questionNum;

    /**
     * 状态（1-启用 0-禁用）
     */
    @Schema(description = "状态（1-启用 0-禁用）")
    private Integer status;
}
