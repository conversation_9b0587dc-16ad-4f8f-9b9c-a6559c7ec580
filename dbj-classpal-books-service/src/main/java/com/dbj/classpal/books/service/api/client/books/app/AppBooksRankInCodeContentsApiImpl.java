package com.dbj.classpal.books.service.api.client.books.app;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.books.client.api.books.app.AppBooksRankInCodeContentsApi;
import com.dbj.classpal.books.client.bo.books.app.BooksRankInCodesContentsTreeAppBO;
import com.dbj.classpal.books.client.dto.books.BooksRankInCodesContentsTreeDTO;
import com.dbj.classpal.books.client.dto.books.app.BooksRankInCodesContentsTreeAppDTO;
import com.dbj.classpal.books.common.enums.books.ContentsTypeEnum;
import com.dbj.classpal.books.common.enums.books.SourceEnum;
import com.dbj.classpal.books.service.biz.books.IBooksRankClassifyBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInCodesContentsBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInfoBiz;
import com.dbj.classpal.books.service.biz.books.IBooksScanInfoBiz;
import com.dbj.classpal.books.service.biz.books.IBooksUserRankInCodeStudyLogBiz;
import com.dbj.classpal.books.service.biz.books.IBooksUserRankStudyLogBiz;
import com.dbj.classpal.books.service.entity.books.BooksRankClassify;
import com.dbj.classpal.books.service.entity.books.BooksRankInCodesContents;
import com.dbj.classpal.books.service.entity.books.BooksRankInfo;
import com.dbj.classpal.books.service.entity.books.BooksScanInfo;
import com.dbj.classpal.books.service.entity.books.BooksUserRankInCodeStudyLog;
import com.dbj.classpal.books.service.entity.books.BooksUserRankStudyLog;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import com.dbj.classpal.framework.utils.util.ContextAppUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@RestController
public class AppBooksRankInCodeContentsApiImpl implements AppBooksRankInCodeContentsApi {

    @Resource
    private IBooksRankInCodesContentsBiz booksRankInCodesContentsBiz;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<List<BooksRankInCodesContentsTreeAppDTO>> list(BooksRankInCodesContentsTreeAppBO bo) throws BusinessException {
        return RestResponse.success(booksRankInCodesContentsBiz.getTree(bo.getRankClassifyId()));
    }





}
