package com.dbj.classpal.books.service.api.client.question;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.question.QuestionBusinessRefApi;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.question.QuestionBusinessRefPageApiBO;
import com.dbj.classpal.books.client.bo.question.QuestionBusinessRefSaveApiBO;
import com.dbj.classpal.books.client.bo.question.QuestionBusinessRefSortApiBO;
import com.dbj.classpal.books.client.dto.question.QuestionBusinessRefApiDTO;
import com.dbj.classpal.books.common.bo.question.QuestionBusinessRefPageBO;
import com.dbj.classpal.books.common.dto.question.QuestionBusinessRefDTO;
import com.dbj.classpal.books.common.enums.AlbumMenusOrderEnum;
import com.dbj.classpal.books.client.enums.BusinessTypeEnum;
import com.dbj.classpal.books.common.enums.question.QuestionTypeEnum;
import com.dbj.classpal.books.service.biz.question.QuestionBiz;
import com.dbj.classpal.books.service.biz.question.QuestionBusinessRefBiz;
import com.dbj.classpal.books.service.entity.question.Question;
import com.dbj.classpal.books.service.entity.question.QuestionBusinessRef;
import com.dbj.classpal.books.service.service.question.QuestionBusinessRefService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

@RequiredArgsConstructor
@RestController
public class QuestionBusinessRefApiServiceImpl implements QuestionBusinessRefApi {

    @Resource
    private QuestionBusinessRefService questionBusinessRefService;
    @Resource
    private QuestionBusinessRefBiz questionBusinessRefBiz;
    @Resource
    private QuestionBiz questionBiz;

    @Override
    public RestResponse<Page<QuestionBusinessRefApiDTO>> pageList(PageInfo<QuestionBusinessRefPageApiBO> pageApiBO) {
        // 1. 转换查询条件
        QuestionBusinessRefPageBO queryBO = new QuestionBusinessRefPageBO();
        BeanUtil.copyProperties(pageApiBO.getData(), queryBO);

        PageInfo<QuestionBusinessRefPageBO> servicePageRequest = new PageInfo<>();
        servicePageRequest.setPageNum(pageApiBO.getPageNum());
        servicePageRequest.setPageSize(pageApiBO.getPageSize());
        servicePageRequest.setData(queryBO);
        Page<QuestionBusinessRefDTO> page = questionBusinessRefService.pageList(servicePageRequest);
        return RestResponse.success((Page<QuestionBusinessRefApiDTO>) page.convert(vo -> {
            QuestionBusinessRefApiDTO dto = new QuestionBusinessRefApiDTO();
            BeanUtil.copyProperties(vo, dto);
            if (dto.getType() != null){
                dto.setTypeName(QuestionTypeEnum.getByValue(dto.getType()).getDesc());
            }
            return dto;
        }));
    }

    @Override
    public RestResponse<Boolean> saveQuestionBusinessRef(QuestionBusinessRefSaveApiBO bo) throws BusinessException {
        if (bo.getQuestionIdList() == null || CollectionUtils.isEmpty(bo.getQuestionIdList())){
            throw new BusinessException(APP_EVALUATION_NODE_SAVE_QUESTION_NO_ID_CODE,APP_EVALUATION_NODE_SAVE_QUESTION_NO_ID_MSG);
        }
        QuestionBusinessRefPageBO questionBusinessRefPageBO = new QuestionBusinessRefPageBO();
        BeanUtil.copyProperties(bo, questionBusinessRefPageBO);
        Map<Integer,Integer>orderNumMap = new HashMap<>();
        List<Question> questions = questionBiz.listByIds(bo.getQuestionIdList());
        if (CollectionUtils.isNotEmpty(questions)){
            orderNumMap = questions.stream().collect(Collectors.toMap(Question::getId,Question::getWeight));
        }
        Map<Integer, Integer> finalOrderNumMap = orderNumMap;
        return RestResponse.success(questionBusinessRefBiz.saveBatch(bo.getQuestionIdList().stream().map(d -> {
            QuestionBusinessRef questionBusinessRef = new QuestionBusinessRef();
            questionBusinessRef.setBusinessId(bo.getBusinessId());
            questionBusinessRef.setQuestionId(d);
            if (finalOrderNumMap.containsKey(d)) {
                questionBusinessRef.setOrderNum(finalOrderNumMap.get(d));
            }else{
                questionBusinessRef.setOrderNum(0);
            }
            questionBusinessRef.setBusinessType(bo.getBusinessType());
            return questionBusinessRef;
        }).collect(Collectors.toList())));
    }

    @Override
    public RestResponse<Boolean> deleteQuestionBusinessRef(CommonIdsApiBO bo) {
        return RestResponse.success(questionBusinessRefBiz.removeBatchByIds(bo.getIds()));
    }

    @Override
    public RestResponse<Boolean> sortQuestionBusinessRef(QuestionBusinessRefSortApiBO bo) throws BusinessException {
        List<QuestionBusinessRef> questionBusinessRefList = new LinkedList<>(questionBusinessRefBiz.lambdaQuery()
                .eq(QuestionBusinessRef::getBusinessId,bo.getAppEvaluationNodeId())
                .eq(QuestionBusinessRef::getBusinessType, BusinessTypeEnum.EVALUATION_BUSINESS.getCode())
                .orderByDesc(QuestionBusinessRef::getOrderNum)
                .list()
                .stream()
                .filter(d -> !(d.getId().equals(bo.getId())))
                .collect(Collectors.toList())
        );
        if (CollectionUtils.isEmpty(questionBusinessRefList)) {
            throw new BusinessException(APP_EVALUATION_NODE_QUESTION_SORT_FAIL_NONE_CODE,APP_EVALUATION_NODE_QUESTION_SORT_FAIL_NONE_MSG);
        }
        //查询当前关联题目关系
        QuestionBusinessRef questionBusinessRef = questionBusinessRefBiz.getById(bo.getId());

        ListIterator<QuestionBusinessRef> iterator = questionBusinessRefList.listIterator();
        while (iterator.hasNext()) {
            QuestionBusinessRef current = iterator.next();
            if (current.getId().equals(bo.getAimId())) {
                if (bo.getOrder().equals(AlbumMenusOrderEnum.ORDER_BEFORE.getCode())){
                    if (iterator.hasPrevious()){
                        iterator.previous(); // 回退到 "Bob" 前
                        iterator.add(questionBusinessRef); // 插入新对象
                    }else{
                        questionBusinessRefList.add(0,questionBusinessRef);
                    }
                }else{
                    if (iterator.hasNext()){
                        iterator.next(); // 回退到 "Bob" 后
                        iterator.add(questionBusinessRef); // 插入新对象
                    }else{
                        questionBusinessRefList.add(questionBusinessRef);
                    }
                }
                break;
            }
        }
        //重新排序赋值
        for (int i = 0; i < questionBusinessRefList.size(); i++) {
            questionBusinessRefList.get(i).setOrderNum(questionBusinessRefList.size()-i);
        }
        return RestResponse.success(questionBusinessRefBiz.updateBatchById(questionBusinessRefList));
    }
}