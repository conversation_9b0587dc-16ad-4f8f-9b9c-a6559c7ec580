package com.dbj.classpal.books.service.util.audio;


import com.dbj.classpal.books.common.constant.AudioConstants;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import ws.schild.jave.Encoder;
import ws.schild.jave.EncoderException;
import ws.schild.jave.MultimediaObject;
import ws.schild.jave.encode.AudioAttributes;
import ws.schild.jave.encode.EncodingAttributes;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.UUID;

@Slf4j
public class WavToMp3Converter {
    /**
     * 读取本地文件
     * @param args
     */
    public static void main(String[] args) {
        // 输入的wav文件路径
        File wavFile = new File("E:\\audio\\output\\output_with_background-5s.wav");
        // 输出的mp3文件路径
        File mp3File = new File(AudioConstants.WAV_TO_MP3_OUTPUT_DIR + File.separator + AudioConstants.WAV_TO_MP3_PREFIX + UUID.randomUUID().toString().replace("-", "") + AudioConstants.BGM_FORMAT_MP3);

        wavToMp3Converter(wavFile, mp3File);

    }

//    public static void main(String[] args) {
//        String onlineWavUrl = "https://example.com/sample.wav"; // 替换为实际在线WAV文件URL
//        String outputMp3Path = "output.mp3"; // 本地输出路径
//
//        try {
//            // 1. 从URL下载WAV文件到临时位置
//            File tempWavFile = downloadOnlineFile(onlineWavUrl);
//
//            // 2. 配置音频属性
//            AudioAttributes audioAttributes = new AudioAttributes();
//            audioAttributes.setCodec("libmp3lame");
//            audioAttributes.setBitRate(128000);
//            audioAttributes.setChannels(2);
//            audioAttributes.setSamplingRate(44100);
//
//            // 3. 配置编码属性
//            EncodingAttributes encodingAttributes = new EncodingAttributes();
//            encodingAttributes.setOutputFormat("mp3");
//            encodingAttributes.setAudioAttributes(audioAttributes);
//
//            // 4. 创建编码器并执行转换
//            File outputMp3File = new File(outputMp3Path);
//            Encoder encoder = new Encoder();
//            encoder.encode(new MultimediaObject(tempWavFile), outputMp3File, encodingAttributes);
//
//            System.out.println("转换成功！MP3文件已保存至: " + outputMp3File.getAbsolutePath());
//
//            // 5. 清理临时文件
//            if(tempWavFile.delete()) {
//                System.out.println("临时WAV文件已删除");
//            } else {
//                System.out.println("临时WAV文件删除失败，请手动清理");
//            }
//
//        } catch (Exception e) {
//            System.err.println("处理失败: " + e.getMessage());
//            log.error(e.getMessage());
//        }
//    }

    public static void wavToMp3Converter(File sourceFile, File targetFile) {
        try {
            // 配置音频属性
            AudioAttributes audioAttributes = new AudioAttributes();
            // 设置编码格式为mp3
            audioAttributes.setCodec("libmp3lame");
            // 设置比特率（单位：kbps）
            audioAttributes.setBitRate(128000);
            // 设置声道数
            audioAttributes.setChannels(2);
            // 设置采样率（单位：Hz）
            audioAttributes.setSamplingRate(44100);

            // 配置编码属性
            EncodingAttributes encodingAttributes = new EncodingAttributes();
            encodingAttributes.setOutputFormat("mp3");
            encodingAttributes.setAudioAttributes(audioAttributes);

            // 创建编码器并执行转换
            Encoder encoder = new Encoder();
            encoder.encode(new MultimediaObject(sourceFile), targetFile, encodingAttributes);

            System.out.println("转换成功！");
        } catch (IllegalArgumentException | EncoderException e) {
            System.err.println("转换失败: " + e.getMessage());
            log.error(e.getMessage());
        }
    }

    private static File downloadOnlineFile(String fileUrl) throws IOException {
        URL url = new URL(fileUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");

        int responseCode = connection.getResponseCode();
        if (responseCode != HttpURLConnection.HTTP_OK) {
            throw new IOException("下载失败，HTTP响应码: " + responseCode);
        }

        // 创建临时文件
        File tempFile = File.createTempFile("audio-converter-", ".wav");
        tempFile.deleteOnExit(); // 程序退出时尝试删除

        // 下载文件
        try (InputStream in = connection.getInputStream();
             FileOutputStream out = new FileOutputStream(tempFile)) {

            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
        }

        return tempFile;
    }

}
