package com.dbj.classpal.books.service.util.audio;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.dbj.classpal.books.common.bo.audio.SpeechBO;
import com.dbj.classpal.books.common.constant.AudioConstants;
import com.dbj.classpal.books.common.dto.audio.SpeechDTO;
import com.dbj.classpal.books.common.enums.audio.AudioEmotionEnum;
import com.dbj.classpal.books.common.enums.audio.AudioSpeakerTypeEnum;
import com.dbj.classpal.books.service.entity.audio.AudioEmotionClassifyConfig;
import com.dbj.classpal.books.service.entity.audio.AudioSpeaker;
import com.dbj.classpal.books.service.service.audio.IAudioEmotionClassifyConfigService;
import com.dbj.classpal.books.service.service.audio.IAudioSpeakerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 发音人模板生成工具
 * <AUTHOR>
 */
@Slf4j
@Component
public class AudioSpeakerTemplateGenerateUtil {
    @Autowired
    private BatchSpeechLongSynthesizerService synthesizerService;
    @Autowired
    private AudioUploadOSS audioUploadOSS;
    @Autowired
    private IAudioSpeakerService audioSpeakerService;
    @Autowired
    private IAudioEmotionClassifyConfigService audioEmotionClassifyConfigService;

    String suffix = "_template.wav";

    /**
     * 注意： 发音人模板数据生成后除非阿里云官方API参数有改动需求，否则不用重复执行以下代码
     */
    @Transactional(rollbackFor = Exception.class)
    public void generateTemplate() {
        List<AudioSpeaker> list = audioSpeakerService.getAllList();
        if (CollectionUtil.isEmpty(list)) {
            log.error("查询发音人列表为空");
            return;
        }
        Map<String, AudioSpeaker> speakerMaps = list.stream().collect(Collectors.toMap(AudioSpeaker::getVoice, v -> v));

        List<SpeechBO> speechBOList = new ArrayList<>();
        for (AudioSpeaker speaker : list) {
            SpeechBO bo = new SpeechBO();
            bo.setVolume(100);
            bo.setVoice(speaker.getVoice());
            if (Objects.equals(AudioSpeakerTypeEnum.EMOTION.getCode(), speaker.getVoiceType())) {
                bo.setLongText("<speak>" +speaker.getPrompt() + "</speak>");
            } else {
                bo.setLongText(speaker.getPrompt());
            }
            speechBOList.add(bo);
        }

        List<AudioSpeaker> updateList = new ArrayList<>();
        List<AudioEmotionClassifyConfig> saveList = new ArrayList<>();

//        List<SpeechDTO> audioFileList  = synthesizerService.processBatch(speechBOList);
//        if (CollectionUtil.isEmpty(audioFileList)) {
//            log.error("生成发音人模板失败！");
//            return;
//        }
//        try {
//            // 休眠5秒，等待合成全部成功
//            Thread.sleep(5000);
//        } catch (InterruptedException e) {
//            throw new RuntimeException(e);
//        }
//
////        for (Map.Entry<String, File> entry : audioFileMaps.entrySet()) {
//        for (SpeechDTO dto : audioFileList) {
//            String fileName = dto.getFileName();
//            File file = dto.getFile();
//            String uploadUrl = audioUploadOSS.upload(fileName, file, AudioConstants.OSS_AUDIO_TEMPLATE_DIR);
//            System.out.println("音频上传oss地址：" + uploadUrl);
//
//            // 截取文件名 "abin_template.wav"
//            String voice = fileName.substring(0, fileName.lastIndexOf(suffix));
//            if (!speakerMaps.containsKey(voice)) {
//                continue;
//            }
//            AudioSpeaker audioSpeaker = speakerMaps.get(voice);
//            if (!Objects.equals(AudioSpeakerTypeEnum.EMOTION.getCode(), audioSpeaker.getVoiceType())) {
//                audioSpeaker.setAudioModelUrl(uploadUrl);
//                updateList.add(audioSpeaker);
//            } else {
//                // 多情感
//                AudioEmotionEnum emotionEnum = AudioEmotionEnum.getByCode(voice);
//                Assert.isFalse(emotionEnum == null, "【多情感】- 找不到对应的多情感枚举：{}", voice);
//
//                emotionEnum.getEmotions().forEach(emotion -> {
//                    AudioEmotionClassifyConfig emotionConfig = new AudioEmotionClassifyConfig();
//                    emotionConfig.setAudioSpeakerId(audioSpeaker.getId());
//                    emotionConfig.setEmotion(emotion);
//                    emotionConfig.setAudioModelUrl(uploadUrl);
//                    saveList.add(emotionConfig);
//                });
//            }
//        }
//        if (CollectionUtil.isNotEmpty(updateList)) {
//            audioSpeakerService.updateBatch(updateList);
//        }
//        if (CollectionUtil.isNotEmpty(saveList)) {
//            audioEmotionClassifyConfigService.saveBatch(saveList);
//        }
    }
}
