package com.dbj.classpal.books.service.entity.pointreading;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 点读书目录表
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("point_reading_menu")
@Schema(name = "PointReadingMenu", description = "点读书目录表")
public class PointReadingMenu extends BizEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "所属点读书ID")
    private Integer bookId;

    @Schema(description = "目录名称")
    private String name;

    @Schema(description = "父级目录ID")
    private Integer parentId;

    @Schema(description = "目录层级")
    private Integer level;

    @Schema(description = "排序")
    private Integer sortNum;

    @Schema(description = "状态：0-停用 1-正常")
    private Integer status;

    @Schema(description = "版本号")
    private Integer version;
}
