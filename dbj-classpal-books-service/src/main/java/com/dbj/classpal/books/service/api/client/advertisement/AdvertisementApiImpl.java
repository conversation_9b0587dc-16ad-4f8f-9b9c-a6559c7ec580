package com.dbj.classpal.books.service.api.client.advertisement;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.advertisement.AdvertisementApi;
import com.dbj.classpal.books.client.bo.advertisement.AdevertisementDelBO;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementAppBO;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementPageBO;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementStatusUpdateBO;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementUpsertBO;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementAppDTO;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementDTO;
import com.dbj.classpal.books.service.service.advertisement.IAdvertisementService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 广告信息表 远程Api 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@RestController
public class AdvertisementApiImpl implements AdvertisementApi {

    @Resource
    private IAdvertisementService advertisementService;


    @Override
    public RestResponse<Page<AdvertisementDTO>> getAdvertisementPage(PageInfo<AdvertisementPageBO> pageInfo) {
        return RestResponse.success(advertisementService.getAdvertisementPage(pageInfo));
    }


    @Override
    public RestResponse<AdvertisementDTO> getAdvertisementInfo(CommonIdApiBO bo) throws BusinessException {
        return RestResponse.success(advertisementService.getAdvertisementInfo(bo));
    }

    @Override
    public RestResponse<Boolean> saveAdvertisement(AdvertisementUpsertBO bo) throws BusinessException {
        return RestResponse.success(advertisementService.saveAdvertisement(bo));
    }

    @Override
    public RestResponse<Boolean> updateAdvertisement(AdvertisementUpsertBO bo) throws BusinessException {
        return RestResponse.success(advertisementService.updateAdvertisement(bo));
    }

    @Override
    public RestResponse<Boolean> updateAdvertisementStatus(AdvertisementStatusUpdateBO bo) {
        return RestResponse.success(advertisementService.updateAdvertisementStatus(bo));
    }

    @Override
    public RestResponse<Boolean> deleteAdvertisement(AdevertisementDelBO bo) throws BusinessException {
        return RestResponse.success(advertisementService.deleteAdvertisement(bo));
    }

    @Override
    public RestResponse<List<AdvertisementAppDTO>> getAppUserAdvertisement(AdvertisementAppBO bo) {
        return RestResponse.success(advertisementService.getAppUserAdvertisement(bo));
    }
}
