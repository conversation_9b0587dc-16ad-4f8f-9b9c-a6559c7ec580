package com.dbj.classpal.books.service.service.pinyin.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinAppBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinDelBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinPageBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinStatusUpdateBO;
import com.dbj.classpal.books.client.bo.pinyin.PinyinUpsertBO;
import com.dbj.classpal.books.client.dto.material.AppCommonMediaDTO;
import com.dbj.classpal.books.client.dto.pinyin.PinyinAppDTO;
import com.dbj.classpal.books.client.dto.pinyin.PinyinDTO;
import com.dbj.classpal.books.common.enums.BusinessTypeEnum;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBusinessRefBiz;
import com.dbj.classpal.books.service.biz.pinyin.IPinyinBiz;
import com.dbj.classpal.books.service.biz.pinyin.IPinyinClassifyBiz;
import com.dbj.classpal.books.service.entity.pinyin.Pinyin;
import com.dbj.classpal.books.service.entity.pinyin.PinyinClassify;
import com.dbj.classpal.books.service.mapper.pinyin.PinyinMapper;
import com.dbj.classpal.books.service.service.pinyin.IPinyinService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.redisson.config.RedissonRedisUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.PINYIN_VERIFICATION_FAIL_CODE;
import static com.dbj.classpal.books.common.constant.RedisKeyConstants.DBJ_CLASSPAL_PINYIN_CLASSIFY_DETAIL_CACHE_KEY;

/**
 * <p>
 * 拼音信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Service
public class PinyinServiceImpl extends ServiceImpl<PinyinMapper, Pinyin> implements IPinyinService {

    @Resource
    private IPinyinBiz pinyinBiz;

    @Resource
    private IPinyinClassifyBiz pinyinClassifyBiz;

    @Resource
    private IAppMaterialBusinessRefBiz appMaterialBusinessRefBiz;

    @Resource
    private RedissonRedisUtils redisUtils;

    @Override
    public Page<PinyinDTO> getPinyinPage(PageInfo<PinyinPageBO> pageInfo) {
        Page<PinyinDTO> page = pinyinBiz.getPinyinPage(pageInfo.getPage(), pageInfo.getData());
        List<PinyinDTO> pinyinDTOList = page.getRecords();
        if (CollUtil.isEmpty(pinyinDTOList)) {
            return page;
        }
        setPinyinMedias(pinyinDTOList);
        return page;
    }

    @Override
    public PinyinDTO getPinyinInfo(CommonIdApiBO bo) {
        Pinyin pinyin = pinyinBiz.getById(bo.getId());
        PinyinDTO pinyinDTO = BeanUtil.copyProperties(pinyin, PinyinDTO.class);

        List<AppCommonMediaDTO> oralAnimationMedias = appMaterialBusinessRefBiz
                .getCommonBusinessList(Collections.singletonList(bo.getId()), BusinessTypeEnum.PINYIN_ORAL_ANIMATION_BUSINESS);
        List<AppCommonMediaDTO> pronouncesMedias = appMaterialBusinessRefBiz
                .getCommonBusinessList(Collections.singletonList(bo.getId()), BusinessTypeEnum.PINYIN_PRONOUNCE_BUSINESS);
        List<AppCommonMediaDTO> fourTonePronounceMedias = appMaterialBusinessRefBiz
                .getCommonBusinessList(Collections.singletonList(bo.getId()), BusinessTypeEnum.PINYIN_FOUR_TONE_PRONOUNCE_BUSINESS);

        pinyinDTO.setOralAnimationMedias(oralAnimationMedias);
        pinyinDTO.setPronounceMedias(pronouncesMedias);
        pinyinDTO.setFourTonePronounceMedias(fourTonePronounceMedias);
        return pinyinDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean savePinyin(PinyinUpsertBO bo) throws BusinessException {
        checkPinyinUpsertParams(bo, false);
        Pinyin pinyin = upsertPinYin(bo);
        desertPinyinMedias(pinyin.getId(), bo);
        redisUtils.delKey(MessageFormat.format(DBJ_CLASSPAL_PINYIN_CLASSIFY_DETAIL_CACHE_KEY, bo.getId()));
        return Boolean.TRUE;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updatePinyin(PinyinUpsertBO bo) throws BusinessException {
        checkPinyinUpsertParams(bo, true);
        Pinyin pinyin = upsertPinYin(bo);
        desertPinyinMedias(pinyin.getId(), bo);
        redisUtils.delKey(MessageFormat.format(DBJ_CLASSPAL_PINYIN_CLASSIFY_DETAIL_CACHE_KEY, bo.getId()));
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updatePinyinStatus(PinyinStatusUpdateBO bo) {
        Pinyin updPinyin = new Pinyin()
                .setId(bo.getId())
                .setStatus(bo.getStatus());
        redisUtils.delKey(MessageFormat.format(DBJ_CLASSPAL_PINYIN_CLASSIFY_DETAIL_CACHE_KEY, bo.getId()));
        return pinyinBiz.updateById(updPinyin);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deletePinyin(PinyinDelBO bo) {
        pinyinBiz.removeByIds(bo.getIds());
        deletedPinyinMedias(bo.getIds());
        bo.getIds().forEach(id -> redisUtils.delKey(MessageFormat.format(DBJ_CLASSPAL_PINYIN_CLASSIFY_DETAIL_CACHE_KEY, bo.getPinyinClassifyId())));
        return Boolean.TRUE;
    }

    @Override
    public PinyinAppDTO getAppPinyinData(PinyinAppBO bo) {
        String catchKey = MessageFormat.format(DBJ_CLASSPAL_PINYIN_CLASSIFY_DETAIL_CACHE_KEY, bo.getClassifyId());
        if(redisUtils.hasKey(catchKey)) {
            return JSON.parseObject(redisUtils.getValue(catchKey), PinyinAppDTO.class);
        }
        PinyinClassify pinyinClassify = pinyinClassifyBiz.getById(bo.getClassifyId());
        if(pinyinClassify == null) {
            return null;
        }
        PinyinAppDTO pinyinAppDTO = BeanUtil.copyProperties(pinyinClassify, PinyinAppDTO.class);

        List<Pinyin> pinyinList = pinyinBiz.lambdaQuery().eq(Pinyin::getClassifyId, bo.getClassifyId()).orderByDesc(Pinyin::getSort).list();
        List<PinyinDTO> pinyinDTOList = BeanUtil.copyToList(pinyinList, PinyinDTO.class);
        setPinyinMedias(pinyinDTOList);
        pinyinAppDTO.setPinyins(pinyinDTOList);
        redisUtils.setValue(catchKey, JSON.toJSONString(pinyinAppDTO) ,7, TimeUnit.DAYS);
        return pinyinAppDTO;
//        List<PinyinClassify> classifyList = pinyinClassifyBiz
//                .lambdaQuery()
//                .eq(bo.getClassifyId() != null, PinyinClassify::getId, bo.getClassifyId())
//                .orderByDesc(PinyinClassify::getSort).list();
//        if(CollUtil.isEmpty(classifyList)) {
//            return Collections.emptyList();
//        }
//        List<Pinyin> pinyinList = pinyinBiz.lambdaQuery().orderByDesc(Pinyin::getSort).list();
//        List<PinyinDTO> pinyinDTOList = BeanUtil.copyToList(pinyinList, PinyinDTO.class);
//
//        setPinyinMedias(pinyinDTOList);
//
//        Map<Integer, List<PinyinDTO>> classifyIdGroup = pinyinDTOList.stream()
//                .collect(Collectors.groupingBy(PinyinDTO::getClassifyId, Collectors.toList()));
//
//        List<PinyinAppDTO> pinyinAppDTOList = classifyList.stream()
//                .map(classify -> {
//                    PinyinAppDTO pinyinAppDTO = BeanUtil.copyProperties(classify, PinyinAppDTO.class);
//                    pinyinAppDTO.setPinyins(classifyIdGroup.getOrDefault(classify.getId(), Collections.emptyList()));
//                    return pinyinAppDTO;
//                }).toList();
//        return pinyinAppDTOList;
    }

    /**
     * 校验拼音参数
     *
     * @param bo 拼音UpsertBO
     * @param isUpdate 是否更新
     * @throws BusinessException 业务异常
     */
    private void checkPinyinUpsertParams(PinyinUpsertBO bo, boolean isUpdate) throws BusinessException {
        //标题不能重复
        LambdaQueryWrapper<Pinyin> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Pinyin::getTitle, bo.getTitle())
                .eq(Pinyin::getTitle, bo.getTitle());
        if (isUpdate) {
            Assert.notNull(bo.getId(), () -> new BusinessException(PINYIN_VERIFICATION_FAIL_CODE, "ID不能为空"));
            queryWrapper.ne(Pinyin::getId, bo.getId());
        } else {
            bo.setId(null);
        }
        long count = pinyinBiz.count(queryWrapper);
        Assert.isTrue(count == 0, () -> new BusinessException(PINYIN_VERIFICATION_FAIL_CODE, "拼音标题已存在"));
    }

    private Pinyin upsertPinYin(PinyinUpsertBO bo) throws BusinessException {
        Pinyin pinyin =  BeanUtil.copyProperties(bo, Pinyin.class);
        boolean upsert = pinyinBiz.saveOrUpdate(pinyin);
        Assert.isTrue(upsert, () -> new BusinessException(PINYIN_VERIFICATION_FAIL_CODE, "信息保存失败"));
        return pinyin;
    }

    /**
     * 删除并插入拼音相关媒体文件
     */
    private void desertPinyinMedias(Integer businessId, PinyinUpsertBO bo) {
        deletedPinyinMedias(Collections.singleton(businessId));
        appMaterialBusinessRefBiz.insert(BusinessTypeEnum.PINYIN_ORAL_ANIMATION_BUSINESS, businessId, bo.getOralAnimationMedias());
        appMaterialBusinessRefBiz.insert(BusinessTypeEnum.PINYIN_PRONOUNCE_BUSINESS, businessId, bo.getPronounceMedias());
        appMaterialBusinessRefBiz.insert(BusinessTypeEnum.PINYIN_FOUR_TONE_PRONOUNCE_BUSINESS, businessId, bo.getFourTonePronounceMedias());
    }

    /**
     * 删除拼音相关媒体文件
     */
    private void deletedPinyinMedias(Collection<Integer> businessIds) {
        appMaterialBusinessRefBiz.delete(businessIds, BusinessTypeEnum.PINYIN_ORAL_ANIMATION_BUSINESS);
        appMaterialBusinessRefBiz.delete(businessIds, BusinessTypeEnum.PINYIN_PRONOUNCE_BUSINESS);
        appMaterialBusinessRefBiz.delete(businessIds, BusinessTypeEnum.PINYIN_FOUR_TONE_PRONOUNCE_BUSINESS);
    }

    private void setPinyinMedias(List<PinyinDTO> pinyinDTOList) {
        Set<Integer> ids = pinyinDTOList.stream().map(PinyinDTO::getId).collect(Collectors.toSet());
        List<AppCommonMediaDTO> oralAnimationMedias = appMaterialBusinessRefBiz
                .getCommonBusinessList(ids, BusinessTypeEnum.PINYIN_ORAL_ANIMATION_BUSINESS);
        List<AppCommonMediaDTO> pronouncesMedias = appMaterialBusinessRefBiz
                .getCommonBusinessList(ids, BusinessTypeEnum.PINYIN_PRONOUNCE_BUSINESS);
        List<AppCommonMediaDTO> fourTonePronounceMedias = appMaterialBusinessRefBiz
                .getCommonBusinessList(ids, BusinessTypeEnum.PINYIN_FOUR_TONE_PRONOUNCE_BUSINESS);

        Map<Integer, List<AppCommonMediaDTO>> oralAnimationMediaMap = oralAnimationMedias.stream().collect(Collectors.groupingBy(AppCommonMediaDTO::getBusinessId));
        Map<Integer, List<AppCommonMediaDTO>> pronounceMediaMap = pronouncesMedias.stream().collect(Collectors.groupingBy(AppCommonMediaDTO::getBusinessId));
        Map<Integer, List<AppCommonMediaDTO>> fourTonePronounceMediaMap = fourTonePronounceMedias.stream().collect(Collectors.groupingBy(AppCommonMediaDTO::getBusinessId));
        pinyinDTOList.forEach(pinyinDTO -> {
            pinyinDTO.setOralAnimationMedias(oralAnimationMediaMap.get(pinyinDTO.getId()));
            pinyinDTO.setPronounceMedias(pronounceMediaMap.get(pinyinDTO.getId()));
            pinyinDTO.setFourTonePronounceMedias(fourTonePronounceMediaMap.get(pinyinDTO.getId()));
        });
    }

}
