package com.dbj.classpal.books.service.biz.advertisement.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.service.biz.advertisement.IAdvertisementConditionBiz;
import com.dbj.classpal.books.service.entity.advertisement.AdvertisementCondition;
import com.dbj.classpal.books.service.mapper.advertisement.AdvertisementConditionMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 广告条件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Deprecated
@Service
public class AdvertisementConditionBizImpl extends ServiceImpl<AdvertisementConditionMapper, AdvertisementCondition> implements IAdvertisementConditionBiz {

}
