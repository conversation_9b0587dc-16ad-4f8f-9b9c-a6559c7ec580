package com.dbj.classpal.books.service.biz.poem.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionBO;
import com.dbj.classpal.books.client.bo.poem.app.AppAncientPoemReciteCollectionPageBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemReciteCollectionDTO;
import com.dbj.classpal.books.client.dto.poem.app.AppAncientPoemReciteCollectionPageDTO;
import com.dbj.classpal.books.service.entity.poem.AncientPoemReciteCollection;
import com.dbj.classpal.books.service.mapper.poem.AncientPoemReciteCollectionMapper;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemReciteCollectionBiz;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.framework.commons.request.PageInfo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 古诗背诵合集表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Service
public class AncientPoemReciteCollectionBizImpl extends ServiceImpl<AncientPoemReciteCollectionMapper, AncientPoemReciteCollection> implements IAncientPoemReciteCollectionBiz {

    @Override
    public List<AncientPoemReciteCollectionDTO> listAncientPoemReciteCollection(AncientPoemReciteCollectionBO ancientPoemReciteCollectionBO) {
        return baseMapper.listAncientPoemReciteCollection(ancientPoemReciteCollectionBO);
    }

    @Override
    public Page<AppAncientPoemReciteCollectionPageDTO> pageAncientPoemReciteCollection(PageInfo<AppAncientPoemReciteCollectionPageBO> pageInfo) {
        return baseMapper.pageAncientPoemReciteCollection(pageInfo.getPage(), pageInfo.getData());
    }
}
