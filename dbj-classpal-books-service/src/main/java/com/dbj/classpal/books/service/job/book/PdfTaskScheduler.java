package com.dbj.classpal.books.service.job.book;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dbj.classpal.books.common.enums.FileStatusEnum;
import com.dbj.classpal.books.common.enums.PdfTaskStatusEnum;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookPdfTaskBiz;
import com.dbj.classpal.books.service.biz.ebooks.IAppEbooksConfigWatermarkTemplateBiz;
import com.dbj.classpal.books.service.biz.ebooks.impl.AppEBookHelper;
import com.dbj.classpal.books.service.entity.ebooks.AppEBookPdfTask;
import com.dbj.classpal.books.service.entity.ebooks.AppEbooksConfigWatermarkTemplate;
import com.dbj.classpal.books.service.entity.product.AppEBook;
import com.dbj.classpal.books.service.mapper.ebooks.AppEBookMapper;
import com.dbj.classpal.books.service.mq.listener.ebooks.AppEBookPdfResultListener;
import com.dbj.classpal.framework.pdf.enums.PdfWaterMarkBusinessTypeEnum;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * PDF任务定时器
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Slf4j
@Component
public class PdfTaskScheduler {

    // 静态常量定义
    private static final String CLEAR_ERROR_MSG = "";

    // 时间相关常量
    private static final int DEFAULT_CLEANUP_TIMEOUT_MINUTES = 30;
    private static final int DEFAULT_RECOVERY_HOURS = 2;
    private static final int DEFAULT_SYNC_HOURS = 6;
    private static final int TASK_TIMEOUT_MINUTES = 2;
    private static final int RESUBMIT_INTERVAL_MINUTES = 10;
    private static final int CLEANUP_COUNT_MAX = 100;
    private static final int CLEANUP_COUNT_START = 0;

    @Resource
    private IAppEBookPdfTaskBiz pdfTaskBiz;

    @Resource
    private AppEBookHelper eBookHelper;

    @Resource
    private AppEBookMapper eBookMapper;

    @Resource
    private AppEBookPdfResultListener appEBookPdfResultListener;

    @Resource
    private IAppEbooksConfigWatermarkTemplateBiz watermarkTemplateBiz;

    /**
     * 清理超时的PDF处理任务
     * xxl-job任务处理器
     */
    @XxlJob("pdfTaskCleanup")
    public void cleanupTimeoutTasks() {
        long startTime = System.currentTimeMillis();

        try {
            // 从xxl-job参数中获取超时时间，默认30分钟
            String param = XxlJobHelper.getJobParam();
            int timeoutMinutes = parseTimeoutMinutes(param);

            log.info("开始执行PDF任务清理，超时时间：{}分钟", timeoutMinutes);

            // 执行清理
            int cleanupCount = pdfTaskBiz.cleanupTimeoutTasks(timeoutMinutes);

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            if (cleanupCount > CLEANUP_COUNT_START) {
                log.info("PDF任务清理完成，清理数量：{}，耗时：{}ms", cleanupCount, duration);
                XxlJobHelper.log("PDF任务清理完成，清理数量：{}，耗时：{}ms", cleanupCount, duration);

                // 如果清理数量过多，记录警告
                if (cleanupCount > CLEANUP_COUNT_MAX) {
                    String warnMsg = String.format("PDF任务超时数量异常，清理了%d个任务，请检查系统状态", cleanupCount);
                    log.warn(warnMsg);
                    XxlJobHelper.log("WARNING: " + warnMsg);
                }
            } else {
                log.info("PDF任务清理完成，没有发现超时任务，耗时：{}ms", duration);
                XxlJobHelper.log("PDF任务清理完成，没有发现超时任务，耗时：{}ms", duration);
            }

            // 标记任务执行成功
            XxlJobHelper.handleSuccess("清理完成，处理数量：" + cleanupCount);

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            String errorMsg = String.format("PDF任务清理失败，耗时：%dms，错误：%s", duration, e.getMessage());
            log.error(errorMsg, e);
            XxlJobHelper.log("ERROR: " + errorMsg);

            // 标记任务执行失败
            XxlJobHelper.handleFail(errorMsg);
        }
    }


    /**
     * 同步PDF任务处理结果到业务数据
     * xxl-job任务处理器
     */
    @XxlJob("pdfTaskResultSync")
    public void syncTaskResults() {
        long startTime = System.currentTimeMillis();

        try {
            // 从xxl-job参数中获取查询时间范围，默认查询最近1小时的任务
            String param = XxlJobHelper.getJobParam();
            int queryHours = parseQueryHours(param);

            log.info("开始同步PDF任务结果，查询时间范围：{}小时", queryHours);

            // 查询已完成但未同步的任务
            List<AppEBookPdfTask> completedTasks = getCompletedTasks(queryHours);

            int syncCount = 0;
            for (AppEBookPdfTask task : completedTasks) {
                try {
                    if (task.getBusinessId() == null){
                        continue;
                    }
                    if (syncTaskResult(task)) {
                        syncCount++;
                    }
                } catch (Exception e) {
                    log.error("同步任务结果失败，taskId: {}, 错误: {}", task.getTaskId(), e.getMessage());
                }
            }

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            log.info("PDF任务结果同步完成，查询任务数：{}，同步成功数：{}，耗时：{}ms",
                    completedTasks.size(), syncCount, duration);
            XxlJobHelper.log("PDF任务结果同步完成，查询任务数：{}，同步成功数：{}，耗时：{}ms",
                    completedTasks.size(), syncCount, duration);

            XxlJobHelper.handleSuccess("同步完成，处理数量：" + syncCount);

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            String errorMsg = String.format("PDF任务结果同步失败，耗时：%dms，错误：%s", duration, e.getMessage());
            log.error(errorMsg, e);
            XxlJobHelper.log("ERROR: " + errorMsg);
            XxlJobHelper.handleFail(errorMsg);
        }
    }

    /**
     * 获取已完成的任务
     */
    private List<AppEBookPdfTask> getCompletedTasks(int queryHours) {
        LocalDateTime startTime = LocalDateTime.now().minusHours(queryHours);

        LambdaQueryWrapper<AppEBookPdfTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppEBookPdfTask::getStatus, PdfTaskStatusEnum.SUCCESS.getCode())
                .ge(AppEBookPdfTask::getUpdateTime, startTime)
                .orderByAsc(AppEBookPdfTask::getUpdateTime);

        return pdfTaskBiz.list(queryWrapper);
    }

    /**
     * 同步单个任务结果
     */
    private boolean syncTaskResult(AppEBookPdfTask task) {
        try {
            // 检查对应的单书状态
            AppEBook eBook = eBookMapper.selectById(task.getBusinessId());
            if (eBook == null) {
                log.warn("未找到对应的单书，businessId: {}, taskId: {}", task.getBusinessId(), task.getTaskId());
                return false;
            }

            // 如果单书状态已经是完成状态，且封面URL一致，则跳过
            if (FileStatusEnum.COMPLETED.getCode().equals(eBook.getFileStatus()) &&
                    task.getCoverUrl() != null && task.getCoverUrl().equals(eBook.getCoverUrl())) {
                return false; // 已经同步过了
            }

            // 更新单书状态
            eBook.setFileStatus(FileStatusEnum.COMPLETED.getCode());

            if (task.getCoverUrl() != null && !task.getCoverUrl().isEmpty()) {
                eBook.setCoverUrl(task.getCoverUrl());
            }

            eBookMapper.updateById(eBook);

            log.info("同步任务结果成功，businessId: {}, taskId: {}, coverUrl: {}",
                    task.getBusinessId(), task.getTaskId(), task.getCoverUrl());

            return true;

        } catch (Exception e) {
            log.error("同步任务结果异常，taskId: {}, 错误: {}", task.getTaskId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 解析查询时间范围参数
     */
    private int parseQueryHours(String param) {
        try {
            if (param != null && !param.trim().isEmpty()) {
                if (param.trim().matches("\\d+")) {
                    return Integer.parseInt(param.trim());
                }
            }
        } catch (Exception e) {
            log.warn("解析查询时间范围参数失败，使用默认值，参数：{}，错误：{}", param, e.getMessage());
        }

        return 1; // 默认查询1小时
    }

    /**
     * 解析超时时间参数
     *
     * @param param xxl-job传入的参数
     * @return 超时时间（分钟）
     */
    private int parseTimeoutMinutes(String param) {
        try {
            if (param != null && !param.trim().isEmpty()) {
                // 支持直接传数字或JSON格式
                if (param.trim().matches("\\d+")) {
                    return Integer.parseInt(param.trim());
                }
                // 可以扩展支持JSON格式参数
                // {"timeoutMinutes": 30, "batchSize": 100}
            }
        } catch (Exception e) {
            log.warn("解析xxl-job参数失败，使用默认值，参数：{}，错误：{}", param, e.getMessage());
        }

        // 默认30分钟
        return DEFAULT_CLEANUP_TIMEOUT_MINUTES;
    }

    /**
     * 恢复处理中的PDF任务
     * 服务重启后，将处理中的任务重新发送到MQ
     */
    @XxlJob("pdfTaskRecovery")
    public void recoverProcessingTasks() {
        long startTime = System.currentTimeMillis();

        try {
            // 从xxl-job参数中获取恢复时间范围，默认恢复最近2小时的任务
            String param = XxlJobHelper.getJobParam();
            int recoveryHours = parseRecoveryHours(param);

            log.info("开始恢复处理中的PDF任务，恢复时间范围：{}小时", recoveryHours);

            // 查询处理中的任务
            List<AppEBookPdfTask> processingTasks = getProcessingTasks(recoveryHours);

            int recoveryCount = 0;
            int failedCount = 0;

            for (AppEBookPdfTask task : processingTasks) {
                try {
                    if (resubmitTask(task)) {
                        recoveryCount++;
                    } else {
                        failedCount++;
                    }
                } catch (Exception e) {
                    failedCount++;
                    log.error("恢复任务失败，taskId: {}, 错误: {}", task.getTaskId(), e.getMessage());
                }
            }

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            log.info("PDF任务恢复完成，查询任务数：{}，恢复成功数：{}，失败数：{}，耗时：{}ms",
                    processingTasks.size(), recoveryCount, failedCount, duration);
            XxlJobHelper.log("PDF任务恢复完成，查询任务数：{}，恢复成功数：{}，失败数：{}，耗时：{}ms",
                    processingTasks.size(), recoveryCount, failedCount, duration);

            if (failedCount > 0) {
                String warnMsg = String.format("PDF任务恢复有%d个失败，请检查系统状态", failedCount);
                log.warn(warnMsg);
                XxlJobHelper.log("WARNING: " + warnMsg);
            }

            XxlJobHelper.handleSuccess("恢复完成，成功数：" + recoveryCount + "，失败数：" + failedCount);

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            String errorMsg = String.format("PDF任务恢复失败，耗时：%dms，错误：%s", duration, e.getMessage());
            log.error(errorMsg, e);
            XxlJobHelper.log("ERROR: " + errorMsg);
            XxlJobHelper.handleFail(errorMsg);
        }
    }

    /**
     * 获取处理中的任务
     */
    private List<AppEBookPdfTask> getProcessingTasks(int recoveryHours) {
        LocalDateTime startTime = LocalDateTime.now().minusHours(recoveryHours);

        LambdaQueryWrapper<AppEBookPdfTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppEBookPdfTask::getStatus, PdfTaskStatusEnum.PROCESSING.getCode())
                .ge(AppEBookPdfTask::getCreateTime, startTime)
                .orderByAsc(AppEBookPdfTask::getCreateTime);

        return pdfTaskBiz.list(queryWrapper);
    }

    /**
     * 重新提交任务到MQ
     */
    private boolean resubmitTask(AppEBookPdfTask task) {
        try {
            // 获取任务相关的单书信息（通过文件MD5关联）
            AppEBook eBook = getEBookByFileMd5(task.getBusinessKey());
            if (eBook == null) {
                log.warn("任务对应的单书不存在，跳过恢复，taskId: {}, fileMd5: {}",
                        task.getTaskId(), task.getBusinessId());
                return false;
            }
            boolean sameFile = eBookHelper.dealFileWithMd5(eBook, eBook.getFileUrl(), eBook.getFileName(), eBook.getFileMd5());
            if (StringUtils.isNotEmpty(eBook.getFileUrl()) && !sameFile) {
                AppEbooksConfigWatermarkTemplate template = null;
                int waterMarkBusinessType = PdfWaterMarkBusinessTypeEnum.ONLY_SLICING_IMAGE.getCode();

                if (Objects.nonNull(eBook.getWatermarkId()) && eBook.getWatermarkId() != 0) {
                    waterMarkBusinessType = PdfWaterMarkBusinessTypeEnum.ONLY_ADD_WATER_MARK_AND_SLICING_IMAGE.getCode();
                    template = watermarkTemplateBiz.getById(eBook.getWatermarkId());
                }
                // 重新发送MQ消息（businessId 应该是文件MD5）
                appEBookPdfResultListener.asyncProcessPdf(
                        task.getTaskId(),
                        eBook.getId(),
                        eBook.getFileMd5(),
                        waterMarkBusinessType,
                        template,
                        eBook.getFileUrl(),
                        eBook.getFileName(),
                        false
                );
            }


            log.info("任务重新提交成功，taskId: {}, fileMd5: {}, bookId: {}",
                task.getTaskId(), eBook.getFileMd5(), eBook.getId());
            return true;

        } catch (Exception e) {
            log.error("重新提交任务失败，taskId: {}, 错误: {}", task.getTaskId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 解析恢复时间范围参数
     */
    private int parseRecoveryHours(String param) {
        try {
            if (param != null && !param.trim().isEmpty()) {
                if (param.trim().matches("\\d+")) {
                    return Integer.parseInt(param.trim());
                }
            }
        } catch (Exception e) {
            log.warn("解析恢复时间范围参数失败，使用默认值，参数：{}，错误：{}", param, e.getMessage());
        }

        return DEFAULT_RECOVERY_HOURS; // 默认恢复2小时内的任务
    }

    /**
     * 同步文件状态
     * 处理 file_status=1 但任务已失败的数据不一致问题
     */
    @XxlJob("fileStatusSync")
    public void syncFileStatus() {
        long startTime = System.currentTimeMillis();

        try {
            // 从xxl-job参数中获取同步时间范围，默认同步最近6小时的数据
            String param = XxlJobHelper.getJobParam();
            int syncHours = parseSyncHours(param);

            log.info("开始同步文件状态，同步时间范围：{}小时", syncHours);

            // 查询 file_status=0 的单书
            List<AppEBook> processingBooks = getProcessingBooks(syncHours);

            int syncCount = 0;
            int resubmitCount = 0;
            int failedCount = 0;

            for (AppEBook eBook : processingBooks) {
                try {
                    SyncResult result = syncBookFileStatus(eBook);
                    switch (result) {
                        case SYNCED:
                            syncCount++;
                            break;
                        case RESUBMITTED:
                            resubmitCount++;
                            break;
                        case FAILED:
                            failedCount++;
                            break;
                    }
                } catch (Exception e) {
                    failedCount++;
                    log.error("同步文件状态失败，bookId: {}, 错误: {}", eBook.getId(), e.getMessage());
                }
            }

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            log.info("文件状态同步完成，查询单书数：{}，状态同步数：{}，重新提交数：{}，失败数：{}，耗时：{}ms",
                    processingBooks.size(), syncCount, resubmitCount, failedCount, duration);
            XxlJobHelper.log("文件状态同步完成，查询单书数：{}，状态同步数：{}，重新提交数：{}，失败数：{}，耗时：{}ms",
                    processingBooks.size(), syncCount, resubmitCount, failedCount, duration);

            if (failedCount > 0) {
                String warnMsg = String.format("文件状态同步有%d个失败，请检查系统状态", failedCount);
                log.warn(warnMsg);
                XxlJobHelper.log("WARNING: " + warnMsg);
            }

            XxlJobHelper.handleSuccess("同步完成，状态同步：" + syncCount + "，重新提交：" + resubmitCount + "，失败：" + failedCount);

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            String errorMsg = String.format("文件状态同步失败，耗时：%dms，错误：%s", duration, e.getMessage());
            log.error(errorMsg, e);
            XxlJobHelper.log("ERROR: " + errorMsg);
            XxlJobHelper.handleFail(errorMsg);
        }
    }

    /**
     * 同步结果枚举
     */
    private enum SyncResult {
        SYNCED,      // 状态已同步
        RESUBMITTED, // 重新提交任务
        FAILED       // 处理失败
    }

    /**
     * 获取处理中状态的单书
     */
    private List<AppEBook> getProcessingBooks(int syncHours) {
        LocalDateTime startTime = LocalDateTime.now().minusHours(syncHours);

        LambdaQueryWrapper<AppEBook> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppEBook::getFileStatus, FileStatusEnum.PROCESSING.getCode())
                .ge(AppEBook::getUpdateTime, startTime)
                .orderByAsc(AppEBook::getUpdateTime);

        return eBookMapper.selectList(queryWrapper);
    }

    /**
     * 同步单书文件状态
     */
    private SyncResult syncBookFileStatus(AppEBook eBook) {
        try {
            // 查询该单书的最新任务状态（通过文件MD5关联）
            AppEBookPdfTask latestTask = getLatestTaskByFileMd5(eBook.getFileMd5());

            if (latestTask == null) {
                // 没有任务记录，但文件状态为处理中，可能需要重新提交
                log.warn("单书没有任务记录但状态为处理中，bookId: {}", eBook.getId());

                // 检查是否需要重新提交任务
                if (shouldResubmitTask(eBook)) {
                    if (resubmitTaskForBook(eBook)) {
                        return SyncResult.RESUBMITTED;
                    }
                }
                return SyncResult.FAILED;
            }

            // 根据任务状态同步文件状态
            if (latestTask.getStatus().equals(PdfTaskStatusEnum.SUCCESS.getCode())) {
                // 任务成功，更新文件状态为完成
                eBook.setFileStatus(FileStatusEnum.COMPLETED.getCode());
                eBook.setFileErrMsg(CLEAR_ERROR_MSG); // 清空错误信息

                if (latestTask.getCoverUrl() != null && !latestTask.getCoverUrl().isEmpty()) {
                    eBook.setCoverUrl(latestTask.getCoverUrl());
                }

                eBookMapper.updateById(eBook);
                log.info("同步文件状态为完成，bookId: {}, taskId: {}", eBook.getId(), latestTask.getTaskId());
                return SyncResult.SYNCED;

            } else if (latestTask.getStatus().equals(PdfTaskStatusEnum.FAILED.getCode())) {
                // 任务失败，更新文件状态为失败
                eBook.setFileStatus(FileStatusEnum.FAILED.getCode());
                eBook.setFileErrMsg(latestTask.getErrorMsg()); // 设置错误信息

                eBookMapper.updateById(eBook);
                log.info("同步文件状态为失败，bookId: {}, taskId: {}, errorMsg: {}",
                        eBook.getId(), latestTask.getTaskId(), latestTask.getErrorMsg());
                return SyncResult.SYNCED;

            } else if (latestTask.getStatus().equals(PdfTaskStatusEnum.PROCESSING.getCode())) {
                // 任务仍在处理中，检查是否超时
                LocalDateTime taskCreateTime = latestTask.getCreateTime();
                LocalDateTime timeoutTime = LocalDateTime.now().minusMinutes(TASK_TIMEOUT_MINUTES);

                if (taskCreateTime.isBefore(timeoutTime)) {
                    // 任务超时，重新提交
                    log.warn("任务超时，准备重新提交，bookId: {}, taskId: {}", eBook.getId(), latestTask.getTaskId());

                    if (resubmitTaskForBook(eBook)) {
                        return SyncResult.RESUBMITTED;
                    }
                }
                // 任务正常处理中，不需要处理
                return SyncResult.SYNCED;
            }

            return SyncResult.FAILED;

        } catch (Exception e) {
            log.error("同步单书文件状态异常，bookId: {}, 错误: {}", eBook.getId(), e.getMessage(), e);
            return SyncResult.FAILED;
        }
    }

    /**
     * 获取单书的最新任务（通过文件MD5关联）
     */
    private AppEBookPdfTask getLatestTaskByFileMd5(String fileMd5) {
        if (fileMd5 == null || fileMd5.trim().isEmpty()) {
            return null;
        }

        LambdaQueryWrapper<AppEBookPdfTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppEBookPdfTask::getBusinessKey, fileMd5)
                .orderByDesc(AppEBookPdfTask::getCreateTime)
                .last("limit 1");

        List<AppEBookPdfTask> tasks = pdfTaskBiz.list(queryWrapper);
        return tasks.isEmpty() ? null : tasks.get(0);
    }

    /**
     * 通过文件MD5获取单书信息
     */
    private AppEBook getEBookByFileMd5(String fileMd5) {
        if (fileMd5 == null || fileMd5.trim().isEmpty()) {
            return null;
        }

        LambdaQueryWrapper<AppEBook> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppEBook::getFileMd5, fileMd5)
                .last("limit 1");

        List<AppEBook> books = eBookMapper.selectList(queryWrapper);
        return books.isEmpty() ? null : books.get(0);
    }

    /**
     * 判断是否需要重新提交任务
     */
    private boolean shouldResubmitTask(AppEBook eBook) {
        // 检查单书是否有PDF文件
        if (eBook.getFileUrl() == null || eBook.getFileUrl().trim().isEmpty()) {
            return false;
        }

        // 检查更新时间，避免频繁重新提交
        LocalDateTime updateTime = eBook.getUpdateTime();
        LocalDateTime thresholdTime = LocalDateTime.now().minusMinutes(RESUBMIT_INTERVAL_MINUTES); // 10分钟内不重复提交

        return updateTime.isBefore(thresholdTime);
    }

    /**
     * 为单书重新提交任务
     */
    private boolean resubmitTaskForBook(AppEBook eBook) {
        try {
            String taskId  = null;
            eBook.setFileStatus(FileStatusEnum.PROCESSING.getCode());
            boolean sameFile = eBookHelper.dealFileWithMd5(eBook,eBook.getFileUrl(),eBook.getFileName(),eBook.getFileMd5());
            if(StringUtils.isNotEmpty(eBook.getFileUrl()) && !sameFile) {
                int waterMarkBusinessType = PdfWaterMarkBusinessTypeEnum.ONLY_SLICING_IMAGE.getCode();
                AppEbooksConfigWatermarkTemplate template = null;
                if(Objects.nonNull(eBook.getWatermarkId()) && eBook.getWatermarkId() != 0) {
                    waterMarkBusinessType = PdfWaterMarkBusinessTypeEnum.ONLY_ADD_WATER_MARK_AND_SLICING_IMAGE.getCode();
                    template = watermarkTemplateBiz.getById(eBook.getWatermarkId());
                }
                taskId = eBookHelper.parsePDFAsync(eBook.getId(),eBook.getFileMd5(),waterMarkBusinessType,template,eBook.getFileUrl(),eBook.getFileName(),false);
            }

            log.info("为单书重新提交任务成功，bookId: {}, fileMd5: {}, newTaskId: {}",
                eBook.getId(), eBook.getFileMd5(), taskId);
            return true;

        } catch (Exception e) {
            log.error("为单书重新提交任务失败，bookId: {}, 错误: {}", eBook.getId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 解析同步时间范围参数
     */
    private int parseSyncHours(String param) {
        try {
            if (param != null && !param.trim().isEmpty()) {
                if (param.trim().matches("\\d+")) {
                    return Integer.parseInt(param.trim());
                }
            }
        } catch (Exception e) {
            log.warn("解析同步时间范围参数失败，使用默认值，参数：{}，错误：{}", param, e.getMessage());
        }

        return DEFAULT_SYNC_HOURS;
    }
}