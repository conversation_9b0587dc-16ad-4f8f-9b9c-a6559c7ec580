package com.dbj.classpal.books.service.mq.listener.file.exports;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dbj.classpal.admin.client.api.file.exports.ExportlFileImportApi;
import com.dbj.classpal.books.common.bo.file.ExcelFileEntity;
import com.dbj.classpal.books.common.constant.QueueConstant;
import com.dbj.classpal.books.common.enums.file.ExportExcelFileStrategyEnum;
import com.dbj.classpal.framework.mq.annotation.ExtractHeader;
import com.dbj.classpal.framework.utils.bo.SysFileExportExcelBO;
import com.dbj.classpal.framework.utils.enums.FileStatusEnum;
import com.dbj.classpal.framework.utils.file.ExcelFileStrategyFactory;
import com.dbj.classpal.framework.utils.file.IExportExcelFileStrategy;
import com.dbj.classpal.framework.utils.util.ContextHolder;
import com.dbj.classpal.framework.utils.util.ContextUtil;
import com.rabbitmq.client.Channel;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ExcelFileListener
 * @date 2023-10-27 10:42
 **/
@Component
@Slf4j
public class ExcelFileExportListener {

    @Resource
    private ExportlFileImportApi exportlFileImportApi;

    @RabbitListener(queues = {QueueConstant.CLASSPAL_EXPORT_FILE_SERVICE_QUEUE_BOOKS})
    @ExtractHeader
    public void handleOrder(ExcelFileEntity excelFileEntity, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag, Message message) throws IOException {
        SysFileExportExcelBO fileDomain = null;
        try {
            log.info("文件上传接收到消息:{}", excelFileEntity);
            if(excelFileEntity == null || excelFileEntity .getId() == null){
                // 直接确认消息进行下一条的处理
                channel.basicAck(tag, false);  // 手动确认
                return;
            }
            fileDomain = exportlFileImportApi.getById(excelFileEntity.getId()).getData();
            log.info("查询文件信息{}", JSON.toJSONString(fileDomain));
            if(fileDomain == null || !Objects.equals(fileDomain.getStatus(), FileStatusEnum.PENDING_PROCESSING.getCode())){
                return;
            }
            SysFileExportExcelBO subFileDomain = new SysFileExportExcelBO();
            subFileDomain.setId(fileDomain.getId());
            exportlFileImportApi.updateFileProcessing(subFileDomain);

            String type = fileDomain.getType();
            SysFileExportExcelBO errFileDomain = new SysFileExportExcelBO();
            errFileDomain.setId(fileDomain.getId());
            if(type == null){
                errFileDomain.setErrorMsg("找不到导出类型,请处理");
                exportlFileImportApi.handleProcessingFailedSys(fileDomain);
                return;

            }
            ExportExcelFileStrategyEnum strategyEnum = ExportExcelFileStrategyEnum.getEnum(type);
            if(strategyEnum == null){
                errFileDomain.setErrorMsg("根据类型找不到对应枚举,请检查ExcelFileStrategyEnum枚举信息");
                exportlFileImportApi.handleProcessingFailedSys(fileDomain);
                return;
            }
            IExportExcelFileStrategy fileStrategy = ExcelFileStrategyFactory.getExportStrategy(strategyEnum.getHandler());
            fileStrategy.handlerExportExcelFile(BeanUtil.copyProperties(fileDomain, SysFileExportExcelBO.class));
        } catch (Exception e) {
            log.error(e.getMessage());

        }finally {
            channel.basicAck(tag, false);  //
        }
    }

}
