package com.dbj.classpal.books.service.api.client.question;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.books.client.api.question.WrongQuestionApi;
import com.dbj.classpal.books.client.bo.question.QueryWrongQuestionsApiBO;
import com.dbj.classpal.books.client.bo.question.WrongQuestionIdsApiBO;
import com.dbj.classpal.books.client.bo.question.WrongQuestionPkIdsApiBO;
import com.dbj.classpal.books.client.bo.question.WrongQuestionSubjectApiBO;
import com.dbj.classpal.books.client.dto.question.QuestionCorrectAnswerApiDTO;
import com.dbj.classpal.books.client.dto.question.WrongQuestionResultApiDTO;
import com.dbj.classpal.books.client.dto.question.WrongQuestionSubjectApiDTO;
import com.dbj.classpal.books.common.bo.question.QueryWrongQuestionsBO;
import com.dbj.classpal.books.common.bo.question.RemoveWrongQuestionBO;
import com.dbj.classpal.books.common.bo.question.WrongQuestionPkIdsBO;
import com.dbj.classpal.books.service.service.question.WrongQuestionService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;

@RestController
public class WrongQuestionApiImpl implements WrongQuestionApi {
    @Resource
    WrongQuestionService service;
    @Override
    public RestResponse<Boolean> removeWrongQuestion(WrongQuestionPkIdsApiBO idsApiBO) throws BusinessException {
        return RestResponse.success(service.removeWrongQuestion(BeanUtil.copyProperties(idsApiBO, WrongQuestionPkIdsBO.class)));
    }

    @Override
    public RestResponse<List<WrongQuestionSubjectApiDTO>> listSubject(WrongQuestionSubjectApiBO subjectApiBO) throws BusinessException {
        return RestResponse.success(BeanUtil.copyToList(service.listSubject(subjectApiBO.getAppUserId()), WrongQuestionSubjectApiDTO.class));
    }

    @Override
    public RestResponse<List<WrongQuestionResultApiDTO>> listWrongQuestions(QueryWrongQuestionsApiBO queryBO) throws BusinessException {
        return RestResponse.success(BeanUtil.copyToList(service.listWrongQuestions(BeanUtil.copyProperties(queryBO, QueryWrongQuestionsBO.class)),WrongQuestionResultApiDTO.class));
    }

    @Override
    public RestResponse<List<QuestionCorrectAnswerApiDTO>> getWrongQuestionsCorrectAnswers(WrongQuestionIdsApiBO idsApiBO) throws BusinessException {
        return RestResponse.success(BeanUtil.copyToList(service.getWrongQuestionsCorrectAnswers(idsApiBO.getAppUserId(), new LinkedHashSet<>(idsApiBO.getQuestionIds())),QuestionCorrectAnswerApiDTO.class));
    }
}
