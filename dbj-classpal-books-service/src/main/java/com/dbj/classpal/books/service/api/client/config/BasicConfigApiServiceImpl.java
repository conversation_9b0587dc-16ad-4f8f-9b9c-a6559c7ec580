package com.dbj.classpal.books.service.api.client.config;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.config.BasicConfigApi;
import com.dbj.classpal.books.client.bo.config.*;
import com.dbj.classpal.books.client.dto.config.BasicConfigApiDTO;
import com.dbj.classpal.books.common.bo.config.BasicConfigBO;
import com.dbj.classpal.books.common.bo.config.BasicConfigIdBO;
import com.dbj.classpal.books.common.bo.config.BasicConfigIdsBO;
import com.dbj.classpal.books.common.bo.config.BasicConfigQueryBO;
import com.dbj.classpal.books.common.dto.config.BasicConfigDTO;
import com.dbj.classpal.books.service.service.config.BasicConfigService;
import com.dbj.classpal.framework.commons.converter.PageInfoConverter;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
public class BasicConfigApiServiceImpl implements BasicConfigApi {

    private final BasicConfigService basicConfigService;

    @Override
    public RestResponse<Integer> create(BasicConfigApiBO apiBO) throws BusinessException {
        BasicConfigBO bo = BeanUtil.copyProperties(apiBO, BasicConfigBO.class);
        Integer id = basicConfigService.create(bo);
        return RestResponse.success(id);
    }

    @Override
    public RestResponse<Void> update(BasicConfigApiBO apiBO) throws BusinessException {
        BasicConfigBO bo = BeanUtil.copyProperties(apiBO, BasicConfigBO.class);
        basicConfigService.update(bo);
        return RestResponse.success(null);
    }

    @Override
    public RestResponse<Void> batchDelete(BasicConfigIdsApiBO idsApiBO) throws BusinessException {
        BasicConfigIdsBO idsBO = BeanUtil.copyProperties(idsApiBO, BasicConfigIdsBO.class);
        basicConfigService.batchDelete(idsBO);
        return RestResponse.success(null);
    }

    @Override
    public RestResponse<BasicConfigApiDTO> detail(BasicConfigIdApiBO idApiBO) {
        BasicConfigIdBO idBO = BeanUtil.copyProperties(idApiBO, BasicConfigIdBO.class);
        BasicConfigDTO dto = basicConfigService.detail(idBO);
        return RestResponse.success(BeanUtil.copyProperties(dto, BasicConfigApiDTO.class));
    }

    @Override
    public RestResponse<Page<BasicConfigApiDTO>> pageList(PageInfo<BasicConfigQueryApiBO> queryApiBO) {
        Page<BasicConfigDTO> page = basicConfigService.pageList(PageInfoConverter.convertPageInfo(queryApiBO, BasicConfigQueryBO.class));
        return RestResponse.success((Page<BasicConfigApiDTO>) page.convert(vo -> {
            BasicConfigApiDTO dto = new BasicConfigApiDTO();
            BeanUtil.copyProperties(vo, dto);
            return dto;
        }));
    }

    @Override
    public RestResponse<List<BasicConfigApiDTO>> list(BasicConfigQueryApiBO queryApiBO) {
        BasicConfigQueryBO serviceBo = BeanUtil.copyProperties(queryApiBO, BasicConfigQueryBO.class);
        return RestResponse.success(BeanUtil.copyToList(basicConfigService.list(serviceBo), BasicConfigApiDTO.class));
    }


    @Override
    public RestResponse<Void> updateSort(BasicConfigSortApiBO sortApiBO) {
        List<BasicConfigBO> boList = BeanUtil.copyToList(sortApiBO.getSortList(), BasicConfigBO.class);
        basicConfigService.updateSort(boList);
        return RestResponse.success(null);
    }
} 