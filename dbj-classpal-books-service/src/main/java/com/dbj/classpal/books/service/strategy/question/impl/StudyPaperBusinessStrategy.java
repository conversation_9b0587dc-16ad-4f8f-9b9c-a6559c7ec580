package com.dbj.classpal.books.service.strategy.question.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dbj.classpal.books.common.bo.paper.RetakePaperBO;
import com.dbj.classpal.books.common.bo.paper.SubmitPaperBO;
import com.dbj.classpal.books.common.dto.paper.UserPaperDTO;
import com.dbj.classpal.books.common.enums.BusinessTypeEnum;
import com.dbj.classpal.books.service.biz.evaluation.IAppUserPaperEvaluationAnalysisBiz;
import com.dbj.classpal.books.service.entity.evaluation.AppUserPaperEvaluationAnalysis;
import com.dbj.classpal.books.service.strategy.question.AbstractPaperBusinessStrategy;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * 评估试卷业务策略实现
 */
@Slf4j
@Component
public class StudyPaperBusinessStrategy extends AbstractPaperBusinessStrategy {


    @Resource
    private IAppUserPaperEvaluationAnalysisBiz appUserPaperEvaluationAnalysisBiz;

    @Override
    public Integer getBusinessType() {
        return BusinessTypeEnum.STUDY_CENTER_QUESTION_BUSINESS.getCode();
    }

    @Override
    public void retakePaper(RetakePaperBO retakePaperBO) throws BusinessException {
        try {
            // 先调用父类的重考方法
            doRetakePaper(retakePaperBO);
            
            // 然后删除评估分析记录
            appUserPaperEvaluationAnalysisBiz.getBaseMapper().delete(
                    new LambdaQueryWrapper<AppUserPaperEvaluationAnalysis>()
                            .eq(AppUserPaperEvaluationAnalysis::getAppUserPaperInfoId, retakePaperBO.getPaperId())
            );
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("重新考试失败", e);
            throw new BusinessException(APP_RE_EXAM_FAIL_CODE,APP_RE_EXAM_FAIL_MSG);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserPaperDTO submitPaper(SubmitPaperBO submitBO) throws BusinessException {
        try {
            return doSubmitParer(submitBO,"学习中心-默认试卷名称");
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("保存试卷结果失败", e);
            throw new BusinessException(APP_SAVE_PAPER_RESULT_FAIL_CODE,APP_SAVE_PAPER_RESULT_FAIL_MSG);
        }
    }
} 