package com.dbj.classpal.books.service.entity.books;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 图书兼容链接表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("books_compatibility")
@Tag(name="BooksCompatibility对象", description="图书兼容链接表")
public class BooksCompatibility extends BizEntity implements Serializable {


    @Schema(description = "册数id或者书内码页id")
    private Integer internalId;

    @Schema(description = "兼容连接")
    private String internalCode;

    @Schema(description = "连接类型（rank=按册数关联，contents=按书内码关联）")
    private String connectionType;

    @Schema(description = "是否启用 1-是 0-否")
    private Integer status;



}
