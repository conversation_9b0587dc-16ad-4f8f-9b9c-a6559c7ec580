package com.dbj.classpal.books.service.biz.ebooks;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.service.entity.product.AppEBook;
import com.dbj.classpal.books.service.entity.product.AppEBookshelfBookRef;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 书架-单书关联 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
public interface IAppEBookshelfBookRefBiz extends IService<AppEBookshelfBookRef> {

    /**
     * 批量保存书架-单书关联
     * 
     * @param shelfId 书架ID
     * @param bookIds 单书ID列表
     * @return 保存结果
     */
    boolean saveBatch(Integer shelfId, List<Integer> bookIds) throws BusinessException;
    
    /**
     * 批量删除书架-单书关联
     *
     * @param shelfIds 书架ID
     * @param bookIds  单书ID列表，为空则删除全部
     * @return 删除结果
     */
    boolean removeBatch(List<Integer> shelfIds, List<Integer> bookIds) throws BusinessException;
    
    /**
     * 更新单书排序
     * 
     * @param shelfId 书架ID
     * @param bookSortMap 单书排序映射，key为单书ID，value为排序序号
     * @return 更新结果
     */
    boolean updateSort(Integer shelfId, Map<Integer, Integer> bookSortMap) throws BusinessException;
    
    /**
     * 查询书架下的单书ID列表
     *
     * @param shelfIds 书架ID
     * @return 单书ID列表
     */
    List<Integer> listBookIds(List<Integer> shelfIds) throws BusinessException;

    /**
     * 批量查询多个书架下的单书ID列表
     *
     * @param shelfIds 书架ID列表
     * @return 书架ID到单书ID列表的映射
     */
    Map<Integer, List<Integer>> batchListBookIds(List<Integer> shelfIds) throws BusinessException;
    
    /**
     * 查询单书所在的所有书架ID列表
     * 
     * @param bookId 单书ID
     * @return 书架ID列表
     */
    List<Integer> listShelfIds(Integer bookId) throws BusinessException;
    
    /**
     * 统计书架下的单书数量
     *
     * @param shelfId 书架ID
     * @return 单书数量
     */
    int countBooks(Integer shelfId) throws BusinessException;

    /**
     * 批量统计多个书架下的单书数量
     *
     * @param shelfIds 书架ID列表
     * @return 书架ID到单书数量的映射
     */
    Map<Integer, Integer> batchCountBooks(List<Integer> shelfIds) throws BusinessException;
}