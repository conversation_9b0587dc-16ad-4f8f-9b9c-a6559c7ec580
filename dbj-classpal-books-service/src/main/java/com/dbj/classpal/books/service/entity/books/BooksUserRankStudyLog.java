package com.dbj.classpal.books.service.entity.books;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 产品分类配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("books_user_rank_study_log")
@Tag(name="BooksUserRankStudyLog对象", description="产品分类配置表")
public class BooksUserRankStudyLog extends BizEntity implements Serializable {




    @Schema(description ="app用户id")
    @TableField("app_user_id")
    private Integer appUserId;

    @Schema(description ="图书id")
    @TableField("books_id")
    private Integer booksId;
    @Schema(description ="册书id")
    @TableField("rank_id")
    private Integer rankId;

    @Schema(description ="最近学习")
    @TableField("is_last_study")
    private Integer isLastStudy;

    @Schema(description ="最近学习")
    @TableField("last_study_time")
    private LocalDateTime lastStudyTime;

    @Schema(description ="是否启用 1-是 0-否")
    @TableField("status")
    private Boolean status;


}
