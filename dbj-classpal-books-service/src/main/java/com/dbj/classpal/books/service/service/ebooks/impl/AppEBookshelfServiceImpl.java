package com.dbj.classpal.books.service.service.ebooks.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookshelfH5QueryBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookshelfQueryBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookshelfSaveBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookshelfUpdateBO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookshelfDTO;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookBiz;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookshelfBiz;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookshelfBookRefBiz;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookstoreShelfRefBiz;
import com.dbj.classpal.books.service.entity.product.AppEBook;
import com.dbj.classpal.books.service.entity.product.AppEBookshelf;
import com.dbj.classpal.books.service.entity.product.AppEBookstoreShelfRef;
import com.dbj.classpal.books.service.mapper.ebooks.AppEBookshelfMapper;
import com.dbj.classpal.books.service.service.ebooks.IAppEBookshelfService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 书架 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
@Slf4j
@Service
public class AppEBookshelfServiceImpl extends ServiceImpl<AppEBookshelfMapper, AppEBookshelf> implements IAppEBookshelfService {

    // 静态常量定义
    private static final String SHELF_ID_LIST_EMPTY_MSG = "书架ID列表不能为空";
    private static final String SHELF_NOT_EXIST_MSG = "书架不存在";
    private static final String SHELF_ENABLED_DELETE_MSG = "存在已启用的书架，请将书架禁用后重试";
    private static final String SHELF_STORE_ASSOCIATED_MSG = "该书架已关联书城，请先从书城中移除后再删除";

    @Resource
    private IAppEBookBiz eBookBiz;

    @Resource
    private IAppEBookshelfBiz eBookshelfBiz;
    
    @Resource
    private IAppEBookshelfBookRefBiz bookshelfBookRefBiz;

    @Resource
    private IAppEBookstoreShelfRefBiz eBookstoreShelfRefBiz;

    @Override
    public Page<AppEBookshelfDTO> page(PageInfo<AppEBookshelfQueryBO> pageRequest) throws BusinessException {
        if (pageRequest == null) {
            throw new BusinessException("分页参数不能为空");
        }
        
        Page<AppEBookshelf> page = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        return eBookshelfBiz.pageBookshelf(page, pageRequest.getData());
    }

    @Override
    public AppEBookshelfDTO detail(Integer id) throws BusinessException {
        return eBookshelfBiz.getBookshelfDetail(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(AppEBookshelfSaveBO saveBO) throws BusinessException {
        if (saveBO == null) {
            throw new BusinessException("保存参数不能为空");
        }
        
        // 构建实体对象
        AppEBookshelf shelf = new AppEBookshelf();
        BeanUtils.copyProperties(saveBO, shelf);

        // 保存书架信息
        boolean saveResult = eBookshelfBiz.save(shelf);
        if (!saveResult) {
            throw new BusinessException("保存书架信息失败");
        }
        
        // 保存书架-单书关联关系
        if (CollectionUtils.isNotEmpty(saveBO.getBookIds())) {
            bookshelfBookRefBiz.saveBatch(shelf.getId(), saveBO.getBookIds());
        }
        
        return shelf.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(AppEBookshelfUpdateBO updateBO) throws BusinessException {
        if (updateBO == null || updateBO.getId() == null) {
            throw new BusinessException("更新参数不能为空");
        }
        
        // 检查书架是否存在
        AppEBookshelf shelf = eBookshelfBiz.getById(updateBO.getId());
        if (shelf == null) {
            throw new BusinessException("书架不存在");
        }
        
        // 更新实体对象
        BeanUtils.copyProperties(updateBO, shelf);
        
        // 更新书架信息
        boolean updateResult = eBookshelfBiz.updateById(shelf);
        if (!updateResult) {
            throw new BusinessException("更新书架信息失败");
        }
        
        // 更新书架-单书关联关系
        if (CollectionUtils.isNotEmpty(updateBO.getBookIds())) {
            // 删除原有关联关系
            bookshelfBookRefBiz.removeBatch(List.of(shelf.getId()), null);
            
            // 添加新的关联关系
            bookshelfBookRefBiz.saveBatch(shelf.getId(), updateBO.getBookIds());
        }
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Integer id) throws BusinessException {
        if (id == null) {
            throw new BusinessException("书架ID不能为空");
        }
        AppEBookshelf eBookshelf = eBookshelfBiz.getBaseMapper().selectById(id);
        if (eBookshelf == null) {
            throw new BusinessException(SHELF_NOT_EXIST_MSG);
        }

        // 检查书架是否已启用
        if(eBookshelf.getLaunchStatus().equals(YesOrNoEnum.YES.getCode())){
            throw new BusinessException(SHELF_ENABLED_DELETE_MSG);
        }

        // 检查书架是否关联了书城
        if (isShelfAssociatedWithStore(id)) {
            throw new BusinessException("书架《" + eBookshelf.getShelfTitle() + "》已关联书城，请先从书城中移除后再删除");
        }

        log.info("开始删除书架，shelfId: {}, shelfName: {}", id, eBookshelf.getShelfTitle());

        // 删除书架-单书关联关系
        bookshelfBookRefBiz.removeBatch(List.of(id), null);

        // 删除书架信息
        boolean result = eBookshelfBiz.removeById(id);

        if (result) {
            log.info("删除书架成功，shelfId: {}", id);
        } else {
            log.warn("删除书架失败，shelfId: {}", id);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBatch(List<Integer> ids) throws BusinessException {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException(SHELF_ID_LIST_EMPTY_MSG);
        }
        List<AppEBookshelf> eBookshelfList = eBookshelfBiz.getBaseMapper().selectByIds(ids);
        if (CollectionUtils.isEmpty(eBookshelfList)) {
            throw new BusinessException(SHELF_NOT_EXIST_MSG);
        }

        // 检查是否有已启用的书架
        if(eBookshelfList.stream().anyMatch(e -> e.getLaunchStatus().equals(YesOrNoEnum.YES.getCode()))){
            throw new BusinessException(SHELF_ENABLED_DELETE_MSG);
        }

        // 检查是否有关联书城的书架
        for (Integer shelfId : ids) {
            if (isShelfAssociatedWithStore(shelfId)) {
                AppEBookshelf shelf = eBookshelfList.stream()
                        .filter(e -> e.getId().equals(shelfId))
                        .findFirst()
                        .orElse(null);
                String shelfName = shelf != null ? shelf.getShelfTitle() : "ID:" + shelfId;
                throw new BusinessException("书架《" + shelfName + "》已关联书城，请先从书城中移除后再删除");
            }
        }

        log.info("开始批量删除书架，数量：{}", ids.size());

        // 删除书架-单书关联关系
        bookshelfBookRefBiz.removeBatch(ids, null);
        eBookshelfBiz.removeBatchByIds(ids);

        log.info("批量删除书架成功，数量：{}", ids.size());
        return true;
    }

    @Override
    public boolean enableBatch(List<Integer> ids) throws BusinessException {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException("书架ID列表不能为空");
        }
        
        List<AppEBookshelf> shelves = eBookshelfBiz.listByIds(ids);
        if (CollectionUtils.isEmpty(shelves)) {
            return true;
        }
        List<Integer> shelfIds = new ArrayList<>();
        shelves.forEach(shelf -> {
            shelf.setLaunchStatus(YesOrNoEnum.YES.getCode());
            shelfIds.add(shelf.getId());
        });
        List<Integer> ebookIds = bookshelfBookRefBiz.listBookIds(shelfIds);
        List<AppEBook> eBookList = eBookBiz.getBaseMapper().selectByIds(ebookIds);
        if(CollectionUtils.isNotEmpty(eBookList) && eBookList.stream().anyMatch(e -> e.getLaunchStatus().equals(YesOrNoEnum.NO.getCode()))){
            throw new BusinessException("存在禁用的图书，请将图书启用后重试");
        }
        return eBookshelfBiz.updateBatchById(shelves);
    }

    @Override
    public boolean disableBatch(List<Integer> ids) throws BusinessException {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException("书架ID列表不能为空");
        }
        
        List<AppEBookshelf> shelves = eBookshelfBiz.listByIds(ids);
        if (CollectionUtils.isEmpty(shelves)) {
            return true;
        }
        
        for (AppEBookshelf shelf : shelves) {
            shelf.setLaunchStatus(YesOrNoEnum.NO.getCode());
        }
        
        return eBookshelfBiz.updateBatchById(shelves);
    }

    @Override
    public boolean setCover(Integer id, Integer bookId, String coverUrl) throws BusinessException {
        return eBookshelfBiz.setBookshelfCover(id, bookId, coverUrl);
    }

    @Override
    public boolean addBooks(Integer shelfId, List<Integer> bookIds) throws BusinessException {
        return eBookshelfBiz.addBooksToShelf(shelfId, bookIds);
    }

    @Override
    public boolean removeBooks(Integer shelfId, List<Integer> bookIds) throws BusinessException {
        return eBookshelfBiz.removeBooksFromShelf(shelfId, bookIds);
    }

    @Override
    public boolean sortBooks(Integer shelfId, List<Integer> bookIds, Map<Integer, Integer> bookSortMap) throws BusinessException {
        bookIds.forEach(id-> {
            bookSortMap.put(id,bookIds.indexOf(id));
        });
        return eBookshelfBiz.sortBooksInShelf(shelfId, bookSortMap);
    }

    @Override
    public boolean allowDownloadBatch(List<Integer> ids) throws BusinessException {
//        List<Integer> ebookIds = bookshelfBookRefBiz.listBookIds(ids);
//        List<AppEBook> eBookList = eBookBiz.getBaseMapper().selectByIds(ebookIds);
//        if(CollectionUtils.isNotEmpty(eBookList) && eBookList.stream().anyMatch(e -> e.getAllowDownload().equals(YesOrNoEnum.NO.getCode()))){
//            throw new BusinessException("存在不允许下载的图书，请重试");
//        }
        return eBookshelfBiz.allowDownloadBatch(ids);
    }

    @Override
    public boolean disableDownloadBatch(List<Integer> ids) throws BusinessException {
        return eBookshelfBiz.disableDownloadBatch(ids);
    }

    /**
     * 检查书架是否关联了书城
     *
     * @param shelfId 书架ID
     * @return true-已关联书城，false-未关联书城
     */
    private boolean isShelfAssociatedWithStore(Integer shelfId) {
        try {
            // 查询书城书架关联表中是否存在该书架
            LambdaQueryWrapper<AppEBookstoreShelfRef> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AppEBookstoreShelfRef::getShelfId, shelfId);

            long count = eBookstoreShelfRefBiz.count(queryWrapper);

            if (count > 0) {
                log.info("书架已关联书城，shelfId: {}, 关联书城数量: {}", shelfId, count);
                return true;
            }

            return false;

        } catch (Exception e) {
            log.error("检查书架书城关联关系异常，shelfId: {}, 错误: {}", shelfId, e.getMessage(), e);
            // 为了安全起见，异常时返回true，阻止删除
            return true;
        }
    }

    @Override
    public Page<AppEBookshelfDTO> pageForH5(PageInfo<AppEBookshelfH5QueryBO> pageRequest) throws BusinessException {
        log.info("H5分页查询书架列表，参数：{}", JSON.toJSONString(pageRequest));

        try {
            Page<AppEBookshelfDTO> result = eBookshelfBiz.pageForH5(pageRequest);
            log.info("H5分页查询书架列表成功，总数：{}", result.getTotal());
            return result;

        } catch (Exception e) {
            log.error("H5分页查询书架列表失败，错误：{}", e.getMessage(), e);
            throw new BusinessException("查询书架列表失败：" + e.getMessage());
        }
    }

}