package com.dbj.classpal.books.service.service.pointreading.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingCategoryQueryBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingCategorySaveBO;
import com.dbj.classpal.books.common.bo.pointreading.PointReadingCategoryUpdateBO;
import com.dbj.classpal.books.common.dto.pointreading.PointReadingCategoryDTO;
import com.dbj.classpal.books.service.biz.pointreading.IPointReadingCategoryBiz;
import com.dbj.classpal.books.service.service.pointreading.IPointReadingCategoryService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 点读书分类 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PointReadingCategoryServiceImpl  implements IPointReadingCategoryService {

    private final IPointReadingCategoryBiz pointReadingCategoryBiz;

    @Override
    public Page<PointReadingCategoryDTO> page(PageInfo<PointReadingCategoryQueryBO> pageRequest) throws BusinessException {
        return pointReadingCategoryBiz.page(pageRequest);
    }

    @Override
    public List<PointReadingCategoryDTO> tree(PointReadingCategoryQueryBO queryBO) throws BusinessException {
        return pointReadingCategoryBiz.tree(queryBO);
    }

    @Override
    public PointReadingCategoryDTO detail(Integer id) throws BusinessException {
        return pointReadingCategoryBiz.detail(id);
    }

    @Override
    public Integer save(PointReadingCategorySaveBO saveBO) throws BusinessException {
        return pointReadingCategoryBiz.save(saveBO);
    }

    @Override
    public boolean update(PointReadingCategoryUpdateBO updateBO) throws BusinessException {
        return pointReadingCategoryBiz.update(updateBO);
    }

    @Override
    public boolean delete(Integer id) throws BusinessException {
        return pointReadingCategoryBiz.delete(id);
    }

    @Override
    public boolean deleteBatch(List<Integer> ids) throws BusinessException {
        return pointReadingCategoryBiz.deleteBatch(ids);
    }

    @Override
    public boolean enableBatch(List<Integer> ids) throws BusinessException {
        return pointReadingCategoryBiz.enableBatch(ids);
    }

    @Override
    public boolean disableBatch(List<Integer> ids) throws BusinessException {
        return pointReadingCategoryBiz.disableBatch(ids);
    }
}
