package com.dbj.classpal.books.service.biz.audio.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.service.biz.audio.IAudioBackgroundBiz;
import com.dbj.classpal.books.service.entity.audio.AudioBackground;
import com.dbj.classpal.books.service.mapper.audio.AudioBackgroundMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 音频背景音 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Service
public class AudioBackgroundBizImpl extends ServiceImpl<AudioBackgroundMapper, AudioBackground> implements IAudioBackgroundBiz {

    @Autowired
    private AudioBackgroundMapper audioBackgroundMapper;

    @Override
    public int deleteByIds(List<Integer> outCountIds) {
        return audioBackgroundMapper.deleteByIds(outCountIds);
    }
}
