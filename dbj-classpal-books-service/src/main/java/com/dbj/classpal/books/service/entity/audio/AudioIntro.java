package com.dbj.classpal.books.service.entity.audio;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serial;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 音频制作列表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("audio_intro")
@ApiModel(value="AudioIntro对象", description="音频制作列表")
public class AudioIntro extends BizEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "音频名称")
    private String name;

    @ApiModelProperty(value = "所属分类id")
    private Integer audioClassifyId;

    @ApiModelProperty(value = "音频时长")
    private Integer duration;

    @ApiModelProperty(value = "音频大小(kb)")
    private Double size;

    @ApiModelProperty(value = "合成次数")
    private Integer frequency;

    @ApiModelProperty(value = "素材id")
    private Integer appMaterialId;

    @ApiModelProperty(value = "成品音频链接地址")
    private String audioUrl;

    @ApiModelProperty(value = "失败原因")
    private String failReason;

    @ApiModelProperty(value = "合成状态：0 待合成 1 合成中 2 已合成（成功）3 合成失败 4 取消")
    private Integer status;

    @ApiModelProperty(value = "上传素材中心状态：0 未上传 1 成功 2 失败")
    private Integer uploadStatus;

    @ApiModelProperty(value = "上传素材失败原因")
    private String materialFailReason;

    @ApiModelProperty(value = "原id")
    private Integer originId;

    @ApiModelProperty(value = "是否取消 0 否 1 是")
    private Integer isCancel;

}
