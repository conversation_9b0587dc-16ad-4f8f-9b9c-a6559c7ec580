package com.dbj.classpal.books.service.biz.ebooks;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.service.entity.product.AppEBookshelf;
import com.dbj.classpal.books.service.entity.product.AppEBookstoreShelfRef;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 书城-书架关联 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
public interface IAppEBookstoreShelfRefBiz extends IService<AppEBookstoreShelfRef> {

    /**
     * 批量保存书城-书架关联
     * 
     * @param storeId 书城ID
     * @param shelfIds 书架ID列表
     * @return 保存结果
     */
    boolean saveBatch(Integer storeId, List<Integer> shelfIds) throws BusinessException;
    
    /**
     * 批量删除书城-书架关联
     *
     * @param storeIds 书城ID
     * @param shelfIds 书架ID列表，为空则删除全部
     * @return 删除结果
     */
    boolean removeBatch(List<Integer> storeIds, List<Integer> shelfIds) throws BusinessException;
    
    /**
     * 更新书架排序
     * 
     * @param storeId 书城ID
     * @param shelfSortMap 书架排序映射，key为书架ID，value为排序序号
     * @return 更新结果
     */
    boolean updateSort(Integer storeId, Map<Integer, Integer> shelfSortMap) throws BusinessException;
    
    /**
     * 查询书城下的书架ID列表
     *
     * @param storeIds 书城ID
     * @return 书架ID列表
     */
    List<Integer> listShelfIds(List<Integer> storeIds) throws BusinessException;
    
    /**
     * 查询书架所在的所有书城ID列表
     * 
     * @param shelfId 书架ID
     * @return 书城ID列表
     */
    List<Integer> listStoreIds(Integer shelfId) throws BusinessException;
    
    /**
     * 统计书城下的书架数量
     * 
     * @param storeId 书城ID
     * @return 书架数量
     */
    int countShelves(Integer storeId) throws BusinessException;
    
    /**
     * 获取书城下的书架列表
     *
     * @param storeId 书城ID
     * @return 书架列表
     */
    List<AppEBookshelf> getShelvesOfStore(Integer storeId) throws BusinessException;

    /**
     * 批量查询多个书城的书架ID映射
     *
     * @param storeIds 书城ID列表
     * @return 书城ID到书架ID列表的映射
     */
    Map<Integer, List<Integer>> batchListShelfIds(List<Integer> storeIds) throws BusinessException;
} 