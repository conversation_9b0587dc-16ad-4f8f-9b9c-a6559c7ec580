package com.dbj.classpal.books.service.service.question.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.common.bo.question.*;
import com.dbj.classpal.books.common.dto.question.QuestionCategoryDTO;
import com.dbj.classpal.books.common.dto.question.QuestionCategoryRefDTO;
import com.dbj.classpal.books.common.enums.BusinessTypeEnum;
import com.dbj.classpal.books.common.enums.MaterialTypeEnum;
import com.dbj.classpal.books.service.biz.books.IBooksInfoBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInCodesContentsBiz;
import com.dbj.classpal.books.service.biz.question.QuestionCategoryBiz;
import com.dbj.classpal.books.service.entity.books.BooksRankInCodesContents;
import com.dbj.classpal.books.service.entity.question.QuestionCategoryBusinessRef;
import com.dbj.classpal.books.service.service.question.QuestionCategoryService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.dbj.classpal.books.service.biz.question.QuestionBiz;
import com.dbj.classpal.books.service.biz.question.IQuestionCategoryBusinessRefBiz;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * 题目分类服务实现类
 */
@Service
@RequiredArgsConstructor
public class QuestionCategoryServiceImpl implements QuestionCategoryService {

    private final QuestionCategoryBiz questionCategoryBiz;
    private final QuestionBiz questionBiz;
    private final IQuestionCategoryBusinessRefBiz questionCategoryBusinessRefBiz;
    private final IBooksRankInCodesContentsBiz booksRankInCodesContentsBiz;
    private final IBooksInfoBiz booksInfoBiz;

    private final static Integer CATEGORY_REF_CODE = 1;
    private final static String CATEGORY_REF_NAME = "答题";


    @Override
    public List<QuestionCategoryDTO> getCategoryTree(QuestionCategoryQueryBO queryBO) {
        return questionCategoryBiz.getCategoryTree(queryBO);
    }

    @Override
    public QuestionCategoryDTO getCategory(QuestionCategoryIdBO idBO) {
        if (Objects.isNull(idBO)) {
            return null;
        }
        List<QuestionCategoryDTO> categories = questionCategoryBiz.getCategoryWithChildren(idBO.getId());
        return CollectionUtils.isEmpty(categories) ? null : categories.get(0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createCategory(QuestionCategoryBO categoryBO) throws BusinessException {
        return questionCategoryBiz.createCategory(categoryBO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCategory(QuestionCategoryBO categoryBO) throws BusinessException {
        questionCategoryBiz.updateCategory(categoryBO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteCategory(QuestionCategoryIdsBO idsBO) throws BusinessException {
        if (Objects.isNull(idsBO)) {
            return;
        }

        // 检查是否存在子分类
        if (questionCategoryBiz.hasChildren(idsBO.getIds())) {
            throw new BusinessException(APP_QUESTION_CATEGORY_HAS_CHILDREN_CODE,APP_QUESTION_CATEGORY_HAS_CHILDREN_MSG);
        }

        // 检查是否有关联的试题
        if (questionBiz.hasCategoryQuestions(idsBO.getIds())) {
            throw new BusinessException(APP_QUESTION_CATEGORY_HAS_QUESTIONS_CODE,APP_QUESTION_CATEGORY_HAS_QUESTIONS_MSG);
        }

        // 检查分类是否已被配套引用
        if (questionCategoryBusinessRefBiz.hasBusinessRefs(idsBO.getIds())) {
            throw new BusinessException(APP_QUESTION_CATEGORY_HAS_REF_CODE,APP_QUESTION_CATEGORY_HAS_REF_MSG);
        }

        // 删除分类
        questionCategoryBiz.removeByIds(idsBO.getIds());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSort(QuestionCategorySortBO sortBO) throws BusinessException {
        questionCategoryBiz.updateSort(sortBO);
    }

    @Override
    public boolean checkNameExists(String name, Integer fatherId, Integer excludeId) {
        return questionCategoryBiz.checkNameExists(name, fatherId, excludeId);
    }

    @Override
    public List<QuestionCategoryRefDTO> getBusinessRefs(QuestionCategoryIdQueryBO queryBO){
        List<QuestionCategoryBusinessRef> entityList = questionCategoryBusinessRefBiz.getBusinessRefs(List.of(queryBO.getCategoryId()));
        List<QuestionCategoryRefDTO> result = new ArrayList<>();
        for (QuestionCategoryBusinessRef businessRef : entityList) {
            QuestionCategoryRefDTO dto = new QuestionCategoryRefDTO();
            BeanUtil.copyProperties(businessRef,dto);
            dto.setBusinessType(BusinessTypeEnum.BOOKS_RESOURCE_BUSINESS.getCode());
            dto.setBusinessTypeStr(BusinessTypeEnum.BOOKS_RESOURCE_BUSINESS.getName());
            BooksRankInCodesContents codesContents = booksRankInCodesContentsBiz.getById(businessRef.getBusinessId());
            if(codesContents != null){
                dto.setBusinessName(booksInfoBiz.getById(codesContents.getBooksId()).getBookName());
                dto.setBooksId(codesContents.getBooksId());
                dto.setBookName(booksInfoBiz.getById(codesContents.getBooksId()).getBookName());
                dto.setRankId(codesContents.getRankId());
                dto.setContentsId(codesContents.getId());
                dto.setRankClassifyId(codesContents.getRankClassifyId());
            }
            dto.setAppMaterialType(CATEGORY_REF_CODE);
            dto.setAppMaterialTypeStr(CATEGORY_REF_NAME);
            result.add(dto);
        }
        return result;
    }
} 