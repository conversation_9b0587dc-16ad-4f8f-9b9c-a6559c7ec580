package com.dbj.classpal.books.service.biz.studycenter.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.bo.studycenter.*;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.books.common.dto.studycenter.AppStudyModuleResourceRelDetailDTO;
import com.dbj.classpal.books.common.dto.studycenter.AppStudyModuleResourceRelListDTO;
import com.dbj.classpal.books.common.dto.studycenter.StudyCenterModuleListDTO;
import com.dbj.classpal.books.service.biz.studycenter.IAppStudyModuleResourceRelBiz;
import com.dbj.classpal.books.service.entity.studycenter.AppStudyModuleProgress;
import com.dbj.classpal.books.service.entity.studycenter.AppStudyModuleResourceRel;
import com.dbj.classpal.books.service.mapper.studycenter.AppStudyModuleProgressMapper;
import com.dbj.classpal.books.service.mapper.studycenter.AppStudyModuleResourceRelMapper;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class AppStudyModuleResourceRelBizImpl extends ServiceImpl<AppStudyModuleResourceRelMapper, AppStudyModuleResourceRel> implements IAppStudyModuleResourceRelBiz {
    @Resource
    private AppStudyModuleResourceRelMapper appStudyModuleResourceRelMapper;

    @Override
    public Boolean create(AppStudyModuleResourceRelCreateBO bo) throws BusinessException {
        log.info("创建资源关联 入参: {}", bo);
        AppStudyModuleResourceRel entity = BeanUtil.copyProperties(bo, AppStudyModuleResourceRel.class);
        int result = appStudyModuleResourceRelMapper.insert(entity);
        log.info("创建资源关联 返回: {}", result > 0);
        return result > 0;
    }

    @Override
    public Boolean update(AppStudyModuleResourceRelUpdateBO bo) throws BusinessException {
        log.info("修改资源关联 入参: {}", bo);
        AppStudyModuleResourceRel entity = BeanUtil.copyProperties(bo, AppStudyModuleResourceRel.class);
        int result = appStudyModuleResourceRelMapper.updateById(entity);
        log.info("Biz-修改资源关联 返回: {}", result > 0);
        return result > 0;
    }

    @Override
    public Boolean delete(List<Integer> ids) throws BusinessException {
        log.info("删除资源关联 入参: {}", ids);
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException(AppErrorCode.ID_LIST_NOT_EMPTY_CODE, AppErrorCode.ID_LIST_NOT_EMPTY_MSG);
        }
        int result = appStudyModuleResourceRelMapper.deleteByIds(ids);
        log.info("删除资源关联 返回: {}", result > 0);
        return result > 0;
    }

    @Override
    public AppStudyModuleResourceRelDetailDTO detail(Integer id) throws BusinessException {
        log.info("查询资源关联详情 入参: {}", id);
        if (id == null) {
            throw new BusinessException(AppErrorCode.ID_NOT_NULL_CODE, AppErrorCode.ID_NOT_NULL_MSG);
        }
        AppStudyModuleResourceRel entity = appStudyModuleResourceRelMapper.selectById(id);
        AppStudyModuleResourceRelDetailDTO dto = BeanUtil.copyProperties(entity, AppStudyModuleResourceRelDetailDTO.class);
        log.info("查询资源关联详情 返回: {}", dto);
        return dto;
    }

} 