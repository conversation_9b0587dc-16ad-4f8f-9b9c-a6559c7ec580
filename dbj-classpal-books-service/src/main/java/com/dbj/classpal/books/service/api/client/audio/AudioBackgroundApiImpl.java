package com.dbj.classpal.books.service.api.client.audio;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.dbj.classpal.books.client.api.audio.AudioBackgroundApi;
import com.dbj.classpal.books.client.bo.audio.AudioBackgroundBO;
import com.dbj.classpal.books.client.bo.audio.AudioReorderBO;
import com.dbj.classpal.books.client.bo.audio.AudioTypeBO;
import com.dbj.classpal.books.client.dto.audio.AudioBackgroundDTO;
import com.dbj.classpal.books.common.enums.audio.AudicBackgroundTypeEnum;
import com.dbj.classpal.books.service.biz.audio.IAudioBackgroundBiz;
import com.dbj.classpal.books.service.entity.audio.AudioBackground;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 背景音
 * <AUTHOR>
 * @since 2025-06-27
 */
@RestController
public class AudioBackgroundApiImpl implements AudioBackgroundApi {

    @Autowired
    private IAudioBackgroundBiz audioBackgroundBiz;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<Integer> save(List<AudioBackgroundBO> bo) throws BusinessException {
        // 校验数量是否超过20条
        List<AudioBackground> bgmList = audioBackgroundBiz.lambdaQuery()
                .eq(AudioBackground::getType, AudicBackgroundTypeEnum.DEFINITION.getCode())
                .orderByDesc(AudioBackground::getOrderNum).list();
        if (CollectionUtil.isEmpty(bgmList)) {
            insertBgm(bo);
            return RestResponse.success(1);
        }

        int max = 20;
        int size = bgmList.size() + bo.size();
        if (size > max) {
            // 超出（20条）
            int subSize = size - max;
            List<Integer> outCountIds = new ArrayList<>();
            for (int i = 0; i < subSize; i++) {
                if (i > bgmList.size()) {
                    break;
                }
                outCountIds.add(bgmList.get(i).getId());
            }
            // 删除排序最靠后的音频
            audioBackgroundBiz.deleteByIds(outCountIds);

            insertBgm(bo);
            List<AudioBackground> updateList = bgmList.stream()
                    .filter(v -> !outCountIds.contains(v.getId()))
                    .sorted(Comparator.comparingInt(AudioBackground::getOrderNum))
                    .toList();
            int orderNum = bo.size();
            for (AudioBackground background : updateList) {
                orderNum++;
                background.setOrderNum(orderNum);
            }
            boolean update = audioBackgroundBiz.updateBatchById(updateList);
            Assert.isTrue(update, "更新排序异常！");
        } else {
            // 未超出
            insertBgm(bo);
        }
        return RestResponse.success(1);
    }

    private void insertBgm(List<AudioBackgroundBO> bo) {
        List<AudioBackground> saveList = new ArrayList<>();
        for (int i = 0; i < bo.size(); i++) {
            AudioBackgroundBO bgmBO = bo.get(i);
            AudioBackground domain = new AudioBackground();
            // 复制一份素材中心文件
            domain.setMaterialName(bgmBO.getMaterialName());
            domain.setMaterialPath(bgmBO.getMaterialPath());
            domain.setMaterialSize(bgmBO.getMaterialSize());
            domain.setMaterialDuration(bgmBO.getMaterialDuration());
            domain.setType(AudicBackgroundTypeEnum.DEFINITION.getCode());
            domain.setOrderNum(i+1);
            saveList.add(domain);
        }
        boolean save = audioBackgroundBiz.saveBatch(saveList);
        Assert.isTrue(save, "保存失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<Integer> reorder(List<Integer> bo) throws BusinessException {
        List<AudioBackground> updateList = new ArrayList<>();
        for (int i = 0; i < bo.size(); i++) {
            AudioBackground bgmBO = new AudioBackground();
            bgmBO.setId(bo.get(i));
            bgmBO.setOrderNum(i+1);
            updateList.add(bgmBO);
        }
        boolean update = audioBackgroundBiz.updateBatchById(updateList);
        Assert.isTrue(update, "重排序异常！");
        return RestResponse.success(1);
    }

    @Override
    public RestResponse<List<AudioBackgroundDTO>> getDefinitionBgm(AudioTypeBO bo) throws BusinessException {
        List<AudioBackground> list = audioBackgroundBiz.lambdaQuery().eq(AudioBackground::getType, bo.getType()).orderByAsc(AudioBackground::getOrderNum).list();
        if (CollectionUtil.isEmpty(list)) {
            return RestResponse.success(new ArrayList<>());
        }

        return RestResponse.success(list.stream().map(item -> {
            AudioBackgroundDTO dto = new AudioBackgroundDTO();
            BeanUtils.copyProperties(item, dto);
            dto.setName(item.getMaterialName());
            return dto;
        }).collect(Collectors.toList()));
    }


}
