package com.dbj.classpal.books.service.entity.audio;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 音频文本合成历史记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("audio_context_info_history")
@ApiModel(value="AudioContextInfoHistory对象", description="音频文本合成历史记录")
public class AudioContextInfoHistory extends BizEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "音频文本详情id")
    private Integer audioContextInfoId;

    @ApiModelProperty(value = "阿里云语音合成的任务id")
    private String taskId;

    @ApiModelProperty(value = "版本号")
    private Integer version;


}
