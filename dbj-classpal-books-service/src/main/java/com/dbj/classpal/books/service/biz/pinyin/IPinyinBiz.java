package com.dbj.classpal.books.service.biz.pinyin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.client.bo.pinyin.PinyinPageBO;
import com.dbj.classpal.books.client.dto.pinyin.PinyinDTO;
import com.dbj.classpal.books.service.entity.pinyin.Pinyin;

/**
 * <p>
 * 拼音信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
public interface IPinyinBiz extends IService<Pinyin> {

    /**
     * 获取拼音分页列表
     *
     * @param page
     * @param bo
     * @return
     */
    Page<PinyinDTO> getPinyinPage(Page<Object> page, PinyinPageBO bo);
}
