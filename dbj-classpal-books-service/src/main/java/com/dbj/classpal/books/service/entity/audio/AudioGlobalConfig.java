package com.dbj.classpal.books.service.entity.audio;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 音频全局配置项
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("audio_global_config")
@ApiModel(value="AudioGlobalConfig对象", description="音频全局配置项")
public class AudioGlobalConfig extends BizEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "音频简介id")
    private Integer audioIntroId;

    @ApiModelProperty(value = "全局配置类型：1 背景音 2 特效")
    private Integer type;

    @ApiModelProperty(value = "背景音类型：1 预置 2 自定义")
    private Integer audioType;

    @ApiModelProperty(value = "全局背景音id")
    private Integer audioBackgroundId;

    @ApiModelProperty(value = "全局特效字典code")
    private String dictEffectsCode;

    @ApiModelProperty(value = "音量")
    private Integer volume;

    @ApiModelProperty(value = "模式：1 循环播放 2 播放一次")
    private Integer model;

}
