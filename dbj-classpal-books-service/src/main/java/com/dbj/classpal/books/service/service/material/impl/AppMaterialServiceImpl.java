package com.dbj.classpal.books.service.service.material.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportQueryApiBO;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportSaveApiBO;
import com.dbj.classpal.admin.client.bo.file.importfile.ExcelFileImportUpdateApiBO;
import com.dbj.classpal.books.common.bo.common.CommonIdBO;
import com.dbj.classpal.books.common.bo.material.*;
import com.dbj.classpal.books.common.dto.material.AppMaterialQueryDTO;
import com.dbj.classpal.books.common.dto.material.AppMaterialQueryDicTreeDTO;
import com.dbj.classpal.books.common.dto.material.AppMaterialStatisticsSizeDTO;
import com.dbj.classpal.books.common.enums.IsRootEnum;
import com.dbj.classpal.books.common.enums.MaterialTypeEnum;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBiz;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBusinessRefBiz;
import com.dbj.classpal.books.service.entity.material.AppMaterial;
import com.dbj.classpal.books.service.entity.material.AppMaterialBusinessRef;
import com.dbj.classpal.books.service.remote.file.ExcelFileRemoteService;
import com.dbj.classpal.books.service.service.material.IAppMaterialService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.utils.util.PinYinUtil;
import io.micrometer.common.util.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppMaterialServiceImpl
 * Date:     2025-04-08 16:22:04
 * Description: 表名： ,描述： 表
 */
@Service
public class AppMaterialServiceImpl implements IAppMaterialService {

    @Autowired
    private IAppMaterialBiz business;
    @Autowired
    private IAppMaterialBusinessRefBiz businessRefBusiness;
    @Autowired
    private ExcelFileRemoteService excelFileRemoteService;


    @Override
    public Page<AppMaterialQueryDTO> pageInfo(PageInfo<AppMaterialQueryBO> pageRequest) {
        // 1. 查询分页数据
        Page<AppMaterial> page = business.pageMaterials(pageRequest);
        // 2. 转换为VO
        return (Page<AppMaterialQueryDTO>) page.convert(this::convertToDTO);
    }

    @Override
    public Boolean materialExist(AppMaterialExistQueryBO bo) throws BusinessException {
        ExcelFileImportQueryApiBO excelFileImportQueryApiBO = new ExcelFileImportQueryApiBO();
        excelFileImportQueryApiBO.setMd5(bo.getMaterialMd5());
        Boolean checkFlag = excelFileRemoteService.checkProcessMd5File(excelFileImportQueryApiBO);
        if (checkFlag){
            throw new BusinessException(FILE_SAME_PROCESSING_EXIST_CODE,FILE_SAME_PROCESSING_EXIST_MSG);
        }
        List<AppMaterial> existList = business.lambdaQuery().eq(AppMaterial::getMaterialMd5, bo.getMaterialMd5()).list();
        if (existList.isEmpty()){
            return false;
        }
        String dirPath = bo.getDirPath();
        Integer parentId = bo.getParentId();
        //如果文件夹id不为空，执行以下代码
        if (parentId == null) {
            throw new BusinessException(FILE_DIRECTORY_NOT_EXIST_CODE,FILE_DIRECTORY_NOT_EXIST_MSG);
        }
        //查询目标文件夹目录是否存在
        AppMaterial byId = business.getById(parentId);
        //不存在，处理失败
        if (byId == null) {
            throw new BusinessException(FILE_DIRECTORY_NOT_EXIST_CODE,FILE_DIRECTORY_NOT_EXIST_MSG);
        }
        if (StringUtils.isNotEmpty(dirPath)){
            String[] pathArr = dirPath.split("/");
            boolean rootNotExist = false;
            List<String>mkdirList = new ArrayList<>();
            for (int i = 0; i < pathArr.length; i++) {
                List<AppMaterial> list = business.lambdaQuery().orderByDesc(AppMaterial::getOrderNum).eq(AppMaterial::getParentId, parentId).eq(AppMaterial::getMaterialType, MaterialTypeEnum.FILE_TYPE_DIR.getCode()).eq(AppMaterial::getMaterialName, pathArr[i]).list();
                //判断是否存在文件夹，如果存在遍历查询上级文件夹id
                if (CollectionUtils.isNotEmpty(list)){
                    AppMaterial appMaterial = list.get(0);
                    parentId = appMaterial.getId();
                    continue;
                }
                //判断是否第一个文件夹就不存在
                if (i == 0){
                    rootNotExist = true;
                    break;
                }else{
                    mkdirList.add(pathArr[i]);
                }
            }
            if(rootNotExist){
                for (String path : pathArr) {
                    AppMaterial appMaterial = new AppMaterial();
                    appMaterial.setParentId(parentId);
                    appMaterial.setMaterialName(path);
                    appMaterial.setMaterialType(MaterialTypeEnum.FILE_TYPE_DIR.getCode());
                    appMaterial.setOrderNum(this.createSort(parentId));
                    business.save(appMaterial);
                    parentId = appMaterial.getId();
                }
            }else{
                for (String path : mkdirList) {
                    AppMaterial appMaterial = new AppMaterial();
                    appMaterial.setParentId(parentId);
                    appMaterial.setMaterialName(path);
                    appMaterial.setMaterialType(MaterialTypeEnum.FILE_TYPE_DIR.getCode());
                    appMaterial.setOrderNum(this.createSort(parentId));
                    business.save(appMaterial);
                    parentId = appMaterial.getId();
                }
            }
        }
        LambdaQueryWrapper<AppMaterial> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppMaterial::getParentId,parentId);
        queryWrapper.orderByDesc(AppMaterial::getOrderNum);
        //查询当前文件夹下其他文件（查询最大的排序号）
        List<AppMaterial> broList = business.list(queryWrapper);
        AppMaterial appMaterial = new AppMaterial();
        appMaterial.setParentId(parentId);
        appMaterial.setMaterialName(bo.getMaterialName());
        appMaterial.setPinYin(existList.get(0).getPinYin());
        appMaterial.setFirstPinYin(existList.get(0).getFirstPinYin());
        appMaterial.setMaterialType(existList.get(0).getMaterialType());
        appMaterial.setMaterialExtension(existList.get(0).getMaterialExtension());
        appMaterial.setMaterialPath(existList.get(0).getMaterialPath());
        appMaterial.setMaterialOriginUrl(existList.get(0).getMaterialOriginUrl());
        appMaterial.setMaterialSize(existList.get(0).getMaterialSize());
        appMaterial.setMaterialDuration(existList.get(0).getMaterialDuration());
        appMaterial.setMaterialStatus(existList.get(0).getMaterialStatus());
        appMaterial.setMaterialCaption(existList.get(0).getMaterialCaption());
        appMaterial.setMaterialMd5(bo.getMaterialMd5());
        //给新增的资源赋予排序号
        if (CollectionUtils.isNotEmpty(broList)) {
            AppMaterial orderNum = broList.get(0);
            appMaterial.setOrderNum(orderNum.getOrderNum()+1);
        }
        //新增素材资源数据
        if(business.save(appMaterial)){
            ExcelFileImportSaveApiBO saveApiBO = new ExcelFileImportSaveApiBO();
            saveApiBO.setFileName(bo.getMaterialName());
            saveApiBO.setFileUrl(appMaterial.getMaterialPath());
            saveApiBO.setProcessedUrl(appMaterial.getMaterialPath());
            saveApiBO.setHandleStartTime(LocalDateTime.now());
            saveApiBO.setHandleEndTime(LocalDateTime.now());
            saveApiBO.setStatus(2);
            excelFileRemoteService.saveExcelFileImportById(saveApiBO);
        }else{
            ExcelFileImportSaveApiBO saveApiBO = new ExcelFileImportSaveApiBO();
            saveApiBO.setFileName(bo.getMaterialName());
            saveApiBO.setFileUrl(appMaterial.getMaterialPath());
            saveApiBO.setProcessedUrl(appMaterial.getMaterialPath());
            saveApiBO.setHandleStartTime(LocalDateTime.now());
            saveApiBO.setHandleEndTime(LocalDateTime.now());
            saveApiBO.setStatus(4);
            excelFileRemoteService.saveExcelFileImportById(saveApiBO);
        }
        return true;
    }

    @Override
    public Integer createSort(Integer parentId) {
        LambdaQueryWrapper<AppMaterial> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppMaterial::getParentId,parentId);
        queryWrapper.orderByDesc(AppMaterial::getOrderNum);
        List<AppMaterial> list = business.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)){
            return list.get(0).getOrderNum()+1;
        }else{
            return 1;
        }
    }

    @Override
    public Boolean materialMkdir(AppMaterialSaveMkdirBO saveMkdirBO) {
        AppMaterial appMaterial = new AppMaterial();
        BeanUtil.copyProperties(saveMkdirBO, appMaterial);
        appMaterial.setMaterialType(MaterialTypeEnum.FILE_TYPE_DIR.getCode());
        appMaterial.setOrderNum(this.createSort(saveMkdirBO.getParentId()));
        String materialName = saveMkdirBO.getMaterialName();
        appMaterial.setFirstPinYin(PinYinUtil.getFirstCharFirstLetter(materialName));
        appMaterial.setPinYin(PinYinUtil.convertChineseToPinyin(materialName));
        return business.save(appMaterial);
    }

    @Override
    public Boolean moveMaterial(AppMaterialIOBO ioBo) throws BusinessException {
        if (ioBo.getParentId().equals(ioBo.getAimParentId())){
            throw new BusinessException(APP_MATERIAL_MOVE_FAIL_SAME_DIR_CODE,APP_MATERIAL_MOVE_FAIL_SAME_DIR_MSG);
        }
        Integer count = this.checkAimIdInChildren(ioBo);
        if (count>0){
            throw new BusinessException(APP_MATERIAL_MOVE_FAIL_CHILDREN_DIR_CODE,APP_MATERIAL_MOVE_FAIL_CHILDREN_DIR_MSG);
        }
        return business.lambdaUpdate().eq(AppMaterial::getId,ioBo.getId()).eq(AppMaterial::getParentId,ioBo.getParentId()).set(AppMaterial::getParentId,ioBo.getAimParentId()).update();
    }

    @Override
    public Boolean batchMoveMaterial(AppMaterialBatchIOBO ioBo) throws BusinessException {
          List<AppMaterialIOBO> ioApiBOList = ioBo.getIoApiBOList();
          Boolean notSupported = false;
          Boolean sameDir = false;
          for (AppMaterialIOBO appMaterialIOBO : ioApiBOList) {
              if (appMaterialIOBO.getParentId().equals(appMaterialIOBO.getAimParentId())){
                  sameDir = true;
                  break;
              }
              Integer count = this.checkAimIdInChildren(appMaterialIOBO);
              if (count>0){
                  notSupported = true;
                  break;
              }
          }
          if (sameDir){
              throw new BusinessException(APP_MATERIAL_MOVE_FAIL_SAME_DIR_CODE,APP_MATERIAL_MOVE_FAIL_SAME_DIR_MSG);
          }
          if (notSupported){
              throw new BusinessException(APP_MATERIAL_MOVE_FAIL_CHILDREN_DIR_CODE,APP_MATERIAL_MOVE_FAIL_CHILDREN_DIR_MSG);
          }
          Set<Integer> idSet = ioApiBOList.stream().map(AppMaterialIOBO::getId).collect(Collectors.toSet());
          Integer aimId = ioApiBOList.stream().map(AppMaterialIOBO::getAimParentId).collect(Collectors.toList()).get(0);
          return business.lambdaUpdate().in(AppMaterial::getId,idSet).set(AppMaterial::getParentId,aimId).update();

    }

    @Override
    public Boolean copyMaterial(AppMaterialIOBO ioBo) throws BusinessException {
        if (ioBo.getParentId().equals(ioBo.getAimParentId())){
            throw new BusinessException(APP_MATERIAL_COPY_FAIL_SAME_DIR_CODE,APP_MATERIAL_COPY_FAIL_SAME_DIR_MSG);
        }
        AppMaterial byId = business.getById(ioBo.getId());
        if (ObjectUtils.isEmpty(byId)){
            throw new BusinessException(APP_MATERIAL_NOT_EXIST_CODE,APP_MATERIAL_NOT_EXIST_MSG);
        }
        Integer count = this.checkAimIdInChildren(ioBo);
        if (count>0){
            throw new BusinessException(APP_MATERIAL_COPY_FAIL_CHILDREN_DIR_CODE,APP_MATERIAL_COPY_FAIL_CHILDREN_DIR_MSG);
        }
        AppMaterial appMaterial = new AppMaterial();
        BeanUtil.copyProperties(byId, appMaterial);
        appMaterial.setParentId(ioBo.getAimParentId());
        String materialName = appMaterial.getMaterialName();
        appMaterial.setFirstPinYin(PinYinUtil.getFirstCharFirstLetter(materialName));
        appMaterial.setPinYin(PinYinUtil.convertChineseToPinyin(materialName));
        return business.save(appMaterial);
    }

    @Override
    public Boolean batchCopyMaterial(AppMaterialBatchIOBO ioBo) throws BusinessException {
        List<AppMaterialIOBO> ioApiBOList = ioBo.getIoApiBOList();
        Boolean notSupported = false;
        Boolean sameDir = false;
        for (AppMaterialIOBO appMaterialIOBO : ioApiBOList) {
            if (appMaterialIOBO.getParentId().equals(appMaterialIOBO.getAimParentId())){
                sameDir = true;
                break;
            }
            Integer count = this.checkAimIdInChildren(appMaterialIOBO);
            if (count>0){
                notSupported = true;
                break;
            }
        }
        if (sameDir){
            throw new BusinessException(APP_MATERIAL_COPY_FAIL_SAME_DIR_CODE,APP_MATERIAL_COPY_FAIL_SAME_DIR_MSG);
        }
        if (notSupported){
            throw new BusinessException(APP_MATERIAL_COPY_FAIL_CHILDREN_DIR_CODE,APP_MATERIAL_COPY_FAIL_CHILDREN_DIR_MSG);
        }
        Set<Integer> idSet = ioApiBOList.stream().map(AppMaterialIOBO::getId).collect(Collectors.toSet());
        Integer aimId = ioApiBOList.stream().map(AppMaterialIOBO::getAimParentId).collect(Collectors.toList()).get(0);
        List<AppMaterial> list = business.lambdaQuery().in(AppMaterial::getId, idSet).list();
        List<Integer> idList = list.stream().map(AppMaterial::getId).collect(Collectors.toList());
        List<Integer> notExistList = idSet.stream()
                .filter(num -> !idList.contains(num))
                .collect(Collectors.toList());
        //判断是否列表中有不存在的资源
        if (CollectionUtils.isNotEmpty(notExistList)){
            throw new BusinessException(APP_MATERIAL_COLLECT_NOT_EXIST_CODE,APP_MATERIAL_COLLECT_NOT_EXIST_MSG);
        }
        List<AppMaterial> saveBeans = list.stream().map(d -> {
            AppMaterial appMaterial = new AppMaterial();
            BeanUtil.copyProperties(d, appMaterial);
            appMaterial.setId(null);
            appMaterial.setParentId(aimId);
            String materialName = appMaterial.getMaterialName();
            appMaterial.setFirstPinYin(PinYinUtil.getFirstCharFirstLetter(materialName));
            appMaterial.setPinYin(PinYinUtil.convertChineseToPinyin(materialName));
            return appMaterial;
        }).collect(Collectors.toList());
        return business.saveBatch(saveBeans);
    }

    @Override
    public AppMaterialQueryDicTreeDTO getAllDirectsTree() {
        LambdaQueryWrapper<AppMaterial> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppMaterial::getIsRoot, IsRootEnum.IS_ROOT_YES.getCode());
        //1. 查询根节点数据
        AppMaterial one =  business.getOne(queryWrapper);
        if (ObjectUtils.isEmpty(one)){
            return null;
        }
        AppMaterialQueryDicTreeDTO rootBean = new AppMaterialQueryDicTreeDTO();
        BeanUtil.copyProperties(one, rootBean);

        //2.查询所有文件夹列表
        List<AppMaterialQueryDicTreeDTO> dirList = business.lambdaQuery().eq(AppMaterial::getMaterialType, MaterialTypeEnum.FILE_TYPE_DIR.getCode()).eq(AppMaterial::getIsRoot,IsRootEnum.IS_ROOT_NO.getCode()).list().stream().map(d -> {
            AppMaterialQueryDicTreeDTO nodeBean = new AppMaterialQueryDicTreeDTO();
            BeanUtil.copyProperties(d, nodeBean);
            return nodeBean;
        }).collect(Collectors.toList());

        //3. 查询根节点下所有文件夹列表
        List<AppMaterialQueryDicTreeDTO> rootNodes = business.lambdaQuery().eq(AppMaterial::getParentId, rootBean.getId()).eq(AppMaterial::getMaterialType, MaterialTypeEnum.FILE_TYPE_DIR.getCode()).list().stream().map(d -> {
            AppMaterialQueryDicTreeDTO nodeBean = new AppMaterialQueryDicTreeDTO();
            BeanUtil.copyProperties(d, nodeBean);
            return nodeBean;
        }).collect(Collectors.toList());

        // 3. 递归设置子节点
        rootNodes.forEach(root -> buildTree(root, dirList));
        rootBean.setChildren(rootNodes);
        return rootBean;
    }

    @Override
    public List<AppMaterialQueryDTO> getMaterialParentsPath(Integer id) {
        return business.getMaterialParentsPath(id).stream().map(d -> {
            AppMaterialQueryDTO appMaterialQueryDTO = new AppMaterialQueryDTO();
            BeanUtil.copyProperties(d, appMaterialQueryDTO);
            return appMaterialQueryDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public String getMaterialParentsNames(Integer id) {
        return business.getMaterialParentsNames(id);
    }

    @Override
    public Integer checkAimIdInChildren(AppMaterialIOBO ioBo) {
        return business.checkAimIdInChildren(ioBo);
    }

    @Override
    public Boolean reNameMaterial(AppMaterialReNameBO reNameBO) {
        String materialName = reNameBO.getMaterialName();
        String firstName = materialName.substring(0, 1);
        return business.lambdaUpdate()
                .eq(AppMaterial::getId, reNameBO.getId())
                .set(AppMaterial::getMaterialName,reNameBO.getMaterialName())
                .set(AppMaterial::getFirstPinYin,PinYinUtil.getFirstCharFirstLetter(firstName))
                .set(AppMaterial::getPinYin,PinYinUtil.convertChineseToPinyin(materialName)).update();
    }

    @Override
    public Boolean editCaption(AppMaterialEditCaptionBO editCaptionBO) {
        return business.lambdaUpdate().eq(AppMaterial::getId, editCaptionBO.getId()).set(AppMaterial::getMaterialCaption,editCaptionBO.getMaterialCaption()).update();
    }

    @Override
    public Boolean deleteMaterial(CommonIdBO bo) throws BusinessException {
        AppMaterial byId = business.getById(bo.getId());
        if (byId.getMaterialType().equals(MaterialTypeEnum.FILE_TYPE_DIR.getCode())){
            List<AppMaterial> childrenFiles = business.getChildrenFiles(bo.getId());
            Set<Integer> idSet = childrenFiles.stream().map(AppMaterial::getId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(idSet)){
                return business.removeById(bo.getId());
            }
            int refCount = businessRefBusiness.lambdaQuery().in(AppMaterialBusinessRef::getAppMaterialId, idSet).count().intValue();
            if (refCount>0){
                throw new BusinessException(APP_MATERIAL_DELETE_REF_FAIL_CODE,APP_MATERIAL_DELETE_REF_FAIL_MSG);
            }
            return business.removeByIds(idSet);
        }else{
            int refCount = businessRefBusiness.lambdaQuery().eq(AppMaterialBusinessRef::getAppMaterialId, bo.getId()).count().intValue();
            if (refCount>0){
                throw new BusinessException(APP_MATERIAL_DELETE_REF_FAIL_CODE,APP_MATERIAL_DELETE_REF_FAIL_MSG);
            }
            return business.removeById(bo.getId());
        }
    }

    @Override
    public Boolean batchDeleteMaterial(AppMaterialBatchCommonIdBO bo) throws BusinessException {
        List<AppMaterial> dataList = business.lambdaQuery().in(AppMaterial::getId, bo.getIds()).list();
        List<Integer> dirIdList = dataList.stream().filter(d -> d.getMaterialType().equals(MaterialTypeEnum.FILE_TYPE_DIR.getCode())).map(AppMaterial::getId).collect(Collectors.toList());
        Set<Integer> fileIdSet = dataList.stream().filter(d -> !(d.getMaterialType().equals(MaterialTypeEnum.FILE_TYPE_DIR.getCode()))).map(AppMaterial::getId).collect(Collectors.toSet());
        int fileRefCount = 0;
        if (CollectionUtils.isNotEmpty(fileIdSet)){
            fileRefCount = businessRefBusiness.lambdaQuery().in(AppMaterialBusinessRef::getAppMaterialId, fileIdSet).count().intValue();
        }
        if (fileRefCount>0){
            throw new BusinessException(APP_MATERIAL_DELETE_REF_FAIL_CODE,APP_MATERIAL_DELETE_REF_FAIL_MSG);
        }
        Set<Integer> dirIdSet  = null;
        if (CollectionUtils.isNotEmpty(dirIdList)){
            dirIdSet = business.getBatchDirChildrenFiles(dirIdList).stream().map(AppMaterial::getId).collect(Collectors.toSet());
        }
        int dirFileRefCount = 0;
        if (CollectionUtils.isNotEmpty(dirIdSet)){
            dirFileRefCount = businessRefBusiness.lambdaQuery().in(AppMaterialBusinessRef::getAppMaterialId, dirIdSet).count().intValue();
        }
        if (dirFileRefCount>0){
            throw new BusinessException(APP_MATERIAL_DELETE_REF_FAIL_CODE,APP_MATERIAL_DELETE_REF_FAIL_MSG);
        }
        boolean fileRemove = true;
        if (CollectionUtils.isNotEmpty(fileIdSet)){
            fileRemove = business.removeByIds(fileIdSet);
        }
        boolean dirRemove = true;
        if (CollectionUtils.isNotEmpty(dirIdSet)){
            dirRemove = business.removeByIds(dirIdSet);
        }
        return  fileRemove && dirRemove;
    }

    @Override
    public AppMaterialStatisticsSizeDTO usedSize() {
        double size = 0d;
        List<AppMaterial> list = business.lambdaQuery().list().stream().filter(d -> {
            return d.getMaterialSize() != null && !d.getMaterialSize().equals("");
        }).collect(Collectors.toList());
        for (AppMaterial appMaterial : list) {
            size += appMaterial.getMaterialSize();
        }
        return new AppMaterialStatisticsSizeDTO(size);
    }

    @Override
    public List<AppMaterial> getChildrenFiles(Integer id) {
        return business.getChildrenFiles(id);
    }

    @Override
    public List<AppMaterial> getBatchDirChildrenFiles(List<Integer> ids) {
        return business.getBatchDirChildrenFiles(ids);
    }

    private AppMaterialQueryDTO convertToDTO(AppMaterial material) {
        AppMaterialQueryDTO vo = new AppMaterialQueryDTO();
        BeanUtil.copyProperties(material, vo);
        return vo;
    }


    private void buildTree(AppMaterialQueryDicTreeDTO parentNode, List<AppMaterialQueryDicTreeDTO> allNodes) {
        List<AppMaterialQueryDicTreeDTO> children = allNodes.stream()
                .filter(node -> node.getParentId().equals(parentNode.getId()))
                .collect(Collectors.toList());

        if (!children.isEmpty()) {
            parentNode.setChildren(children);
            children.forEach(child -> buildTree(child, allNodes));
        }
    }
}
