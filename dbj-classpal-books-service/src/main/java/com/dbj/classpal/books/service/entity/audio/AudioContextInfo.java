package com.dbj.classpal.books.service.entity.audio;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 音频文本详情
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("audio_context_info")
@ApiModel(value="AudioContextInfo对象", description="音频文本详情")
public class AudioContextInfo extends BizEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "音频简介id")
    private Integer audioIntroId;

    @ApiModelProperty(value = "tts格式音频文本")
    private String text;

    @ApiModelProperty(value = "html格式音频文本")
    private String htmlText;

    @ApiModelProperty(value = "发音人id")
    private Integer audioSpeakerId;

    @ApiModelProperty(value = "情感：neutral（中性）、happy（开心）、angry（生气）、sad（悲伤）、fear（害怕）、hate（憎恨）、surprise（惊讶）、arousal（激动）、serious（严肃）、disgust（厌恶）、jealousy（嫉妒）、embarrassed（尴尬）、frustrated（沮丧）、affectionate（深情）、gentle（温柔）、newscast（播报）、customer-service（客服）、story（小说）、living（直播）")
    private String emotion;

    @ApiModelProperty(value = "音量，取值范围：0~100")
    private Integer volume;

    @ApiModelProperty(value = "语速，取值范围：-500~500")
    private Integer speechRate;

    @ApiModelProperty(value = "语调，取值范围：-500~500")
    private Integer pitchRate;

    @ApiModelProperty(value = "情绪强度，数值范围 [0.01,2.0]")
    private BigDecimal intensity;

    @ApiModelProperty(value = "阿里云语音合成的任务id")
    private String taskId;

    @ApiModelProperty(value = "版本号")
    private Integer version;

    @ApiModelProperty(value = "排序")
    private Integer orderNum;

    @ApiModelProperty(value = "预置提示音id，多个用英文逗号隔开")
    private String audioHintMusicIds;


}
