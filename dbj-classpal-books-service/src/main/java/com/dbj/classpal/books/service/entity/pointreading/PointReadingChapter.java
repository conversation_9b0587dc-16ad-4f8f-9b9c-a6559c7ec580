package com.dbj.classpal.books.service.entity.pointreading;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 点读书章节表
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("point_reading_chapter")
@Schema(name = "PointReadingChapter", description = "点读书章节表")
public class PointReadingChapter extends BizEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "所属目录ID")
    private Integer menuId;

    @Schema(description = "章节名称")
    private String name;

    @Schema(description = "章节图片ID")
    private String imageId;

    @Schema(description = "章节图片URL")
    private String imageUrl;

    @Schema(description = "章节图片名称")
    private String imageName;

    @Schema(description = "图片来源：1-点读书 2-素材中心")
    private Integer imageSourceType;

    @Schema(description = "热点数量")
    private Integer hotspotCount;

    @Schema(description = "关联媒体数量")
    private Integer mediaCount;

    @Schema(description = "关联媒体类型：10-音频")
    private Integer mediaType;

    @Schema(description = "排序")
    private Integer sortNum;

    @Schema(description = "状态：0-停用 1-正常")
    private Integer status;

    @Schema(description = "版本号")
    private Integer version;
}
