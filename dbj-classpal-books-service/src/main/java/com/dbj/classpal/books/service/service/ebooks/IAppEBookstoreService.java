package com.dbj.classpal.books.service.service.ebooks;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookstoreH5QueryBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookstoreQueryBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookstoreSaveBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookstoreUpdateBO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookstoreDTO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookstoreH5DTO;
import com.dbj.classpal.books.service.entity.ebooks.AppEBookstore;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 书城 服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
public interface IAppEBookstoreService extends IService<AppEBookstore> {

    /**
     * 分页查询书城列表
     * 
     * @param pageRequest 分页查询参数
     * @return 书城分页数据
     */
    Page<AppEBookstoreDTO> page(PageInfo<AppEBookstoreQueryBO> pageRequest) throws BusinessException;

    /**
     * 分页查询书城集
     * 支持两种查询模式：
     * 1. 默认查询已启用的所有书城集
     * 2. 根据书城ID查询特定的分享书城
     *
     * @param pageRequest 分页查询参数
     * @return 书城集分页数据
     */
    Page<AppEBookstoreH5DTO> pageBookstoreCollections(PageInfo<AppEBookstoreH5QueryBO> pageRequest) throws BusinessException;

    /**
     * 查询书城详情
     * 
     * @param id 书城ID
     * @return 书城详情数据
     */
    AppEBookstoreDTO detail(Integer id) throws BusinessException;

    /**
     * 新增书城
     * 
     * @param saveBO 书城保存参数
     * @return 新增书城ID
     */
    Integer save(AppEBookstoreSaveBO saveBO) throws BusinessException;

    /**
     * 更新书城
     * 
     * @param updateBO 书城更新参数
     * @return 更新结果
     */
    boolean update(AppEBookstoreUpdateBO updateBO) throws BusinessException;

    /**
     * 删除书城
     * 
     * @param id 书城ID
     * @return 删除结果
     */
    boolean delete(Integer id) throws BusinessException;

    /**
     * 批量删除书城
     * 
     * @param ids ID列表
     * @return 批量删除结果
     */
    boolean deleteBatch(List<Integer> ids) throws BusinessException;

    /**
     * 批量启用书城
     * 
     * @param ids ID列表
     * @return 批量启用结果
     */
    boolean enableBatch(List<Integer> ids) throws BusinessException;

    /**
     * 批量禁用书城
     * 
     * @param ids ID列表
     * @return 批量禁用结果
     */
    boolean disableBatch(List<Integer> ids) throws BusinessException;
    
    /**
     * 设置书城封面
     * 
     * @param id 书城ID
     * @param shelfId 书架ID，用于获取封面，可为null
     * @param picUrl 自定义封面URL，优先级高于shelfId
     * @return 设置结果
     */
    boolean setCover(Integer id, Integer shelfId, String picUrl) throws BusinessException;
    
    /**
     * 向书城添加书架
     * 
     * @param storeId 书城ID
     * @param shelfIds 书架ID列表
     * @return 添加结果
     */
    boolean addShelves(Integer storeId, List<Integer> shelfIds) throws BusinessException;
    
    /**
     * 从书城移除书架
     * 
     * @param storeId 书城ID
     * @param shelfIds 书架ID列表
     * @return 移除结果
     */
    boolean removeShelves(Integer storeId, List<Integer> shelfIds) throws BusinessException;
    
    /**
     * 调整书城书架排序
     *
     * @param storeId      书城ID
     * @param shelfIds
     * @param shelfSortMap 书架排序映射，key为书架ID，value为排序序号
     * @return 排序结果
     */
    boolean sortShelves(Integer storeId,List<Integer> shelfIds, Map<Integer, Integer> shelfSortMap) throws BusinessException;
    /**
     * 获取书城统计信息
     * 
     * @param id 书城ID
     * @return 统计信息
     */
    AppEBookstoreDTO getStatistics(Integer id) throws BusinessException;
    /**
     * 批量允许下载
     *
     * @param ids ID列表
     * @return 批量允许下载结果
     */
    boolean allowDownloadBatch(List<Integer> ids) throws BusinessException;

    /**
     * 批量关闭下载
     *
     * @param ids ID列表
     * @return 批量关闭下载结果
     */
    boolean disableDownloadBatch(List<Integer> ids) throws BusinessException;
} 