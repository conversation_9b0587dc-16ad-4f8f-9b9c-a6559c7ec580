package com.dbj.classpal.books.service.strategy.question.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dbj.classpal.books.common.bo.paper.QueryPaperQuestionsBO;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.books.common.dto.paper.PaperQuestionDTO;
import com.dbj.classpal.books.common.enums.BusinessTypeEnum;
import com.dbj.classpal.books.service.entity.question.Question;
import com.dbj.classpal.books.service.entity.studycenter.AppStudyModuleQuestionExt;
import com.dbj.classpal.books.service.mapper.studycenter.AppStudyModuleQuestionExtMapper;
import com.dbj.classpal.books.service.strategy.question.AbstractQuestionBusinessStrategy;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * 图书题目业务策略实现
 */
@Slf4j
@Component
public class StudyQuestionBusinessStrategy extends AbstractQuestionBusinessStrategy  {

    @Resource
    private AppStudyModuleQuestionExtMapper questionSettingMapper;

    @Override
    public Integer getBusinessType() {
        return BusinessTypeEnum.STUDY_CENTER_QUESTION_BUSINESS.getCode();
    }


    @Override
    public List<PaperQuestionDTO> getPaperQuestions(QueryPaperQuestionsBO queryBO) throws BusinessException {
        super.validateQueryParams(queryBO);
        AppStudyModuleQuestionExt questionSetting = questionSettingMapper.selectOne(new LambdaQueryWrapper<AppStudyModuleQuestionExt>().eq(AppStudyModuleQuestionExt::getModuleId,queryBO.getBusinessId()));
        if (questionSetting == null) {
            throw new BusinessException(AppErrorCode.APP_QUESTION_SETTING_NOT_EXIST_CODE, AppErrorCode.APP_QUESTION_SETTING_NOT_EXIST_MSG);
        }
        List<Question> selectedQuestions = queryQuestionCategoryBusinessRef(questionSetting.getId(),getBusinessType(), questionSetting.getQuestionNum(), questionSetting.getQuestionMethod());
        Map<Integer, Integer> questionOrderMap = new LinkedHashMap<>();
        int order = 1;
        for (Question question : selectedQuestions) {
            questionOrderMap.put(question.getId(), order++);
        }
        return buildPaperQuestionDTOs(selectedQuestions, questionOrderMap);
    }


    @Override
    protected List<Question> getQuestionsByBusinessId(Integer businessId, Integer businessType) throws BusinessException {
        if (businessId == null) {
            throw new BusinessException(APP_BUSINESS_ID_NOT_NULL_CODE,APP_BUSINESS_ID_NOT_NULL_MSG);
        }
        if (businessType == null) {
            throw new BusinessException(APP_BUSINESS_TYPE_NOT_NULL_CODE,APP_BUSINESS_TYPE_NOT_NULL_MSG);
        }

        try {
            AppStudyModuleQuestionExt questionSetting = questionSettingMapper.selectOne(new LambdaQueryWrapper<AppStudyModuleQuestionExt>().eq(AppStudyModuleQuestionExt::getModuleId,businessId));
            if (questionSetting == null) {
                throw new BusinessException(APP_QUESTION_SETTING_NOT_EXIST_CODE,APP_QUESTION_SETTING_NOT_EXIST_MSG);
            }
            return queryQuestionCategoryBusinessRef(questionSetting.getId(),getBusinessType(), questionSetting.getQuestionNum(), questionSetting.getQuestionMethod());
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("根据业务信息获取题目失败", e);
            throw new BusinessException(APP_GET_QUESTION_FAIL_CODE,APP_GET_QUESTION_FAIL_MSG);
        }
    }

}