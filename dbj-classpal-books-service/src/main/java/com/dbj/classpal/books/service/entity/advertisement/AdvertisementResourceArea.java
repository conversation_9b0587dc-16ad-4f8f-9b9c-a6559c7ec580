package com.dbj.classpal.books.service.entity.advertisement;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 广告页面区域表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("advertisement_resource_area")
@Tag(name="广告页面区域表", description="广告页面区域表")
public class AdvertisementResourceArea implements Serializable {



    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "页面id")
    private Integer resourceId;

    @Schema(description = "广告类型")
    private String type;

    @Schema(description = "广告类型名称")
    private String name;

    @Schema(description = "排序（越大越靠前）")
    private Integer sort;

    @Schema(description = "创建者")
    private Integer createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新者")
    private Integer updateBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "是否删除 0-否 1-是")
    private Boolean isDeleted;

    @Schema(description = "租户ID")
    private Integer tenantId;


}
