package com.dbj.classpal.books.service.api.client.studycenter;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.studycenter.StudyCenterApi;
import com.dbj.classpal.books.client.bo.studycenter.*;
import com.dbj.classpal.books.client.dto.studycenter.*;
import com.dbj.classpal.books.common.bo.studycenter.*;
import com.dbj.classpal.books.common.dto.studycenter.*;
import com.dbj.classpal.books.service.service.studycenter.IAppStudyModuleProgressService;
import com.dbj.classpal.books.service.service.studycenter.IAppStudyModuleService;
import com.dbj.classpal.framework.commons.converter.PageInfoConverter;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
public class StudyCenterApiImpl implements StudyCenterApi {
    @Resource
    private IAppStudyModuleService appStudyModuleService;
    @Resource
    private IAppStudyModuleProgressService appStudyModuleProgressService;


    @Override
    public RestResponse<Boolean> createModule(@Validated @RequestBody AppStudyModuleCreateApiBO bo) throws BusinessException {
        log.info("创建学习模块 入参: {}", JSON.toJSONString(bo));
        AppStudyModuleCreateBO serviceBO = BeanUtil.copyProperties(bo, AppStudyModuleCreateBO.class);
        Boolean result = appStudyModuleService.create(serviceBO);
        log.info("创建学习模块 返回: {}", result);
        return RestResponse.success(result);
    }
    @Override
    public RestResponse<Boolean> updateModule(@Validated @RequestBody AppStudyModuleUpdateApiBO bo) throws BusinessException {
        log.info("修改学习模块 入参: {}", JSON.toJSONString(bo));
        AppStudyModuleUpdateBO serviceBO = BeanUtil.copyProperties(bo, AppStudyModuleUpdateBO.class);
        Boolean result = appStudyModuleService.update(serviceBO);
        log.info("修改学习模块 返回: {}", result);
        return RestResponse.success(result);
    }
    @Override
    public RestResponse<Boolean> deleteModule(@Validated @RequestBody AppStudyModuleIdsApiBO idsApiBO) throws BusinessException {
        log.info("删除学习模块 入参: {}", idsApiBO);
        Boolean result = appStudyModuleService.delete(idsApiBO.getIds());
        log.info("删除学习模块 返回: {}", result);
        return RestResponse.success(result);
    }
    @Override
    public RestResponse<Page<AppStudyModuleListApiDTO>> pageModule(@Validated @RequestBody PageInfo<AppStudyModuleQueryPageApiBO> pageInfo) throws BusinessException {
        log.info("分页查询学习模块 入参: {}", JSON.toJSONString(pageInfo));
        PageInfo<AppStudyModuleQueryPageBO> servicePageInfo = PageInfoConverter.convertPageInfo(pageInfo, AppStudyModuleQueryPageBO.class);
        Page<AppStudyModuleListDTO> servicePage = appStudyModuleService.page(servicePageInfo);
        Page<AppStudyModuleListApiDTO> apiPage = new Page<>();
        BeanUtil.copyProperties(servicePage, apiPage);
        List<AppStudyModuleListApiDTO> apiRecords = BeanUtil.copyToList(servicePage.getRecords(), AppStudyModuleListApiDTO.class);
        apiPage.setRecords(apiRecords);
        log.info("分页查询学习模块 返回: {}", apiPage);
        return RestResponse.success(apiPage);
    }
    @Override
    public RestResponse<AppStudyModuleDetailApiDTO> getModuleDetail(@Validated @RequestBody AppStudyModuleIdApiBO idApiBO) throws BusinessException {
        log.info("查询学习模块详情 入参: {}", idApiBO);
        AppStudyModuleDetailDTO serviceDTO = appStudyModuleService.detail(idApiBO.getId());
        AppStudyModuleDetailApiDTO apiDTO = BeanUtil.copyProperties(serviceDTO, AppStudyModuleDetailApiDTO.class);
        log.info("查询学习模块详情 返回: {}", apiDTO);
        return RestResponse.success(apiDTO);
    }


    @Override
    public RestResponse<List<AppStudyModuleProgressListApiDTO>> listRecentStudy(@Validated @RequestBody StudyCenterModuleListQueryApiBO queryBO) throws BusinessException {
        log.info("最近学习列表 入参: {}", JSON.toJSONString(queryBO));
        StudyCenterModuleListQueryBO serviceBO = BeanUtil.copyProperties(queryBO, StudyCenterModuleListQueryBO.class);
        List<AppStudyModuleProgressListDTO> serviceList = appStudyModuleProgressService.listRecentStudy(serviceBO);
        List<AppStudyModuleProgressListApiDTO> apiList = BeanUtil.copyToList(serviceList, AppStudyModuleProgressListApiDTO.class);
        log.info("最近学习列表 返回: {}", apiList);
        return RestResponse.success(apiList);
    }

    @Override
    public RestResponse<Boolean> recordStudy(@Validated @RequestBody StudyCenterModuleStudyApiBO studyBO) throws BusinessException {
        log.info("记录学习进度 入参: {}", JSON.toJSONString(studyBO));
        StudyCenterModuleStudyBO serviceBO = BeanUtil.copyProperties(studyBO, StudyCenterModuleStudyBO.class);
        Boolean result = appStudyModuleProgressService.recordStudy(serviceBO);
        log.info("记录学习进度 结果: {}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> deleteStudyRecord(@Validated @RequestBody StudyCenterModuleStudyApiBO studyBO) throws BusinessException {
        log.info("删除学习记录 入参: {}", JSON.toJSONString(studyBO));
        StudyCenterModuleStudyBO serviceBO = BeanUtil.copyProperties(studyBO, StudyCenterModuleStudyBO.class);
        Boolean result = appStudyModuleProgressService.deleteStudyRecord(serviceBO);
        log.info("删除学习记录 结果: {}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<List<StudyCenterCategoryApiDTO>> listHome(@Validated @RequestBody StudyCenterModuleListQueryApiBO queryBO) throws BusinessException {
        log.info("首页所有模块/内容 入参: {}", JSON.toJSONString(queryBO));
        StudyCenterModuleListQueryBO serviceBO = BeanUtil.copyProperties(queryBO, StudyCenterModuleListQueryBO.class);
        List<StudyCenterCategoryDTO> serviceList = appStudyModuleService.listHome(serviceBO);
        List<StudyCenterCategoryApiDTO> apiList = BeanUtil.copyToList(serviceList, StudyCenterCategoryApiDTO.class);
        log.info("首页所有模块/内容 返回: {}", apiList);
        return RestResponse.success(apiList);
    }

    @Override
    public RestResponse<Boolean> batchPublish(AppStudyModuleIdsApiBO idsApiBO) throws BusinessException {
        Boolean result = appStudyModuleService.batchPublish(idsApiBO.getIds());
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> batchUnpublish(AppStudyModuleIdsApiBO idsApiBO) throws BusinessException {
        Boolean result = appStudyModuleService.batchUnpublish(idsApiBO.getIds());
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> batchShow(AppStudyModuleIdsApiBO idsApiBO) throws BusinessException {
        Boolean result = appStudyModuleService.batchShow(idsApiBO.getIds());
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> batchHide(AppStudyModuleIdsApiBO idsApiBO) throws BusinessException {
        Boolean result = appStudyModuleService.batchHide(idsApiBO.getIds());
        return RestResponse.success(result);
    }
}