package com.dbj.classpal.books.service.entity.studycenter;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_study_module_resource_rel")
@Tag(name = "学习模块资源关联", description = "学习中心-模块资源关联表")
@AllArgsConstructor
@NoArgsConstructor
public class AppStudyModuleResourceRel extends BizEntity implements Serializable {


    /** 学习模块ID */
    private Integer moduleId;

    /** 资源ID（评测、音频专辑、视频专辑、功能等的主键ID） */
    private Integer resourceId;

    /** 资源类型（评测、音频专辑、视频专辑、功能） */
    private String resourceType;

    @Schema(description = "资源名称")
    private String resourceName;

    @Schema(description = "资源icon")
    private String resourceIcon;

    /** 排序权重 */
    private Integer sortNum;

    /** 状态 0-禁用 1-启用 */
    private Integer status;
}