package com.dbj.classpal.books.service.biz.audio.impl;

import com.dbj.classpal.books.service.entity.audio.AudioGlobalConfig;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.service.biz.audio.IAudioGlobalConfigBiz;
import com.dbj.classpal.books.service.mapper.audio.AudioGlobalConfigMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 音频全局配置项 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Service
public class AudioGlobalConfigBizImpl extends ServiceImpl<AudioGlobalConfigMapper, AudioGlobalConfig> implements IAudioGlobalConfigBiz {

}
