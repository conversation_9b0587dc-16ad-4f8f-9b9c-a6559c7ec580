package com.dbj.classpal.books.service.entity.question;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("question_answer")
@Tag(name="题目答案选项", description="题目答案选项")
@AllArgsConstructor
@NoArgsConstructor
public class QuestionAnswer extends BizEntity implements Serializable {



    /**
     * 题目id
     */
    private Integer questionId;
    /**
     * 完形填空区域id
     */
    private Integer blankAreaId;

    /**
     * 序号
     */
    private Integer serialNo;

    /**
     * 选项名称
     */
    private String optionName;

    /**
     * 选项内容
     */
    private String optionContent;

    /**
     * 是否答案（0-否 1-是）
     */
    private Integer isAnswer;

    /**
     * 是否启用 1-是 0-否
     */
    private Integer status;
} 