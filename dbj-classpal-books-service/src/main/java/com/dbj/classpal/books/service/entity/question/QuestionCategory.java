package com.dbj.classpal.books.service.entity.question;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("question_category")
public class QuestionCategory extends BizEntity {


    /**
     * 类型名称
     */
    private String name;

    /**
     * 父级id
     */
    private Integer fatherId;

    /**
     * 是否默认分类：0 否 1 是
     */
    private Boolean isDefault;

    /**
     * 排序号
     */
    private Integer sortNum;

    /**
     * 状态 0-正常 1-停用
     */
    private Integer status;
} 