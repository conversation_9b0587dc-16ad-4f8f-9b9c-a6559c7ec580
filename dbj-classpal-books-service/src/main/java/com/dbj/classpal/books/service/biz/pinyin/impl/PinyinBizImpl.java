package com.dbj.classpal.books.service.biz.pinyin.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.client.bo.pinyin.PinyinPageBO;
import com.dbj.classpal.books.client.dto.pinyin.PinyinDTO;
import com.dbj.classpal.books.service.biz.pinyin.IPinyinBiz;
import com.dbj.classpal.books.service.entity.pinyin.Pinyin;
import com.dbj.classpal.books.service.mapper.pinyin.PinyinMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 拼音信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Service
public class PinyinBizImpl extends ServiceImpl<PinyinMapper, Pinyin> implements IPinyinBiz {

    @Override
    public Page<PinyinDTO> getPinyinPage(Page<Object> page, PinyinPageBO bo) {
        return this.baseMapper.getPinyinPage(page, bo);
    }
}
