package com.dbj.classpal.books.service.entity.books;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 产品分类配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("books_category_ref")
@Tag(name="BooksCategoryRef对象", description="产品分类配置表")
public class BooksCategoryRef extends BizEntity implements Serializable {


    @Schema(description = "分类id")
    @TableField("category_id")
    private Integer categoryId;

    @Schema(description = "图书id")
    @TableField("books_id")
    private Integer booksId;

    @Schema(description = "是否启用 1-是 0-否")
    @TableField("status")
    private Boolean status;


}
