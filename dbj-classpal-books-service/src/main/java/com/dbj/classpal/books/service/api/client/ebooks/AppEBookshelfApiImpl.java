package com.dbj.classpal.books.service.api.client.ebooks;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.ebooks.AppEBookshelfApi;
import com.dbj.classpal.books.client.bo.ebooks.*;
import com.dbj.classpal.books.client.dto.ebooks.AppEBookshelfApiDTO;
import com.dbj.classpal.books.client.dto.ebooks.AppEBookshelfLandingApiDTO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookshelfH5QueryBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookshelfQueryBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookshelfSaveBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookshelfUpdateBO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookshelfDTO;
import com.dbj.classpal.books.service.service.ebooks.IAppEBookshelfService;
import com.dbj.classpal.framework.commons.converter.PageInfoConverter;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 书架API接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
@Slf4j
@RestController
public class AppEBookshelfApiImpl implements AppEBookshelfApi {

    @Resource
    private IAppEBookshelfService eBookshelfService;

    @Override
    public RestResponse<Page<AppEBookshelfApiDTO>> page(PageInfo<AppEBookshelfQueryApiBO> pageRequest) throws BusinessException {
        log.info("分页查询书架列表 入参：{}", JSON.toJSONString(pageRequest));

        PageInfo<AppEBookshelfQueryBO> pageBO = PageInfoConverter.convertPageInfo(pageRequest, AppEBookshelfQueryBO.class);

        Page<AppEBookshelfDTO> page = eBookshelfService.page(pageBO);

        log.info("分页查询书架列表 返回记录：{}", page.getRecords());
        return RestResponse.success((Page<AppEBookshelfApiDTO>) page.convert(vo -> {
            AppEBookshelfApiDTO dto = new AppEBookshelfApiDTO();
            BeanUtil.copyProperties(vo, dto);
            return dto;
        }));
    }

    @Override
    public RestResponse<AppEBookshelfApiDTO> detail(AppEBookshelfIdApiBO idBO) throws BusinessException {
        log.info("查询书架详情 入参：{}", JSON.toJSONString(idBO));
        AppEBookshelfDTO dto = eBookshelfService.detail(idBO.getId());
        AppEBookshelfApiDTO apiDTO = BeanUtil.copyProperties(dto, AppEBookshelfApiDTO.class);
        log.info("查询书架详情 结果：{}", JSON.toJSONString(apiDTO));
        return RestResponse.success(apiDTO);
    }

    @Override
    public RestResponse<Integer> save(AppEBookshelfSaveApiBO saveBO) throws BusinessException {
        log.info("新增书架 入参：{}", JSON.toJSONString(saveBO));
        AppEBookshelfSaveBO serviceSaveBO = BeanUtil.copyProperties(saveBO, AppEBookshelfSaveBO.class);
        Integer id = eBookshelfService.save(serviceSaveBO);
        log.info("新增书架 结果：{}", id);
        return RestResponse.success(id);
    }

    @Override
    public RestResponse<Boolean> update(AppEBookshelfUpdateApiBO updateBO) throws BusinessException {
        log.info("更新书架 入参：{}", JSON.toJSONString(updateBO));
        AppEBookshelfUpdateBO serviceUpdateBO = BeanUtil.copyProperties(updateBO, AppEBookshelfUpdateBO.class);
        boolean result = eBookshelfService.update(serviceUpdateBO);
        log.info("更新书架 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> delete(AppEBookshelfIdApiBO idBO) throws BusinessException {
        log.info("删除书架 入参：{}", JSON.toJSONString(idBO));
        boolean result = eBookshelfService.delete(idBO.getId());
        log.info("删除书架 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> deleteBatch(AppEBookshelfIdsApiBO idsBO) throws BusinessException {
        log.info("批量删除书架 入参：{}", JSON.toJSONString(idsBO));
        boolean result = eBookshelfService.deleteBatch(idsBO.getIds());
        log.info("批量删除书架 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> enableBatch(AppEBookshelfIdsApiBO idsBO) throws BusinessException {
        log.info("批量启用书架 入参：{}", JSON.toJSONString(idsBO));
        boolean result = eBookshelfService.enableBatch(idsBO.getIds());
        log.info("批量启用书架 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> disableBatch(AppEBookshelfIdsApiBO idsBO) throws BusinessException {
        log.info("批量禁用书架 入参：{}", JSON.toJSONString(idsBO));
        boolean result = eBookshelfService.disableBatch(idsBO.getIds());
        log.info("批量禁用书架 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> allowDownloadBatch(AppEBookshelfIdsApiBO idsBO) throws BusinessException {
        log.info("批量允许下载书架 入参：{}", JSON.toJSONString(idsBO));
        boolean result = eBookshelfService.allowDownloadBatch(idsBO.getIds());
        log.info("批量允许下载单书 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> disableDownloadBatch(AppEBookshelfIdsApiBO idsBO) throws BusinessException {
        log.info("批量关闭下载书架 入参：{}", JSON.toJSONString(idsBO));
        boolean result = eBookshelfService.disableDownloadBatch(idsBO.getIds());
        log.info("批量关闭下载单书 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Page<AppEBookshelfLandingApiDTO>> pageForH5(PageInfo<AppEBookshelfH5QueryApiBO> pageRequest) throws BusinessException {
        log.info("H5分页查询书架列表，参数：{}", JSON.toJSONString(pageRequest));

        try {
            // 转换请求参数
            PageInfo<AppEBookshelfH5QueryBO> serviceRequest = new PageInfo<>();
            serviceRequest.setPageNum(pageRequest.getPageNum());
            serviceRequest.setPageSize(pageRequest.getPageSize());

            if (pageRequest.getData() != null) {
                AppEBookshelfH5QueryBO serviceQuery = BeanUtil.copyProperties(pageRequest.getData(), AppEBookshelfH5QueryBO.class);
                serviceRequest.setData(serviceQuery);
            }

            // 调用服务层
            Page<AppEBookshelfDTO> serviceResponse = eBookshelfService.pageForH5(serviceRequest);

            // 转换响应数据（H5优化版本：不包含books字段）
            Page<AppEBookshelfLandingApiDTO> apiResponse = (Page<AppEBookshelfLandingApiDTO>) serviceResponse.convert(dto ->
                BeanUtil.copyProperties(dto, AppEBookshelfLandingApiDTO.class)
            );

            log.info("H5分页查询书架列表成功，总数：{}", apiResponse.getTotal());
            return RestResponse.success(apiResponse);

        } catch (Exception e) {
            log.error("H5分页查询书架列表失败，错误：{}", e.getMessage(), e);
            throw new BusinessException("查询书架列表失败：" + e.getMessage());
        }
    }

}