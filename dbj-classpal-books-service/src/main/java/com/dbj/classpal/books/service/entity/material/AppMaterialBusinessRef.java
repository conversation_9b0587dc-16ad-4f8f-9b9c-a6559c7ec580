package com.dbj.classpal.books.service.entity.material;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialBusinessRef
 * Date:     2025-04-14 08:42:30
 * Description: 表名： ,描述： 表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_material_business_ref")
@Tag(name="素材与关联业务关系表", description="素材与关联业务关系表")
public class AppMaterialBusinessRef extends BizEntity implements Serializable {

    @TableField("app_material_id")
    @Schema(description = "素材库id")
    private Integer appMaterialId;


    @TableField("business_id")
    @Schema(description = "业务id")
    private Integer businessId;

    @TableField("business_type")
    @Schema(description = "业务类型 1内容管理-音频专辑 2内容管理-视频专辑 3图书管理-图书资源 4图书管理-题库 5图书管理-音频专辑 6图书管理-视频专辑 10内容管理-评测-评测项")
    private Integer businessType;

    @TableField("business_name")
    @Schema(description = "业务名称")
    private String businessName;

    @TableField("order_num")
    @Schema(description = "排序")
    private Integer orderNum;
}
