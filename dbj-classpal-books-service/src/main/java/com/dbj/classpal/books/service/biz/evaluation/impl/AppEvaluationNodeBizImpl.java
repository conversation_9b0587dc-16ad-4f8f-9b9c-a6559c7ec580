package com.dbj.classpal.books.service.biz.evaluation.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.service.biz.evaluation.IAppEvaluationNodeBiz;
import com.dbj.classpal.books.service.entity.evaluation.AppEvaluationNode;
import com.dbj.classpal.books.service.mapper.evaluation.AppEvaluationNodeMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationNodeBizImpl
 * Date:     2025-04-08 16:26:13
 * Description: 表名： ,描述： 表
 */
@Service
public class AppEvaluationNodeBizImpl extends ServiceImpl<AppEvaluationNodeMapper, AppEvaluationNode> implements IAppEvaluationNodeBiz {

    @Override
    public Integer getMaxSortNum(Integer id) {
        return baseMapper.getMaxSortNum(id);
    }
}
