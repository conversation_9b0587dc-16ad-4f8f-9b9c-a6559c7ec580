package com.dbj.classpal.books.service.biz.evaluation.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.bo.evaluation.AdminUserPaperEvaluationQueryBO;
import com.dbj.classpal.books.common.bo.evaluation.app.AppUserPaperEvaluationSaveBO;
import com.dbj.classpal.books.common.dto.evaluation.app.AppUserPaperEvaluationSaveDTO;
import com.dbj.classpal.books.service.biz.evaluation.IAppUserPaperEvaluationAnalysisBiz;
import com.dbj.classpal.books.service.biz.evaluation.IAppUserPaperEvaluationBiz;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBusinessRefBiz;
import com.dbj.classpal.books.service.biz.question.QuestionAnswerBiz;
import com.dbj.classpal.books.service.biz.question.QuestionBiz;
import com.dbj.classpal.books.service.biz.question.QuestionBlankAreaBiz;
import com.dbj.classpal.books.service.biz.question.QuestionBusinessRefBiz;
import com.dbj.classpal.books.service.entity.evaluation.AppUserPaperEvaluation;
import com.dbj.classpal.books.service.mapper.evaluation.AppUserPaperEvaluationMapper;
import com.dbj.classpal.books.service.mapper.material.AppMaterialMapper;
import com.dbj.classpal.books.service.remote.sys.SysAppUserRemoteService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationBizImpl
 * Date:     2025-04-08 16:26:13
 * Description: 表名： ,描述： 表
 */
@Service
public class AppUserPaperEvaluationBizImpl extends ServiceImpl<AppUserPaperEvaluationMapper, AppUserPaperEvaluation> implements IAppUserPaperEvaluationBiz {

    @Resource
    private QuestionBusinessRefBiz questionBusinessRefBiz;
    @Resource
    private QuestionBiz questionBiz;
    @Resource
    private IAppMaterialBusinessRefBiz materialBusinessRefBiz;
    @Resource
    private AppMaterialMapper materialMapper;
    @Resource
    private QuestionAnswerBiz questionAnswerBiz;
    @Resource
    private QuestionBlankAreaBiz questionBlankAreaBiz;
    @Resource
    private SysAppUserRemoteService sysAppUserRemoteService;
    @Resource
    private IAppUserPaperEvaluationAnalysisBiz appUserPaperEvaluationAnalysisBiz;

    @Override
    public Page<AppUserPaperEvaluation> pageInfo(PageInfo<AdminUserPaperEvaluationQueryBO> page) {
        return baseMapper.pageInfo(page.getPage(),page.getData());
    }

    @Override
    public AppUserPaperEvaluationSaveDTO saveEvaluationReport(AppUserPaperEvaluationSaveBO bo) throws BusinessException {
        List<AppUserPaperEvaluation> list = this.lambdaQuery().eq(AppUserPaperEvaluation::getAppUserId, bo.getAppUserId()).eq(AppUserPaperEvaluation::getAppEvaluationId, bo.getAppEvaluationId()).orderByDesc(AppUserPaperEvaluation::getCreateTime).list();
        AppUserPaperEvaluationSaveDTO saveDTO = new AppUserPaperEvaluationSaveDTO();
        if (CollectionUtils.isEmpty(list)) {
            AppUserPaperEvaluation saveBean = new AppUserPaperEvaluation();
            BeanUtil.copyProperties(bo, saveBean);
            boolean save = this.save(saveBean);
            if (!save){
                throw new BusinessException(APP_EVALUATION_SAVE_FAIL_CODE,APP_EVALUATION_SAVE_FAIL_MSG);
            }
            BeanUtil.copyProperties(saveBean, saveDTO);
            return saveDTO;
        }
        AppUserPaperEvaluation evaluation = list.get(0);
        BeanUtil.copyProperties(evaluation, saveDTO);
        return saveDTO;
    }
}
