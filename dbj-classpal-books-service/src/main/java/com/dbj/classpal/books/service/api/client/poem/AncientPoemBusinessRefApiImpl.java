package com.dbj.classpal.books.service.api.client.poem;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.dbj.classpal.books.client.api.poem.AncientPoemBusinessRefApi;
import com.dbj.classpal.books.client.bo.poem.AncientPoemBusinessRefListBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemBusinessRefSaveBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemBusinessRefSortBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemBusinessRefListDTO;
import com.dbj.classpal.books.common.enums.poem.PoemBusinessTypeEnum;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemBiz;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemBusinessRefBiz;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemReciteCategoryBiz;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemReciteCollectionBiz;
import com.dbj.classpal.books.service.entity.poem.AncientPoem;
import com.dbj.classpal.books.service.entity.poem.AncientPoemBusinessRef;
import com.dbj.classpal.books.service.entity.poem.AncientPoemReciteCategory;
import com.dbj.classpal.books.service.entity.poem.AncientPoemReciteCollection;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.ANCIENT_POEM_BUSINESS_REF_ALREADY_EXIST_CODE;
import static com.dbj.classpal.books.common.constant.AppErrorCode.ANCIENT_POEM_BUSINESS_REF_ALREADY_EXIST_MSG;
import static com.dbj.classpal.books.common.constant.AppErrorCode.ANCIENT_POEM_BUSINESS_REF_NOT_EXIST_CODE;
import static com.dbj.classpal.books.common.constant.AppErrorCode.ANCIENT_POEM_BUSINESS_REF_NOT_EXIST_MSG;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className AncientPoemBusinessRefApiImpl
 * @description
 * @date 2025-05-27 09:45
 **/
@RestController
public class AncientPoemBusinessRefApiImpl implements AncientPoemBusinessRefApi {

    @Resource
    private IAncientPoemBusinessRefBiz ancientPoemBusinessRefBiz;
    @Resource
    private IAncientPoemBiz ancientPoemBiz;
    @Resource
    private IAncientPoemReciteCollectionBiz ancientPoemReciteCollectionBiz;
    @Resource
    private IAncientPoemReciteCategoryBiz ancientPoemReciteCategoryBiz;


    @Override
    public RestResponse<List<AncientPoemBusinessRefListDTO>> listAncientPoemBusinessRef(AncientPoemBusinessRefListBO anomalyPoemBusinessRefListBO) {
        List<AncientPoemBusinessRefListDTO> ancientPoemBusinessRefListDTOList = ancientPoemBusinessRefBiz.listAncientPoemBusinessRef(anomalyPoemBusinessRefListBO);
        return RestResponse.success(ancientPoemBusinessRefListDTOList);
    }

    @Override
    public RestResponse<Boolean> batchSave(AncientPoemBusinessRefSaveBO ancientPoemBusinessRefSaveBO) throws BusinessException {
        List<AncientPoemBusinessRef> ancientPoemBusinessRefList = new ArrayList<>();
        List<Integer> ancientPoemIds = ancientPoemBusinessRefSaveBO.getAncientPoemIds();
        List<AncientPoem> ancientPoems = ancientPoemBiz.listByIds(ancientPoemIds);

        //先判空然后转换成map
        Map<Integer, AncientPoem> ancientPoemMap = Collections.emptyMap();
        if(CollectionUtils.isNotEmpty(ancientPoems)){
            ancientPoemMap =ancientPoems.stream().collect(Collectors.toMap(AncientPoem::getId, Function.identity()));
        }

        //校验是否已存在
        List<AncientPoemBusinessRef> existAncientPoemBusinessRefList = ancientPoemBusinessRefBiz.lambdaQuery()
                .eq(AncientPoemBusinessRef::getBusinessId, ancientPoemBusinessRefSaveBO.getBusinessId())
                .eq(AncientPoemBusinessRef::getBusinessType, ancientPoemBusinessRefSaveBO.getBusinessType())
                .in(AncientPoemBusinessRef::getAncientPoemId, ancientPoemIds).list();

        if(CollUtil.isNotEmpty(existAncientPoemBusinessRefList)) {
            Set<Integer> existAncientPoemIds = existAncientPoemBusinessRefList.stream().map(AncientPoemBusinessRef::getAncientPoemId).collect(Collectors.toSet());
            String existPoemTitles = ancientPoemMap.values().stream()
                    .filter(ancientPoem -> existAncientPoemIds.contains(ancientPoem.getId()))
                    .map(AncientPoem::getTitle).collect(Collectors.joining("、"));
            throw new BusinessException(ANCIENT_POEM_BUSINESS_REF_ALREADY_EXIST_CODE, ANCIENT_POEM_BUSINESS_REF_ALREADY_EXIST_MSG + "【"+existPoemTitles+"】");
        }

        String jumpType = null;
        Integer businessType = ancientPoemBusinessRefSaveBO.getBusinessType();
        Integer businessId = ancientPoemBusinessRefSaveBO.getBusinessId();
        if(Objects.equals(PoemBusinessTypeEnum.ANCIENT_POEM_RECITE_BUSINESS.getCode(), businessType)){
            AncientPoemReciteCollection ancientPoemReciteCollection = ancientPoemReciteCollectionBiz.getById(businessId);
            if(ancientPoemReciteCollection !=  null){
                AncientPoemReciteCategory ancientPoemReciteCategory = ancientPoemReciteCategoryBiz.getById(ancientPoemReciteCollection.getCategoryId());
                if(ancientPoemReciteCategory != null){
                    jumpType = ancientPoemReciteCategory.getType();
                }
            }
        }
        for(Integer ancientPoemId : ancientPoemBusinessRefSaveBO.getAncientPoemIds()){
            if(!ancientPoemMap.containsKey(ancientPoemId)){
                throw new BusinessException(ANCIENT_POEM_BUSINESS_REF_NOT_EXIST_CODE,ANCIENT_POEM_BUSINESS_REF_NOT_EXIST_MSG);
            }
            AncientPoemBusinessRef ancientPoemBusinessRef = new AncientPoemBusinessRef();
            ancientPoemBusinessRef.setAncientPoemId(ancientPoemId);
            ancientPoemBusinessRef.setBusinessId(businessId);
            ancientPoemBusinessRef.setBusinessName(ancientPoemMap.get(ancientPoemId).getTitle());
            ancientPoemBusinessRef.setBusinessType(businessType);
            ancientPoemBusinessRef.setJumpType(jumpType);
            ancientPoemBusinessRefList.add(ancientPoemBusinessRef);
        }
        ancientPoemBusinessRefBiz.saveBatch(ancientPoemBusinessRefList);
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> batchDelete(List<Integer> ids) {
        ancientPoemBusinessRefBiz.removeBatchByIds(ids);
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> batchSort(List<AncientPoemBusinessRefSortBO> anotherPoemBusinessRefSortList) {
        List<AncientPoemBusinessRef> ancientPoemBusinessRefList = BeanUtil.copyToList(anotherPoemBusinessRefSortList,AncientPoemBusinessRef.class);
        ancientPoemBusinessRefBiz.updateBatchById(ancientPoemBusinessRefList);
        return RestResponse.success(true);
    }
}
