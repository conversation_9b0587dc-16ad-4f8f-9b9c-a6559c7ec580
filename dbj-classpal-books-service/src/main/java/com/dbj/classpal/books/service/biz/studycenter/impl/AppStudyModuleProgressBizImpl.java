package com.dbj.classpal.books.service.biz.studycenter.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.bo.studycenter.StudyCenterModuleListQueryBO;
import com.dbj.classpal.books.common.bo.studycenter.StudyCenterModuleStudyBO;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.books.common.dto.studycenter.AppStudyModuleProgressListDTO;
import com.dbj.classpal.books.common.dto.studycenter.AppStudyModuleResourceRelDetailDTO;
import com.dbj.classpal.books.service.biz.studycenter.IAppStudyModuleProgressBiz;
import com.dbj.classpal.books.service.entity.studycenter.AppStudyModule;
import com.dbj.classpal.books.service.entity.studycenter.AppStudyModuleProgress;
import com.dbj.classpal.books.service.entity.studycenter.AppStudyModuleResourceRel;
import com.dbj.classpal.books.service.mapper.studycenter.AppStudyModuleMapper;
import com.dbj.classpal.books.service.mapper.studycenter.AppStudyModuleProgressMapper;
import com.dbj.classpal.books.service.mapper.studycenter.AppStudyModuleResourceRelMapper;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AppStudyModuleProgressBizImpl extends ServiceImpl<AppStudyModuleProgressMapper, AppStudyModuleProgress> implements IAppStudyModuleProgressBiz {
    @Resource
    private AppStudyModuleProgressMapper appStudyModuleProgressMapper;
    @Resource
    private AppStudyModuleMapper moduleMapper;
    @Resource
    private AppStudyModuleResourceRelMapper resourceRelMapper;

    @Override
    public Boolean delete(List<Integer> ids) throws BusinessException {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException(AppErrorCode.ID_LIST_NOT_EMPTY_CODE, AppErrorCode.ID_LIST_NOT_EMPTY_MSG);
        }
        int result = appStudyModuleProgressMapper.deleteByIds(ids);
        return result > 0;
    }



    /**
     * 记录用户学习进度
     *
     * @param bo 学习记录BO
     * @return 是否成功
     * @throws BusinessException 业务异常
     */
    @Override
    public Boolean recordStudy(StudyCenterModuleStudyBO bo) throws BusinessException {
        if (bo == null || bo.getUserId() == null || bo.getModuleId() == null) {
            throw new BusinessException(AppErrorCode.PARAM_NOT_NULL_CODE, AppErrorCode.PARAM_NOT_NULL_MSG);
        }

        log.info("记录学习进度 入参: {}", bo);

        AppStudyModuleProgress progress = this.lambdaQuery()
                .eq(AppStudyModuleProgress::getUserId, bo.getUserId())
                .eq(AppStudyModuleProgress::getModuleId, bo.getModuleId())
                .one();

        if (progress != null) {
            progress.setLastLearnTime(LocalDateTime.now());
            progress.setUpdateTime(LocalDateTime.now());
            this.updateById(progress);
        } else {
            progress = new AppStudyModuleProgress();
            progress.setUserId(bo.getUserId());
            progress.setModuleId(bo.getModuleId());
            progress.setLastLearnTime(LocalDateTime.now());
            progress.setCreateTime(LocalDateTime.now());
            progress.setUpdateTime(LocalDateTime.now());
            this.save(progress);
        }

        log.info("记录学习进度 成功");
        return true;
    }

    /**
     * 查询用户最近学习记录
     *
     * @param queryBO 查询条件
     * @return 最近学习记录列表
     * @throws BusinessException 业务异常
     */
    @Override
    public List<AppStudyModuleProgressListDTO> listRecentStudy(StudyCenterModuleListQueryBO queryBO) throws BusinessException {
        if (queryBO == null || queryBO.getUserId() == null) {
            throw new BusinessException(AppErrorCode.PARAM_NOT_NULL_CODE, AppErrorCode.PARAM_NOT_NULL_MSG);
        }
        log.info("查询最近学习记录 入参: {}", queryBO);

        // 查询最近学习记录
        List<AppStudyModuleProgress> progressList = this.lambdaQuery()
                .eq(AppStudyModuleProgress::getUserId, queryBO.getUserId())
                .orderByDesc(AppStudyModuleProgress::getLastLearnTime)
                .last(queryBO.getLimit() != null ? "limit " + queryBO.getLimit() : null)
                .list();

        if (CollectionUtils.isEmpty(progressList)) {
            return Collections.emptyList();
        }

        // 获取模块ID列表
        List<Integer> moduleIds = progressList.stream()
                .map(AppStudyModuleProgress::getModuleId)
                .collect(Collectors.toList());

        // 查询模块详情
        Map<Integer, AppStudyModule> moduleMap = moduleMapper.selectList(new LambdaQueryWrapper<AppStudyModule>()
                .in(AppStudyModule::getId, moduleIds))
                .stream()
                .collect(Collectors.toMap(AppStudyModule::getId, v -> v, (a, b) -> a));

        // 获取资源关联信息
        Map<Integer, List<AppStudyModuleResourceRelDetailDTO>> resourceRelMap = fetchResourceRelInfo(moduleIds);

        // 组装结果
        List<AppStudyModuleProgressListDTO> resultList = progressList.stream()
                .map(progress -> {
                    AppStudyModuleProgressListDTO dto = new AppStudyModuleProgressListDTO();
                    // 设置学习进度信息
                    dto.setUserId(progress.getUserId());
                    dto.setModuleId(progress.getModuleId());
                    dto.setLastLearnTime(progress.getLastLearnTime());

                    // 设置模块信息
                    AppStudyModule module = moduleMap.get(progress.getModuleId());
                    if (module != null) {
                        BeanUtil.copyProperties(module, dto);
                    }

                    // 设置资源关联信息
                    if (resourceRelMap.containsKey(progress.getModuleId())) {
                        dto.setResourceList(resourceRelMap.get(progress.getModuleId()));
                    }

                    return dto;
                })
                .collect(Collectors.toList());

        log.info("查询最近学习记录 返回条数: {}", resultList.size());
        return resultList;
    }

    /**
     * 删除学习记录
     *
     * @param bo 删除条件
     * @return 是否成功
     * @throws BusinessException 业务异常
     */
    @Override
    public Boolean deleteStudyRecord(StudyCenterModuleStudyBO bo) throws BusinessException {
        if (bo == null || bo.getUserId() == null) {
            throw new BusinessException(AppErrorCode.PARAM_NOT_NULL_CODE, AppErrorCode.PARAM_NOT_NULL_MSG);
        }

        log.info("删除学习记录 入参: {}", bo);

        // 逻辑删除
        boolean success = this.lambdaUpdate()
                .eq(AppStudyModuleProgress::getUserId, bo.getUserId())
                .eq(Objects.nonNull(bo.getModuleId()),AppStudyModuleProgress::getModuleId, bo.getModuleId())
                .set(AppStudyModuleProgress::getIsDeleted, YesOrNoEnum.YES.getCode())
                .set(AppStudyModuleProgress::getUpdateTime, LocalDateTime.now())
                .update();

        log.info("删除学习记录 结果: {}", success);
        return success;
    }



    /**
     * 获取并处理资源关联信息
     *
     * @param moduleIds 学习模块ID列表
     * @return 模块ID到资源关联信息列表的映射
     */
    private Map<Integer, List<AppStudyModuleResourceRelDetailDTO>> fetchResourceRelInfo(List<Integer> moduleIds) {
        if (CollectionUtils.isEmpty(moduleIds)) {
            return Collections.emptyMap();
        }

        // 查询资源关联信息
        List<AppStudyModuleResourceRel> allResourceRels = resourceRelMapper.selectList(new LambdaQueryWrapper<AppStudyModuleResourceRel>()
                .in(AppStudyModuleResourceRel::getModuleId, moduleIds));

        if (CollectionUtils.isEmpty(allResourceRels)) {
            return Collections.emptyMap();
        }

        // 按模块ID分组并转换为DTO
        return allResourceRels.stream()
                .collect(Collectors.groupingBy(
                        AppStudyModuleResourceRel::getModuleId,
                        Collectors.mapping(
                                rel -> {
                                    AppStudyModuleResourceRelDetailDTO dto = new AppStudyModuleResourceRelDetailDTO();
                                    dto.setModuleId(rel.getModuleId());
                                    dto.setResourceId(rel.getResourceId());
                                    dto.setResourceType(rel.getResourceType());
                                    dto.setResourceName(rel.getResourceName());
                                    dto.setResourceIcon(rel.getResourceIcon());
                                    return dto;
                                },
                                Collectors.toList()
                        )
                ));
    }
} 