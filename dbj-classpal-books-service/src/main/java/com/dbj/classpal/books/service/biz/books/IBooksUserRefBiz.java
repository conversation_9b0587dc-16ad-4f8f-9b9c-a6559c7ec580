package com.dbj.classpal.books.service.biz.books;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.client.bo.books.app.BooksUserShelfBO;
import com.dbj.classpal.books.client.dto.books.BooksUserCountDTO;
import com.dbj.classpal.books.client.dto.books.app.BooksUserShelfDTO;
import com.dbj.classpal.books.service.entity.books.BooksUserRef;

import java.util.List;

/**
 * <p>
 * 产品分类配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
public interface IBooksUserRefBiz extends IService<BooksUserRef> {

    /**
     * @param
     * @return
     * <AUTHOR>
     * @Description 分页查询书架上的图书信息
     * @Date 2025/4/21 16:57
     **/
    List<BooksUserShelfDTO> booksUserShellist(BooksUserShelfBO booksUserShelfBO);
    /**
     * @param
     * @return
     * <AUTHOR>
     * @Description 统计书本被添加次数
     * @Date 2025/4/21 16:57
     **/
    List<BooksUserCountDTO> booksUserCount(List<Integer> bookIds);



}
