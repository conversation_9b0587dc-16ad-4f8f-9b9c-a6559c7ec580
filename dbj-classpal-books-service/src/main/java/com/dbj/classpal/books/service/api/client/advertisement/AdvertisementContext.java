package com.dbj.classpal.books.service.api.client.advertisement;

import com.dbj.classpal.books.client.dto.advertisement.AdvertisementLevelConditionDTO;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-06-11 9:54
 */
@Data
@Accessors(chain = true)
public class AdvertisementContext {

    /** 广告类型 */
    private String advertisementType;
    /** 年级id */
    private String gradeId;
    /** 性别枚举 */
    private String gender;
    /** 关系类型 */
    private Integer relationType;
    /** ip省属地 */
    private String provinceName;
    /** ip市属地 */
    private String cityName;
    /** app用户id */
    private Integer appUserId;

    private AdvertisementLevelConditionDTO advertisementLevelCondition;

}
