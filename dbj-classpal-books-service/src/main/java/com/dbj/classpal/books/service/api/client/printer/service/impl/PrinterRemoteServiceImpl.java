package com.dbj.classpal.books.service.api.client.printer.service.impl;

import com.alibaba.fastjson.JSON;
import com.dbj.business.external.client.model.request.ProductInfoIdRequest;
import com.dbj.business.external.client.model.request.ProductInfoRequest;
import com.dbj.business.external.client.model.response.BasicConfigProductCategoryResponse;
import com.dbj.business.external.client.model.response.ProductInfoDetailResponse;
import com.dbj.business.external.client.model.response.ProductInfoResponse;
import com.dbj.classpal.books.common.enums.question.OpenApiServiceNameEnum;
import com.dbj.classpal.books.service.api.client.printer.service.PrinterRemoteService;
import com.dbj.classpal.books.service.openapi.constant.OpenapiConstants;
import com.dbj.classpal.books.service.openapi.factory.DynamicApiClientFactory;
import com.dbj.classpal.books.service.openapi.service.BaseApiService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.framework.openapi.response.ApiResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;


@Slf4j
@Service
public class PrinterRemoteServiceImpl extends BaseApiService implements PrinterRemoteService {

    public PrinterRemoteServiceImpl(DynamicApiClientFactory apiClientFactory) {
        super(apiClientFactory, OpenApiServiceNameEnum.BOOK.getCode());
    }

    @Override
    public List<BasicConfigProductCategoryResponse> getProductCategoryTreeList() throws BusinessException {
        try {
            CompletableFuture<ApiResponse<List<BasicConfigProductCategoryResponse>>> responseFuture = createAuthClient()
                    .thenCompose(apiRequest ->
                            apiRequest.post(
                                    OpenapiConstants.API_BASE_PATH + "/productCategoryTreeList",
                                    null,
                                    new TypeReference<List<BasicConfigProductCategoryResponse>>() {
                                    }
                            )
                    );

            ApiResponse<List<BasicConfigProductCategoryResponse>> response = responseFuture.get();
            if (response.isSuccess() && response.getData() != null) {
                log.info("获取图书分类信息: {}", JSON.toJSON(response));
            } else {
                log.error("获取图书分类信息失败: {}", response.getMessage());
                throw new RuntimeException("获取图书分类信息失败: " + response.getMessage());
            }
            return response.getData();
        } catch (InterruptedException | ExecutionException e) {
            log.error("获取图书分类信息时发生异常", e);
            throw new BusinessException(OPENAPI_GET_BOOK_CATEGORY_FAIL_CODE,OPENAPI_GET_BOOK_CATEGORY_FAIL_MSG);
        }
    }

    @Override
    public List<ProductInfoResponse> queryProductListByCodeOrName(ProductInfoRequest request) throws BusinessException {
        try {
            CompletableFuture<ApiResponse<List<ProductInfoResponse>>> responseFuture = createAuthClient()
                    .thenCompose(apiRequest ->
                            apiRequest.post(
                                    OpenapiConstants.API_BASE_PATH + "/queryProductListByCodeOrName",
                                    request,
                                    new TypeReference<List<ProductInfoResponse>>() {
                                    }
                            )
                    );

            ApiResponse<List<ProductInfoResponse>> response = responseFuture.get();
            if (response.isSuccess() && response.getData() != null) {
                log.info("获取图书列表信息: {}", JSON.toJSON(response));
            } else {
                log.error("获取图书列表信息失败: {}", response.getMessage());
                throw new RuntimeException("获取图书列表信息失败: " + response.getMessage());
            }
            return response.getData();
        } catch (InterruptedException | ExecutionException e) {
            log.error("获取图书列表信息时发生异常", e);
            throw new BusinessException(OPENAPI_GET_BOOK_LIST_FAIL_CODE,OPENAPI_GET_BOOK_LIST_FAIL_MSG);

        }
    }

    @Override
    public ProductInfoDetailResponse queryProductInfoById(ProductInfoIdRequest bo) throws BusinessException {
        try {
            CompletableFuture<ApiResponse<ProductInfoDetailResponse>> responseFuture = createAuthClient()
                    .thenCompose(apiRequest ->
                            apiRequest.post(
                                    OpenapiConstants.API_BASE_PATH + "/queryProductInfoById",
                                    bo,
                                    ProductInfoDetailResponse.class
                            )
                    );

            ApiResponse<ProductInfoDetailResponse> response = responseFuture.get();
            if (response.isSuccess() && response.getData() != null) {
                log.info("获取图书详情信息: {}", JSON.toJSON(response));
            } else {
                log.error("获取图书详情信息失败: {}", response.getMessage());
            }
            return response.getData();
        } catch (InterruptedException | ExecutionException e) {
            log.error("获取图书详情信息时发生异常", e);
            throw new BusinessException(OPENAPI_GET_BOOK_INFO_FAIL_CODE,OPENAPI_GET_BOOK_INFO_FAIL_MSG);
        }
    }

}