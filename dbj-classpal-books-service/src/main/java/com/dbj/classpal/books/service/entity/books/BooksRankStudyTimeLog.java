package com.dbj.classpal.books.service.entity.books;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 图书卷册赠册用户学习时长表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("books_rank_study_time_log")
@Tag(name="BooksRankStudyTimeLog对象", description="图书卷册赠册用户学习时长表")
public class BooksRankStudyTimeLog extends BizEntity implements Serializable {




    @Schema(description ="app用户id")
    @TableField("app_user_id")
    private Integer appUserId;

    @Schema(description ="产品id")
    @TableField("book_id")
    private Integer bookId;

    @Schema(description ="册书id")
    @TableField("rank_id")
    private Integer rankId;


    @Schema(description ="册数功能分类id")
    @TableField("rank_classify_id")
    private Integer rankClassifyId;

    @Schema(description ="书内码内容目录id")
    @TableField("in_codes_contents_id")
    private Integer inCodesContentsId;


    @Schema(description ="时常 按秒存")
    @TableField("study_time")
    private Integer studyTime;

    @Schema(description ="是否启用 1-是 0-否")
    @TableField("status")
    private Boolean status;


}
