package com.dbj.classpal.books.service.entity.question;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: QuestionBusinessRef
 * Date:     2025-05-19 11:04:27
 * Description: 表名： ,描述： 表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("question_business_ref")
@Tag(name="题目业务关联表", description="题目业务关联表")
@AllArgsConstructor
@NoArgsConstructor
public class QuestionBusinessRef extends BizEntity {

    @TableField("question_id")
    @Schema(name = "题目id")
    private Integer questionId;

    @TableField("business_id")
    @Schema(name = "业务id")
    private Integer businessId;

    @TableField("business_type")
    @Schema(name = "业务类型 1内容管理-评测-评测项")
    private Integer businessType;

    @TableField("order_num")
    @Schema(name = "排序")
    private Integer orderNum;

}
