package com.dbj.classpal.books.service.api.client.poem.app;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.poem.app.AppAncientPoemReciteAppUserAssessmentScoreApi;
import com.dbj.classpal.books.client.bo.poem.app.AncientPoemReciteAppUserAssessmentScorePageBO;
import com.dbj.classpal.books.client.bo.poem.app.AncientPoemReciteAppUserAssessmentScoreSaveBO;
import com.dbj.classpal.books.client.dto.material.AppCommonMediaDTO;
import com.dbj.classpal.books.client.dto.poem.app.AncientPoemReciteAppUserAssessmentScoreDetailDTO;
import com.dbj.classpal.books.client.dto.poem.app.AncientPoemReciteAppUserAssessmentScorePageDTO;
import com.dbj.classpal.books.client.dto.poem.app.AncientPoemReciteAppUserCountDTO;
import com.dbj.classpal.books.common.enums.BusinessTypeEnum;
import com.dbj.classpal.books.common.enums.poem.PoemReciteStandardTypeEnum;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBusinessRefBiz;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemBiz;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemReciteAppUserAssessmentScoresBiz;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemReciteStandardBiz;
import com.dbj.classpal.books.service.entity.poem.AncientPoem;
import com.dbj.classpal.books.service.entity.poem.AncientPoemReciteAppUserAssessmentScores;
import com.dbj.classpal.books.service.entity.poem.AncientPoemReciteStandard;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import com.dbj.classpal.framework.redisson.config.RedissonRedisUtils;
import com.dbj.classpal.framework.utils.util.ContextAppUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.RedisKeyConstants.DBJ_ANCIENT_POEM_RECITE_STANDARD_TYPE_CACHE_KEY;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className AppAncientPoemReciteAppUserAssessmentScoreApiImpl
 * @description
 * @date 2025-05-28 11:17
 **/
@RestController
public class AppAncientPoemReciteAppUserAssessmentScoreApiImpl implements AppAncientPoemReciteAppUserAssessmentScoreApi {
    @Resource
    private IAncientPoemReciteAppUserAssessmentScoresBiz ancientPoemReciteAppUserAssessmentScoresBiz;
    @Resource
    private IAncientPoemBiz ancientPoemBiz;
    @Resource
     private IAncientPoemReciteStandardBiz ancientPoemReciteStandardBiz;
    @Resource
    private RedissonRedisUtils redissonRedisUtils;
    @Resource
    private IAppMaterialBusinessRefBiz appMaterialBusinessRefBiz;

    @Override
    public RestResponse<Page<AncientPoemReciteAppUserAssessmentScorePageDTO>> pageAncientPoemReciteAppUserAssessmentScore(PageInfo<AncientPoemReciteAppUserAssessmentScorePageBO> pageInfo) {
        pageInfo.getData().setAppUserId(ContextAppUtil.getAppUserIdInt());
        Page<AncientPoemReciteAppUserAssessmentScorePageDTO> ancientPoemReciteAppUserAssessmentScorePageDTOPage = ancientPoemReciteAppUserAssessmentScoresBiz.pageAncientPoemReciteAppUserAssessmentScore(pageInfo);
        List<AncientPoemReciteAppUserAssessmentScorePageDTO> ancientPoemReciteAppUserAssessmentScorePageDTOList = ancientPoemReciteAppUserAssessmentScorePageDTOPage.getRecords();
        if(CollectionUtils.isNotEmpty(ancientPoemReciteAppUserAssessmentScorePageDTOList)){
            List<Integer> ids = ancientPoemReciteAppUserAssessmentScorePageDTOList.stream().map(AncientPoemReciteAppUserAssessmentScorePageDTO::getAncientPoemId).collect(Collectors.toList());
            List<AncientPoem> ancientPoems = ancientPoemBiz.listByIds(ids);
            Map<Integer,AncientPoem> ancientPoemMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(ancientPoems)){
                ancientPoemMap = ancientPoems.stream().collect(Collectors.toMap(AncientPoem::getId, Function.identity()));
            }
            for(AncientPoemReciteAppUserAssessmentScorePageDTO anomalyScorePageDTO : ancientPoemReciteAppUserAssessmentScorePageDTOList){
                AncientPoem ancientPoem = ancientPoemMap.get(anomalyScorePageDTO.getAncientPoemId());
                if(ancientPoem != null){
                    anomalyScorePageDTO.setTitle(ancientPoem.getTitle());
                    anomalyScorePageDTO.setTitlePinyin(ancientPoem.getTitlePinyin());
                    anomalyScorePageDTO.setAuthor(ancientPoem.getAuthor());
                    anomalyScorePageDTO.setDynasty(ancientPoem.getDynasty());
                    anomalyScorePageDTO.setGrade(ancientPoem.getGrade());
                    anomalyScorePageDTO.setIsOut(ancientPoem.getIsOut());
                    anomalyScorePageDTO.setCoverUrl(ancientPoem.getCoverUrl());
                    anomalyScorePageDTO.setIntroduction(ancientPoem.getIntroduction());
                }
            }
        }
        return RestResponse.success(ancientPoemReciteAppUserAssessmentScorePageDTOPage);
    }

    @Override
    @Transactional(rollbackFor =  Exception.class)
    public RestResponse<Boolean> saveAncientPoemReciteAppUserAssessmentScore(AncientPoemReciteAppUserAssessmentScoreSaveBO ancientPoemReciteAppUserAssessmentScoreSaveBO) {
        Integer appUserId = ContextAppUtil.getAppUserIdInt();
        //先查看是否当前古诗已经背诵过了，如果背诵过了则直接修改
        List<AncientPoemReciteAppUserAssessmentScores> ancientPoemReciteAppUserAssessmentScoresList = ancientPoemReciteAppUserAssessmentScoresBiz.lambdaQuery().eq(AncientPoemReciteAppUserAssessmentScores::getAppUserId,appUserId)
                .eq(AncientPoemReciteAppUserAssessmentScores::getPoemId,ancientPoemReciteAppUserAssessmentScoreSaveBO.getPoemId()).list();
        AncientPoemReciteAppUserAssessmentScores ancientPoemReciteAppUserAssessmentScores = BeanUtil.copyProperties(ancientPoemReciteAppUserAssessmentScoreSaveBO,AncientPoemReciteAppUserAssessmentScores.class);
        ancientPoemReciteAppUserAssessmentScores.setAppUserId(appUserId);
        ancientPoemReciteAppUserAssessmentScores.setAssessmentTime(LocalDateTime.now());
        if(CollectionUtils.isNotEmpty(ancientPoemReciteAppUserAssessmentScoresList)){
            ancientPoemReciteAppUserAssessmentScores.setId(ancientPoemReciteAppUserAssessmentScoresList.get(0).getId());
        }
        //计算超越的百分比
        BigDecimal totalScore = ancientPoemReciteAppUserAssessmentScoreSaveBO.getTotalScore();
        AncientPoemReciteStandard ancientPoemReciteStandard = ancientPoemReciteStandardBiz.getByScore(totalScore,PoemReciteStandardTypeEnum.ANCIENT_POEM_RECITE_TOTAL_SCORE.getCode());
        if (ancientPoemReciteStandard != null){
            String key = MessageFormat.format(DBJ_ANCIENT_POEM_RECITE_STANDARD_TYPE_CACHE_KEY, PoemReciteStandardTypeEnum.ANCIENT_POEM_RECITE_TOTAL_SCORE.getCode(),totalScore);
            String remark = redissonRedisUtils.getValue(key);
            if(StringUtils.isBlank(remark)){
                BigDecimal lowestScore = ancientPoemReciteStandard.getLowestScore();
                BigDecimal highestScore = ancientPoemReciteStandard.getHighestScore();
                BigDecimal lowestPercentage = ancientPoemReciteStandard.getLowestPercentage();
                BigDecimal highestPercentage = ancientPoemReciteStandard.getHighestPercentage();
                remark = ancientPoemReciteStandard.getRemark();
                if(lowestScore.compareTo(new BigDecimal(100)) != 0 &&  lowestPercentage.compareTo(BigDecimal.ZERO) != 0){
                    BigDecimal difference = highestScore.subtract(lowestScore);
                    if(difference.compareTo( BigDecimal.ZERO) == 0){
                        remark = MessageFormat.format( remark,lowestPercentage);
                    }else {
                        //计算百分比差
                        BigDecimal percentageDifference = highestPercentage.subtract(lowestPercentage);
                        //percentageDifference.divide(percentageDifference)四舍五入保留两位小数
                        percentageDifference = percentageDifference.divide(difference,2, RoundingMode.HALF_UP);
                        remark = MessageFormat.format( remark,lowestPercentage.add(totalScore.subtract(lowestScore).multiply(percentageDifference)));
                    }
                }
                redissonRedisUtils.setValue(key,  remark,1,  TimeUnit.DAYS);
            }
            ancientPoemReciteAppUserAssessmentScores.setComment( remark);
        }
        ancientPoemReciteAppUserAssessmentScoresBiz.saveOrUpdate(ancientPoemReciteAppUserAssessmentScores);
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<AncientPoemReciteAppUserAssessmentScoreDetailDTO> detail(Integer poemId) {
        //先查询古诗信息 ，如果古诗不存在 则直接报错
        AncientPoemReciteAppUserAssessmentScoreDetailDTO ancientPoemReciteAppUserAssessmentScoreDetailDTO = new AncientPoemReciteAppUserAssessmentScoreDetailDTO();
        AncientPoem ancientPoem = ancientPoemBiz.getById(poemId);

        if(ancientPoem != null){
            BeanUtil.copyProperties(ancientPoem,ancientPoemReciteAppUserAssessmentScoreDetailDTO);
            ancientPoemReciteAppUserAssessmentScoreDetailDTO.setAncientPoemId(ancientPoem.getId());
            List<AppCommonMediaDTO> originalAudioMedias = appMaterialBusinessRefBiz.getCommonBusinessList(Collections.singletonList(poemId),
                    BusinessTypeEnum.ANCIENT_POEM_ORIGINAL_AUDIO_BUSINESS);
            if (CollectionUtils.isNotEmpty(originalAudioMedias)){
                ancientPoemReciteAppUserAssessmentScoreDetailDTO.setOriginalAudioMedia(originalAudioMedias.get(0));
            }
            List<AppCommonMediaDTO> explanationAudioMedias = appMaterialBusinessRefBiz.getCommonBusinessList(Collections.singletonList(poemId),
                    BusinessTypeEnum.ANCIENT_POEM_EXPLANATION_AUDIO_BUSINESS);
            if (CollectionUtils.isNotEmpty(explanationAudioMedias)){
                ancientPoemReciteAppUserAssessmentScoreDetailDTO.setExplanationAudioMedia(explanationAudioMedias.get(0));
            }
        }
        List<AncientPoemReciteAppUserAssessmentScores> ancientPoemReciteAppUserAssessmentScoresList = ancientPoemReciteAppUserAssessmentScoresBiz.lambdaQuery().eq(AncientPoemReciteAppUserAssessmentScores::getPoemId,poemId)
                .eq(AncientPoemReciteAppUserAssessmentScores::getAppUserId,ContextAppUtil.getAppUserIdInt()).list();
        if(CollectionUtils.isNotEmpty(ancientPoemReciteAppUserAssessmentScoresList)){
            AncientPoemReciteAppUserAssessmentScores ancientPoemReciteAppUserAssessmentScores = ancientPoemReciteAppUserAssessmentScoresList.get(0);
            ancientPoemReciteAppUserAssessmentScoreDetailDTO.setToneScore(ancientPoemReciteAppUserAssessmentScores.getToneScore());
            ancientPoemReciteAppUserAssessmentScoreDetailDTO.setTotalScore(ancientPoemReciteAppUserAssessmentScores.getTotalScore());
            ancientPoemReciteAppUserAssessmentScoreDetailDTO.setFluencyScore(ancientPoemReciteAppUserAssessmentScores.getFluencyScore());
            ancientPoemReciteAppUserAssessmentScoreDetailDTO.setCompletenessScore(ancientPoemReciteAppUserAssessmentScores.getCompletenessScore());
            ancientPoemReciteAppUserAssessmentScoreDetailDTO.setRhythmScore(ancientPoemReciteAppUserAssessmentScores.getRhythmScore());
            ancientPoemReciteAppUserAssessmentScoreDetailDTO.setPronunciationScore(ancientPoemReciteAppUserAssessmentScores.getPronunciationScore());
            ancientPoemReciteAppUserAssessmentScoreDetailDTO.setComment(ancientPoemReciteAppUserAssessmentScores.getComment());
            ancientPoemReciteAppUserAssessmentScoreDetailDTO.setContent(ancientPoemReciteAppUserAssessmentScores.getContent());
            ancientPoemReciteAppUserAssessmentScoreDetailDTO.setIsAssessment(YesOrNoEnum.YES.getCode());
        }
        ancientPoemReciteAppUserAssessmentScoreDetailDTO.setIsAssessment(YesOrNoEnum.NO.getCode());
        return RestResponse.success(ancientPoemReciteAppUserAssessmentScoreDetailDTO);
    }

    @Override
    public RestResponse<AncientPoemReciteAppUserCountDTO> count() {
        AncientPoemReciteAppUserCountDTO ancientPoemReciteAppUserCountDTO = new AncientPoemReciteAppUserCountDTO();
        Long totalCount = ancientPoemReciteAppUserAssessmentScoresBiz.lambdaQuery().eq(AncientPoemReciteAppUserAssessmentScores::getAppUserId,ContextAppUtil.getAppUserIdInt()).count();
        ancientPoemReciteAppUserCountDTO.setTotalNum(totalCount);

        //计算超越的百分比
        //计算超越的百分比
        BigDecimal totalCountBig = BigDecimal.valueOf(totalCount);
        AncientPoemReciteStandard ancientPoemReciteStandard = ancientPoemReciteStandardBiz.getByScore(totalCountBig,PoemReciteStandardTypeEnum.ANCIENT_POEM_RECITE_TOTAL_NUM.getCode());
        if (ancientPoemReciteStandard != null) {
            String key = MessageFormat.format(DBJ_ANCIENT_POEM_RECITE_STANDARD_TYPE_CACHE_KEY, PoemReciteStandardTypeEnum.ANCIENT_POEM_RECITE_TOTAL_NUM.getCode(), totalCountBig);
            String comment = redissonRedisUtils.getValue(key);
            if (StringUtils.isBlank(comment)) {
                BigDecimal lowestScore = ancientPoemReciteStandard.getLowestScore();
                BigDecimal highestScore = ancientPoemReciteStandard.getHighestScore();
                BigDecimal lowestPercentage = ancientPoemReciteStandard.getLowestPercentage();
                BigDecimal highestPercentage = ancientPoemReciteStandard.getHighestPercentage();
                comment = ancientPoemReciteStandard.getRemark();
                if(highestScore != null){
                    if (lowestScore.compareTo(new BigDecimal(100)) != 0 && lowestPercentage.compareTo(BigDecimal.ZERO) != 0) {
                        BigDecimal difference = highestScore.subtract(lowestScore);
                        if (difference.compareTo(BigDecimal.ZERO) == 0) {
                            comment = MessageFormat.format(comment, lowestPercentage);
                        } else {
                            //计算百分比差
                            BigDecimal percentageDifference = highestPercentage.subtract(lowestPercentage);
                            //percentageDifference.divide(percentageDifference)四舍五入保留两位小数
                            percentageDifference = percentageDifference.divide(difference, 2, RoundingMode.HALF_UP);
                            comment = MessageFormat.format(comment, lowestPercentage.add(totalCountBig.subtract(lowestScore).multiply(percentageDifference)));
                        }
                    }
                }
                redissonRedisUtils.setValue(key, comment, 1, TimeUnit.DAYS);
            }
            ancientPoemReciteAppUserCountDTO.setComment(comment);
        }
        return RestResponse.success(ancientPoemReciteAppUserCountDTO);
    }

}
