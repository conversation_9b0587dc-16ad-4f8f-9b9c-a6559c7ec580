package com.dbj.classpal.books.service.biz.ebooks.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.service.entity.ebooks.AppEBookshelf;
import com.dbj.classpal.books.service.entity.ebooks.AppEBookstoreShelfRef;
import com.dbj.classpal.books.service.mapper.ebooks.AppEBookshelfMapper;
import com.dbj.classpal.books.service.mapper.ebooks.AppEBookstoreShelfRefMapper;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookstoreShelfRefBiz;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 书城-书架关联 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
@Slf4j
@Service
public class AppEBookstoreShelfRefBizImpl extends ServiceImpl<AppEBookstoreShelfRefMapper, AppEBookstoreShelfRef> implements IAppEBookstoreShelfRefBiz {

    @Resource
    private AppEBookshelfMapper eBookshelfMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(Integer storeId, List<Integer> shelfIds) throws BusinessException {
        if (storeId == null || CollectionUtils.isEmpty(shelfIds)) {
            throw new BusinessException("参数不能为空");
        }
        
        // 查询已经存在的关联关系，避免重复添加
        List<Integer> existShelfIds = listShelfIds(List.of(storeId));
        
        // 过滤出需要添加的ID
        List<Integer> needAddShelfIds = shelfIds.stream()
                .filter(shelfId -> !existShelfIds.contains(shelfId))
                .collect(Collectors.toList());
        
        if (CollectionUtils.isEmpty(needAddShelfIds)) {
            return true;
        }
        
        // 批量添加关联关系
        List<AppEBookstoreShelfRef> refList = new ArrayList<>();
        
        // 获取当前最大排序号
        Integer maxSortNum = 0;
        if (!existShelfIds.isEmpty()) {
            LambdaQueryWrapper<AppEBookstoreShelfRef> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AppEBookstoreShelfRef::getStoreId, storeId)
                    .orderByDesc(AppEBookstoreShelfRef::getSortNum)
                    .last("LIMIT 1");
            AppEBookstoreShelfRef maxSortRef = this.getOne(queryWrapper);
            if (maxSortRef != null) {
                maxSortNum = maxSortRef.getSortNum();
            }
        }
        
        // 构建关联实体列表
        for (Integer shelfId : needAddShelfIds) {
            AppEBookstoreShelfRef ref = new AppEBookstoreShelfRef();
            ref.setStoreId(storeId);
            ref.setShelfId(shelfId);
            ref.setSortNum(++maxSortNum);
            refList.add(ref);
        }
        
        return this.saveBatch(refList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeBatch(List<Integer> storeIds, List<Integer> shelfIds) throws BusinessException {
        if (storeIds == null) {
            throw new BusinessException("书城ID不能为空");
        }
        
        LambdaQueryWrapper<AppEBookstoreShelfRef> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AppEBookstoreShelfRef::getStoreId, storeIds);
        
        // 如果指定了书架ID列表，则只删除这些关联
        if (CollectionUtils.isNotEmpty(shelfIds)) {
            queryWrapper.in(AppEBookstoreShelfRef::getShelfId, shelfIds);
        }
        
        return this.remove(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSort(Integer storeId, Map<Integer, Integer> shelfSortMap) throws BusinessException {
        if (storeId == null || shelfSortMap == null || shelfSortMap.isEmpty()) {
            throw new BusinessException("参数不能为空");
        }
        
        List<Integer> shelfIds = new ArrayList<>(shelfSortMap.keySet());
        
        // 查询需要更新的关联记录
        LambdaQueryWrapper<AppEBookstoreShelfRef> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppEBookstoreShelfRef::getStoreId, storeId)
                .in(AppEBookstoreShelfRef::getShelfId, shelfIds);
        
        List<AppEBookstoreShelfRef> refList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(refList)) {
            return true;
        }
        
        // 更新排序号
        for (AppEBookstoreShelfRef ref : refList) {
            Integer sortNum = shelfSortMap.get(ref.getShelfId().intValue());
            if (sortNum != null) {
                ref.setSortNum(sortNum);
            }
        }
        
        return this.updateBatchById(refList);
    }

    @Override
    public List<Integer> listShelfIds(List<Integer> storeIds) throws BusinessException {
        if (storeIds == null) {
            return new ArrayList<>();
        }
        
        LambdaQueryWrapper<AppEBookstoreShelfRef> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AppEBookstoreShelfRef::getStoreId, storeIds)
                .orderByAsc(AppEBookstoreShelfRef::getSortNum);
        
        List<AppEBookstoreShelfRef> refList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(refList)) {
            return new ArrayList<>();
        }
        
        return refList.stream()
                .map(AppEBookstoreShelfRef::getShelfId)
                .collect(Collectors.toList());
    }

    @Override
    public List<Integer> listStoreIds(Integer shelfId) throws BusinessException {
        if (shelfId == null) {
            return new ArrayList<>();
        }
        
        LambdaQueryWrapper<AppEBookstoreShelfRef> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppEBookstoreShelfRef::getShelfId, shelfId);
        
        List<AppEBookstoreShelfRef> refList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(refList)) {
            return new ArrayList<>();
        }
        
        return refList.stream()
                .map(ref -> ref.getStoreId().intValue())
                .collect(Collectors.toList());
    }

    @Override
    public int countShelves(Integer storeId) throws BusinessException {
        if (storeId == null) {
            return 0;
        }
        
        LambdaQueryWrapper<AppEBookstoreShelfRef> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppEBookstoreShelfRef::getStoreId, storeId);
        
        return (int)this.count(queryWrapper);
    }
    
    @Override
    public List<AppEBookshelf> getShelvesOfStore(Integer storeId) throws BusinessException {
        if (storeId == null) {
            return new ArrayList<>();
        }

        // 获取书城下的书架ID列表
        List<Integer> shelfIds = listShelfIds(List.of(storeId));

        if (CollectionUtils.isEmpty(shelfIds)) {
            return new ArrayList<>();
        }

        // 根据书架ID列表查询书架信息
        return eBookshelfMapper.selectByIds(shelfIds);
    }

    @Override
    public Map<Integer, List<Integer>> batchListShelfIds(List<Integer> storeIds) throws BusinessException {
        if (CollectionUtils.isEmpty(storeIds)) {
            return Map.of();
        }

        // 批量查询所有书城的书架关联关系
        LambdaQueryWrapper<AppEBookstoreShelfRef> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AppEBookstoreShelfRef::getStoreId, storeIds);
        queryWrapper.orderByAsc(AppEBookstoreShelfRef::getStoreId, AppEBookstoreShelfRef::getSortNum);

        List<AppEBookstoreShelfRef> refList = this.list(queryWrapper);

        // 按书城ID分组，返回每个书城对应的书架ID列表
        return refList.stream()
                .collect(Collectors.groupingBy(
                        AppEBookstoreShelfRef::getStoreId,
                        Collectors.mapping(AppEBookstoreShelfRef::getShelfId, Collectors.toList())
                ));
    }
}