package com.dbj.classpal.books.service.biz.ebooks.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.service.dto.StoreShelfMappingDTO;
import com.dbj.classpal.books.service.entity.product.AppEBookshelf;
import com.dbj.classpal.books.service.entity.product.AppEBookstoreShelfRef;
import com.dbj.classpal.books.service.mapper.ebooks.AppEBookshelfMapper;
import com.dbj.classpal.books.service.mapper.ebooks.AppEBookstoreShelfRefMapper;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookstoreShelfRefBiz;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import jakarta.annotation.Resource;
import java.util.HashMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 书城-书架关联 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
@Slf4j
@Service
public class AppEBookstoreShelfRefBizImpl extends ServiceImpl<AppEBookstoreShelfRefMapper, AppEBookstoreShelfRef> implements IAppEBookstoreShelfRefBiz {

    @Resource
    private AppEBookshelfMapper eBookshelfMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(Integer storeId, List<Integer> shelfIds) throws BusinessException {
        if (storeId == null || CollectionUtils.isEmpty(shelfIds)) {
            throw new BusinessException("参数不能为空");
        }

        List<AppEBookstoreShelfRef> refList = new ArrayList<>();
        
        int maxSortNum = 0;
        
        for (Integer shelfId : shelfIds) {
            AppEBookstoreShelfRef ref = new AppEBookstoreShelfRef();
            ref.setStoreId(storeId);
            ref.setShelfId(shelfId);
            ref.setSortNum(++maxSortNum);
            refList.add(ref);
        }
        
        return this.saveBatch(refList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeBatch(List<Integer> storeIds, List<Integer> shelfIds) throws BusinessException {
        if (storeIds == null) {
            throw new BusinessException("书城ID不能为空");
        }
        
        LambdaQueryWrapper<AppEBookstoreShelfRef> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AppEBookstoreShelfRef::getStoreId, storeIds);
        
        // 如果指定了书架ID列表，则只删除这些关联
        if (CollectionUtils.isNotEmpty(shelfIds)) {
            queryWrapper.in(AppEBookstoreShelfRef::getShelfId, shelfIds);
        }
        
        return this.remove(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSort(Integer storeId, Map<Integer, Integer> shelfSortMap) throws BusinessException {
        if (storeId == null || shelfSortMap == null || shelfSortMap.isEmpty()) {
            throw new BusinessException("参数不能为空");
        }
        
        List<Integer> shelfIds = new ArrayList<>(shelfSortMap.keySet());
        
        // 查询需要更新的关联记录
        LambdaQueryWrapper<AppEBookstoreShelfRef> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppEBookstoreShelfRef::getStoreId, storeId)
                .in(AppEBookstoreShelfRef::getShelfId, shelfIds);
        
        List<AppEBookstoreShelfRef> refList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(refList)) {
            return true;
        }
        
        // 更新排序号
        for (AppEBookstoreShelfRef ref : refList) {
            Integer sortNum = shelfSortMap.get(ref.getShelfId());
            if (sortNum != null) {
                ref.setSortNum(sortNum);
            }
        }
        
        return this.updateBatchById(refList);
    }

    @Override
    public List<Integer> listShelfIds(List<Integer> storeIds) throws BusinessException {
        return getShelfWithDetailsByStoreIds(storeIds);
    }

    @Override
    public List<Integer> listStoreIds(Integer shelfId) throws BusinessException {
        if (shelfId == null) {
            return new ArrayList<>();
        }
        
        LambdaQueryWrapper<AppEBookstoreShelfRef> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppEBookstoreShelfRef::getShelfId, shelfId);
        
        List<AppEBookstoreShelfRef> refList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(refList)) {
            return new ArrayList<>();
        }
        
        return refList.stream()
                .map(AppEBookstoreShelfRef::getStoreId)
                .collect(Collectors.toList());
    }

    @Override
    public int countShelves(Integer storeId) {
        if (storeId == null) {
            return 0;
        }
        
        LambdaQueryWrapper<AppEBookstoreShelfRef> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppEBookstoreShelfRef::getStoreId, storeId);
        
        return (int)this.count(queryWrapper);
    }
    
    @Override
    public List<AppEBookshelf> getShelvesOfStore(Integer storeId) throws BusinessException {
        if (storeId == null) {
            return new ArrayList<>();
        }

        // 获取书城下的书架ID列表
        List<Integer> shelfIds = listShelfIds(List.of(storeId));

        if (CollectionUtils.isEmpty(shelfIds)) {
            return new ArrayList<>();
        }

        // 根据书架ID列表查询书架信息
        return eBookshelfMapper.selectByIds(shelfIds);
    }

    @Override
    public Map<Integer, List<Integer>> batchListShelfIds(List<Integer> storeIds) throws BusinessException {
        if (CollectionUtils.isEmpty(storeIds)) {
            return Map.of();
        }

        // 使用XML映射器中的JOIN查询和窗口函数优化性能
        List<StoreShelfMappingDTO> resultList = baseMapper.batchGetShelfIdsByStoreIds(storeIds);

        if (CollectionUtils.isEmpty(resultList)) {
            return Map.of();
        }

        // 转换为Map<Integer, List<Integer>>格式
        Map<Integer, List<Integer>> result = new HashMap<>();
        for (StoreShelfMappingDTO mapping : resultList) {
            result.computeIfAbsent(mapping.getStoreId(), k -> new ArrayList<>()).add(mapping.getShelfId());
        }

        return result;
    }

    private List<Integer> getShelfWithDetailsByStoreIds(List<Integer> storeIds) {
        if (CollectionUtils.isEmpty(storeIds)) {
            return new ArrayList<>();
        }
        return baseMapper.getShelfWithDetailsByStoreIds(storeIds);
    }
}