package com.dbj.classpal.books.service.biz.ebooks;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookstoreQueryBO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookstoreDTO;
import com.dbj.classpal.books.service.entity.product.AppEBookstore;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 书城 业务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
public interface IAppEBookstoreBiz extends IService<AppEBookstore> {

    /**
     * 分页查询书城列表
     * 
     * @param page 分页对象
     * @param queryBO 查询参数
     * @return 书城分页数据
     */
    Page<AppEBookstoreDTO> pageBookstore(Page<AppEBookstore> page, AppEBookstoreQueryBO queryBO) throws BusinessException;

    /**
     * 查询书城详情
     * 
     * @param id 书城ID
     * @return 书城详情数据
     */
    AppEBookstoreDTO getBookstoreDetail(Integer id) throws BusinessException;

    /**
     * 设置书城封面
     * 
     * @param id 书城ID
     * @param shelfId 书架ID，用于获取封面，可为null
     * @param picUrl 自定义封面URL，优先级高于shelfId
     * @return 设置结果
     */
    boolean setBookstoreCover(Integer id, Integer shelfId, String picUrl) throws BusinessException;
    
    /**
     * 向书城添加书架
     * 
     * @param storeId 书城ID
     * @param shelfIds 书架ID列表
     * @return 添加结果
     */
    boolean addShelvesToStore(Integer storeId, List<Integer> shelfIds) throws BusinessException;
    
    /**
     * 从书城移除书架
     * 
     * @param storeId 书城ID
     * @param shelfIds 书架ID列表
     * @return 移除结果
     */
    boolean removeShelvesFromStore(Integer storeId, List<Integer> shelfIds) throws BusinessException;
    
    /**
     * 调整书城书架排序
     * 
     * @param storeId 书城ID
     * @param shelfSortMap 书架排序映射，key为书架ID，value为排序序号
     * @return 排序结果
     */
    boolean sortShelvesInStore(Integer storeId, Map<Integer, Integer> shelfSortMap) throws BusinessException;

    /**
     * 获取书城统计信息
     * 
     * @param id 书城ID
     * @return 统计信息DTO
     */
    AppEBookstoreDTO getBookstoreStatistics(Integer id) throws BusinessException;
    /**
     * 批量允许下载单书
     *
     * @param ids ID列表
     * @return 批量允许下载结果
     */
    boolean allowDownloadBatch(List<Integer> ids) throws BusinessException;

    /**
     * 批量关闭下载单书
     *
     * @param ids ID列表
     * @return 批量关闭下载结果
     */
    boolean disableDownloadBatch(List<Integer> ids) throws BusinessException;

}