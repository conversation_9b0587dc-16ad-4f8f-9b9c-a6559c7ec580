package com.dbj.classpal.books.service.biz.ebooks.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dbj.classpal.books.common.bo.ebooks.GetShareInfoBO;
import com.dbj.classpal.books.common.bo.ebooks.RemoveShareUrlBO;
import com.dbj.classpal.books.common.bo.watermark.WaterMarkEntity;
import com.dbj.classpal.books.common.config.books.BookCodeUrlConfig;
import com.dbj.classpal.books.common.constant.RedisKeyConstants;
import com.dbj.classpal.books.common.dto.config.BasicConfigDTO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookDTO;
import com.dbj.classpal.books.common.dto.ebooks.ShareUrlParamsDTO;
import com.dbj.classpal.books.common.dto.ebooks.ShareUrlResultDTO;
import com.dbj.classpal.books.common.enums.FileStatusEnum;
import com.dbj.classpal.books.common.enums.PdfTaskStatusEnum;
import com.dbj.classpal.books.common.enums.ResourceTypeEnum;
import com.dbj.classpal.books.service.biz.config.BasicConfigBiz;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookPdfTaskBiz;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookResourceBiz;
import com.dbj.classpal.books.service.biz.ebooks.IAppEbooksConfigCategoryBiz;
import com.dbj.classpal.books.service.biz.ebooks.IAppEbooksConfigWatermarkTemplateBiz;
import com.dbj.classpal.books.service.biz.shorts.IShortUrlInfoBiz;
import com.dbj.classpal.books.service.config.ConfigCacheManager;
import com.dbj.classpal.books.service.entity.config.BasicConfig;
import com.dbj.classpal.books.service.entity.product.AppEBookPdfTask;
import com.dbj.classpal.books.service.entity.ebooks.AppEbooksConfigCategory;
import com.dbj.classpal.books.service.entity.ebooks.AppEbooksConfigWatermarkTemplate;
import com.dbj.classpal.books.service.entity.material.AppMaterial;
import com.dbj.classpal.books.service.entity.ebooks.AppEBook;
import com.dbj.classpal.books.service.entity.product.AppEBookResource;
import com.dbj.classpal.books.service.entity.ebooks.AppEBookshelf;
import com.dbj.classpal.books.service.entity.ebooks.AppEBookstore;
import com.dbj.classpal.books.service.mapper.ebooks.AppEBookMapper;
import com.dbj.classpal.books.service.mapper.ebooks.AppEBookshelfMapper;
import com.dbj.classpal.books.service.mapper.ebooks.AppEBookstoreMapper;
import com.dbj.classpal.books.service.mapper.material.AppMaterialMapper;
import com.dbj.classpal.books.service.mq.listener.ebooks.AppEBookPdfResultListener;
import com.dbj.classpal.books.service.util.ShareUrlBuilderHelper;
import com.dbj.classpal.books.service.util.ShortUrlGenerator;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import com.dbj.classpal.framework.oss.utils.OssUtil;
import com.dbj.classpal.framework.pdf.enums.PdfWaterMarkBusinessTypeEnum;
import com.dbj.classpal.framework.redisson.config.RedissonRedisUtils;
import com.dbj.classpal.framework.utils.util.QRCodeGenerator;
import com.dbj.classpal.framework.utils.util.QRCodeHelper;
import com.dbj.classpal.framework.utils.util.enums.ShareUrlTypeEnum;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import java.awt.image.BufferedImage;
import java.io.InputStream;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.Constant.DEFAULT_CACHE_EXPIRE;

@Component
@Slf4j
public class AppEBookHelper {

    // 静态常量定义
    private static final String CLEAR_ERROR_MSG = "";
    private static final String SHARE_CLEAR_MSG = "";

    @Resource
    private ShortUrlGenerator shortUrlGenerator;

    @Resource
    private IShortUrlInfoBiz shortUrlInfoBiz;

    @Resource
    private AppMaterialMapper materialMapper;

    @Resource
    private BookCodeUrlConfig bookCodeUrlConfig;

    @Resource
    private IAppEbooksConfigCategoryBiz categoryBiz;

    @Resource
    private BasicConfigBiz configBiz;

    @Resource
    private AppEBookPdfResultListener appEBookPdfResultListener;

    @Resource
    private OssUtil ossUtil;

    @Resource
    private ShareUrlBuilderHelper shareUrlBuilderHelper;

    @Resource
    private IAppEBookPdfTaskBiz pdfTaskBiz;

    @Resource
    private IAppEbooksConfigWatermarkTemplateBiz watermarkTemplateBiz;

    @Resource
    private AppEBookshelfMapper eBookshelfMapper;

    @Resource
    private AppEBookstoreMapper eBookstoreMapper;

    @Resource
    private AppEBookMapper eBookMapper;

    @Resource
    private IAppEBookResourceBiz eBookResourceBiz;

    @Resource
    private RedissonRedisUtils redisUtils;

    @Resource
    private ConfigCacheManager configCacheManager;


    public String parsePDF(Integer businessId,String businessKey, Integer waterMarkBusinessType, AppEbooksConfigWatermarkTemplate template, String fileUrl, String fileName) {
        try {
            WaterMarkEntity result = appEBookPdfResultListener.syncProcessPdf(businessId,businessKey, waterMarkBusinessType, template, fileUrl, fileName, 30);

            if (result.getIsError() == null || !result.getIsError()) {
                log.info("PDF处理成功，bookId: {}", businessId);
                String coverUrl = null;
                List<String> waterMarkList = result.getWaterMarkOssPathList();
                if (CollectionUtils.isNotEmpty(waterMarkList) && !waterMarkList.isEmpty()) {
                    coverUrl = waterMarkList.get(0);
                    log.info("设置单书封面，ID:{}, 封面URL:{}", businessId, coverUrl);
                }
                appEBookPdfResultListener.saveProcessResults(result,coverUrl);
                return coverUrl;
            } else {
                log.error("PDF处理失败，bookId: {}, 错误信息: {}", businessId, result.getErrorMsg());
            }
        } catch (TimeoutException e) {
            log.error("PDF处理超时，bookId: {}", businessId, e);
        } catch (Exception e) {
            log.error("PDF处理异常，bookId: {}", businessId, e);
        }
        return Strings.EMPTY;
    }

    /**
     * 异步获取封面URL
     *    * @param businessId          业务ID
     * @param businessKey            业务key
     * @param waterMarkBusinessType 水印业务类型
     * @param template              水印模板
     * @param fileUrl               文件URL
     * @param fileName              文件名
     * @return 任务ID，用于后续查询处理结果
     */
    public String parsePDFAsync(Integer businessId,String businessKey, Integer waterMarkBusinessType,
                                AppEbooksConfigWatermarkTemplate template,
                                String fileUrl,
                                String fileName) throws BusinessException {
        try {
            if(StringUtils.isEmpty(businessKey) ){
                log.warn("businessKey为空，无法创建PDF处理任务");
                return Strings.EMPTY;
            }
            String taskId = pdfTaskBiz.createTask(businessId,businessKey , waterMarkBusinessType, fileUrl, fileName);

            appEBookPdfResultListener.asyncProcessPdf(taskId,businessId, businessKey, waterMarkBusinessType, template, fileUrl, fileName);

            log.info("异步PDF处理任务已创建，taskId: {}, businessId: {}", taskId, businessId);
            return taskId;

        } catch (Exception e) {
            log.error("创建异步PDF处理任务失败，businessId: {}", businessId, e);
            throw new BusinessException("创建PDF处理任务失败：" + e.getMessage());
        }
    }

    /**
     * 查询PDF处理任务状态
     *
     * @param taskId 任务ID
     * @return 任务信息
     */
    public AppEBookPdfTask getPdfTaskStatus(String taskId) throws BusinessException {
        return pdfTaskBiz.getTaskByTaskId(taskId);
    }

    /**
     * 获取水印模板
     *
     * @param templateId 模板ID
     * @return 水印模板
     */
    public AppEbooksConfigWatermarkTemplate getWatermarkTemplate(Integer templateId) throws BusinessException {
        if (templateId == null) {
            return null;
        }

        AppEbooksConfigWatermarkTemplate template = watermarkTemplateBiz.getById(templateId);
        if (template == null) {
            throw new BusinessException("水印模板不存在，ID：" + templateId);
        }

        return template;
    }


    /**
     * 转换适用年级名称（使用全量缓存优化）
     *
     * @param applicableGrades 逗号分隔的年级ID字符串
     * @return 逗号分隔的年级名称字符串
     */
    public String convertApplicableGradesNames(String applicableGrades) {
        if (StringUtils.isBlank(applicableGrades)) {
            return Strings.EMPTY;
        }

        try {
            List<Integer> gradeIds = Arrays.stream(applicableGrades.split(","))
                    .filter(StringUtils::isNotBlank)
                    .map(String::trim)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());

            if (gradeIds.isEmpty()) {
                return Strings.EMPTY;
            }

            Map<Integer, String> allConfigMap = getAllConfigNamesMap();

            List<GradeInfo> gradeInfos = new ArrayList<>();
            for (Integer gradeId : gradeIds) {
                String gradeName = allConfigMap.get(gradeId);
                if (gradeName != null && !gradeName.isEmpty()) {
                    gradeInfos.add(new GradeInfo(gradeId, gradeName));
                }
            }

            if (gradeInfos.isEmpty()) {
                return Strings.EMPTY;
            }

            if (gradeInfos.size() > 1) {
                List<BasicConfig> configGrades = configBiz.getBaseMapper().selectByIds(gradeIds);
                Map<Integer, Integer> sortMap = configGrades.stream()
                        .collect(Collectors.toMap(BasicConfig::getId,
                                config -> config.getSortNum() != null ? config.getSortNum() : 0));

                gradeInfos.sort((a, b) -> {
                    Integer sortA = sortMap.getOrDefault(a.getId(), 0);
                    Integer sortB = sortMap.getOrDefault(b.getId(), 0);
                    return sortB.compareTo(sortA); // 降序排列
                });
            }

            return gradeInfos.stream()
                    .map(GradeInfo::getName)
                    .collect(Collectors.joining(","));

        } catch (NumberFormatException e) {
            log.warn("解析年级ID列表失败: {}", applicableGrades, e);
            return Strings.EMPTY;
        } catch (Exception e) {
            log.error("转换年级名称异常: {}", applicableGrades, e);
            return convertApplicableGradesNamesLegacy(applicableGrades);
        }
    }

    /**
     * 年级信息内部类
     */
    @Getter
    private static class GradeInfo {
        private final Integer id;
        private final String name;

        public GradeInfo(Integer id, String name) {
            this.id = id;
            this.name = name;
        }

    }

    /**
     * 降级方法：原始的年级名称转换逻辑
     */
    private String convertApplicableGradesNamesLegacy(String applicableGrades) {
        String gradeNames = Strings.EMPTY;
        if (StringUtils.isNotBlank(applicableGrades)) {
            try {
                List<Integer> gradeIds = Arrays.stream(applicableGrades.split(","))
                        .filter(StringUtils::isNotBlank)
                        .map(String::trim)
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());

                if (!gradeIds.isEmpty()) {
                    List<BasicConfig> configGrades = configBiz.getBaseMapper().selectByIds(gradeIds);
                    if (!CollectionUtils.isEmpty(configGrades)) {
                        configGrades.sort(Comparator.comparing(BasicConfig::getSortNum,
                                Comparator.nullsLast(Comparator.reverseOrder())));
                        gradeNames = configGrades.stream()
                                .map(BasicConfig::getName)
                                .filter(Objects::nonNull)
                                .collect(Collectors.joining(","));
                    }
                }
            } catch (NumberFormatException e) {
                log.warn("解析年级ID列表失败: {}", applicableGrades, e);
            }
        }
        return gradeNames;
    }


    /**
     * 转换分类名称（使用全量缓存优化）
     *
     * @param categoryIdList 逗号分隔的分类ID字符串
     * @return 逗号分隔的分类名称字符串
     */
    public String convertCategoryNames(String categoryIdList) {
        if (StringUtils.isBlank(categoryIdList)) {
            return Strings.EMPTY;
        }

        try {
            List<Integer> categoryIds = Arrays.stream(categoryIdList.split(","))
                    .filter(StringUtils::isNotBlank)
                    .map(String::trim)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());

            if (categoryIds.isEmpty()) {
                return Strings.EMPTY;
            }

            Map<Integer, String> allConfigMap = getAllConfigNamesMap();

            List<CategoryInfo> categoryInfos = new ArrayList<>();
            for (Integer categoryId : categoryIds) {
                String categoryName = allConfigMap.get(categoryId);
                if (categoryName != null && !categoryName.isEmpty()) {
                    categoryInfos.add(new CategoryInfo(categoryId, categoryName));
                }
            }

            if (categoryInfos.isEmpty()) {
                return Strings.EMPTY;
            }

            if (categoryInfos.size() > 1) {
                List<AppEbooksConfigCategory> configCategories = categoryBiz.getBaseMapper().selectByIds(categoryIds);
                Map<Integer, Integer> sortMap = configCategories.stream()
                        .collect(Collectors.toMap(AppEbooksConfigCategory::getId,
                                category -> category.getSort() != null ? category.getSort() : 0));

                categoryInfos.sort((a, b) -> {
                    Integer sortA = sortMap.getOrDefault(a.getId(), 0);
                    Integer sortB = sortMap.getOrDefault(b.getId(), 0);
                    return sortB.compareTo(sortA); // 降序排列
                });
            }

            return categoryInfos.stream()
                    .map(CategoryInfo::getName)
                    .collect(Collectors.joining(","));

        } catch (NumberFormatException e) {
            log.warn("解析分类ID列表失败: {}", categoryIdList, e);
            return Strings.EMPTY;
        } catch (Exception e) {
            log.error("转换分类名称异常: {}", categoryIdList, e);
            return convertCategoryNamesLegacy(categoryIdList);
        }
    }

    /**
     * 分类信息内部类
     */
    @Getter
    private static class CategoryInfo {
        private final Integer id;
        private final String name;

        public CategoryInfo(Integer id, String name) {
            this.id = id;
            this.name = name;
        }

    }

    /**
     * 降级方法：原始的分类名称转换逻辑
     */
    private String convertCategoryNamesLegacy(String categoryIdList) {
        String categoryNames = Strings.EMPTY;
        if (StringUtils.isNotBlank(categoryIdList)) {
            try {
                List<Integer> categoryIds = Arrays.stream(categoryIdList.split(","))
                        .filter(StringUtils::isNotBlank)
                        .map(String::trim)
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());

                if (!categoryIds.isEmpty()) {
                    List<AppEbooksConfigCategory> configCategories = categoryBiz.getBaseMapper().selectByIds(categoryIds);
                    if (!CollectionUtils.isEmpty(configCategories)) {
                        configCategories.sort(Comparator.comparing(AppEbooksConfigCategory::getSort,
                                Comparator.nullsLast(Comparator.reverseOrder())));
                        categoryNames = configCategories.stream()
                                .map(AppEbooksConfigCategory::getName)
                                .filter(Objects::nonNull)
                                .collect(Collectors.joining(","));
                    }
                }
            } catch (NumberFormatException e) {
                log.warn("解析分类ID列表失败: {}", categoryIdList, e);
            }
        }
        return categoryNames;
    }

    /**
     * 批量转换年级名称（高性能版本）
     * 一次性处理多个年级ID字符串，减少数据库查询次数
     *
     * @param applicableGradesList 年级ID字符串列表
     * @return 年级ID字符串到名称字符串的映射
     */
    public Map<String, String> batchConvertApplicableGradesNames(List<String> applicableGradesList) {
        Map<String, String> result = new HashMap<>();

        if (CollectionUtils.isEmpty(applicableGradesList)) {
            return result;
        }

        try {
            Set<Integer> allGradeIds = new HashSet<>();
            Map<String, List<Integer>> gradeListMap = new HashMap<>();

            for (String applicableGrades : applicableGradesList) {
                if (StringUtils.isNotBlank(applicableGrades)) {
                    List<Integer> gradeIds = Arrays.stream(applicableGrades.split(","))
                            .filter(StringUtils::isNotBlank)
                            .map(String::trim)
                            .map(Integer::parseInt)
                            .collect(Collectors.toList());

                    if (!gradeIds.isEmpty()) {
                        allGradeIds.addAll(gradeIds);
                        gradeListMap.put(applicableGrades, gradeIds);
                    }
                }
            }

            if (allGradeIds.isEmpty()) {
                return result;
            }

            Map<Integer, String> allConfigMap = getAllConfigNamesMap();

            List<BasicConfig> configGrades = configBiz.getBaseMapper().selectByIds(allGradeIds);
            Map<Integer, Integer> sortMap = configGrades.stream()
                    .collect(Collectors.toMap(BasicConfig::getId,
                            config -> config.getSortNum() != null ? config.getSortNum() : 0));

            for (Map.Entry<String, List<Integer>> entry : gradeListMap.entrySet()) {
                String originalGrades = entry.getKey();
                List<Integer> gradeIds = entry.getValue();

                List<GradeInfo> gradeInfos = gradeIds.stream()
                        .map(gradeId -> {
                            String gradeName = allConfigMap.get(gradeId);
                            return gradeName != null ? new GradeInfo(gradeId, gradeName) : null;
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                if (!gradeInfos.isEmpty()) {
                    gradeInfos.sort((a, b) -> {
                        Integer sortA = sortMap.getOrDefault(a.getId(), 0);
                        Integer sortB = sortMap.getOrDefault(b.getId(), 0);
                        return sortB.compareTo(sortA); // 降序排列
                    });

                    String gradeNames = gradeInfos.stream()
                            .map(GradeInfo::getName)
                            .collect(Collectors.joining(","));

                    result.put(originalGrades, gradeNames);
                }
            }

        } catch (Exception e) {
            log.error("批量转换年级名称异常", e);
            for (String applicableGrades : applicableGradesList) {
                result.put(applicableGrades, convertApplicableGradesNames(applicableGrades));
            }
        }

        return result;
    }

    /**
     * 批量转换分类名称（高性能版本）
     * 一次性处理多个分类ID字符串，减少数据库查询次数
     *
     * @param categoryIdLists 分类ID字符串列表
     * @return 分类ID字符串到名称字符串的映射
     */
    public Map<String, String> batchConvertCategoryNames(List<String> categoryIdLists) {
        Map<String, String> result = new HashMap<>();

        if (CollectionUtils.isEmpty(categoryIdLists)) {
            return result;
        }

        try {
            Set<Integer> allCategoryIds = new HashSet<>();
            Map<String, List<Integer>> categoryListMap = new HashMap<>();

            for (String categoryIdList : categoryIdLists) {
                if (StringUtils.isNotBlank(categoryIdList)) {
                    List<Integer> categoryIds = Arrays.stream(categoryIdList.split(","))
                            .filter(StringUtils::isNotBlank)
                            .map(String::trim)
                            .map(Integer::parseInt)
                            .collect(Collectors.toList());

                    if (!categoryIds.isEmpty()) {
                        allCategoryIds.addAll(categoryIds);
                        categoryListMap.put(categoryIdList, categoryIds);
                    }
                }
            }

            if (allCategoryIds.isEmpty()) {
                return result;
            }

            Map<Integer, String> allConfigMap = getAllConfigNamesMap();

            List<AppEbooksConfigCategory> configCategories = categoryBiz.getBaseMapper().selectByIds(allCategoryIds);
            Map<Integer, Integer> sortMap = configCategories.stream()
                    .collect(Collectors.toMap(AppEbooksConfigCategory::getId,
                            category -> category.getSort() != null ? category.getSort() : 0));

            for (Map.Entry<String, List<Integer>> entry : categoryListMap.entrySet()) {
                String originalCategories = entry.getKey();
                List<Integer> categoryIds = entry.getValue();

                List<CategoryInfo> categoryInfos = categoryIds.stream()
                        .map(categoryId -> {
                            String categoryName = allConfigMap.get(categoryId);
                            return categoryName != null ? new CategoryInfo(categoryId, categoryName) : null;
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                if (!categoryInfos.isEmpty()) {
                    categoryInfos.sort((a, b) -> {
                        Integer sortA = sortMap.getOrDefault(a.getId(), 0);
                        Integer sortB = sortMap.getOrDefault(b.getId(), 0);
                        return sortB.compareTo(sortA);
                    });

                    String categoryNames = categoryInfos.stream()
                            .map(CategoryInfo::getName)
                            .collect(Collectors.joining(","));

                    result.put(originalCategories, categoryNames);
                }
            }

        } catch (Exception e) {
            log.error("批量转换分类名称异常", e);
            for (String categoryIdList : categoryIdLists) {
                result.put(categoryIdList, convertCategoryNames(categoryIdList));
            }
        }

        return result;
    }

    /**
     * 转换配置ID为配置名称（带缓存优化）
     * 缓存时间：7天
     */
    public String convertConfigName(Integer configId) {
        if (configId == null) {
            return Strings.EMPTY;
        }

        String cacheKey = MessageFormat.format(RedisKeyConstants.DBJ_CLASSPAL_BASIC_CONFIG_NAME_CACHE_KEY, configId.toString());

        try {
            if (redisUtils.hasKey(cacheKey)) {
                String cachedName = redisUtils.getValue(cacheKey);
                if (cachedName != null) {
                    log.debug("从缓存获取配置名称，configId: {}, name: {}", configId, cachedName);
                    return cachedName;
                }
            }

            BasicConfigDTO config = configBiz.detail(configId);
            String configName = (config != null && config.getName() != null) ? config.getName() : Strings.EMPTY;

            redisUtils.setValue(cacheKey, configName, DEFAULT_CACHE_EXPIRE, TimeUnit.DAYS);
            log.debug("配置名称已缓存，configId: {}, name: {}", configId, configName);

            return configName;

        } catch (Exception e) {
            log.error("获取配置名称异常，configId: {}", configId, e);
            // 异常情况下直接查询数据库
            BasicConfigDTO config = configBiz.detail(configId);
            return (config != null && config.getName() != null) ? config.getName() : Strings.EMPTY;
        }
    }

    /**
     * 批量转换配置名称（全量缓存优化）
     * 同时查询 basic_config 和 app_ebooks_config_category 两个表
     *
     * @param configIds 配置ID集合
     * @return 配置ID到名称的映射
     */
    public Map<Integer, String> batchConvertConfigNames(Set<Integer> configIds) {
        if (configIds == null || configIds.isEmpty()) {
            return new HashMap<>();
        }

        Map<Integer, String> result = new HashMap<>();

        try {
            Map<Integer, String> allConfigMap = getAllConfigNamesMap();

            for (Integer configId : configIds) {
                String configName = allConfigMap.get(configId);
                if (configName != null && !configName.isEmpty()) {
                    result.put(configId, configName);
                }
            }

            log.debug("批量配置名称查询完成 - 请求数量: {}, 成功匹配: {}", configIds.size(), result.size());

        } catch (Exception e) {
            log.error("批量配置名称查询异常", e);
            for (Integer configId : configIds) {
                String name = convertConfigName(configId);
                if (name != null && !name.isEmpty()) {
                    result.put(configId, name);
                }
            }
        }

        return result;
    }

    /**
     * 获取所有配置名称的映射（包含 basic_config 和 app_ebooks_config_category）
     * 委托给统一的缓存管理器
     *
     * @return 配置ID到名称的完整映射
     */
    private Map<Integer, String> getAllConfigNamesMap() {
        return configCacheManager.getAllConfigNamesMap();
    }

    /**
     * 构建分享URL (兼容原有方法)
     */
    public String builderShareUrl(String businessType, Integer businessId) throws BusinessException {
        ShareUrlParamsDTO params = new ShareUrlParamsDTO();
        params.setBusinessType(businessType);
        params.setBusinessId(businessId);
        return builderShareUrl(params);
    }

    /**
     * 构建分享URL (新方法，支持多种参数结构)
     */
    public String builderShareUrl(ShareUrlParamsDTO params) throws BusinessException {
        String shortUrl = bookCodeUrlConfig.getShortUrl();
        String shortCode = shortUrlGenerator.generate();
        String shareUrl = shortUrl + shortCode;

        // 使用新的URL构建器
        String longUrl = shareUrlBuilderHelper.buildH5ShareUrl(bookCodeUrlConfig, params);
        shortUrlInfoBiz.saveShortUrlInfo(shortCode, longUrl);
        return shareUrl;
    }

    public String builderShareQrCode(String fileName, String shareUrl) throws Exception {
        BufferedImage qrImage = QRCodeGenerator.generateQRCodeImage(shareUrl);
        InputStream inputStream = QRCodeHelper.imageToInputStream(qrImage);
        return ossUtil.uploadFileWithRetuenPath(QRCodeHelper.generateOssPath(fileName), inputStream);
    }

    public List<Integer> commaSeparatedStringToIntegerList(String commaSeparatedStr) {
        if (StringUtils.isBlank(commaSeparatedStr)) {
            return Collections.emptyList();
        }
        return Arrays.stream(commaSeparatedStr.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .map(idStr -> {
                    try {
                        return Integer.parseInt(idStr);
                    } catch (NumberFormatException e) {
                        log.warn("警告：无效的整数格式: {}", idStr);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 根据业务ID查找失败的任务
     *
     * @param businessId 业务ID
     * @return 失败的任务列表
     */
    public List<AppEBookPdfTask> getFailedTasksByBusinessId(Integer businessId) {
        if (businessId == null) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<AppEBookPdfTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppEBookPdfTask::getBusinessId, businessId)
                .eq(AppEBookPdfTask::getStatus, PdfTaskStatusEnum.FAILED.getCode())
                .orderByDesc(AppEBookPdfTask::getCreateTime);

        return pdfTaskBiz.list(queryWrapper);
    }

    /**
     * 标记任务为已清理
     *
     * @param taskId 任务ID
     */
    public void markTaskAsCleaned(String taskId) {
        try {
            AppEBookPdfTask task = pdfTaskBiz.getTaskByTaskId(taskId);
            task.setErrorMsg("[已清理] " + (task.getErrorMsg() != null ? task.getErrorMsg() : ""));
            pdfTaskBiz.updateById(task);

            log.info("任务已标记为清理，taskId：{}", taskId);
        } catch (Exception e) {
            log.warn("标记任务清理失败，taskId：{}，错误：{}", taskId, e.getMessage());
        }
    }

    /**
     * 获取单书分享信息
     */
    public ShareUrlResultDTO getBookShareInfo(GetShareInfoBO request) throws BusinessException {
        Integer bookId = request.getBusinessId();

        AppEBook eBook = eBookMapper.selectById(bookId);
        if (eBook == null) {
            throw new BusinessException("单书不存在，ID：" + bookId);
        }

        if (!request.getForceRegenerate() &&
                eBook.getShareUrl() != null && !eBook.getShareUrl().isEmpty() &&
                eBook.getShareQrCode() != null && !eBook.getShareQrCode().isEmpty()) {

            return buildShareUrlResult(ShareUrlTypeEnum.BOOK.getCode(), bookId, eBook.getBookTitle(),
                    eBook.getShareUrl(), eBook.getShareQrCode(), eBook.getShareStatus(), eBook.getShareTime());
        }

        try {
            String shareUrl = builderShareUrl(ShareUrlTypeEnum.BOOK.getCode(), bookId);
            String shareQrCode = builderShareQrCode(eBook.getBookTitle(), shareUrl);

            eBook.setShareUrl(shareUrl);
            eBook.setShareQrCode(shareQrCode);
            eBook.setShareStatus(YesOrNoEnum.YES.getCode());
            eBook.setShareTime(LocalDateTime.now());
            eBookMapper.updateById(eBook);

            log.info("生成单书分享信息成功，bookId：{}，shareUrl：{}", bookId, shareUrl);

            return buildShareUrlResult(ShareUrlTypeEnum.BOOK.getCode(), bookId, eBook.getBookTitle(),
                    shareUrl, shareQrCode, YesOrNoEnum.YES.getCode(), eBook.getShareTime());

        } catch (Exception e) {
            log.error("生成单书分享信息失败，bookId：{}", bookId, e);
            throw new BusinessException("生成分享信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取书架分享信息
     */
    public ShareUrlResultDTO getBookshelfShareInfo(GetShareInfoBO request) throws BusinessException {
        Integer shelfId = request.getBusinessId();

        AppEBookshelf bookshelf = eBookshelfMapper.selectById(shelfId);
        if (bookshelf == null) {
            throw new BusinessException("书架不存在，ID：" + shelfId);
        }

        if (!request.getForceRegenerate() &&
                bookshelf.getShareUrl() != null && !bookshelf.getShareUrl().isEmpty() &&
                bookshelf.getShareQrCode() != null && !bookshelf.getShareQrCode().isEmpty()) {

            return buildShareUrlResult(ShareUrlTypeEnum.BOOKSHELF.getCode(), shelfId, bookshelf.getShelfTitle(),
                    bookshelf.getShareUrl(), bookshelf.getShareQrCode(), bookshelf.getShareStatus(), bookshelf.getShareTime());
        }

        try {
            String shareUrl = builderShareUrl(ShareUrlTypeEnum.BOOKSHELF.getCode(), shelfId);
            String shareQrCode = builderShareQrCode(bookshelf.getShelfTitle(), shareUrl);

            // 更新数据库
            bookshelf.setShareUrl(shareUrl);
            bookshelf.setShareQrCode(shareQrCode);
            bookshelf.setShareStatus(YesOrNoEnum.YES.getCode());
            bookshelf.setShareTime(LocalDateTime.now());
            eBookshelfMapper.updateById(bookshelf);

            log.info("生成书架分享信息成功，shelfId：{}，shareUrl：{}", shelfId, shareUrl);

            return buildShareUrlResult(ShareUrlTypeEnum.BOOKSHELF.getCode(), shelfId, bookshelf.getShelfTitle(),
                    shareUrl, shareQrCode, YesOrNoEnum.YES.getCode(), bookshelf.getShareTime());

        } catch (Exception e) {
            log.error("生成书架分享信息失败，shelfId：{}", shelfId, e);
            throw new BusinessException("生成分享信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取书城分享信息
     */
    public ShareUrlResultDTO getBookstoreShareInfo(GetShareInfoBO request) throws BusinessException {
        Integer storeId = request.getBusinessId();

        AppEBookstore bookstore = eBookstoreMapper.selectById(storeId);
        if (bookstore == null) {
            throw new BusinessException("书城不存在，ID：" + storeId);
        }

        if (!request.getForceRegenerate() &&
                bookstore.getShareUrl() != null && !bookstore.getShareUrl().isEmpty() &&
                bookstore.getShareQrCode() != null && !bookstore.getShareQrCode().isEmpty()) {

            return buildShareUrlResult(ShareUrlTypeEnum.BOOKSTORE.getCode(), storeId, bookstore.getStoreTitle(),
                    bookstore.getShareUrl(), bookstore.getShareQrCode(), bookstore.getShareStatus(), bookstore.getShareTime());
        }

        try {
            String shareUrl = builderShareUrl(ShareUrlTypeEnum.BOOKSTORE.getCode(), storeId);
            String shareQrCode = builderShareQrCode(bookstore.getStoreTitle(), shareUrl);

            // 更新数据库
            bookstore.setShareUrl(shareUrl);
            bookstore.setShareQrCode(shareQrCode);
            bookstore.setShareStatus(YesOrNoEnum.YES.getCode());
            bookstore.setShareTime(LocalDateTime.now());
            eBookstoreMapper.updateById(bookstore);

            log.info("生成书城分享信息成功，storeId：{}，shareUrl：{}", storeId, shareUrl);

            return buildShareUrlResult(ShareUrlTypeEnum.BOOKSTORE.getCode(), storeId, bookstore.getStoreTitle(),
                    shareUrl, shareQrCode, YesOrNoEnum.YES.getCode(), bookstore.getShareTime());

        } catch (Exception e) {
            log.error("生成书城分享信息失败，storeId：{}", storeId, e);
            throw new BusinessException("生成分享信息失败：" + e.getMessage());
        }
    }

    /**
     * 删除单书分享链接
     */
    public void removeBookShareUrl(RemoveShareUrlBO request) throws BusinessException {
        Integer bookId = request.getBusinessId();

        AppEBook eBook = eBookMapper.selectById(bookId);
        if (eBook == null) {
            throw new BusinessException("单书不存在，ID：" + bookId);
        }

        eBook.setShareUrl(SHARE_CLEAR_MSG);
        eBook.setShareQrCode(SHARE_CLEAR_MSG);
        eBook.setShareStatus(YesOrNoEnum.NO.getCode());
        eBook.setShareTime(null);
        eBookMapper.updateById(eBook);

        log.info("删除单书分享链接成功，bookId：{}", bookId);
    }

    /**
     * 删除书架分享链接
     */
    public void removeBookshelfShareUrl(RemoveShareUrlBO request) throws BusinessException {
        Integer shelfId = request.getBusinessId();

        AppEBookshelf bookshelf = eBookshelfMapper.selectById(shelfId);
        if (bookshelf == null) {
            throw new BusinessException("书架不存在，ID：" + shelfId);
        }

        bookshelf.setShareUrl(SHARE_CLEAR_MSG);
        bookshelf.setShareQrCode(SHARE_CLEAR_MSG);
        bookshelf.setShareStatus(YesOrNoEnum.NO.getCode());
        bookshelf.setShareTime(null);
        eBookshelfMapper.updateById(bookshelf);

        log.info("删除书架分享链接成功，shelfId：{}", shelfId);
    }

    /**
     * 删除书城分享链接
     */
    public void removeBookstoreShareUrl(RemoveShareUrlBO request) throws BusinessException {
        Integer storeId = request.getBusinessId();

        AppEBookstore bookstore = eBookstoreMapper.selectById(storeId);
        if (bookstore == null) {
            throw new BusinessException("书城不存在，ID：" + storeId);
        }

        // 清空分享信息
        bookstore.setShareUrl(SHARE_CLEAR_MSG);
        bookstore.setShareQrCode(SHARE_CLEAR_MSG);
        bookstore.setShareStatus(YesOrNoEnum.NO.getCode());
        bookstore.setShareTime(null);
        eBookstoreMapper.updateById(bookstore);

        log.info("删除书城分享链接成功，storeId：{}", storeId);
    }

    /**
     * 构建分享链接结果DTO
     */
    private ShareUrlResultDTO buildShareUrlResult(String businessType, Integer businessId, String businessName,
                                                  String shareUrl, String shareQrCode, Integer shareStatus, LocalDateTime shareTime) {
        ShareUrlResultDTO result = new ShareUrlResultDTO();
        result.setBusinessType(businessType);
        result.setBusinessId(businessId);
        result.setBusinessName(businessName);
        result.setShareUrl(shareUrl);
        result.setShareQrCode(shareQrCode);
        result.setShareStatus(shareStatus);

        if (shareTime != null) {
            result.setShareTime(shareTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }

        return result;
    }


    public boolean dealFileWithMd5(AppEBook eBook, String fileUrl, String fileName, String fileMd5) {
        Integer watermarkBusinessType = getWatermarkBusinessType(eBook.getWatermarkId());

        String coverUrl = this.getCoverUrlByMd5(fileMd5, watermarkBusinessType);

        if (StringUtils.isNotEmpty(coverUrl)) {
            eBook.setFileUrl(fileUrl);
            eBook.setFileName(fileName);
            eBook.setFileMd5(fileMd5);
            eBook.setCoverUrl(coverUrl);
            eBook.setFileStatus(FileStatusEnum.COMPLETED.getCode());
            eBook.setFileErrMsg(CLEAR_ERROR_MSG);

            // 复制其他资源（切图等）
            //copyExistingResources(eBook, fileMd5, watermarkBusinessType);

            log.info("复用已存在的文件处理结果，bookId: {}, fileMd5: {}, watermarkType: {}, coverUrl: {}",
                eBook.getId(), fileMd5, watermarkBusinessType, coverUrl);

            return true;
        }

        return false;
    }
    /**
     * 根据文件MD5和水印类型获取封面URL
     *
     * @param fileMd5 文件MD5
     * @param watermarkBusinessType 水印业务类型
     * @return 封面URL，如果不存在则返回null
     */
    public String getCoverUrlByMd5(String fileMd5, Integer watermarkBusinessType) {
        if (StringUtils.isBlank(fileMd5) || watermarkBusinessType == null) {
            return null;
        }
        try {
            LambdaQueryWrapper<AppEBookResource> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AppEBookResource::getResourceKey, fileMd5)
                    .eq(AppEBookResource::getBusinessType, watermarkBusinessType)
                    .eq(AppEBookResource::getResourceType, ResourceTypeEnum.COVER_IMAGE.getCode())
                    .last("limit 1");

            AppEBookResource resource = eBookResourceBiz.getOne(queryWrapper);
            if (resource != null) {
                log.info("找到已存在的切图资源，fileMd5: {}, watermarkType: {}, coverUrl: {}",
                    fileMd5, watermarkBusinessType, resource.getResourceUrl());
                return resource.getResourceUrl();
            }
        } catch (Exception e) {
            log.error("根据文件MD5和水印类型获取封面异常，fileMd5: {}, watermarkType: {}",
                fileMd5, watermarkBusinessType, e);
        }
        return null;
    }


    /**
     * 获取单书的水印业务类型
     */
    public Integer getWatermarkBusinessType(Integer watermarkId) {
        if(Objects.nonNull(watermarkId) && !watermarkId.equals(YesOrNoEnum.NO.getCode())) {
            return PdfWaterMarkBusinessTypeEnum.ONLY_ADD_WATER_MARK_AND_SLICING_IMAGE.getCode();
        } else {
            return PdfWaterMarkBusinessTypeEnum.ONLY_SLICING_IMAGE.getCode();
        }
    }

    /**
     * 复制已存在的资源到当前单书
     */
    private void copyExistingResources(AppEBook eBook, String fileMd5, Integer watermarkBusinessType) {
        try {
            // 查询已存在的所有资源
            LambdaQueryWrapper<AppEBookResource> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AppEBookResource::getResourceKey, fileMd5)
                    .eq(AppEBookResource::getBusinessType, watermarkBusinessType);

            List<AppEBookResource> existingResources = eBookResourceBiz.list(queryWrapper);

            if (CollectionUtils.isNotEmpty(existingResources)) {
                // 为当前单书创建资源记录
                List<AppEBookResource> newResources = new ArrayList<>();

                for (AppEBookResource existingResource : existingResources) {
                    AppEBookResource newResource = new AppEBookResource();
                    BeanUtil.copyProperties(existingResource, newResource);
                    newResource.setId(null);
                    newResource.setResourceId(eBook.getId());
                    newResource.setResourceKey(fileMd5);
                    newResource.setCreateTime(LocalDateTime.now());
                    newResource.setUpdateTime(LocalDateTime.now());

                    newResources.add(newResource);
                }

                // 批量保存资源记录
                eBookResourceBiz.saveBatch(newResources);

                log.info("复制资源记录成功，bookId: {}, 资源数量: {}", eBook.getId(), newResources.size());
            }

        } catch (Exception e) {
            log.error("复制已存在资源异常，bookId: {}, fileMd5: {}, watermarkType: {}",
                eBook.getId(), fileMd5, watermarkBusinessType, e);
        }
    }

    public void resolveCodesToNames(AppEBook eBook, AppEBookDTO dto) {
        dto.setCategoryIds(commaSeparatedStringToIntegerList(eBook.getCategoryIds()));
        dto.setApplicableGrades(commaSeparatedStringToIntegerList(eBook.getApplicableGrades()));
        dto.setApplicableGradeNames(convertApplicableGradesNames(eBook.getApplicableGrades()));
        dto.setCategoryNames(convertCategoryNames(eBook.getCategoryIds()));
        dto.setSubjectName(convertConfigName(eBook.getSubjectId()));
        dto.setTextbookVersionName(convertConfigName(eBook.getTextbookVersionId()));
        dto.setStageName(convertConfigName(eBook.getStageId()));
    }

    public Map<Integer,AppMaterial> convertMediaName(List<Integer> appMediaIds){
        List<AppMaterial> appMaterials = materialMapper.selectByIds(appMediaIds);
        if(CollUtil.isNotEmpty(appMaterials)){
            return appMaterials.stream().collect(Collectors.toMap(AppMaterial::getId, Function.identity()));
        }
        return Map.of();
    }
}
