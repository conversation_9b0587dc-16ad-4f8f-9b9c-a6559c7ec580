package com.dbj.classpal.books.service.entity.advertisement;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 广告条件层级表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("advertisement_level_condition")
@Tag(name="AdvertisementLevelCondition对象", description="广告条件层级表")
public class AdvertisementLevelCondition extends BizEntity implements Serializable {


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "广告id")
    private Integer advertisementId;

    @Schema(description = "父级id")
    private Integer parentId;

    @Schema(description = "逻辑类型 1-且 2-或 3-非")
    private Integer logicType;

    @Schema(description = "层级")
    private Integer level;

    @Schema(description = "广告条件类型字典项")
    private String conditionType;

    @Schema(description = "广告条件类型字典名")
    private String name;
}
