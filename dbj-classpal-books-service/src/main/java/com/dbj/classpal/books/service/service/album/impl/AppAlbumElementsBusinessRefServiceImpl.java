package com.dbj.classpal.books.service.service.album.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsBusinessRefQueryBO;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsBusinessRefQueryCommonBO;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsBusinessRefSaveBO;
import com.dbj.classpal.books.common.bo.common.CommonIdBO;
import com.dbj.classpal.books.common.bo.material.AppMaterialBusinessRefQueryCommonBO;
import com.dbj.classpal.books.common.dto.album.AppAlbumElementsBusinessRefMaterialQueryDTO;
import com.dbj.classpal.books.common.dto.album.AppAlbumElementsBusinessRefQueryDTO;
import com.dbj.classpal.books.common.dto.books.BooksRefDirectDTO;
import com.dbj.classpal.books.common.dto.material.AppMaterialBusinessRefDTO;
import com.dbj.classpal.books.common.enums.BusinessTypeEnum;
import com.dbj.classpal.books.common.enums.FileTypeEnum;
import com.dbj.classpal.books.common.enums.MaterialTypeEnum;
import com.dbj.classpal.books.common.enums.strategy.AppAlbumBusinessRefTypeEnum;
import com.dbj.classpal.books.common.enums.strategy.AppMaterialBusinessRefTypeEnum;
import com.dbj.classpal.books.service.biz.album.IAppAlbumElementsBiz;
import com.dbj.classpal.books.service.biz.album.IAppAlbumElementsBusinessRefBiz;
import com.dbj.classpal.books.service.biz.books.IBooksInfoBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInCodesContentsBiz;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBiz;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBusinessRefBiz;
import com.dbj.classpal.books.service.entity.album.AppAlbumElements;
import com.dbj.classpal.books.service.entity.album.AppAlbumElementsBusinessRef;
import com.dbj.classpal.books.service.entity.books.BooksInfo;
import com.dbj.classpal.books.service.entity.books.BooksRankInCodesContents;
import com.dbj.classpal.books.service.factory.CommonBusinessStrategyFactory;
import com.dbj.classpal.books.service.service.album.IAppElementsBusinessRefService;
import com.dbj.classpal.books.service.strategy.business.handler.ICommonBusinessStrategyHandler;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppAlbumElementsBusinessRefServiceImpl
 * Date:     2025-04-15 10:22:36
 * Description: 表名： ,描述： 表
 */
@Service
public class AppAlbumElementsBusinessRefServiceImpl implements IAppElementsBusinessRefService {

    @Resource
    private IAppAlbumElementsBusinessRefBiz biz;
    @Resource
    private IAppAlbumElementsBiz albumBiz;
    @Resource
    private IAppMaterialBusinessRefBiz materialBusinessRefBiz;
    @Resource
    private IAppMaterialBiz materialBiz;
    @Autowired
    private IBooksRankInCodesContentsBiz booksRankInCodesContentsBiz;
    @Resource
    private CommonBusinessStrategyFactory commonBusinessStrategyFactory;
    @Resource
    private IBooksInfoBiz booksInfoBiz;


    @Override
    public Integer checkMenuRefCount(Integer id) {
        return biz.checkMenuRefCount(id);
    }

    @Override
    public List<AppAlbumElementsBusinessRefQueryDTO> getRefBusinessList(AppAlbumElementsBusinessRefQueryCommonBO bo) {
        Map<Integer, List<AppAlbumElementsBusinessRefQueryDTO>> typeMap = biz.getRefBusinessList(bo).stream().collect(Collectors.groupingBy(AppAlbumElementsBusinessRefQueryDTO::getBusinessType));

        for (Map.Entry<Integer, List<AppAlbumElementsBusinessRefQueryDTO>> integerListEntry : typeMap.entrySet()) {
            Integer businessType = integerListEntry.getKey();
            List<AppAlbumElementsBusinessRefQueryDTO> businessRefDTOList = integerListEntry.getValue();
            List<Integer> businessIdList = businessRefDTOList.stream().map(AppAlbumElementsBusinessRefQueryDTO::getBusinessId).collect(Collectors.toList());
            AppAlbumBusinessRefTypeEnum businessRefTypeEnum = AppAlbumBusinessRefTypeEnum.getByCode(businessType);
            if (ObjectUtil.isNotEmpty(businessRefTypeEnum)) {
                ICommonBusinessStrategyHandler strategy = commonBusinessStrategyFactory.getStrategy(businessRefTypeEnum.getStrategy());
                Map<Integer, String> nameMap = strategy.getBusinessName(businessIdList);
                businessRefDTOList.stream().peek(d -> {
                    if (nameMap.containsKey(d.getBusinessId())) {
                        d.setBusinessName(nameMap.get(d.getBusinessId()));
                    }
                    d.setBusinessTypeStr(Objects.requireNonNull(BusinessTypeEnum.getByCode(d.getBusinessType())).getName());
                    d.setAppMaterialTypeStr(Objects.requireNonNull(BusinessTypeEnum.getByCode(d.getAppMaterialType())).getName());
                }).collect(Collectors.toList());
                typeMap.put(businessType, businessRefDTOList);
            }
        }
        return typeMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    @Override
    public AppAlbumElementsBusinessRefMaterialQueryDTO getElementsBusinessRefMaterialRef(AppAlbumElementsBusinessRefQueryBO bo) {
        //查询专辑关联信息
        List<AppAlbumElementsBusinessRef> albumElementsBusinessRefList = biz.lambdaQuery().eq(AppAlbumElementsBusinessRef::getBusinessId, bo.getBusinessId()).eq(AppAlbumElementsBusinessRef::getBusinessType, bo.getBusinessType()).list();
        if (CollectionUtils.isEmpty(albumElementsBusinessRefList)) {
            return null;
        }
        AppAlbumElementsBusinessRef appAlbumElementsBusinessRef = albumElementsBusinessRefList.get(0);
        //查询专辑信息
        AppAlbumElements byId = albumBiz.getById(appAlbumElementsBusinessRef.getAppAlbumId());
        if (byId == null) {
            return null;
        }
        AppAlbumElementsBusinessRefMaterialQueryDTO queryDTO = new AppAlbumElementsBusinessRefMaterialQueryDTO();
        BeanUtil.copyProperties(byId, queryDTO);
        queryDTO.setAppAlbumId(appAlbumElementsBusinessRef.getAppAlbumId());
        queryDTO.setBusinessId(appAlbumElementsBusinessRef.getBusinessId());
        queryDTO.setId(appAlbumElementsBusinessRef.getId());
        //查询资源关联数据
        AppMaterialBusinessRefQueryCommonBO materialBusinessRefQueryCommonBO = new AppMaterialBusinessRefQueryCommonBO();
        materialBusinessRefQueryCommonBO.setBusinessId(appAlbumElementsBusinessRef.getAppAlbumId());

        BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.getByCode(bo.getBusinessType());
        materialBusinessRefQueryCommonBO.setBusinessType(businessTypeEnum.getAlbumType());

        if (bo.getAppMaterialType() != null){
            materialBusinessRefQueryCommonBO.setAppMaterialType(bo.getAppMaterialType());
        }
        List<AppMaterialBusinessRefDTO> appMaterialBusinessRefDTOS = materialBusinessRefBiz.refBusinessList(materialBusinessRefQueryCommonBO).stream().peek(d -> {
            if (d.getAppMaterialType() != null){
                if (d.getAppMaterialType().equals(MaterialTypeEnum.FILE_TYPE_DOC.getCode())){
                    d.setAppMaterialTypeStr(MaterialTypeEnum.getByCode(d.getAppMaterialType()).getDesc()+"("+d.getMaterialExtension()+")");
                    FileTypeEnum fileTypeEnum = FileTypeEnum.getByCode(d.getMaterialExtension());
                    if (fileTypeEnum != null){
                        d.setMaterialIcon(fileTypeEnum.getIcon());
                    }
                }else{
                    d.setAppMaterialTypeStr(MaterialTypeEnum.getByCode(d.getAppMaterialType()).getDesc());
                    d.setMaterialIcon(MaterialTypeEnum.getByCode(d.getAppMaterialType()).getIcon());
                }
            }
            if (d.getBusinessType() != null){
                d.setBusinessTypeStr(BusinessTypeEnum.getByCode(d.getBusinessType()).getType());
            }
        }).collect(Collectors.toList());
        queryDTO.setRefBusinessList(appMaterialBusinessRefDTOS);
        Integer materialDurations = 0;
        Set<Integer> materialIdSet = appMaterialBusinessRefDTOS.stream().map(AppMaterialBusinessRefDTO::getAppMaterialId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(materialIdSet)){
            materialDurations = materialBiz.sumDuration(materialIdSet);
        }
        queryDTO.setMaterialDurations(materialDurations);
        return queryDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdateElementsBusinessRefMaterialRef(AppAlbumElementsBusinessRefSaveBO bo) {
        AppAlbumElementsBusinessRef appAlbumElementsBusinessRef = new AppAlbumElementsBusinessRef();
        BeanUtil.copyProperties(bo, appAlbumElementsBusinessRef);
        List<AppAlbumElementsBusinessRef> albumElementsBusinessRefList = biz.lambdaQuery().eq(AppAlbumElementsBusinessRef::getBusinessId, bo.getBusinessId()).eq(AppAlbumElementsBusinessRef::getBusinessType, bo.getBusinessType()).list();
        if (CollectionUtils.isNotEmpty(albumElementsBusinessRefList)) {
           appAlbumElementsBusinessRef.setId(albumElementsBusinessRefList.get(0).getId());
        }
        return biz.saveOrUpdate(appAlbumElementsBusinessRef);


    }

    @Override
    public BooksRefDirectDTO getAppAlbumElementsBusinessRefBooks(CommonIdBO bo) throws BusinessException {
        //查询内容管理-专辑关联图书管理-专辑数据
        AppAlbumElementsBusinessRef byId = biz.getById(bo.getId());
        if (ObjectUtils.isEmpty(byId)) {
            throw new BusinessException(APP_MATERIAL_REF_NOT_EXIST_FAIL_CODE,APP_MATERIAL_REF_NOT_EXIST_FAIL_MSG);
        }
        //查询图书详情信息
        BooksRankInCodesContents booksRankInCodesContents = booksRankInCodesContentsBiz.getById(byId.getBusinessId());
        if (ObjectUtils.isEmpty(booksRankInCodesContents)) {
            throw new BusinessException(BOOK_INFO_NOT_EXIST_CODE,BOOK_INFO_NOT_EXIST_MSG);
        }

        BooksRefDirectDTO directDTO = new BooksRefDirectDTO();
        directDTO.setBooksId(booksRankInCodesContents.getBooksId());
        directDTO.setRankId(booksRankInCodesContents.getRankId());
        directDTO.setRankClassifyId(booksRankInCodesContents.getRankClassifyId());
        directDTO.setBusinessId(booksRankInCodesContents.getId());
        if (booksRankInCodesContents.getBooksId() != null){
            BooksInfo booksInfo = booksInfoBiz.getById(booksRankInCodesContents.getBooksId());
            directDTO.setBookName(booksInfo.getBookName());
        }

        return directDTO;
    }
}
