package com.dbj.classpal.books.service.biz.ebooks.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.bo.ebooks.*;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookResourceBatchQueryBO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookDTO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookResourceDTO;
import com.dbj.classpal.books.common.dto.ebooks.ShareUrlResultDTO;
import com.dbj.classpal.books.common.enums.FileStatusEnum;
import com.dbj.classpal.books.common.enums.ResourceTypeEnum;
import com.dbj.classpal.books.service.biz.ebooks.*;
import com.dbj.classpal.books.service.entity.ebooks.AppEbooksConfigCategory;
import com.dbj.classpal.books.service.entity.ebooks.AppEbooksConfigWatermarkTemplate;
import com.dbj.classpal.books.service.entity.material.AppMaterial;
import com.dbj.classpal.books.service.entity.product.AppEBook;
import com.dbj.classpal.books.service.entity.product.AppEBookshelfBookRef;
import com.dbj.classpal.books.service.mapper.ebooks.AppEBookMapper;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import com.dbj.classpal.framework.pdf.enums.PdfWaterMarkBusinessTypeEnum;
import com.dbj.classpal.framework.utils.util.enums.ShareUrlTypeEnum;
import jakarta.annotation.Resource;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 单书 业务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Slf4j
@Service
public class AppEBookBizImpl extends ServiceImpl<AppEBookMapper, AppEBook> implements IAppEBookBiz {

    // 静态常量定义
    private static final String BOOK_NOT_EXIST_MSG = "单书不存在";
    private static final String BOOK_ENABLED_DELETE_MSG = "存在已启用的图书，请将图书禁用后重试";
    private static final String BOOK_SHELF_ASSOCIATED_MSG = "该图书已关联书架，请先从书架中移除后再删除";

    @Resource
    private IAppEBookResourceBiz eBookResourceBiz;

    @Resource
    private IAppEbooksConfigWatermarkTemplateBiz watermarkTemplateBiz;

    @Resource
    private IAppEbooksConfigCategoryBiz categoryBiz;

    @Resource
    private AppEBookHelper eBookHelper;

    @Resource
    private IAppEBookshelfBookRefBiz eBookshelfBookRefBiz;


    @Override
    public Page<AppEBookDTO> page(PageInfo<AppEBookQueryBO> pageRequest) throws BusinessException {
        AppEBookQueryBO query = pageRequest.getData();
        List<AppEbooksConfigCategory> categories = List.of();
        if(StringUtils.isNotEmpty(query.getCategoryName())){
            categories = categoryBiz.getBaseMapper().selectList(new LambdaQueryWrapper<AppEbooksConfigCategory>()
                    .like(StringUtils.isNotEmpty(query.getCategoryName()),AppEbooksConfigCategory::getName,query.getCategoryName()));
            if(CollectionUtils.isEmpty(categories)){
                return new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0L);
            }
        }
       
        LambdaQueryWrapper<AppEBook> wrapper = new LambdaQueryWrapper<AppEBook>()
                .like(StringUtil.isNotBlank(query.getBookTitle()), AppEBook::getBookTitle, query.getBookTitle())
                .in(CollectionUtils.isNotEmpty(query.getStageIds()), AppEBook::getStageId, query.getStageIds())
                .in(CollectionUtils.isNotEmpty(query.getSubjectIds()), AppEBook::getSubjectId, query.getSubjectIds())
                .eq(Objects.nonNull(query.getLaunchStatus()), AppEBook::getLaunchStatus, query.getLaunchStatus())
                .eq(Objects.nonNull(query.getAllowDownload()), AppEBook::getAllowDownload, query.getAllowDownload())
                .orderByDesc(AppEBook::getSortNum,AppEBook::getCreateTime);
        List<Integer> queryGrades = query.getApplicableGrades();
        if(CollectionUtils.isNotEmpty(categories)){
           query.setCategoryIds(categories.stream().map(e -> e.getId().toString()).collect(Collectors.joining(",")));
        }
        String queryCategoryIds = query.getCategoryIds();
        if (CollectionUtils.isNotEmpty(queryGrades)) {
            wrapper.and(w -> {
                for (Integer gradeId : queryGrades) {
                    w.or().apply("find_in_set({0}, applicable_grades)", gradeId);
                }
            });
        }
        if (StringUtil.isNotBlank(queryCategoryIds)) {
            String[] categoryArr = queryCategoryIds.split(",");
            wrapper.and(w -> {
                for (String category : categoryArr) {
                    w.or().apply("find_in_set({0}, category_ids)", category);
                }
            });
        }
        Page<AppEBook> page = page(pageRequest.getPage(), wrapper);

        return convertDTOPage(page);
    }




    @Override
    public AppEBookDTO detail(Integer id) throws BusinessException {
        AppEBook eBook = this.getById(id);
        if (eBook == null) {
            throw new BusinessException("单书不存在");
        }
        AppEBookDTO dto = new AppEBookDTO();
        BeanUtil.copyProperties(eBook, dto);
        eBookHelper.resolveCodesToNames(eBook, dto);
        if(eBook.getFileId() != null ){
            List<Integer> appMediaIds = List.of(eBook.getFileId());
            Map<Integer, AppMaterial> pdfMediaMap = eBookHelper.convertMediaName(appMediaIds);
            if(pdfMediaMap.containsKey(eBook.getFileId())) {
                dto.setFileName(pdfMediaMap.get(eBook.getFileId()).getMaterialName());
            }
        }
        if(eBook.getCoverUrlId() != null ){
            List<Integer> coverMediaIds = List.of(eBook.getCoverUrlId());
            Map<Integer, AppMaterial> coverMediaMap = eBookHelper.convertMediaName(coverMediaIds);

            if(coverMediaMap.containsKey(eBook.getCoverUrlId())) {
                dto.setCoverUrlName(coverMediaMap.get(eBook.getCoverUrlId()).getMaterialName());
            }

        }

        if (eBook.getFileStatus() != null) {
            FileStatusEnum fileStatusEnum = FileStatusEnum.fromCode(eBook.getFileStatus());
            if (fileStatusEnum != null) {
                dto.setFileStatusDesc(fileStatusEnum.getDesc());
            }
        }
        fillResourceInfo(dto);
        return dto;
    }



    @Override
    public List<AppEBookDTO> getDetailList(List<Integer> ids) {
        return getDetailList(ids, false);
    }

    /**
     * 批量查询书籍详情列表
     * @param ids 书籍ID列表（保持顺序）
     * @param includeResources 是否包含资源信息
     * @return 书籍详情列表（与输入ID顺序一致）
     */
    public List<AppEBookDTO> getDetailList(List<Integer> ids, boolean includeResources) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }

        // 查询所有书籍并转换为Map，便于按ID快速查找
        List<AppEBook> books = this.listByIds(ids);
        if (CollectionUtils.isEmpty(books)) {
            return new ArrayList<>();
        }

        Map<Integer, AppEBook> bookMap = books.stream()
                .collect(Collectors.toMap(AppEBook::getId, book -> book));

        Map<Integer, String> configNameMap = batchQueryConfigNames(books);
        List<Integer> appMediaIds = books.stream().map(AppEBook::getFileId).collect(Collectors.toList());
        Map<Integer, AppMaterial> pdfMediaMap = eBookHelper.convertMediaName(appMediaIds);

        List<Integer> coverMediaIds = books.stream().map(AppEBook::getCoverUrlId).collect(Collectors.toList());
        Map<Integer, AppMaterial> coverMediaMap = eBookHelper.convertMediaName(coverMediaIds);

        Map<Integer, List<AppEBookResourceDTO>> resourceMap = new HashMap<>();
        if (includeResources) {
            resourceMap = batchQueryResourceInfo(ids);
        }

        // 按照输入的 ids 顺序构建结果列表
        List<AppEBookDTO> result = new ArrayList<>();
        for (Integer id : ids) {
            AppEBook book = bookMap.get(id);
            if (book != null) {
                AppEBookDTO dto = new AppEBookDTO();
                BeanUtil.copyProperties(book, dto);

                if(book.getFileId() != null && pdfMediaMap.containsKey(book.getFileId())) {
                    dto.setFileName(pdfMediaMap.get(book.getFileId()).getMaterialName());
                }
                if(book.getCoverUrlId() != null && coverMediaMap.containsKey(book.getCoverUrlId())) {
                    dto.setCoverUrlName(coverMediaMap.get(book.getCoverUrlId()).getMaterialName());
                }

                // 设置配置名称
                setConfigNamesFromCache(book, dto, configNameMap);

                // 设置文件状态描述
                if (book.getFileStatus() != null) {
                    FileStatusEnum fileStatusEnum = FileStatusEnum.fromCode(book.getFileStatus());
                    if (fileStatusEnum != null) {
                        dto.setFileStatusDesc(fileStatusEnum.getDesc());
                    }
                }

                if (includeResources) {
                    setResourceInfoFromBatch(dto, resourceMap.get(book.getId()));
                }

                result.add(dto);
            }
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(AppEBookSaveBO saveBO) throws BusinessException {
        AppEBook eBook = new AppEBook();
        BeanUtil.copyProperties(saveBO, eBook);
        if(CollectionUtils.isNotEmpty(saveBO.getCategoryIds())){
            eBook.setCategoryIds(saveBO.getCategoryIds().stream().map(Object::toString).collect(Collectors.joining(",")));
        }else{
            eBook.setCategoryIds(null);
        }
        if(CollectionUtils.isNotEmpty(saveBO.getApplicableGrades())){
            eBook.setApplicableGrades(saveBO.getApplicableGrades().stream().map(Object::toString).collect(Collectors.joining(",")));
        }else{
            eBook.setApplicableGrades(null);
        }
        if(eBook.getLaunchStatus().equals(YesOrNoEnum.YES.getCode())
                || eBook.getAllowDownload().equals(YesOrNoEnum.YES.getCode())){
            checkFileStatus(List.of(eBook));
        }
        eBook.setFileStatus(FileStatusEnum.PROCESSING.getCode());
        boolean sameFile = eBookHelper.dealFileWithMd5(eBook,saveBO.getFileUrl(),saveBO.getFileName(),saveBO.getFileMd5());
        boolean result = this.save(eBook);

        if(result && !sameFile){
            execParsePDF(eBook.getId(),eBook.getWatermarkId(),eBook.getFileUrl(),eBook.getFileName(),eBook.getFileMd5(),sameFile);
        } else if (result) {
            // 继承最新的任务结果
            eBookHelper.inheritLatestTaskResult(eBook.getCreateBy(),eBook.getId(), saveBO.getFileMd5(), eBook.getWatermarkId());
        }

        return eBook.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(AppEBookUpdateBO updateBO) throws BusinessException {
        if (updateBO.getId() == null) {
            throw new BusinessException("单书ID不能为空");
        }
        
        AppEBook eBook = this.getById(updateBO.getId());
        if (eBook == null) {
            throw new BusinessException("单书不存在");
        }

        if(eBook.getLaunchStatus().equals(YesOrNoEnum.YES.getCode())
                || eBook.getAllowDownload().equals(YesOrNoEnum.YES.getCode())){
            checkFileStatus(List.of(eBook));
        }

        BeanUtil.copyProperties(updateBO, eBook);

        if(CollectionUtils.isNotEmpty(updateBO.getCategoryIds())){
            eBook.setCategoryIds(updateBO.getCategoryIds().stream().map(Object::toString).collect(Collectors.joining(",")));
        }else{
            eBook.setCategoryIds(null);
        }
        if(CollectionUtils.isNotEmpty(updateBO.getApplicableGrades())){
            eBook.setApplicableGrades(updateBO.getApplicableGrades().stream().map(Object::toString).collect(Collectors.joining(",")));
        }else {
            eBook.setApplicableGrades(null);
        }

        return this.updateById(eBook);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Integer id) throws BusinessException {
        AppEBook eBook = this.getById(id);
        if (eBook == null) {
            throw new BusinessException(BOOK_NOT_EXIST_MSG);
        }

        if(eBook.getLaunchStatus().equals(YesOrNoEnum.YES.getCode())){
            throw new BusinessException(BOOK_ENABLED_DELETE_MSG);
        }

        if (isBookAssociatedWithShelf(id)) {
            throw new BusinessException(BOOK_SHELF_ASSOCIATED_MSG);
        }

        log.info("开始删除单书，bookId: {}, bookTitle: {}", id, eBook.getBookTitle());
        boolean result = this.removeById(id);

        if (result) {
            log.info("删除单书成功，bookId: {}", id);
        } else {
            log.warn("删除单书失败，bookId: {}", id);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBatch(List<Integer> ids) throws BusinessException {
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }
        List<AppEBook> eBookList = this.baseMapper.selectByIds(ids);
        if (CollectionUtils.isEmpty(eBookList)) {
            throw new BusinessException(BOOK_NOT_EXIST_MSG);
        }

        if(eBookList.stream().anyMatch(e -> e.getLaunchStatus().equals(YesOrNoEnum.YES.getCode()))){
            throw new BusinessException(BOOK_ENABLED_DELETE_MSG);
        }

        for (Integer bookId : ids) {
            if (isBookAssociatedWithShelf(bookId)) {
                AppEBook book = eBookList.stream()
                        .filter(e -> e.getId().equals(bookId))
                        .findFirst()
                        .orElse(null);
                String bookTitle = book != null ? book.getBookTitle() : "ID:" + bookId;
                throw new BusinessException("图书《" + bookTitle + "》已关联书架，请先从书架中移除后再删除");
            }
        }

        log.info("开始批量删除单书，数量：{}", ids.size());
        boolean result = this.removeByIds(ids);

        if (result) {
            log.info("批量删除单书成功，数量：{}", ids.size());
        } else {
            log.warn("批量删除单书失败，bookIds：{}", ids);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableBatch(List<Integer> ids) throws BusinessException {
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }
        List<AppEBook> updateList = ids.stream().map(id -> {
            AppEBook book = new AppEBook();
            book.setId(id);
            book.setLaunchStatus(YesOrNoEnum.YES.getCode());
            return book;
        }).collect(Collectors.toList());
        checkFileStatus(this.getBaseMapper().selectByIds(ids));
        return this.updateBatchById(updateList);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableBatch(List<Integer> ids) throws BusinessException {
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }
        
        List<AppEBook> updateList = ids.stream().map(id -> {
            AppEBook book = new AppEBook();
            book.setId(id);
            book.setLaunchStatus(YesOrNoEnum.NO.getCode());
            return book;
        }).collect(Collectors.toList());
        
        return this.updateBatchById(updateList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean allowDownloadBatch(List<Integer> ids) throws BusinessException {
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }
        
        List<AppEBook> updateList = ids.stream().map(id -> {
            AppEBook book = new AppEBook();
            book.setId(id);
            book.setAllowDownload(YesOrNoEnum.YES.getCode());
            return book;
        }).collect(Collectors.toList());
        checkFileStatus(this.getBaseMapper().selectByIds(ids));
        return this.updateBatchById(updateList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableDownloadBatch(List<Integer> ids) throws BusinessException {
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }

        List<AppEBook> updateList = ids.stream().map(id -> {
            AppEBook book = new AppEBook();
            book.setId(id);
            book.setAllowDownload(YesOrNoEnum.NO.getCode());
            return book;
        }).collect(Collectors.toList());
        
        return this.updateBatchById(updateList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFile(AppEBookUpdateFileBO updateFileBO) throws BusinessException {
        String fileUrl = updateFileBO.getFileUrl();
        AppEBook eBook = this.getById(updateFileBO.getId());
        if (eBook == null) {
            throw new BusinessException("单书不存在");
        }
        eBook.setFileUrl(fileUrl);
        eBook.setFileStatus(FileStatusEnum.PROCESSING.getCode());
        eBook.setFileErrMsg(StringUtils.EMPTY);

        eBook.setFileId(updateFileBO.getFileId());
        if (StringUtils.isBlank(eBook.getFileName()) && StringUtils.isNotBlank(fileUrl)) {
            String fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
            eBook.setFileName(fileName);
        }

        boolean sameFile = eBookHelper.dealFileWithMd5(eBook,updateFileBO.getFileUrl(),updateFileBO.getFileName(),updateFileBO.getFileMd5());

        boolean result = this.updateById(eBook);

        if(result && !sameFile){
            execParsePDF(eBook.getId(),eBook.getWatermarkId(),updateFileBO.getFileUrl(),updateFileBO.getFileName(),updateFileBO.getFileMd5(),sameFile);
        } else if (result) {
            // 继承最新的任务结果
            eBookHelper.inheritLatestTaskResult(eBook.getUpdateBy(),eBook.getId(), updateFileBO.getFileMd5(), eBook.getWatermarkId());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateWatermark(Integer id, Integer watermarkId) throws BusinessException {
        AppEBook eBook = this.getById(id);
        if (eBook == null) {
            throw new BusinessException("单书不存在");
        }
        eBook.setFileStatus(FileStatusEnum.PROCESSING.getCode());
        eBook.setFileErrMsg(StringUtils.EMPTY);
        eBook.setWatermarkId(watermarkId);
        boolean result = this.updateById(eBook);
        if(result){
            execParsePDF(eBook.getId(),watermarkId,eBook.getFileUrl(),eBook.getFileName(),eBook.getFileMd5(),false);
        }
        return result;
    }

    @Override
    public String coverUrl(AppEBookFileBO fileBO) throws BusinessException{
        String fileUrl = fileBO.getFileUrl();
        String fileName = fileBO.getFileName();
        if (fileUrl.isEmpty() && fileName.isEmpty()) {
            throw new BusinessException("文件地址和文件名不能为空");
        }
        return eBookHelper.parsePDF(null,fileBO.getFileMd5(),PdfWaterMarkBusinessTypeEnum.ONLY_SLICING_IMAGE.getCode(),null,fileBO.getFileUrl(),fileBO.getFileName());
    }

    private void execParsePDF(Integer businessId,Integer watermarkId,String fileUrl,String fileName,String fileMd5,boolean sameFile) throws BusinessException {
        if (StringUtils.isNotBlank(fileUrl) && !sameFile) {
            AppEbooksConfigWatermarkTemplate template = null;
            Integer watermarkBusinessType = eBookHelper.getWatermarkBusinessType(watermarkId);
            if(Objects.nonNull(watermarkId)) {
                template = watermarkTemplateBiz.getById(watermarkId);
            }
            eBookHelper.parsePDFAsync(businessId,fileMd5,watermarkBusinessType,template,fileUrl,fileName,false);
        }
    }
    /**
     * 填充资源信息
     *
     * @param dto 单书DTO
     */
    private void fillResourceInfo(AppEBookDTO dto) {
        try {
            if (dto == null || dto.getId() == null || StringUtils.isEmpty(dto.getFileMd5())) {
                return;
            }
            // 查询加水印PDF资源
            List<AppEBookResourceDTO> pdfResources = eBookResourceBiz.listByBookIdAndType(
                    dto.getId(),  List.of(ResourceTypeEnum.WATERMARKED_PDF.getCode()));
            if (!pdfResources.isEmpty()) {
                AppEBookResourceDTO watermarkedPdf = pdfResources.get(0);
                dto.setWatermarkedPdf(watermarkedPdf);
                // 同时设置加水印文件URL字段
                if (StringUtils.isNotEmpty(watermarkedPdf.getResourceUrl())) {
                    dto.setWatermarkedFileUrl(watermarkedPdf.getResourceUrl());
                }
            }
            List<AppEBookResourceDTO> imageResources = eBookResourceBiz.listByBookIdAndType(
                    dto.getId(), List.of(ResourceTypeEnum.PAGE_IMAGE.getCode(),ResourceTypeEnum.COVER_IMAGE.getCode()));
            if (!imageResources.isEmpty()) {
                dto.setPageImages(imageResources);
            }
        } catch (BusinessException e) {
            log.error("填充资源信息异常", e);
        }
    }

    @Override
    public Page<AppEBookDTO> pageForH5(PageInfo<AppEBookH5QueryBO> pageRequest) throws BusinessException {
        if(pageRequest.getData() == null){
            pageRequest.setData(new AppEBookH5QueryBO());
        }
        AppEBookH5QueryBO query = pageRequest.getData();

        if (query.getShelfId() == null && query.getStoreId() == null) {
            log.info("未提供shelfId和storeId，将查询默认启用的书城");
        }

        Page<AppEBook> page;

        log.info("H5分页查询单书，shelfId: {}, storeId: {}", query.getShelfId(), query.getStoreId());
        page = this.getBaseMapper().pageForH5(pageRequest.getPage(), query);

        return convertDTOPage(page);
    }

    /**
     * 检查H5查询条件是否全部为空
     *
     * @param query 查询条件
     * @return true-所有条件都为空，false-至少有一个条件不为空
     */
    private boolean isAllQueryConditionsEmpty(AppEBookH5QueryBO query) {
        return (query.getBookTitle() == null || query.getBookTitle().trim().isEmpty()) &&
               query.getShelfId() == null &&
               query.getStoreId() == null &&
               (query.getSubjectIds() == null || query.getSubjectIds().isEmpty()) &&
               (query.getStageIds() == null || query.getStageIds().isEmpty()) &&
               (query.getCategoryIds() == null || query.getCategoryIds().isEmpty()) &&
               (query.getTextbookVersionIds() == null || query.getTextbookVersionIds().isEmpty()) &&
               (query.getApplicableGrades() == null || query.getApplicableGrades().isEmpty());
    }

    private void checkFileStatus(List<AppEBook> updateList) throws BusinessException {
        if(updateList.stream().anyMatch(e -> e.getFileStatus().equals(FileStatusEnum.PROCESSING.getCode())
                || e.getFileStatus().equals(FileStatusEnum.FAILED.getCode()))){
            throw new BusinessException("存在文件正在处理中或处理失败的图书，请稍后重试");
        }
    }

    @Override
    public ShareUrlResultDTO getShareInfo(GetShareInfoBO request) throws BusinessException {
        log.info("获取分享信息，业务类型：{}，业务ID：{}", request.getBusinessType(), request.getBusinessId());

        ShareUrlTypeEnum typeEnum = ShareUrlTypeEnum.getByCode(request.getBusinessType());
        if (typeEnum == null) {
            throw new BusinessException("不支持的业务类型：" + request.getBusinessType());
        }

        // 根据业务类型调用不同的处理逻辑
        return switch (typeEnum) {
            case BOOK -> eBookHelper.getBookShareInfo(request);
            case BOOKSHELF -> eBookHelper.getBookshelfShareInfo(request);
            case BOOKSTORE -> eBookHelper.getBookstoreShareInfo(request);
        };
    }

    @Override
    public void removeShareUrl(RemoveShareUrlBO request) throws BusinessException {
        log.info("删除分享链接，业务类型：{}，业务ID：{}", request.getBusinessType(), request.getBusinessId());

        ShareUrlTypeEnum typeEnum = ShareUrlTypeEnum.getByCode(request.getBusinessType());
        if (typeEnum == null) {
            throw new BusinessException("不支持的业务类型：" + request.getBusinessType());
        }

        switch (typeEnum) {
            case BOOK:
                eBookHelper.removeBookShareUrl(request);
                break;
            case BOOKSHELF:
                eBookHelper.removeBookshelfShareUrl(request);
                break;
            case BOOKSTORE:
                eBookHelper.removeBookstoreShareUrl(request);
                break;
            default:
                throw new BusinessException("不支持的业务类型：" + request.getBusinessType());
        }
    }

    /**
     * 检查单书是否关联了书架
     *
     * @param bookId 单书ID
     * @return true-已关联书架，false-未关联书架
     */
    private boolean isBookAssociatedWithShelf(Integer bookId) {
        try {
            // 查询书架单书关联表中是否存在该单书
            LambdaQueryWrapper<AppEBookshelfBookRef> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AppEBookshelfBookRef::getBookId, bookId);

            long count = eBookshelfBookRefBiz.count(queryWrapper);

            if (count > 0) {
                log.info("单书已关联书架，bookId: {}, 关联书架数量: {}", bookId, count);
                return true;
            }

            return false;

        } catch (Exception e) {
            log.error("检查单书书架关联关系异常，bookId: {}, 错误: {}", bookId, e.getMessage(), e);
            return true;
        }
    }
    private Page<AppEBookDTO> convertDTOPage(Page<AppEBook> page) {
        List<AppEBook> books = page.getRecords();
        if (CollectionUtils.isEmpty(books)) {
            return new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        }

        // 提取书籍ID列表
        List<Integer> bookIds = books.stream().map(AppEBook::getId).collect(Collectors.toList());

        // 批量查询配置信息
        Map<Integer, String> configNameMap = batchQueryConfigNames(books);

        // 批量查询加水印文件URL
        Map<Integer, String> watermarkedFileUrlMap = batchQueryWatermarkedFileUrls(bookIds);

        List<AppEBookDTO> dtoList = books.stream().map(eBook -> {
            AppEBookDTO dto = new AppEBookDTO();
            BeanUtil.copyProperties(eBook, dto);

            // 设置配置名称
            setConfigNamesFromCache(eBook, dto, configNameMap);

            // 设置加水印文件URL
            String watermarkedFileUrl = watermarkedFileUrlMap.getOrDefault(eBook.getId(),eBook.getFileUrl());
            if (StringUtils.isNotEmpty(watermarkedFileUrl)) {
                dto.setWatermarkedFileUrl(watermarkedFileUrl);
            }

            if (eBook.getFileStatus() != null) {
                FileStatusEnum fileStatusEnum = FileStatusEnum.fromCode(eBook.getFileStatus());
                if (fileStatusEnum != null) {
                    dto.setFileStatusDesc(fileStatusEnum.getDesc());
                }
            }
            return dto;
        }).collect(Collectors.toList());

        Page<AppEBookDTO> resultPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        resultPage.setRecords(dtoList);
        return resultPage;
    }

    /**
     * 批量查询加水印文件URL
     *
     * @param bookIds 书籍ID列表
     * @return 书籍ID -> 加水印文件URL的映射
     */
    public Map<Integer, String> batchQueryWatermarkedFileUrls(List<Integer> bookIds) {
        if (CollectionUtils.isEmpty(bookIds)) {
            return new HashMap<>();
        }

        try {
            // 构建批量查询参数，只查询加水印PDF资源（resource_type = 1）
            AppEBookResourceBatchQueryBO queryBO = new AppEBookResourceBatchQueryBO();
            queryBO.setBookIds(bookIds);
            queryBO.setResourceType(ResourceTypeEnum.WATERMARKED_PDF.getCode());
            queryBO.setGroupByBook(true);

            // 调用资源业务层的批量查询方法
            Map<Integer, List<AppEBookResourceDTO>> resourceMap = eBookResourceBiz.batchQueryByBooks(queryBO);

            // 转换为 bookId -> watermarkedFileUrl 的映射
            Map<Integer, String> urlMap = new HashMap<>();
            for (Map.Entry<Integer, List<AppEBookResourceDTO>> entry : resourceMap.entrySet()) {
                List<AppEBookResourceDTO> resources = entry.getValue();
                if (!CollectionUtils.isEmpty(resources)) {
                    // 取第一个加水印PDF资源的URL
                    AppEBookResourceDTO firstResource = resources.get(0);
                    if (firstResource != null && StringUtils.isNotEmpty(firstResource.getResourceUrl())) {
                        urlMap.put(entry.getKey(), firstResource.getResourceUrl());
                    }
                }
            }

            return urlMap;
        } catch (Exception e) {
            log.error("批量查询加水印文件URL失败，bookIds: {}", bookIds, e);
            return new HashMap<>();
        }
    }

    /**
     * 批量查询资源信息
     */
    private Map<Integer, List<AppEBookResourceDTO>> batchQueryResourceInfo(List<Integer> bookIds) {
        try {
            Map<Integer, List<AppEBookResourceDTO>> resourceMap = new HashMap<>();

            List<AppEBookResourceDTO> pdfResources = eBookResourceBiz.listByBookIdsAndType(
                    bookIds, ResourceTypeEnum.WATERMARKED_PDF.getCode());

            List<AppEBookResourceDTO> imageResources = eBookResourceBiz.listByBookIdsAndType(
                    bookIds, ResourceTypeEnum.PAGE_IMAGE.getCode());

            for (AppEBookResourceDTO resource : pdfResources) {
                resourceMap.computeIfAbsent(resource.getResourceId(), k -> new ArrayList<>()).add(resource);
            }
            for (AppEBookResourceDTO resource : imageResources) {
                resourceMap.computeIfAbsent(resource.getResourceId(), k -> new ArrayList<>()).add(resource);
            }

            return resourceMap;
        } catch (Exception e) {
            log.error("批量查询资源信息失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 批量查询配置名称（已优化缓存）
     */
    private Map<Integer, String> batchQueryConfigNames(List<AppEBook> books) {
        // 收集所有需要查询的配置ID
        Set<Integer> configIds = new HashSet<>();
        for (AppEBook book : books) {
            if (book.getSubjectId() != null) configIds.add(book.getSubjectId());
            if (book.getStageId() != null) configIds.add(book.getStageId());
            if (book.getTextbookVersionId() != null) configIds.add(book.getTextbookVersionId());
        }

        if (configIds.isEmpty()) {
            return new HashMap<>();
        }

        Map<Integer, String> configNameMap = eBookHelper.batchConvertConfigNames(configIds);

        log.debug("批量查询配置名称完成，查询数量: {}, 成功获取: {}", configIds.size(), configNameMap.size());
        return configNameMap;
    }

    /**
     * 从缓存中设置配置名称
     */
    private void setConfigNamesFromCache(AppEBook eBook, AppEBookDTO dto, Map<Integer, String> configNameMap) {
        dto.setCategoryIds(eBookHelper.commaSeparatedStringToIntegerList(eBook.getCategoryIds()));
        dto.setApplicableGrades(eBookHelper.commaSeparatedStringToIntegerList(eBook.getApplicableGrades()));

        dto.setApplicableGradeNames(eBookHelper.convertApplicableGradesNames(eBook.getApplicableGrades()));
        dto.setCategoryNames(eBookHelper.convertCategoryNames(eBook.getCategoryIds()));

        dto.setSubjectName(configNameMap.get(eBook.getSubjectId()));
        dto.setTextbookVersionName(configNameMap.get(eBook.getTextbookVersionId()));
        dto.setStageName(configNameMap.get(eBook.getStageId()));
    }

    /**
     * 批量设置配置名称（超高性能版本）
     * 一次性处理所有书籍的年级和分类名称转换，最大化减少数据库查询
     */
    private void batchSetConfigNamesFromCache(List<AppEBook> books, List<AppEBookDTO> dtos, Map<Integer, String> configNameMap) {
        if (books.size() != dtos.size()) {
            throw new IllegalArgumentException("书籍列表和DTO列表大小不匹配");
        }

        try {
            List<String> allApplicableGrades = books.stream()
                    .map(AppEBook::getApplicableGrades)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());

            List<String> allCategoryIds = books.stream()
                    .map(AppEBook::getCategoryIds)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());

            Map<String, String> gradeNamesMap = eBookHelper.batchConvertApplicableGradesNames(allApplicableGrades);
            Map<String, String> categoryNamesMap = eBookHelper.batchConvertCategoryNames(allCategoryIds);

            for (int i = 0; i < books.size(); i++) {
                AppEBook book = books.get(i);
                AppEBookDTO dto = dtos.get(i);

                dto.setCategoryIds(eBookHelper.commaSeparatedStringToIntegerList(book.getCategoryIds()));
                dto.setApplicableGrades(eBookHelper.commaSeparatedStringToIntegerList(book.getApplicableGrades()));

                dto.setApplicableGradeNames(gradeNamesMap.getOrDefault(book.getApplicableGrades(), ""));
                dto.setCategoryNames(categoryNamesMap.getOrDefault(book.getCategoryIds(), ""));

                dto.setSubjectName(configNameMap.get(book.getSubjectId()));
                dto.setTextbookVersionName(configNameMap.get(book.getTextbookVersionId()));
                dto.setStageName(configNameMap.get(book.getStageId()));
            }

            log.debug("批量设置配置名称完成 - 书籍数量: {}, 年级类型: {}, 分类类型: {}",
                    books.size(), allApplicableGrades.size(), allCategoryIds.size());

        } catch (Exception e) {
            log.error("批量设置配置名称异常，降级到逐个处理", e);
            for (int i = 0; i < books.size(); i++) {
                setConfigNamesFromCache(books.get(i), dtos.get(i), configNameMap);
            }
        }
    }

    /**
     * 从批量查询结果中设置资源信息
     */
    private void setResourceInfoFromBatch(AppEBookDTO dto, List<AppEBookResourceDTO> resources) {
        if (CollectionUtils.isEmpty(resources)) {
            return;
        }

        for (AppEBookResourceDTO resource : resources) {
            if (ResourceTypeEnum.WATERMARKED_PDF.getCode().equals(resource.getResourceType())) {
                dto.setWatermarkedPdf(resource);
            } else if (ResourceTypeEnum.PAGE_IMAGE.getCode().equals(resource.getResourceType())) {
                if (dto.getPageImages() == null) {
                    dto.setPageImages(new ArrayList<>());
                }
                dto.getPageImages().add(resource);
            }
        }
    }

    @Override
    public Map<Integer, List<AppEBookResourceDTO>> batchQueryResources(List<Integer> bookIds, Integer resourceType) throws BusinessException {
        if (CollectionUtils.isEmpty(bookIds)) {
            return new HashMap<>();
        }

        try {
            AppEBookResourceBatchQueryBO queryBO = new AppEBookResourceBatchQueryBO();
            queryBO.setBookIds(bookIds);
            queryBO.setResourceType(resourceType);
            queryBO.setGroupByBook(true);

            return eBookResourceBiz.batchQueryByBooks(queryBO);
        } catch (Exception e) {
            log.error("批量查询书籍资源失败，bookIds: {}, resourceType: {}", bookIds, resourceType, e);
            throw new BusinessException("批量查询书籍资源失败");
        }
    }

}