package com.dbj.classpal.books.service.api.client.evaluation.app;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.app.client.bo.user.UserIdApiBO;
import com.dbj.classpal.app.client.dto.user.CurrentUserApiDTO;
import com.dbj.classpal.books.client.api.evaluation.app.AppEvaluationApi;
import com.dbj.classpal.books.client.bo.evaluation.app.AppEvaluationQueryApiBO;
import com.dbj.classpal.books.client.dto.evaluation.app.AppEvaluationNodeQueryApiDTO;
import com.dbj.classpal.books.client.dto.evaluation.app.AppEvaluationQueryApiDTO;
import com.dbj.classpal.books.common.enums.BusinessTypeEnum;
import com.dbj.classpal.books.service.biz.evaluation.IAppEvaluationBiz;
import com.dbj.classpal.books.service.biz.evaluation.IAppEvaluationNodeBiz;
import com.dbj.classpal.books.service.biz.evaluation.IAppUserPaperEvaluationAnalysisBiz;
import com.dbj.classpal.books.service.biz.evaluation.IAppUserPaperEvaluationBiz;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBiz;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBusinessRefBiz;
import com.dbj.classpal.books.service.biz.paper.IAppUserPaperInfoBiz;
import com.dbj.classpal.books.service.entity.evaluation.AppEvaluation;
import com.dbj.classpal.books.service.entity.evaluation.AppEvaluationNode;
import com.dbj.classpal.books.service.entity.evaluation.AppUserPaperEvaluation;
import com.dbj.classpal.books.service.entity.evaluation.AppUserPaperEvaluationAnalysis;
import com.dbj.classpal.books.service.entity.material.AppMaterial;
import com.dbj.classpal.books.service.entity.material.AppMaterialBusinessRef;
import com.dbj.classpal.books.service.remote.sys.SysAppUserRemoteService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import com.dbj.classpal.framework.utils.util.ContextAppUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_EVALUATION_NOT_EXIST_CODE;
import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_EVALUATION_NOT_EXIST_MSG;
import static com.dbj.classpal.books.common.enums.evaluation.PaperEnvaluationStatusEnum.EVALUATION_GENERATED_NO;
import static com.dbj.classpal.books.common.enums.evaluation.PaperEnvaluationStatusEnum.EVALUATION_GENERATED_YES;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationApiImpl
 * Date:     2025-05-20 14:53:51
 * Description: 表名： ,描述： 表
 */
@RestController
public class AppEvaluationApiImpl implements AppEvaluationApi {

    @Resource
    private IAppEvaluationBiz evaluationBiz;
    @Resource
    private IAppEvaluationNodeBiz evaluationNodeBiz;
    @Resource
    private IAppUserPaperEvaluationBiz userPaperEvaluationBiz;
    @Resource
    private IAppUserPaperEvaluationAnalysisBiz userPaperEvaluationAnalysisBiz;
    @Resource
    private IAppMaterialBusinessRefBiz appMaterialBusinessRefBiz;
    @Resource
    private IAppMaterialBiz appMaterialBiz;


    @Override
    public RestResponse<AppEvaluationQueryApiDTO> getEvaluation(AppEvaluationQueryApiBO bo) throws BusinessException {
        //1. 查询评测表
        AppEvaluation evaluation = evaluationBiz.getById(bo.getAppEvaluationId());
        if (ObjectUtils.isEmpty(evaluation)) {
            throw new BusinessException(APP_EVALUATION_NOT_EXIST_CODE,APP_EVALUATION_NOT_EXIST_MSG);
        }
        AppEvaluationQueryApiDTO queryApiDTO = new AppEvaluationQueryApiDTO();
        BeanUtil.copyProperties(evaluation, queryApiDTO);
        //2.查询用户是否存在该评测表的评测报告
        List<AppUserPaperEvaluation> paperEvaluationList = userPaperEvaluationBiz.lambdaQuery().eq(AppUserPaperEvaluation::getAppUserId, ContextAppUtil.getAppUserIdInt()).eq(AppUserPaperEvaluation::getAppEvaluationId, bo.getAppEvaluationId()).orderByDesc(AppUserPaperEvaluation::getCreateTime).list();
        //3. 查询测试表下的测试项
        List<AppEvaluationNode> evaluationNodeList = evaluationNodeBiz.lambdaQuery().eq(AppEvaluationNode::getAppEvaluationId, evaluation.getId()).orderByAsc(AppEvaluationNode::getAppEvaluationOrder).list();
        //6. 如果不存在评测报告记录,则设置查询到的评测项列表信息
        if (CollectionUtils.isEmpty(paperEvaluationList)) {
            queryApiDTO.setIsGenerated(EVALUATION_GENERATED_NO.getCode());
            //7. 如果评测项列表不为空,则赋值
            if (CollectionUtils.isNotEmpty(evaluationNodeList)) {
                queryApiDTO.setNodeQueryApiDTOList(evaluationNodeList.stream().map(d -> {
                    AppEvaluationNodeQueryApiDTO appEvaluationNodeQueryApiDTO = new AppEvaluationNodeQueryApiDTO();
                    BeanUtil.copyProperties(d,appEvaluationNodeQueryApiDTO);
                    appEvaluationNodeQueryApiDTO.setIsGenerated(EVALUATION_GENERATED_NO.getCode());
                    return appEvaluationNodeQueryApiDTO;
                }).collect(Collectors.toList()));
            }
            return RestResponse.success(queryApiDTO);
        }
        // 8. 如果存在评测报告,则取最近生成的评测报告，赋值评测报告的状态
        AppUserPaperEvaluation historyEvaluation = paperEvaluationList.get(0);
        queryApiDTO.setIsGenerated(historyEvaluation.getIsGenerated());
        queryApiDTO.setAppUserPaperEvaluationId(historyEvaluation.getId());

        //9. 如果评测项不为空，获取评测项列表的id集合，查询各评测项的评测结果数据并赋值
        if (CollectionUtils.isNotEmpty(evaluationNodeList)) {
            Set<Integer> evaluationNodeIdSet = evaluationNodeList.stream().map(BizEntity::getId).collect(Collectors.toSet());
            //10. 查询是否存在各评测项评测报告分析详情
            List<AppUserPaperEvaluationAnalysis> analysisList = userPaperEvaluationAnalysisBiz.lambdaQuery().eq(AppUserPaperEvaluationAnalysis::getAppUserPaperEvaluationId, historyEvaluation.getId()).in(AppUserPaperEvaluationAnalysis::getAppEvaluationNodeId, evaluationNodeIdSet).list();
            Map<Integer, AppUserPaperEvaluationAnalysis> analysisMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(analysisList)){
                analysisMap = analysisList.stream().collect(Collectors.toMap(AppUserPaperEvaluationAnalysis::getAppEvaluationNodeId, a -> a));
            }
            Map<Integer, AppUserPaperEvaluationAnalysis> finalAnalysisMap = analysisMap;
            queryApiDTO.setNodeQueryApiDTOList(evaluationNodeList.stream().map(d -> {
                AppEvaluationNodeQueryApiDTO appEvaluationNodeQueryApiDTO = new AppEvaluationNodeQueryApiDTO();
                BeanUtil.copyProperties(d,appEvaluationNodeQueryApiDTO);
                if (finalAnalysisMap.containsKey(d.getId())){
                    AppUserPaperEvaluationAnalysis analysis = finalAnalysisMap.get(d.getId());
                    appEvaluationNodeQueryApiDTO.setIsGenerated(EVALUATION_GENERATED_YES.getCode());
                    appEvaluationNodeQueryApiDTO.setAppUserPaperEvaluationAnalysisId(analysis.getId());
                    appEvaluationNodeQueryApiDTO.setAppUserPaperInfoId(analysis.getAppUserPaperInfoId());
                }else{
                    appEvaluationNodeQueryApiDTO.setIsGenerated(EVALUATION_GENERATED_NO.getCode());
                }
                return appEvaluationNodeQueryApiDTO;
            }).collect(Collectors.toList()));
        }
        return RestResponse.success(queryApiDTO);
    }
}
