package com.dbj.classpal.books.service.biz.album;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.common.bo.album.AppAlbumMenusQueryBO;
import com.dbj.classpal.books.common.dto.album.AppAlbumMenusInfoDTO;
import com.dbj.classpal.books.service.entity.album.AppAlbumMenus;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppAlbumMenusBiz
 * Date:     2025-04-08 16:26:13
 * Description: 表名： ,描述： 表
 */
public interface IAppAlbumMenusBiz extends IService<AppAlbumMenus> {

    /**
     * 根据分类id统计专辑信息
     *
     * @return SysUserDeptGroupDTO
     */
    List<AppAlbumMenusInfoDTO> getAppAlbumMenusInfoByDeptIdGroup(AppAlbumMenusQueryBO bo);


    /**
     * 获取某个节点下子孙节点深度层数
     * @param id
     * @return
     */
    Integer getChildrenDepth(Integer id);


    /**
     * 获取某个节点到根节点深度层数
     * @param id
     * @return
     */
    Integer getRootDepth(Integer id);

    /**
     * 获取最大排序号
     * @param id
     * @return
     */
    Integer getMaxOrderNum(Integer id);
}
