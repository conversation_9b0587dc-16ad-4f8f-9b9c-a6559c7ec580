package com.dbj.classpal.books.service.biz.paper.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.bo.paper.CreateUserPaperBO;
import com.dbj.classpal.books.common.dto.paper.UserPaperDTO;
import com.dbj.classpal.books.service.biz.paper.IAppUserPaperInfoBiz;
import com.dbj.classpal.books.service.entity.paper.AppUserPaperInfo;
import com.dbj.classpal.books.service.mapper.paper.AppUserPaperInfoMapper;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * 用户试卷信息业务实现类
 */
@Slf4j
@Service
public class AppUserPaperInfoBizImpl  extends ServiceImpl<AppUserPaperInfoMapper, AppUserPaperInfo> implements IAppUserPaperInfoBiz {

    @Resource
    private AppUserPaperInfoMapper paperInfoMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserPaperDTO createPaper(CreateUserPaperBO createBO) throws BusinessException {

        try {
            AppUserPaperInfo paperInfo = new AppUserPaperInfo();
            BeanUtil.copyProperties(createBO, paperInfo);
            paperInfoMapper.insert(paperInfo);
            UserPaperDTO paperDTO = new UserPaperDTO();
            BeanUtil.copyProperties(paperInfo, paperDTO);
            return paperDTO;
        } catch (Exception e) {
            log.error("创建试卷失败", e);
            throw new BusinessException(APP_CREATE_PAPER_FAIL_CODE,APP_CREATE_PAPER_FAIL_MSG);
        }
    }

    @Override
    public UserPaperDTO getPaperInfo(Integer paperId) throws BusinessException {
        if (paperId == null) {
            return null;
        }

        try {
            AppUserPaperInfo paperInfo = paperInfoMapper.selectById(paperId);
            if (paperInfo == null) {
                throw new BusinessException(APP_PAPER_NOT_EXIST_CODE,APP_PAPER_NOT_EXIST_MSG);
            }
            UserPaperDTO paperDTO = new UserPaperDTO();
            BeanUtil.copyProperties(paperInfo, paperDTO);
            return paperDTO;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取试卷信息失败", e);
            throw new BusinessException(APP_GET_PAPER_INFO_ERROR_CODE,APP_GET_PAPER_INFO_ERROR_MSG);
        }
    }

} 