package com.dbj.classpal.books.service.entity.product;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 单书资源表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_ebook_resource")
@Tag(name="AppEBookResource", description="单书资源表")
public class AppEBookResource extends BizEntity implements Serializable {

    @Schema(description = "关联的单书id")
    private Integer resourceId;

    @Schema(description = "关联的单书文件md5")
    private String resourceKey;

    @Schema(description = "资源类型：1-加水印PDF, 2-切图图片")
    private Integer resourceType;

    @Schema(description = "资源URL")
    private String resourceUrl;

    @Schema(description = "页码，适用于切图")
    private Integer pageNum;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "排序序号")
    private Integer sortNum;

    @Schema(description = "处理类型：1-仅添加水印, 2-仅切图, 3-加水印并切图")
    private Integer businessType;

} 