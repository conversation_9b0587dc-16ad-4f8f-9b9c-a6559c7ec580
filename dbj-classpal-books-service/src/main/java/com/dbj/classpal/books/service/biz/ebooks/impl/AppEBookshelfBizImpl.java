package com.dbj.classpal.books.service.biz.ebooks.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookshelfH5QueryBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookshelfQueryBO;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookDTO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookshelfDTO;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookBiz;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookshelfBiz;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookshelfBookRefBiz;
import com.dbj.classpal.books.service.entity.ebooks.AppEBook;
import com.dbj.classpal.books.service.entity.ebooks.AppEBookshelf;
import com.dbj.classpal.books.service.entity.ebooks.AppEBookshelfBookRef;
import com.dbj.classpal.books.service.mapper.ebooks.AppEBookshelfMapper;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 书架 业务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
@Slf4j
@Service
public class AppEBookshelfBizImpl extends ServiceImpl<AppEBookshelfMapper, AppEBookshelf> implements IAppEBookshelfBiz {

    @Resource
    private IAppEBookshelfBookRefBiz eBookshelfBookRefService;
    
    @Resource
    private IAppEBookBiz eBookBiz;

    @Resource
    private IAppEBookshelfBookRefBiz eBookshelfBookRefBiz;

    @Resource
    private AppEBookHelper eBookHelper;


    @Override
    public Page<AppEBookshelfDTO> pageBookshelf(Page<AppEBookshelf> page, AppEBookshelfQueryBO queryBO) throws BusinessException {
        // 构建查询条件
        LambdaQueryWrapper<AppEBookshelf> queryWrapper = new LambdaQueryWrapper<>();
        
        if (queryBO != null) {
            // 书架名称模糊查询
            if (StringUtils.isNotBlank(queryBO.getShelfTitle())) {
                queryWrapper.like(AppEBookshelf::getShelfTitle, queryBO.getShelfTitle());
            }
            
            // 是否隐藏查询
            if (queryBO.getAllowDownload() != null) {
                queryWrapper.eq(AppEBookshelf::getAllowDownload, queryBO.getAllowDownload());
            }
            
            // 上下架状态查询
            if (queryBO.getLaunchStatus() != null) {
                queryWrapper.eq(AppEBookshelf::getLaunchStatus, queryBO.getLaunchStatus());
            }
        }
        
        // 默认按排序序号和ID降序排序
        queryWrapper.orderByDesc(AppEBookshelf::getSortNum)
                .orderByDesc(AppEBookshelf::getSortNum,AppEBookshelf::getCreateTime);

        // 执行分页查询
        Page<AppEBookshelf> resultPage = this.page(page, queryWrapper);

        return (Page<AppEBookshelfDTO>) resultPage.convert(shelf -> {
            AppEBookshelfDTO dto = new AppEBookshelfDTO();
            BeanUtils.copyProperties(shelf, dto);
            // 统计书籍数量
            int bookCount = 0;
            try {
                bookCount = eBookshelfBookRefService.countBooks(shelf.getId());
            } catch (BusinessException e) {
                throw new RuntimeException(e);
            }
            dto.setBookCount(bookCount);
            return dto;
        });
    }


    @Override
    public AppEBookshelfDTO getBookshelfDetail(Integer id) throws BusinessException {
        if (id == null) {
            throw new BusinessException(AppErrorCode.BOOKSHELF_ID_NOT_NULL_CODE, AppErrorCode.BOOKSHELF_ID_NOT_NULL_MSG);
        }

        // 查询书架信息
        AppEBookshelf shelf = this.getById(id);
        if (shelf == null) {
            throw new BusinessException(AppErrorCode.BOOKSHELF_NOT_EXIST_CODE, AppErrorCode.BOOKSHELF_NOT_EXIST_MSG);
        }

        // 转换为DTO
        AppEBookshelfDTO dto = new AppEBookshelfDTO();
        BeanUtils.copyProperties(shelf, dto);

        // 查询书架下的单书ID列表
        List<Integer> bookIds = eBookshelfBookRefService.listBookIds(List.of(id));

        // 批量查询单书详情列表（不包含资源信息，提升性能）
        if (CollectionUtils.isNotEmpty(bookIds)) {
            List<AppEBookDTO> bookDTOList = eBookBiz.getDetailList(bookIds, false);
            dto.setBooks(bookDTOList);
            dto.setBookCount(bookDTOList.size());
        }
        return dto;
    }

    @Override
    public boolean setBookshelfCover(Integer id, Integer bookId, String coverUrl) throws BusinessException {
        if (id == null) {
            throw new BusinessException(AppErrorCode.BOOKSHELF_ID_NOT_NULL_CODE, AppErrorCode.BOOKSHELF_ID_NOT_NULL_MSG);
        }
        
        // 查询书架信息
        AppEBookshelf shelf = this.getById(id);
        if (shelf == null) {
            throw new BusinessException(AppErrorCode.BOOKSHELF_NOT_EXIST_CODE, AppErrorCode.BOOKSHELF_NOT_EXIST_MSG);
        }
        
        // 如果直接提供了封面URL，则直接使用
        if (StringUtils.isNotBlank(coverUrl)) {
            shelf.setCoverUrl(coverUrl);
        } 
        // 否则根据单书ID获取封面
        else if (bookId != null) {
            AppEBook book = eBookBiz.getById(bookId);
            if (book == null) {
                throw new BusinessException(AppErrorCode.EBOOK_NOT_EXIST_CODE, AppErrorCode.EBOOK_NOT_EXIST_MSG);
            }
            
            if (StringUtils.isNotBlank(book.getCoverUrl())) {
                shelf.setCoverUrl(book.getCoverUrl());
            } else {
                throw new BusinessException(AppErrorCode.BOOK_COVER_URL_EMPTY_CODE, AppErrorCode.BOOK_COVER_URL_EMPTY_MSG);
            }
        } else {
            throw new BusinessException(AppErrorCode.COVER_URL_BOOKID_EMPTY_SHELF_CODE, AppErrorCode.COVER_URL_BOOKID_EMPTY_SHELF_MSG);
        }
        
        return this.updateById(shelf);
    }

    @Override
    public boolean addBooksToShelf(Integer shelfId, List<Integer> bookIds) throws BusinessException {
        if (shelfId == null || CollectionUtils.isEmpty(bookIds)) {
            throw new BusinessException(AppErrorCode.PARAMS_NOT_NULL_CODE, AppErrorCode.PARAMS_NOT_NULL_MSG);
        }
        
        // 检查书架是否存在
        if (!this.existsShelf(shelfId)) {
            throw new BusinessException(AppErrorCode.BOOKSHELF_NOT_EXIST_CODE, AppErrorCode.BOOKSHELF_NOT_EXIST_MSG);
        }

        List<AppEBook> appEBooks = eBookBiz.getBaseMapper().selectByIds(bookIds);
        if (CollectionUtils.isEmpty(appEBooks)) {
            throw new BusinessException(AppErrorCode.EBOOK_NOT_EXIST_CODE, AppErrorCode.EBOOK_NOT_EXIST_MSG);
        }
        List<Integer> existBookIds = eBookshelfBookRefService.listBookIds(List.of(shelfId));
        List<Integer> alreadyExistBookIds = bookIds.stream()
                .filter(existBookIds::contains)
                .toList();

        if (!alreadyExistBookIds.isEmpty()) {
            throw new BusinessException(AppErrorCode.BOOK_ALREADY_EXIST_CODE, AppErrorCode.BOOK_ALREADY_EXIST_MSG);
        }
        return eBookshelfBookRefService.saveBatch(shelfId, bookIds);
    }

    @Override
    public boolean removeBooksFromShelf(Integer shelfId, List<Integer> bookIds) throws BusinessException {
        if (shelfId == null) {
            throw new BusinessException(AppErrorCode.BOOKSHELF_ID_NOT_NULL_CODE, AppErrorCode.BOOKSHELF_ID_NOT_NULL_MSG);
        }
        
        // 检查书架是否存在
        if (!this.existsShelf(shelfId)) {
            throw new BusinessException(AppErrorCode.BOOKSHELF_NOT_EXIST_CODE, AppErrorCode.BOOKSHELF_NOT_EXIST_MSG);
        }
        // 删除关联关系
        return eBookshelfBookRefService.removeBatch(List.of(shelfId), bookIds);
    }

    @Override
    public boolean sortBooksInShelf(Integer shelfId, Map<Integer, Integer> bookSortMap) throws BusinessException {
        if (shelfId == null || bookSortMap == null || bookSortMap.isEmpty()) {
            throw new BusinessException(AppErrorCode.PARAMS_NOT_NULL_CODE, AppErrorCode.PARAMS_NOT_NULL_MSG);
        }
        
        // 检查书架是否存在
        if (!this.existsShelf(shelfId)) {
            throw new BusinessException(AppErrorCode.BOOKSHELF_NOT_EXIST_CODE, AppErrorCode.BOOKSHELF_NOT_EXIST_MSG);
        }
        
        // 更新排序
        return eBookshelfBookRefService.updateSort(shelfId, bookSortMap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean allowDownloadBatch(List<Integer> ids) throws BusinessException {
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }

        // 批量修改允许下载状态
        List<AppEBookshelf> updateList = ids.stream().map(id -> {
            AppEBookshelf bookshelf = new AppEBookshelf();
            bookshelf.setId(id);
            bookshelf.setAllowDownload(YesOrNoEnum.YES.getCode());
            return bookshelf;
        }).collect(Collectors.toList());

        return this.updateBatchById(updateList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableDownloadBatch(List<Integer> ids) throws BusinessException {
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }

        // 批量修改禁止下载状态
        List<AppEBookshelf> updateList = ids.stream().map(id -> {
            AppEBookshelf bookshelf = new AppEBookshelf();
            bookshelf.setId(id);
            bookshelf.setAllowDownload(YesOrNoEnum.NO.getCode());
            return bookshelf;
        }).collect(Collectors.toList());

        return this.updateBatchById(updateList);
    }

    @Override
    public int countBooks(Integer shelfId) throws BusinessException {
        if (shelfId == null) {
            throw new BusinessException(AppErrorCode.BOOKSHELF_ID_NOT_NULL_CODE, AppErrorCode.BOOKSHELF_ID_NOT_NULL_MSG);
        }

        return eBookshelfBookRefService.countBooks(shelfId);
    }

    /**
     * 批量统计书架书籍数量（高性能版本）
     * 一次查询获取多个书架的书籍数量，避免N+1查询问题
     *
     * @param shelfIds 书架ID列表
     * @return 书架ID到书籍数量的映射
     */
    @Override
    public Map<Integer, Integer> batchCountBooks(List<Integer> shelfIds) {
        if (CollectionUtils.isEmpty(shelfIds)) {
            return new HashMap<>();
        }

        try {
            // 批量查询书架书籍关联关系
            return eBookshelfBookRefService.batchCountBooks(shelfIds);
        } catch (Exception e) {
            log.error("批量统计书架书籍数量失败，书架IDs：{}", shelfIds, e);
            // 降级到逐个查询
            Map<Integer, Integer> result = new HashMap<>();
            for (Integer shelfId : shelfIds) {
                try {
                    result.put(shelfId, eBookshelfBookRefService.countBooks(shelfId));
                } catch (Exception ex) {
                    log.warn("统计书架{}书籍数量失败", shelfId, ex);
                    result.put(shelfId, 0);
                }
            }
            return result;
        }
    }
    
    @Override
    public List<AppEBookDTO> getFilteredBooksForShelf(Integer shelfId, 
                                                    String bookTitle, 
                                                    List<Integer> subjectIds, 
                                                    List<Integer> stageIds, 
                                                    List<Integer> categoryIds, 
                                                    List<Integer> textbookVersionIds, 
                                                    List<Integer> applicableGrades) throws BusinessException {
        if (shelfId == null) {
            throw new BusinessException(AppErrorCode.BOOKSHELF_ID_NOT_NULL_CODE, AppErrorCode.BOOKSHELF_ID_NOT_NULL_MSG);
        }
        
        // 获取书架下的单书ID列表
        List<Integer> bookIds = eBookshelfBookRefService.listBookIds(List.of(shelfId));
        
        if (CollectionUtils.isEmpty(bookIds)) {
            return List.of();
        }
        
        // 从bookIds中筛选符合条件的单书列表
        LambdaQueryWrapper<AppEBook> queryWrapper = new LambdaQueryWrapper<AppEBook>()
            .in(AppEBook::getId, bookIds)
            .eq(AppEBook::getLaunchStatus, YesOrNoEnum.YES.getCode())
            .like(StringUtils.isNotBlank(bookTitle), AppEBook::getBookTitle, bookTitle)
            .in(CollectionUtils.isNotEmpty(subjectIds), AppEBook::getSubjectId, subjectIds)
            .in(CollectionUtils.isNotEmpty(stageIds), AppEBook::getStageId, stageIds)
            .in(CollectionUtils.isNotEmpty(textbookVersionIds), AppEBook::getTextbookVersionId, textbookVersionIds);
        
        // 对于逗号分隔的字符串字段，使用自定义SQL处理
        if (CollectionUtils.isNotEmpty(categoryIds)) {
            queryWrapper.and(wrapper -> {
                for (Integer categoryId : categoryIds) {
                    wrapper.or().apply("FIND_IN_SET({0}, category_ids)", categoryId);
                }
            });
        }
        
        if (CollectionUtils.isNotEmpty(applicableGrades)) {
            queryWrapper.and(wrapper -> {
                for (Integer grade : applicableGrades) {
                    wrapper.or().apply("FIND_IN_SET({0}, applicable_grades)", grade);
                }
            });
        }
        
        List<AppEBook> filteredBooks = eBookBiz.getBaseMapper().selectList(queryWrapper);
        
        // 转换为DTO
        return filteredBooks.stream()
                .map(book -> {
                    AppEBookDTO dto = new AppEBookDTO();
                    BeanUtils.copyProperties(book, dto);
                    return dto;
                })
                .collect(Collectors.toList());
    }

    /**
     * 检查书架是否存在
     * 
     * @param id 书架ID
     * @return 是否存在
     */
    private boolean existsShelf(Integer id) {
        if (id == null) {
            return false;
        }
        return this.getById(id) != null;
    }

    @Override
    public Page<AppEBookshelfDTO> pageForH5(PageInfo<AppEBookshelfH5QueryBO> pageRequest) throws BusinessException {
        if (pageRequest.getData() == null) {
            pageRequest.setData(new AppEBookshelfH5QueryBO());
        }
        AppEBookshelfH5QueryBO query = pageRequest.getData();

        // 统一使用XML查询，支持所有查询条件和关联查询
        log.info("H5分页查询书架，storeId: {}", query.getStoreId());
        Page<AppEBookshelf> page = this.getBaseMapper().pageForH5(pageRequest.getPage(), query);

        // H5分页查询优化：只返回统计信息，不查询具体书籍列表
        return batchConvertToDTOForH5(page);
    }

    /**
     * H5分页查询专用：批量转换为DTO（只包含统计信息，不查询书籍列表）
     */
    private Page<AppEBookshelfDTO> batchConvertToDTOForH5(Page<AppEBookshelf> page) throws BusinessException {
        List<AppEBookshelf> shelves = page.getRecords();
        if (CollectionUtils.isEmpty(shelves)) {
            return new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        }

        // 提取书架ID列表
        List<Integer> shelfIds = shelves.stream()
                .map(AppEBookshelf::getId)
                .collect(Collectors.toList());

        // 批量查询书架书籍数量（只查询数量，不查询具体书籍）
        Map<Integer, Integer> shelfBookCountMap = batchCountBooks(shelfIds);

        // 组装DTO（H5优化版本：不包含books字段）
        List<AppEBookshelfDTO> dtoList = new ArrayList<>();
        for (AppEBookshelf shelf : shelves) {
            AppEBookshelfDTO dto = new AppEBookshelfDTO();
            BeanUtil.copyProperties(shelf, dto);

            // 只设置书籍数量，不设置books字段（H5分页查询不需要具体书籍列表）
            Integer bookCount = shelfBookCountMap.getOrDefault(shelf.getId(), 0);
            dto.setBookCount(bookCount);
            // 注意：不设置 books 字段，H5分页查询不需要具体书籍列表

            dtoList.add(dto);
        }

        Page<AppEBookshelfDTO> resultPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        resultPage.setRecords(dtoList);
        return resultPage;
    }

    /**
     * 批量转换为DTO（优化N+1查询）
     */
    private Page<AppEBookshelfDTO> batchConvertToDTO(Page<AppEBookshelf> page) throws BusinessException {
        List<AppEBookshelf> shelves = page.getRecords();
        if (CollectionUtils.isEmpty(shelves)) {
            return new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        }

        // 提取书架ID列表
        List<Integer> shelfIds = shelves.stream()
                .map(AppEBookshelf::getId)
                .collect(Collectors.toList());

        // 批量查询书架-书籍关联关系
        Map<Integer, List<Integer>> shelfBookIdsMap = eBookshelfBookRefBiz.batchListBookIds(shelfIds);

        // 收集所有书籍ID
        Set<Integer> allBookIds = shelfBookIdsMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toSet());

        // 批量查询书籍详情（不包含资源信息，提升性能）
        Map<Integer, AppEBookDTO> bookMap = new HashMap<>();
        if (!allBookIds.isEmpty()) {
            List<AppEBookDTO> books = eBookBiz.getDetailList(new ArrayList<>(allBookIds), false);
            bookMap = books.stream()
                    .collect(Collectors.toMap(AppEBookDTO::getId, book -> book));
        }

        // 组装DTO
        List<AppEBookshelfDTO> dtoList = new ArrayList<>();
        for (AppEBookshelf shelf : shelves) {
            AppEBookshelfDTO dto = new AppEBookshelfDTO();
            BeanUtil.copyProperties(shelf, dto);

            // 设置书架下的书籍列表
            List<Integer> bookIds = shelfBookIdsMap.get(shelf.getId());
            if (CollectionUtils.isNotEmpty(bookIds)) {
                List<AppEBookDTO> shelfBooks = bookIds.stream()
                        .map(bookMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                dto.setBooks(shelfBooks);
            } else {
                dto.setBooks(new ArrayList<>());
            }

            dtoList.add(dto);
        }

        Page<AppEBookshelfDTO> resultPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        resultPage.setRecords(dtoList);
        return resultPage;
    }

    /**
     * 获取书架中的单书列表
     *
     * @param shelfId 书架ID
     * @return 单书列表
     */
    private List<AppEBookDTO> getShelfBooks(Integer shelfId) {
        try {
            // 通过书架-单书关联表查询单书ID列表
            LambdaQueryWrapper<AppEBookshelfBookRef> refWrapper = new LambdaQueryWrapper<>();
            refWrapper.eq(AppEBookshelfBookRef::getShelfId, shelfId)
                    .orderByAsc(AppEBookshelfBookRef::getSortNum);

            List<AppEBookshelfBookRef> refs = eBookshelfBookRefBiz.list(refWrapper);

            if (CollectionUtils.isEmpty(refs)) {
                return new ArrayList<>();
            }

            // 提取单书ID列表
            List<Integer> bookIds = refs.stream()
                    .map(AppEBookshelfBookRef::getBookId)
                    .collect(Collectors.toList());

            // 查询单书详情
            LambdaQueryWrapper<AppEBook> bookWrapper = new LambdaQueryWrapper<>();
            bookWrapper.in(AppEBook::getId, bookIds)
                    .eq(AppEBook::getLaunchStatus, YesOrNoEnum.YES.getCode())
                    .eq(AppEBook::getIsDeleted, YesOrNoEnum.NO.getCode());

            List<AppEBook> books = eBookBiz.list(bookWrapper);

            if (CollectionUtils.isEmpty(books)) {
                return new ArrayList<>();
            }

            // 按照关联表中的排序顺序重新排列
            Map<Integer, Integer> sortMap = refs.stream()
                    .collect(Collectors.toMap(
                            AppEBookshelfBookRef::getBookId,
                            AppEBookshelfBookRef::getSortNum,
                            (existing, replacement) -> existing
                    ));

            // 转换为DTO并排序
            return books.stream()
                    .sorted(Comparator.comparing(book -> sortMap.getOrDefault(book.getId(), Integer.MAX_VALUE)))
                    .map(book -> {
                        AppEBookDTO dto = new AppEBookDTO();
                        BeanUtil.copyProperties(book, dto);

                        // 转换逗号分隔的字段为列表
                        eBookHelper.resolveCodesToNames(book, dto);

                        return dto;
                    })
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询书架单书列表失败，shelfId: {}, 错误: {}", shelfId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}