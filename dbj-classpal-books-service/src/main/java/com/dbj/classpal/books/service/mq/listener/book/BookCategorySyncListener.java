package com.dbj.classpal.books.service.mq.listener.book;

import com.dbj.classpal.books.common.constant.QueueConstant;
import com.dbj.classpal.books.service.biz.books.IBooksCategoryBiz;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.mq.annotation.ExtractHeader;
import com.dbj.classpal.framework.mq.entity.RabbitmqEntity;
import com.rabbitmq.client.Channel;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ExcelFileListener
 * @date 2023-10-27 10:42
 **/
@Component
@Slf4j
public class BookCategorySyncListener {

    @Resource
    private IBooksCategoryBiz booksCategoryBiz;

    @ExtractHeader
    @RabbitListener(queues = {QueueConstant.BOOK_CATEGORY_SYNC_QUEUE})
    public void materialFileHandler(RabbitmqEntity rabbitmqEntity, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag, Message message) throws IOException, BusinessException {
        try {
            booksCategoryBiz.sync();
        }finally {
            channel.basicAck(tag, false);  // 拒绝并重新入队
        }
    }
}
