package com.dbj.classpal.books.service.strategy.poem;

import com.dbj.classpal.books.client.dto.poem.AncientPoemRelateDTO;
import com.dbj.classpal.books.service.entity.poem.AncientPoem;
import com.dbj.classpal.books.service.entity.poem.AncientPoemBusinessRef;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-04-26 14:27
 */
public interface IAncientPoemReleateStrategy {
    /**
     * 获取古诗文关联业务关系
     *
     * @param businessType
     * @param ancientPoemBusinessRefList   业务id
     * @param ancientPoem
     * @return 古诗文关联业务关系
     */
    List<AncientPoemRelateDTO> getAncientPoemRelate(Integer businessType, List<AncientPoemBusinessRef> ancientPoemBusinessRefList, AncientPoem ancientPoem);
}
