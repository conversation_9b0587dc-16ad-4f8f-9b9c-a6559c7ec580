package com.dbj.classpal.books.service.api.client.books;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.books.client.api.books.AdminBooksRankApi;
import com.dbj.classpal.books.client.bo.books.BooksRankInfoApiBO;
import com.dbj.classpal.books.client.dto.books.BooksRankInTreeDTO;
import com.dbj.classpal.books.client.dto.books.BooksRankInfoApiDTO;
import com.dbj.classpal.books.client.bo.books.BooksRankInfoUpdForceApiBO;
import com.dbj.classpal.books.client.dto.books.BooksRankInfoDetailDTO;
import com.dbj.classpal.books.client.dto.books.BooksTreeDTO;
import com.dbj.classpal.books.client.dto.books.app.BooksRankInCodesContentsTreeAppDTO;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.books.common.enums.books.RankClassifyEnum;
import com.dbj.classpal.books.service.biz.books.IBooksInfoBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankClassifyBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInCodesContentsBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInfoBiz;
import com.dbj.classpal.books.service.entity.books.BooksInfo;
import com.dbj.classpal.books.service.entity.books.BooksRankClassify;
import com.dbj.classpal.books.service.entity.books.BooksRankInfo;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className AdminBooksRankApiImpl
 * @description
 * @date 2025-04-15 09:35
 **/
@RestController
public class AdminBooksRankApiImpl implements AdminBooksRankApi {

    @Resource
    private IBooksRankInfoBiz bookRankBiz;
    @Resource
    private IBooksInfoBiz booksInfoBiz;
    @Resource
    private IBooksRankClassifyBiz booksRankClassifyBiz;
    @Resource
    private IBooksRankInCodesContentsBiz booksRankInCodesContentsBiz;

    @Override
    public RestResponse<List<BooksRankInfoApiDTO>> list(BooksRankInfoApiBO bookRankInfoApiBO) throws BusinessException {

        List<BooksRankInfo> booksRankInfoList =  bookRankBiz.lambdaQuery().eq(BooksRankInfo::getBookId, bookRankInfoApiBO.getBookId())
                .eq(BooksRankInfo::getStatus, YesOrNoEnum.YES.getCode()).orderByAsc(BooksRankInfo::getSerialNo).list();
        List<BooksRankInfoApiDTO> booksRankInfoDTOList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(booksRankInfoList)){
            booksRankInfoDTOList = BeanUtil.copyToList(booksRankInfoList,BooksRankInfoApiDTO.class);
        }
        return RestResponse.success(booksRankInfoDTOList);
    }

    @Override
    public RestResponse<Boolean> updateForcePromotionUrl(BooksRankInfoUpdForceApiBO booksRankInfoUpdForceApiDTO) throws BusinessException {
        bookRankBiz.lambdaUpdate().eq(BooksRankInfo::getId, booksRankInfoUpdForceApiDTO.getId()).set(BooksRankInfo::getForcePromotionUrl, booksRankInfoUpdForceApiDTO.getForcePromotionUrl()).update();
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<BooksRankInfoDetailDTO> detail(Integer id) throws BusinessException {
        BooksRankInfo booksRankInfo = bookRankBiz.getById(id);
        if(booksRankInfo == null){
            throw new BusinessException(AppErrorCode.BOOK_RANK_INFO_NOT_EXIST_CODE,AppErrorCode.BOOK_RANK_INFO_NOT_EXIST_MSG);
        }
        BooksRankInfoDetailDTO booksRankInfoDetailDTO = new BooksRankInfoDetailDTO();
        BeanUtil.copyProperties(booksRankInfo, booksRankInfoDetailDTO);
        return RestResponse.success(booksRankInfoDetailDTO);
    }

    @Override
    public RestResponse<BooksTreeDTO> tree(Integer booksId) throws BusinessException {
        //获取所有图书数据
        BooksInfo booksInfo = booksInfoBiz.getById(booksId);
        if(booksInfo == null){
            throw new BusinessException(AppErrorCode.BOOK_INFO_NOT_EXIST_CODE,AppErrorCode.BOOK_INFO_NOT_EXIST_MSG);
        }
        BooksTreeDTO booksTreeDTO = new BooksTreeDTO();
        booksTreeDTO.setBookName(booksInfo.getBookName());
        List<BooksRankInfo> booksRankInfoList = bookRankBiz.lambdaQuery().eq(BooksRankInfo::getBookId, booksId).eq(BooksRankInfo::getStatus, YesOrNoEnum.YES.getCode()).orderByAsc(BooksRankInfo::getSerialNo).list();
        //获取所有书内码数据
        if(CollectionUtils.isEmpty(booksRankInfoList)){
            throw new BusinessException(AppErrorCode.BOOK_RANK_INFO_NOT_EXIST_CODE,AppErrorCode.BOOK_RANK_INFO_NOT_EXIST_MSG);
        }
        List<Integer> rankIds = booksRankInfoList.stream().map(BooksRankInfo::getId).collect(Collectors.toList());
        List<BooksRankClassify> booksRankClassifyList = booksRankClassifyBiz.lambdaQuery().in(BooksRankClassify::getRankId, rankIds).eq(BooksRankClassify::getType, RankClassifyEnum.BOOK_IN_CODES.getCode()).list();
        Map<Integer, Integer> booksRankClassifyMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(booksRankClassifyList)){
            booksRankClassifyMap = booksRankClassifyList.stream().collect(Collectors.toMap(BooksRankClassify::getRankId,BooksRankClassify::getId, (v1, v2) -> v1));
        }
        List<BooksRankInTreeDTO> booksRankInTreeDTOList = new ArrayList<>();
        for (BooksRankInfo booksRankInfo : booksRankInfoList) {
            BooksRankInTreeDTO booksRankInTreeDTO = new BooksRankInTreeDTO();
            BeanUtil.copyProperties(booksRankInfo, booksRankInTreeDTO);
            booksRankInTreeDTO.setProductItemName(booksRankInfo.getProductItemName());
            booksRankInTreeDTO.setNewPrintCodeUrl(booksRankInfo.getNewPrintCodeUrl());
            booksRankInTreeDTO.setH5PageUrl(booksRankInfo.getH5PageUrl());
            //获取所有书内码分类数据
            Integer booksRankClassifyId = booksRankClassifyMap.get(booksRankInfo.getId());
            if(booksRankClassifyId != null){
                booksRankInTreeDTO.setBooksRankInCodesContentsTreeDTOList(booksRankInCodesContentsBiz.getTree(booksRankClassifyId));
            }
            booksRankInTreeDTOList.add(booksRankInTreeDTO);
        }
        booksTreeDTO.setBooksRankInTreeDTOList(booksRankInTreeDTOList);
        return RestResponse.success(booksTreeDTO);
    }
}
