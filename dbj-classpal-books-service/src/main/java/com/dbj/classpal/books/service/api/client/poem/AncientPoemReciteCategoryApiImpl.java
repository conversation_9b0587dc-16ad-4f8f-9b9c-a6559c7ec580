package com.dbj.classpal.books.service.api.client.poem;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.books.client.api.poem.AncientPoemReciteCategoryApi;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCategorySaveBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCategorySortBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCategoryUpdateBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemReciteCategoryDTO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemReciteCategoryDetailDTO;
import com.dbj.classpal.books.client.enums.poem.AncientPoemReciteCategoryTypeEnum;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemReciteCategoryBiz;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemReciteCollectionBiz;
import com.dbj.classpal.books.service.entity.poem.AncientPoemReciteCategory;
import com.dbj.classpal.books.service.entity.poem.AncientPoemReciteCollection;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.dbj.classpal.books.common.constant.AppErrorCode.ANCIENT_POEM_RECITE_CATEGORY_EXIST_COLLECTION_CODE;
import static com.dbj.classpal.books.common.constant.AppErrorCode.ANCIENT_POEM_RECITE_CATEGORY_EXIST_COLLECTION_MSG;
import static com.dbj.classpal.books.common.constant.AppErrorCode.ANCIENT_POEM_RECITE_CATEGORY_NOT_EXIST_CODE;
import static com.dbj.classpal.books.common.constant.AppErrorCode.ANCIENT_POEM_RECITE_CATEGORY_NOT_EXIST_MSG;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className AncientPoemReciteCategoryApiImpl
 * @description
 * @date 2025-05-26 09:21
 **/
@RestController
public class AncientPoemReciteCategoryApiImpl implements AncientPoemReciteCategoryApi {

    @Resource
    private IAncientPoemReciteCategoryBiz ancientPoemReciteCategoryBiz;
    @Resource
    private IAncientPoemReciteCollectionBiz ancientPoemReciteCollectionBiz;


    @Override
    public RestResponse<List<AncientPoemReciteCategoryDTO>> listAncientPoemReciteCategory() {
        List<AncientPoemReciteCategory> ancientPoemReciteCategoryList = ancientPoemReciteCategoryBiz.lambdaQuery().orderByAsc(AncientPoemReciteCategory::getSort).list();
        if(CollectionUtils.isEmpty(ancientPoemReciteCategoryList)){
            return RestResponse.success(null);
        }
        List<AncientPoemReciteCategoryDTO> ancientPoemReciteCategoryDTOList = BeanUtil.copyToList(ancientPoemReciteCategoryList,AncientPoemReciteCategoryDTO.class);
        return RestResponse.success(ancientPoemReciteCategoryDTOList);
    }

    @Override
    public RestResponse<AncientPoemReciteCategoryDetailDTO> getAncientPoemReciteCategory(Integer id) throws BusinessException {

        AncientPoemReciteCategory ancientPoemReciteCategory = ancientPoemReciteCategoryBiz.getById(id);
        if(ancientPoemReciteCategory == null){
            throw new BusinessException(ANCIENT_POEM_RECITE_CATEGORY_NOT_EXIST_CODE,ANCIENT_POEM_RECITE_CATEGORY_NOT_EXIST_MSG);

        }
        AncientPoemReciteCategoryDetailDTO ancientPoemReciteCategoryDetailDTO = new AncientPoemReciteCategoryDetailDTO();
        BeanUtil.copyProperties(ancientPoemReciteCategory,ancientPoemReciteCategoryDetailDTO);
        return RestResponse.success(ancientPoemReciteCategoryDetailDTO);
    }

    @Override
    public RestResponse<Boolean> save(AncientPoemReciteCategorySaveBO ancientPoemReciteCategorySaveBO) {
        //名称重复判断
        AncientPoemReciteCategory ancientPoemReciteCategory = BeanUtil.copyProperties(ancientPoemReciteCategorySaveBO,AncientPoemReciteCategory.class);
        ancientPoemReciteCategory.setType(AncientPoemReciteCategoryTypeEnum.OTHER.getCode());
        ancientPoemReciteCategoryBiz.save(ancientPoemReciteCategory);
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> update(AncientPoemReciteCategoryUpdateBO ancientPoemReciteCategoryUpdateBO) throws BusinessException {
        AncientPoemReciteCategory ancientPoemReciteCategory = ancientPoemReciteCategoryBiz.getById(ancientPoemReciteCategoryUpdateBO.getId());
        if(ancientPoemReciteCategory == null){
            throw new BusinessException(ANCIENT_POEM_RECITE_CATEGORY_NOT_EXIST_CODE,ANCIENT_POEM_RECITE_CATEGORY_NOT_EXIST_MSG);

        }
        ancientPoemReciteCategoryBiz.updateById(BeanUtil.copyProperties(ancientPoemReciteCategoryUpdateBO,AncientPoemReciteCategory.class));
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> delete(Integer id) throws BusinessException {
        // 判断是否存在古诗文明细
        List<AncientPoemReciteCollection> ancientPoemReciteCollectionList = ancientPoemReciteCollectionBiz.lambdaQuery().eq(AncientPoemReciteCollection::getCategoryId,id).list();
        if(CollectionUtils.isNotEmpty(ancientPoemReciteCollectionList)){
            throw new BusinessException(ANCIENT_POEM_RECITE_CATEGORY_EXIST_COLLECTION_CODE,ANCIENT_POEM_RECITE_CATEGORY_EXIST_COLLECTION_MSG);
        }
        ancientPoemReciteCategoryBiz.removeById(id);
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> sort(List<AncientPoemReciteCategorySortBO> ancientPoemReciteCategorySortBOs) throws BusinessException {
        List<AncientPoemReciteCategory> ancientPoemReciteCategoryList = BeanUtil.copyToList(ancientPoemReciteCategorySortBOs,AncientPoemReciteCategory.class);
        ancientPoemReciteCategoryBiz.updateBatchById(ancientPoemReciteCategoryList);
        return RestResponse.success(true);
    }
}
