package com.dbj.classpal.books.service.entity.ebooks;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * PDF处理任务实体
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_ebook_pdf_task")
@Schema(description = "PDF处理任务")
public class AppEBookPdfTask extends BizEntity {


    @Schema(description = "任务ID（UUID）")
    private String taskId;

    @Schema(description = "业务ID")
    private Integer businessId;

    @Schema(description = "业务唯一key")
    private String businessKey;

    @Schema(description = "水印业务类型")
    private Integer waterMarkBusinessType;

    @Schema(description = "PDF文件URL")
    private String pdfUrl;

    @Schema(description = "PDF文件名")
    private String pdfName;

    @Schema(description = "任务状态：0-处理中，1-成功，2-失败")
    private Integer status;

    @Schema(description = "封面URL")
    private String coverUrl;

    @Schema(description = "错误信息")
    private String errorMsg;

    @Schema(description = "处理结果JSON")
    private String resultJson;
}
