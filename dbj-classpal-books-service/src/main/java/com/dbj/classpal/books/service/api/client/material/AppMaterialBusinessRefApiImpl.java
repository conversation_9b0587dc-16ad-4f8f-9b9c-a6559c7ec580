package com.dbj.classpal.books.service.api.client.material;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.dbj.classpal.books.client.api.material.AppMaterialBusinessRefApi;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.material.AppMaterialBusinessRefQueryCommonApiBO;
import com.dbj.classpal.books.client.bo.material.AppMaterialBusinessRefReNameApiBO;
import com.dbj.classpal.books.client.bo.material.AppMaterialBusinessRefSaveApiBO;
import com.dbj.classpal.books.client.dto.books.BooksRefDirectApiDTO;
import com.dbj.classpal.books.client.dto.material.*;
import com.dbj.classpal.books.common.bo.common.CommonIdBO;
import com.dbj.classpal.books.common.bo.common.CommonIdsBO;
import com.dbj.classpal.books.common.bo.material.AppMaterialBusinessRefQueryCommonBO;
import com.dbj.classpal.books.common.bo.material.AppMaterialBusinessRefReNameBO;
import com.dbj.classpal.books.common.bo.material.AppMaterialBusinessRefSaveBO;
import com.dbj.classpal.books.common.dto.books.BooksRefDirectDTO;
import com.dbj.classpal.books.common.dto.material.*;
import com.dbj.classpal.books.client.enums.MaterialTypeEnum;
import com.dbj.classpal.books.service.service.material.IAppMaterialBusinessRefService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApiImpl
 * Date:     2025-04-10 11:53:30
 * Description: 表名： ,描述： 表
 */
@RestController
public class AppMaterialBusinessRefApiImpl implements AppMaterialBusinessRefApi {

    @Resource
    private IAppMaterialBusinessRefService businessRefService;

    @Override
    public RestResponse<List<AppMaterialBusinessRefMaterialQueryApiDTO>> beRefBusinessList(AppMaterialBusinessRefQueryCommonApiBO bo) throws BusinessException {
        AppMaterialBusinessRefQueryCommonBO queryBo = new AppMaterialBusinessRefQueryCommonBO();
        BeanUtil.copyProperties(bo, queryBo);
        List<AppMaterialBusinessRefDTO> appMaterialBusinessRefDTOList =  businessRefService.beRefBusinessList(queryBo);
        List<AppMaterialBusinessRefMaterialQueryApiDTO> appMaterialBusinessRefMaterialQueryApiDTOList = Lists.newArrayList();
        if (ObjectUtils.isNotEmpty(appMaterialBusinessRefDTOList)){
            appMaterialBusinessRefMaterialQueryApiDTOList = BeanUtil.copyToList(appMaterialBusinessRefDTOList, AppMaterialBusinessRefMaterialQueryApiDTO.class);
        }
        return RestResponse.success(appMaterialBusinessRefMaterialQueryApiDTOList);
    }

    @Override
    public RestResponse<List<AppMaterialBusinessRefMaterialQueryApiDTO>> refBusinessList(AppMaterialBusinessRefQueryCommonApiBO bo) throws BusinessException {
        AppMaterialBusinessRefQueryCommonBO queryBo = new AppMaterialBusinessRefQueryCommonBO();
        BeanUtil.copyProperties(bo, queryBo);
        List<AppMaterialBusinessRefDTO> appMaterialBusinessRefDTOList =  businessRefService.refBusinessList(queryBo);

        List<AppMaterialBusinessRefMaterialQueryApiDTO> appMaterialBusinessRefMaterialQueryApiDTOList = Lists.newArrayList();
        if (ObjectUtils.isNotEmpty(appMaterialBusinessRefDTOList)){
            appMaterialBusinessRefMaterialQueryApiDTOList = BeanUtil.copyToList(appMaterialBusinessRefDTOList, AppMaterialBusinessRefMaterialQueryApiDTO.class);
        }
        return RestResponse.success(appMaterialBusinessRefMaterialQueryApiDTOList);
    }

    @Override
    public RestResponse<List<AppMaterialBusinessRefTypeCountApiDTO>> getMaterialBusinessRefTypeCount(CommonIdApiBO bo) throws BusinessException {
        CommonIdBO commonIdBO = new CommonIdBO();
        BeanUtil.copyProperties(bo, commonIdBO);
        List<AppMaterialBusinessRefTypeCountDTO> materialBusinessRefTypeCount = businessRefService.getMaterialBusinessRefTypeCount(commonIdBO).stream().peek(d -> {
            if (d.getAppMaterialType() != null){
                d.setAppMaterialTypeStr(MaterialTypeEnum.getByCode(d.getAppMaterialType()).getDesc());
            }
        }).collect(Collectors.toList());
        List<AppMaterialBusinessRefTypeCountApiDTO> apiDTOS = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(materialBusinessRefTypeCount)){
            apiDTOS = BeanUtil.copyToList(materialBusinessRefTypeCount,AppMaterialBusinessRefTypeCountApiDTO.class);
        }
        return RestResponse.success(apiDTOS);
    }

    @Override
    public RestResponse<Boolean> saveAppMaterialBusinessRef(AppMaterialBusinessRefSaveApiBO bo) throws BusinessException {
        AppMaterialBusinessRefSaveBO businessRefSaveBO = new AppMaterialBusinessRefSaveBO();
        BeanUtil.copyProperties(bo, businessRefSaveBO);
        if (!businessRefService.saveAppMaterialBusinessRef(businessRefSaveBO)){
            throw new BusinessException(APP_ALBUM_ELEMENTS_REF_SAVE_FAIL_CODE,APP_ALBUM_ELEMENTS_REF_SAVE_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> reNameAppMaterialBusinessRef(AppMaterialBusinessRefReNameApiBO bo) throws BusinessException {
        AppMaterialBusinessRefReNameBO businessRefSaveBO = new AppMaterialBusinessRefReNameBO();
        BeanUtil.copyProperties(bo, businessRefSaveBO);
        if (!businessRefService.reNameAppMaterialBusinessRef(businessRefSaveBO)){
            throw new BusinessException(APP_ALBUM_ELEMENTS_REF_RENAME_FAIL_CODE,APP_ALBUM_ELEMENTS_REF_RENAME_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> removeAppMaterialBusinessRef(CommonIdApiBO bo) throws BusinessException {
        CommonIdBO commonIdBO = new CommonIdBO();
        BeanUtil.copyProperties(bo, commonIdBO);
        if (!businessRefService.removeAppMaterialBusinessRef(commonIdBO)){
            throw new BusinessException(APP_ALBUM_ELEMENTS_REF_REMOVE_FAIL_CODE,APP_ALBUM_ELEMENTS_REF_REMOVE_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> removeBatchAppMaterialBusinessRef(CommonIdsApiBO bo) throws BusinessException {
        CommonIdsBO commonIdBO = new CommonIdsBO();
        BeanUtil.copyProperties(bo, commonIdBO);
        if (!businessRefService.removeBatchAppMaterialBusinessRef(commonIdBO)){
            throw new BusinessException(APP_ALBUM_ELEMENTS_REF_REMOVE_FAIL_CODE,APP_ALBUM_ELEMENTS_REF_REMOVE_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> changeAppMaterialBusinessRefOrderNum(CommonIdsApiBO bo) throws BusinessException {
        CommonIdsBO changeOrderNumBO = new CommonIdsBO();
        BeanUtil.copyProperties(bo, changeOrderNumBO);
        if (!businessRefService.changeAppMaterialBusinessRefOrderNum(changeOrderNumBO)){
            throw new BusinessException(APP_ALBUM_ELEMENTS_REF_ORDER_FAIL_CODE,APP_ALBUM_ELEMENTS_REF_ORDER_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<AppMaterialBusinessRefAlbumDirectApiDTO> getAppMaterialBusinessRefAlbum(CommonIdApiBO bo) throws BusinessException {
        CommonIdBO commonIdBO = new CommonIdBO();
        BeanUtil.copyProperties(bo, commonIdBO);
        AppMaterialBusinessRefAlbumDirectDTO refAlbum = businessRefService.getAppMaterialBusinessRefAlbum(commonIdBO);
        AppMaterialBusinessRefAlbumDirectApiDTO directApiDTO = new AppMaterialBusinessRefAlbumDirectApiDTO();
        BeanUtil.copyProperties(refAlbum, directApiDTO);
        return RestResponse.success(directApiDTO);
    }

    @Override
    public RestResponse<BooksRefDirectApiDTO> getAppMaterialBusinessRefBooks(CommonIdApiBO bo) throws BusinessException {
        CommonIdBO commonIdBO = new CommonIdBO();
        BeanUtil.copyProperties(bo, commonIdBO);
        BooksRefDirectDTO refBooks = businessRefService.getAppMaterialBusinessRefBooks(commonIdBO);
        BooksRefDirectApiDTO directApiDTO = new BooksRefDirectApiDTO();
        BeanUtil.copyProperties(refBooks, directApiDTO);
        return RestResponse.success(directApiDTO);
    }

    @Override
    public RestResponse<AppMaterialBusinessRefQuestionDirectApiDTO> getAppMaterialBusinessRefQuestion(CommonIdApiBO bo) throws BusinessException {
        CommonIdBO commonIdBO = new CommonIdBO();
        BeanUtil.copyProperties(bo, commonIdBO);
        AppMaterialBusinessRefQuestionDirectDTO refQuestion = businessRefService.getAppMaterialBusinessRefQuestion(commonIdBO);
        AppMaterialBusinessRefQuestionDirectApiDTO directApiDTO = new AppMaterialBusinessRefQuestionDirectApiDTO();
        BeanUtil.copyProperties(refQuestion, directApiDTO);
        return RestResponse.success(directApiDTO);
    }
}
