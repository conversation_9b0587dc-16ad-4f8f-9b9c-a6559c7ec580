package com.dbj.classpal.books.service.entity.poem;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 古诗背诵评测得分
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ancient_poem_recite_app_user_assessment_scores")
@Tag(name="AncientPoemReciteAppUserAssessmentScores对象", description="古诗背诵评测得分")
public class AncientPoemReciteAppUserAssessmentScores extends BizEntity implements Serializable {




    @Schema(description = "关联用户ID")
    @TableField("app_user_id")
    private Integer appUserId;

    @Schema(description = "关联古诗ID")
    @TableField("poem_id")
    private Integer poemId;

    @Schema(description = "总分（0-100）")
    @TableField("total_score")
    private BigDecimal totalScore;

    @Schema(description = "流利度得分（0-20）")
    @TableField("fluency_score")
    private BigDecimal fluencyScore;

    @Schema(description = "完整度得分（0-20）")
    @TableField("completeness_score")
    private BigDecimal completenessScore;

    @Schema(description = "韵律得分（0-20）")
    @TableField("rhythm_score")
    private BigDecimal rhythmScore;

    @Schema(description = "声调得分（0-20）")
    @TableField("tone_score")
    private BigDecimal toneScore;

    @Schema(description = "发音得分（0-20）")
    @TableField("pronunciation_score")
    private BigDecimal pronunciationScore;

    @Schema(description = "评测时间")
    @TableField("assessment_time")
    private LocalDateTime assessmentTime;

    @Schema(description = "评语")
    @TableField("comment")
    private String comment;

    @Schema(description = "内容")
    @TableField("content")
    private String content;


}
