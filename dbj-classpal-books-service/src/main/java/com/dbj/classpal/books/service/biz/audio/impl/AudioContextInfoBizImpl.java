package com.dbj.classpal.books.service.biz.audio.impl;

import com.dbj.classpal.books.common.dto.audio.AudioHintRefNumDTO;
import com.dbj.classpal.books.service.biz.audio.IAudioContextInfoBiz;
import com.dbj.classpal.books.service.entity.audio.AudioContextInfo;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.service.mapper.audio.AudioContextInfoMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 音频文本详情 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Service
public class AudioContextInfoBizImpl extends ServiceImpl<AudioContextInfoMapper, AudioContextInfo> implements IAudioContextInfoBiz {

    @Override
    public List<AudioHintRefNumDTO> statRefNum(List<Integer> audioHintMusicIds) {
        return baseMapper.statRefNum(audioHintMusicIds);
    }
}
