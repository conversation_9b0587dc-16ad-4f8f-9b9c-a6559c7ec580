package com.dbj.classpal.books.service.biz.audio.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.service.biz.audio.IAudioContextHintMusicBiz;
import com.dbj.classpal.books.service.entity.audio.AudioContextHintMusic;
import com.dbj.classpal.books.service.mapper.audio.AudioContextHintMusicMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 音频文本提示音 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Service
public class AudioContextHintMusicBizImpl extends ServiceImpl<AudioContextHintMusicMapper, AudioContextHintMusic> implements IAudioContextHintMusicBiz {

    @Autowired
    private AudioContextHintMusicMapper audioContextHintMusicMapper;

    @Override
    public int deleteByIds(List<Integer> outCountIds) {
        return audioContextHintMusicMapper.deleteByIds(outCountIds);
    }
}
