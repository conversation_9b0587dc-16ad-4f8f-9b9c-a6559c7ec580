package com.dbj.classpal.books.service.entity.ebooks;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 书城-书架关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_ebookstore_shelf_ref")
@Tag(name="AppEBookstoreShelfRef", description="书城-书架关联表")
public class AppEBookstoreShelfRef extends BizEntity implements Serializable {

    @Schema(description = "书城ID")
    private Integer storeId;

    @Schema(description = "书架ID")
    private Integer shelfId;

    @Schema(description = "排序序号")
    private Integer sortNum;
} 