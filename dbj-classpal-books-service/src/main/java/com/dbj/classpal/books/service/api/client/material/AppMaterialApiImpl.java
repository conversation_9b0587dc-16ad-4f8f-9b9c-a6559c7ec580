package com.dbj.classpal.books.service.api.client.material;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.material.AppMaterialApi;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.material.*;
import com.dbj.classpal.books.client.dto.material.AppMaterialQueryApiDTO;
import com.dbj.classpal.books.client.dto.material.AppMaterialQueryDicTreeApiDTO;
import com.dbj.classpal.books.client.dto.material.AppMaterialQueryRootApiDTO;
import com.dbj.classpal.books.client.dto.material.AppMaterialStatisticsSizeApiDTO;
import com.dbj.classpal.books.common.bo.common.CommonIdBO;
import com.dbj.classpal.books.common.bo.material.*;
import com.dbj.classpal.books.common.dto.material.AppMaterialQueryDTO;
import com.dbj.classpal.books.common.dto.material.AppMaterialQueryDicTreeDTO;
import com.dbj.classpal.books.common.dto.material.AppMaterialStatisticsSizeDTO;
import com.dbj.classpal.books.client.enums.FileTypeEnum;
import com.dbj.classpal.books.client.enums.MaterialTypeEnum;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBiz;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBusinessRefBiz;
import com.dbj.classpal.books.service.entity.material.AppMaterial;
import com.dbj.classpal.books.service.entity.material.AppMaterialBusinessRef;
import com.dbj.classpal.books.service.service.material.IAppMaterialService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;
import static com.dbj.classpal.books.common.enums.IsRootEnum.IS_ROOT_YES;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApiImpl
 * Date:     2025-04-10 11:53:30
 * Description: 表名： ,描述： 表
 */
@RestController
public class AppMaterialApiImpl implements AppMaterialApi {

    @Resource
    private IAppMaterialService service;
    @Resource
    private IAppMaterialBiz materialBiz;
    @Resource
    private IAppMaterialBusinessRefBiz materialBusinessRefBiz;

    @Override
    public RestResponse<Page<AppMaterialQueryApiDTO>> pageInfo(PageInfo<AppMaterialQueryApiBO> pageRequest) {
        // 1. 转换查询条件
        AppMaterialQueryBO queryBO = new AppMaterialQueryBO();
        BeanUtil.copyProperties(pageRequest.getData(), queryBO);

        PageInfo<AppMaterialQueryBO> servicePageRequest = new PageInfo<>();
        servicePageRequest.setPageNum(pageRequest.getPageNum());
        servicePageRequest.setPageSize(pageRequest.getPageSize());
        servicePageRequest.setData(queryBO);
        servicePageRequest.setOrders(pageRequest.getOrders());
        // 2. 调用Service
        Page<AppMaterialQueryDTO> page = service.pageInfo(servicePageRequest);

        Set<Integer> idSet = page.getRecords().stream().map(d -> d.getId()).collect(Collectors.toSet());
        Map<Integer, Integer> refMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(idSet)){
            refMap = materialBusinessRefBiz.lambdaQuery().in(AppMaterialBusinessRef::getAppMaterialId, idSet).list().stream().collect(Collectors.groupingBy(
                    AppMaterialBusinessRef::getAppMaterialId,
                    Collectors.collectingAndThen(
                            Collectors.counting(),
                            Long::intValue
                    )
            ));

        }
        // 3. 转换返回结果
        Map<Integer, Integer> finalRefMap = refMap;
        return RestResponse.success((Page<AppMaterialQueryApiDTO>) page.convert(vo -> {
            AppMaterialQueryApiDTO dto = new AppMaterialQueryApiDTO();
            BeanUtil.copyProperties(vo, dto);
            if (dto.getMaterialType() != null) {
                if (dto.getMaterialType().equals(MaterialTypeEnum.FILE_TYPE_DOC.getCode())){
                    dto.setMaterialTypeStr(MaterialTypeEnum.getByCode(dto.getMaterialType()).getDesc()+"("+dto.getMaterialExtension()+")");
                    FileTypeEnum fileTypeEnum = FileTypeEnum.getByCode(dto.getMaterialExtension());
                    if (fileTypeEnum != null){
                        dto.setMaterialIcon(fileTypeEnum.getIcon());
                    }
                }else{
                    dto.setMaterialTypeStr(MaterialTypeEnum.getByCode(dto.getMaterialType()).getDesc());
                    dto.setMaterialIcon(MaterialTypeEnum.getByCode(dto.getMaterialType()).getIcon());
                }
            }
            if(StringUtils.isNotEmpty(queryBO.getMaterialName())){
                dto.setMaterialDir(service.getMaterialParentsNames(dto.getId()));
            }
            if (finalRefMap.containsKey(dto.getId())){
                dto.setIsRef(true);
            }else{
                dto.setIsRef(false);
            }
            return dto;
        }));
    }

    @Override
    public RestResponse<AppMaterialQueryRootApiDTO> getRoot() {
        List<AppMaterial> rootList = materialBiz.lambdaQuery().eq(AppMaterial::getIsRoot, IS_ROOT_YES.getCode()).list();
        AppMaterialQueryRootApiDTO appMaterialQueryRootApiDTO = new AppMaterialQueryRootApiDTO();
        if(CollectionUtils.isNotEmpty(rootList)){
            BeanUtil.copyProperties(rootList.get(0), appMaterialQueryRootApiDTO);
        }
        return RestResponse.success(appMaterialQueryRootApiDTO);
    }

    @Override
    public RestResponse<Boolean> materialExist(AppMaterialExistQueryApiBO bo) throws BusinessException {
        AppMaterialExistQueryBO appMaterialExistQueryBO = new AppMaterialExistQueryBO();
        BeanUtil.copyProperties(bo, appMaterialExistQueryBO);
        return RestResponse.success(service.materialExist(appMaterialExistQueryBO));
    }

    @Override
    public RestResponse<Boolean> materialMkdir(AppMaterialSaveMkdirApiBO saveMkdirBO) throws BusinessException {
        AppMaterialSaveMkdirBO materialSaveMkdirBO = new AppMaterialSaveMkdirBO();
        BeanUtil.copyProperties(saveMkdirBO, materialSaveMkdirBO);
        if (!service.materialMkdir(materialSaveMkdirBO)) {
            throw new BusinessException(APP_MATERIAL_MKDIR_FAIL_CODE,APP_MATERIAL_MKDIR_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> renameMaterial(AppMaterialReNameApiBO bo) throws BusinessException {
        AppMaterialReNameBO reNameBO = new AppMaterialReNameBO();
        BeanUtil.copyProperties(bo, reNameBO);
        if (!service.reNameMaterial(reNameBO)){
            throw new BusinessException(APP_MATERIAL_RENAME_FAIL_CODE,APP_MATERIAL_RENAME_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> moveMaterial(AppMaterialIOApiBO ioBo) throws BusinessException {
        AppMaterialIOBO appMaterialIOBO = new AppMaterialIOBO();
        BeanUtil.copyProperties(ioBo, appMaterialIOBO);
        if (!service.moveMaterial(appMaterialIOBO)) {
            throw new BusinessException(APP_MATERIAL_MOVE_FAIL_CODE,APP_MATERIAL_MOVE_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> batchMoveMaterial(AppMaterialBatchIOApiBO ioBo) throws BusinessException {
        AppMaterialBatchIOBO appMaterialBatchIOBO = new AppMaterialBatchIOBO();
        BeanUtil.copyProperties(ioBo, appMaterialBatchIOBO);
        if (!service.batchMoveMaterial(appMaterialBatchIOBO)) {
            throw new BusinessException(APP_MATERIAL_COLLECT_MOVE_FAIL_CODE,APP_MATERIAL_COLLECT_MOVE_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> copyMaterial(AppMaterialIOApiBO ioBo) throws BusinessException {
        AppMaterialIOBO appMaterialIOBO = new AppMaterialIOBO();
        BeanUtil.copyProperties(ioBo, appMaterialIOBO);
        if (!service.copyMaterial(appMaterialIOBO)) {
            throw new BusinessException(APP_MATERIAL_COPY_FAIL_CODE,APP_MATERIAL_COPY_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> batchCopyMaterial(AppMaterialBatchIOApiBO ioBo) throws BusinessException {
        AppMaterialBatchIOBO appMaterialBatchIOBO = new AppMaterialBatchIOBO();
        BeanUtil.copyProperties(ioBo, appMaterialBatchIOBO);
        if (!service.batchCopyMaterial(appMaterialBatchIOBO)) {
            throw new BusinessException(APP_MATERIAL_COLLECT_COPY_FAIL_CODE,APP_MATERIAL_COLLECT_COPY_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<AppMaterialQueryDicTreeApiDTO> getAllDirectsTree() {
        AppMaterialQueryDicTreeDTO allDirectsTree = service.getAllDirectsTree();
        AppMaterialQueryDicTreeApiDTO apiDTO = new AppMaterialQueryDicTreeApiDTO();
        BeanUtil.copyProperties(allDirectsTree, apiDTO);
        return RestResponse.success(apiDTO);
    }

    @Override
    public RestResponse<List<AppMaterialQueryApiDTO>> getMaterialParentsPath(AppMaterialQueryApiBO bo) throws BusinessException {
        if (ObjectUtil.isEmpty(bo) || bo.getId() == null){
            throw new BusinessException(APP_MATERIAL_DIR_NOT_EXIST_CODE,APP_MATERIAL_DIR_NOT_EXIST_MSG);
        }
        return RestResponse.success(service.getMaterialParentsPath(bo.getId()).stream().map(d -> {
            AppMaterialQueryApiDTO dto = new AppMaterialQueryApiDTO();
            BeanUtil.copyProperties(d, dto);
            return dto;
        }).collect(Collectors.toList()));
    }

    @Override
    public RestResponse<Boolean> editCaption(AppMaterialEditCaptionApiBO bo) throws BusinessException {
        AppMaterialEditCaptionBO editCaptionBO = new AppMaterialEditCaptionBO();
        BeanUtil.copyProperties(bo, editCaptionBO);
        if (!service.editCaption(editCaptionBO)) {
            throw new BusinessException(APP_MATERIAL_EDIT_CAPTION_FAIL_CODE,APP_MATERIAL_EDIT_CAPTION_FAIL_MSG);
        }
        return RestResponse.success(true);
    }



    @Override
    public RestResponse<Boolean> deleteMaterial(CommonIdApiBO bo) throws BusinessException {
        CommonIdBO idBO = new CommonIdBO();
        BeanUtil.copyProperties(bo, idBO);
        if (!service.deleteMaterial(idBO)) {
            throw new BusinessException(APP_MATERIAL_DELETE_FAIL_CODE,APP_MATERIAL_DELETE_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> batchDeleteMaterial(AppMaterialBatchCommonIdApiBO bo) throws BusinessException {
        AppMaterialBatchCommonIdBO batchCommonIdBO = new AppMaterialBatchCommonIdBO();
        BeanUtil.copyProperties(bo, batchCommonIdBO);
        if (!service.batchDeleteMaterial(batchCommonIdBO)) {
            throw new BusinessException(APP_MATERIAL_DELETE_FAIL_CODE,APP_MATERIAL_DELETE_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<AppMaterialStatisticsSizeApiDTO> usedSize() throws BusinessException {
        AppMaterialStatisticsSizeApiDTO statisticsSizeApiDTO = new AppMaterialStatisticsSizeApiDTO();
        AppMaterialStatisticsSizeDTO sizeDTO = service.usedSize();
        if (ObjectUtil.isNotEmpty(statisticsSizeApiDTO)) {
            BeanUtil.copyProperties(sizeDTO, statisticsSizeApiDTO);
        }
        return RestResponse.success(statisticsSizeApiDTO);
    }

}
