package com.dbj.classpal.books.service.strategy.question.impl;

import com.dbj.classpal.books.common.bo.paper.RetakePaperBO;
import com.dbj.classpal.books.common.bo.paper.SubmitPaperBO;
import com.dbj.classpal.books.common.dto.paper.UserPaperDTO;
import com.dbj.classpal.books.common.enums.BusinessTypeEnum;
import com.dbj.classpal.books.service.strategy.question.AbstractPaperBusinessStrategy;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_SAVE_PAPER_RESULT_FAIL_CODE;
import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_SAVE_PAPER_RESULT_FAIL_MSG;

/**
 * 图书试卷业务策略实现
 */
@Slf4j
@Component
public class BookPaperBusinessStrategy extends AbstractPaperBusinessStrategy {


    @Override
    public Integer getBusinessType() {
        return BusinessTypeEnum.QUESTION_BUSINESS.getCode();
    }

    @Override
    public void retakePaper(RetakePaperBO retakePaperBO) throws BusinessException {
        doRetakePaper(retakePaperBO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserPaperDTO submitPaper(SubmitPaperBO submitBO) throws BusinessException {
        try {
            return doSubmitParer(submitBO,"图书答题-默认试卷名称");
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("保存试卷结果失败", e);
            throw new BusinessException(APP_SAVE_PAPER_RESULT_FAIL_CODE,APP_SAVE_PAPER_RESULT_FAIL_MSG);
        }
    }
} 