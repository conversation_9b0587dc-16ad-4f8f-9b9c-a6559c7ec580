package com.dbj.classpal.books.service.biz.shorts.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.constant.RedisKeyConstants;
import com.dbj.classpal.books.service.biz.shorts.IShortUrlInfoBiz;
import com.dbj.classpal.books.service.entity.shorts.ShortUrlInfo;
import com.dbj.classpal.books.service.mapper.shorts.ShortUrlInfoMapper;
import com.dbj.classpal.framework.redisson.config.RedissonRedisUtils;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.List;

/**
 * <p>
 * 图书表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-23
 */
@Service
public class ShortUrlInfoBizImpl extends ServiceImpl<ShortUrlInfoMapper, ShortUrlInfo> implements IShortUrlInfoBiz {
    @Resource
    private RedissonRedisUtils redisUtils;
    @Override
    public Boolean saveShortUrlInfo(String code, String longUrl) {
        ShortUrlInfo shortUrlInfo = new ShortUrlInfo();
        shortUrlInfo.setShortCode(code);
        shortUrlInfo.setLongUrl(longUrl);
        redisUtils.setValue(MessageFormat.format(RedisKeyConstants.SHORT_URL,code),longUrl,-1,null);
        return save(shortUrlInfo);
    }

    @Override
    public String getLongUrl(String shortUrl) {
        String longUrl = redisUtils.getValue(MessageFormat.format(RedisKeyConstants.SHORT_URL,shortUrl));
        //之后可能需要数据库双写 不是永久保存
        return longUrl;
    }


    @Override
    public Boolean batchSaveShortUrlInfo(List<ShortUrlInfo> shortUrlInfoList) {
        for(ShortUrlInfo shortUrlInfo : shortUrlInfoList){
            redisUtils.setValue(MessageFormat.format(RedisKeyConstants.SHORT_URL,shortUrlInfo.getShortCode()),shortUrlInfo.getLongUrl(),-1,null);
        }
        return saveBatch(shortUrlInfoList);
    }
}
