package com.dbj.classpal.books.service.api.client.album;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.album.AppAlbumMenusApi;
import com.dbj.classpal.books.client.bo.album.*;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.dto.album.AppAlbumElementsQueryApiDTO;
import com.dbj.classpal.books.client.dto.album.AppAlbumMenusTreeApiDTO;
import com.dbj.classpal.books.common.bo.album.*;
import com.dbj.classpal.books.common.bo.common.CommonIdBO;
import com.dbj.classpal.books.common.bo.common.CommonIdsBO;
import com.dbj.classpal.books.common.dto.album.AppAlbumElementsQueryDTO;
import com.dbj.classpal.books.common.dto.album.AppAlbumMenusTreeDTO;
import com.dbj.classpal.books.service.service.album.IAppAlbumElementsService;
import com.dbj.classpal.books.service.service.album.IAppAlbumMenusService;
import com.dbj.classpal.books.service.service.album.IAppElementsBusinessRefService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumApiImpl
 * Date:     2025-04-15 11:25:28
 * Description: 表名： ,描述： 表
 */
@RestController
public class AppAlbumMenusApiImpl implements AppAlbumMenusApi {
    @Resource
    private IAppAlbumMenusService menusService;
    @Resource
    private IAppAlbumElementsService elementsService;
    @Resource
    private IAppElementsBusinessRefService elementsBusinessRefService;

    @Override
    public RestResponse<AppAlbumMenusTreeApiDTO> getAllAlbumMenusTree(AppAlbumMenusQueryApiBO bo) {
        AppAlbumMenusQueryBO queryBO = new AppAlbumMenusQueryBO();
        BeanUtil.copyProperties(bo, queryBO);
        AppAlbumMenusTreeDTO allAlbumMenusTree = menusService.getAllAlbumMenusTree(queryBO);
        AppAlbumMenusTreeApiDTO apiDTO = new AppAlbumMenusTreeApiDTO();
        if (allAlbumMenusTree != null) {
            BeanUtil.copyProperties(allAlbumMenusTree, apiDTO);
        }
        return RestResponse.success(apiDTO);
    }

    @Override
    public RestResponse<Boolean> reNameAlbumMenus(AppAlbumMenusReNameApiBO bo) throws BusinessException {
        AppAlbumMenusReNameBO reNameBO = new AppAlbumMenusReNameBO();
        BeanUtil.copyProperties(bo, reNameBO);
        if (!menusService.reNameAlbumMenus(reNameBO)){
            throw new BusinessException(APP_ALBUM_MENUS_RENAME_FAIL_CODE,APP_ALBUM_MENUS_RENAME_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> saveAlbumMenus(AppAlbumMenusSaveApiBO bo) throws BusinessException {
        AppAlbumMenusSaveBO saveBO = new AppAlbumMenusSaveBO();
        BeanUtil.copyProperties(bo, saveBO);
        Integer rootDepth = menusService.getRootDepth(bo.getParentId());
        if (rootDepth>=5){
            throw new BusinessException(APP_ALBUM_MENUS_ORDER_COVER_MAX_FAIL_CODE,APP_ALBUM_MENUS_ORDER_COVER_MAX_FAIL_MSG);
        }
        if (!menusService.saveAlbumMenus(saveBO)) {
            throw new BusinessException(APP_ALBUM_MENUS_SAVE_FAIL_CODE,APP_ALBUM_MENUS_SAVE_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> deleteAlbumMenus(AppAlbumMenusDeleteApiBO bo) throws BusinessException {
        AppAlbumMenusDeleteBO deleteBO = new AppAlbumMenusDeleteBO();
        BeanUtil.copyProperties(bo, deleteBO);
        if (!menusService.deleteAlbumMenus(deleteBO)) {
            throw new BusinessException(APP_ALBUM_MENUS_DELETE_FAIL_CODE,APP_ALBUM_MENUS_DELETE_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> resetAlbumMenusOrderNum(AppAlbumMenusBatchMoveApiBO bo) throws BusinessException {
        if (ObjectUtils.isEmpty(bo)){
            throw new BusinessException(APP_ALBUM_MENUS_RESET_ORDER_NUM_PARAM_ERROR_FAIL_CODE,APP_ALBUM_MENUS_RESET_ORDER_NUM_PARAM_ERROR_FAIL_MSG);
        }
        AppAlbumMenusBatchMoveBO moveBO = new AppAlbumMenusBatchMoveBO();
        BeanUtil.copyProperties(bo, moveBO);
        if (!menusService.resetAlbumMenusOrderNum(moveBO)) {
            throw new BusinessException(APP_ALBUM_MENUS_RESET_ORDER_NUM_FAIL_CODE,APP_ALBUM_MENUS_RESET_ORDER_NUM_FAIL_MSG);
        }
        return RestResponse.success(true);
    }

    public PageInfo<AppAlbumElementsQueryBO> convertToBO(PageInfo<AppAlbumElementsQueryApiBO> bo) {
        PageInfo<AppAlbumElementsQueryBO> vo = new PageInfo<AppAlbumElementsQueryBO>();
        BeanUtil.copyProperties(bo, vo);
        return vo;
    }
}
