package com.dbj.classpal.books.service.api.client.books;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.books.client.api.books.AdminBooksRankClassifyApi;
import com.dbj.classpal.books.client.bo.books.BooksRankClassifyBO;
import com.dbj.classpal.books.client.dto.books.BooksRankClassifyDTO;
import com.dbj.classpal.books.service.biz.books.IBooksRankClassifyBiz;
import com.dbj.classpal.books.service.entity.books.BooksRankClassify;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@RestController
public class AdminBooksRankClassifyApiImpl implements AdminBooksRankClassifyApi {


    @Resource
    private IBooksRankClassifyBiz booksRankClassifyBiz;

    @Override
    public RestResponse<List<BooksRankClassifyDTO>> list(BooksRankClassifyBO boardBooksRankClassifyBO) throws BusinessException {
        List<BooksRankClassify> booksRankClassifyList =  booksRankClassifyBiz.lambdaQuery().eq(BooksRankClassify::getRankId, boardBooksRankClassifyBO.getRankId()).orderByAsc(BooksRankClassify::getWeight).list();
        List<BooksRankClassifyDTO> booksRankClassifyDTOList = new ArrayList<>();

        if(CollectionUtils.isNotEmpty(booksRankClassifyList)){
            booksRankClassifyDTOList = BeanUtil.copyToList(booksRankClassifyList,BooksRankClassifyDTO.class);
        }
        return RestResponse.success(booksRankClassifyDTOList);
    }
}
