package com.dbj.classpal.books.service.biz.question.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.client.enums.BasicConfigBizTypeEnum;
import com.dbj.classpal.books.common.bo.question.AddNormalWrongQuestionBO;
import com.dbj.classpal.books.service.biz.question.IAppUserWrongQuestionNormalBiz;
import com.dbj.classpal.books.service.entity.wrongquestion.AppUserWrongQuestionNormal;
import com.dbj.classpal.books.service.mapper.question.AppUserWrongQuestionNormalMapper;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 用户普通题错题本业务实现类
 */
@Slf4j
@Service
public class AppUserWrongQuestionNormalBizImpl extends ServiceImpl<AppUserWrongQuestionNormalMapper, AppUserWrongQuestionNormal> implements IAppUserWrongQuestionNormalBiz {

    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAddWrongQuestions(List<AddNormalWrongQuestionBO> boList) throws BusinessException {
        if (CollectionUtils.isEmpty(boList)) {
            log.error("批量添加错题失败，参数为空");
            throw new BusinessException(AppErrorCode.PARAMS_NOT_NULL_CODE, AppErrorCode.PARAMS_NOT_NULL_MSG);
        }
        
        List<AppUserWrongQuestionNormal> entityList = new ArrayList<>(boList.size());
        
        for (AddNormalWrongQuestionBO bo : boList) {
            AppUserWrongQuestionNormal entity = new AppUserWrongQuestionNormal();
            BeanUtil.copyProperties(bo, entity);
            
            // 确保字段名映射正确
            entity.setAnswerIds(bo.getAnswerIds());
            
            entityList.add(entity);
        }

        saveBatch(entityList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeWrongQuestion(Integer appUserId, List<Integer> ids) throws BusinessException {
        if (appUserId == null || ids == null) {
            log.error("删除错题失败，参数为空，appUserId={}, ids={}", appUserId, ids);
            throw new BusinessException(AppErrorCode.PARAMS_NOT_NULL_CODE, AppErrorCode.PARAMS_NOT_NULL_MSG);
        }
        
        LambdaUpdateWrapper<AppUserWrongQuestionNormal> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AppUserWrongQuestionNormal::getAppUserId, appUserId)
                .in(AppUserWrongQuestionNormal::getId, ids)
                .set(AppUserWrongQuestionNormal::getIsDeleted, YesOrNoEnum.YES.getCode());

        update(updateWrapper);
    }

    @Override
    public List<AppUserWrongQuestionNormal> listUserWrongQuestions(Integer appUserId, Integer subjectId) throws BusinessException {
        if (appUserId == null) {
            log.error("获取用户错题列表失败，用户ID为空");
            throw new BusinessException(AppErrorCode.PARAMS_NOT_NULL_CODE, AppErrorCode.PARAMS_NOT_NULL_MSG);
        }
        
        LambdaQueryWrapper<AppUserWrongQuestionNormal> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppUserWrongQuestionNormal::getAppUserId, appUserId)
                .eq(AppUserWrongQuestionNormal::getIsDeleted, 0);
        
        if (subjectId != null) {
            queryWrapper.eq(AppUserWrongQuestionNormal::getSubjectId, subjectId);
        }
        
        queryWrapper.orderByDesc(AppUserWrongQuestionNormal::getCreateTime);
        
        return list(queryWrapper);
    }

    @Override
    public List<AppUserWrongQuestionNormal> queryNormalWrongQuestionsWithJoin(Integer appUserId, Integer subjectId, Set<Integer> questionIds, Integer type) {
         return baseMapper.queryNormalWrongQuestionsWithJoin(appUserId, subjectId, questionIds, BasicConfigBizTypeEnum.SUBJECT.getCode(), type);
    }

} 