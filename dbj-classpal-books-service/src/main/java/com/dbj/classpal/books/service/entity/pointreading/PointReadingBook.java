package com.dbj.classpal.books.service.entity.pointreading;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 点读书表
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("point_reading_book")
@Schema(name = "PointReadingBook", description = "点读书表")
public class PointReadingBook extends BizEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "点读书名称")
    private String name;

    @Schema(description = "所属分类ID")
    private Integer categoryId;

    @Schema(description = "封面文件ID")
    private String coverId;

    @Schema(description = "封面URL")
    private String coverUrl;

    @Schema(description = "封面文件名")
    private String coverName;

    @Schema(description = "原始文件ID")
    private String originalFileId;

    @Schema(description = "原始文件URL")
    private String originalUrl;

    @Schema(description = "原始文件名")
    private String originalName;

    @Schema(description = "原始文件MD5")
    private String originalMd5;

    @Schema(description = "解析状态：10-待解析 20-解析中 30-已解析 40-解析失败")
    private Integer parseStatus;

    @Schema(description = "启用状态：0-未启用 1-已启用")
    private Integer launchStatus;

    @Schema(description = "排序")
    private Integer sortNum;

    @Schema(description = "状态：0-停用 1-正常")
    private Integer status;

    @Schema(description = "版本号")
    private Integer version;
}
