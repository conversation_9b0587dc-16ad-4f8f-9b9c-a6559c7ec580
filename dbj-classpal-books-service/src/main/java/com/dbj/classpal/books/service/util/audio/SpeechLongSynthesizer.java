package com.dbj.classpal.books.service.util.audio;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.nls.client.protocol.NlsClient;
import com.alibaba.nls.client.protocol.OutputFormatEnum;
import com.alibaba.nls.client.protocol.SampleRateEnum;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizer;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizerListener;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizerResponse;
import com.dbj.classpal.books.common.bo.audio.SpeechBO;
import com.dbj.classpal.books.common.constant.AudioConstants;
import com.dbj.classpal.books.common.dto.audio.SpeechDTO;
import com.dbj.classpal.books.common.dto.audio.TaskErrorDTO;
import com.dbj.classpal.framework.redisson.config.RedissonRedisUtils;
import com.dbj.classpal.framework.tts.audio.GetTTSToken;
import com.dbj.classpal.framework.tts.config.AliyunTTSConfig;
import com.dbj.classpal.framework.tts.entity.TTSToken;
import com.dbj.classpal.framework.tts.enums.aliyun.AliyunTtsErrorCodeEnum;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 此示例演示了
 *      长文本语音合成API调用(setLongText)
 *      流式合成TTS
 *      首包延迟计算
 * (仅作演示，需用户根据实际情况实现)
 *
 * 说明：这个示例和nls-example-tts下的SpeechSynthesizerLongText不完全相同，长文本语音合成是单独的产品功能，是将一长串文本直接发送给服务端去合成；
 * 而SpeechSynthesizerLongText演示的是将一长串文本在调用方处切割然后分段调用语音合成接口
 */
@Slf4j
@Component
public class SpeechLongSynthesizer {
    @Autowired
    private RedissonRedisUtils redisUtils;

    private static long startTime;
    NlsClient client;
    @Autowired
    private AliyunTTSConfig aliyunTtsConfig;

    // 其他成员变量保持不变
    private static final Map<String, TaskErrorDTO> errorMsgMap = new ConcurrentHashMap<>();
    // 添加音频输出目录管理
    private final File audioOutputDir = new File(AudioConstants.AUDIO_OUTPUT_DIR);

    private String token = "";

    @PostConstruct
    public void init() {
        if (aliyunTtsConfig == null) {
            throw new RuntimeException("【阿里云语音合成器】- aliyunTtsConfig 未注入，请检查配置");
        }
        if (StringUtils.isBlank(aliyunTtsConfig.getUrl())) {
            throw new RuntimeException("【阿里云语音合成器】- aliyunTtsConfig.url 为空，请检查配置");
        }
        // 此时 aliyunTtsConfig 已被注入，可安全使用
        client = new NlsClient(aliyunTtsConfig.getUrl(), token);
        log.info("【阿里云语音合成器】- 初始化 NlsClient 成功");
    }

    /**
     * 动态设置Token（核心改造点）
     */
    public void setToken() {
        try {
            String token;
            Long expire = redisUtils.getExpire(AudioConstants.ALIYUN_TTS_TOKEN_KEY);
            if (expire <= 0) {
                TTSToken accessToken = GetTTSToken.createToken(aliyunTtsConfig.getAccessId(), aliyunTtsConfig.getAccessSecret());
                if (accessToken == null || StringUtils.isBlank(accessToken.getToken())) {
                    throw new RuntimeException("【阿里云语音合成器】- 获取Token失败！");
                }
                token = accessToken.getToken();
                log.info("【阿里云语音合成器】- 获取Token成功, 过期时间：{}", accessToken.getExpireTime());
                redisUtils.setValue(AudioConstants.ALIYUN_TTS_TOKEN_KEY, token, AudioConstants.ALIYUN_TTS_TOKEN_EXPIRE, TimeUnit.SECONDS);
            } else {
                token = redisUtils.getValue(AudioConstants.ALIYUN_TTS_TOKEN_KEY);
                log.info("【阿里云语音合成器】- 获取redis Token: {}", token);
            }
            Assert.isFalse(StringUtils.isBlank(token), "【阿里云语音合成器】- Token为空！");
            this.token = token;
            // 重新初始化客户端
            if (client != null) {
                client.shutdown();
            }
            client = new NlsClient(token);
        } catch (Exception e) {
            log.error("【阿里云语音合成器】- 获取Token失败！", e);
        }

    }

    /**
     * 初始化音频输出目录
     */
    private void initAudioDir() {
        if (!audioOutputDir.exists()) {
            if (audioOutputDir.mkdirs()) {
                log.info("【阿里云语音合成器】- 创建音频输出目录: {}", audioOutputDir.getAbsolutePath());
            } else {
                log.error("【阿里云语音合成器】- 无法创建音频输出目录: {}", audioOutputDir.getAbsolutePath());
            }
        }
    }

    private static SpeechSynthesizerListener getSynthesizerListener(File outputFile, SpeechBO bo) {
        return new SpeechSynthesizerListener() {
            private FileOutputStream fout;
            private boolean firstRecvBinary = true;
            private long taskStartTime = System.currentTimeMillis();
            private String taskId;
            private static final int BUFFER_SIZE = 8192; // 缓冲区大小
            private final byte[] buffer = new byte[BUFFER_SIZE];

            {
                try {
                    fout = new FileOutputStream(outputFile);
                } catch (IOException e) {
                    log.error("【阿里云语音合成处理器】- 创建文件输出流失败", e);
                }
            }
            //语音合成结束
            @Override
            public void onComplete(SpeechSynthesizerResponse response) {
                taskId = response.getTaskId();
                try {
                    fout.flush(); // 确保数据写入磁盘
                    log.info("【阿里云语音合成处理器】- 完成: {}, 文件大小: {}KB", taskId, outputFile.length() / 1024);
                } catch (IOException e) {
                    log.error("【阿里云语音合成处理器】- 刷新文件流失败", e);
                } finally {
                    // 确保流关闭
                    try {
                        fout.close();
                    } catch (IOException e) {
                        log.error("【阿里云语音合成处理器】- 关闭文件流失败", e);
                    }
                }
            }

            //语音合成的语音二进制数据
            @Override
            public void onMessage(ByteBuffer message) {
                try {
                    if(firstRecvBinary) {
                        firstRecvBinary = false;
                        long now = System.currentTimeMillis();
                        log.info("【阿里云语音合成处理器】- tts first latency : " + (now - SpeechLongSynthesizer.startTime) + " ms");
                    }
                    // 分段读取音频数据，避免大内存占用
                    while (message.hasRemaining()) {
                        int length = Math.min(message.remaining(), BUFFER_SIZE);
                        message.get(buffer, 0, length);
                        fout.write(buffer, 0, length);
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            @Override
            public void onFail(SpeechSynthesizerResponse response){
                taskId = response.getTaskId();
                log.error("【阿里云语音合成处理器】- 合成失败: task_id={}, status={}, error={}", taskId, response.getStatus(), response.getStatusText());
                TaskErrorDTO errorDTO = new TaskErrorDTO();
                errorDTO.setAudioContextInfoId(bo.getAudioContextInfoId());
                errorDTO.setStatus(response.getStatus());
                errorDTO.setErrorMessage(response.getStatusText());
                errorDTO.setTaskId(response.getTaskId());
                AliyunTtsErrorCodeEnum aliyunTtsErrorCodeEnum = AliyunTtsErrorCodeEnum.fromCode(errorDTO.getStatus());
                if (aliyunTtsErrorCodeEnum != null) {
                    errorDTO.setReason(aliyunTtsErrorCodeEnum.getReason());
                    errorDTO.setSolution(aliyunTtsErrorCodeEnum.getSolution());
                }
                errorMsgMap.put(taskId, errorDTO);
            }
        };
    }

    public SpeechDTO process(SpeechBO bo) {
        // 初始化音频输出目录
        initAudioDir();
        // 设置Token
        setToken();

        errorMsgMap.clear();

        SpeechSynthesizer synthesizer = null;
        try {
            // 生成唯一文件名
            String fileName =  bo.getVoice() + "_" + UUID.randomUUID().toString().replace("-", "") + AudioConstants.BGM_FORMAT_WAV;
            //  TODO 2025/6/26 待完善：测试专用文件名,生成后需注释
//            String fileName = bo.getVoice() + "_" + "template" + AudioConstants.bgm_format_wav;
            File outputFile = new File(audioOutputDir, fileName); // 使用统一目录

            //创建实例,建立连接
            synthesizer = new SpeechSynthesizer(client, getSynthesizerListener(outputFile, bo));
            synthesizer.setAppKey(aliyunTtsConfig.getAppKey());
            synthesizer.setFormat(OutputFormatEnum.WAV);
            synthesizer.setSampleRate(SampleRateEnum.SAMPLE_RATE_16K);
            synthesizer.setVoice(StringUtils.isEmpty(bo.getVoice()) ? AudioConstants.ALIYUN_DEFAULT_VOICE : bo.getVoice());
            synthesizer.setLongText(bo.getLongText());
            if (bo.getPitchRate() != null) {
                synthesizer.setPitchRate(bo.getPitchRate());
            }
            if (bo.getSpeechRate() != null) {
                synthesizer.setSpeechRate(bo.getSpeechRate());
            }
            if (bo.getVolume() != null) {
                synthesizer.setVolume(bo.getVolume());
            }
            synthesizer.addCustomedParam("enable_subtitle", true);
            //此方法将以上参数设置序列化为json发送给服务端,并等待服务端确认
            long start = System.currentTimeMillis();
            synthesizer.start();
            log.info("【阿里云语音合成器】 - tts start latency " + (System.currentTimeMillis() - start) + " ms");

            SpeechLongSynthesizer.startTime = System.currentTimeMillis();
            //等待语音合成结束
            synthesizer.waitForComplete();
            log.info("【阿里云语音合成器】 - tts stop latency " + (System.currentTimeMillis() - start) + " ms");

            // 记录任务信息
            String taskId = synthesizer.getTaskId();
            SpeechDTO speechDTO = new SpeechDTO();
            speechDTO.setAudioContextInfoId(bo.getAudioContextInfoId());
            speechDTO.setFile(outputFile);
            speechDTO.setFileName(fileName);
            speechDTO.setTaskId(taskId);
            speechDTO.setFilePath(outputFile.getAbsolutePath());

            if (CollectionUtil.isNotEmpty(errorMsgMap)) {
                speechDTO.setErrorMsg(errorMsgMap.get(speechDTO.getTaskId()));
            }
            return speechDTO;
        } catch (Exception e) {
            log.error("【阿里云语音合成处理器】- 执行异常: {}", e.getMessage(), e);
        } finally {
            //关闭连接
            if (null != synthesizer) {
                synthesizer.close();
            }
            shutdown();
        }
        return null;
    }

    public void shutdown() {
        client.shutdown();
    }
}
