package com.dbj.classpal.books.service.api.client.books;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.books.client.api.books.AdminBooksCategoryApi;
import com.dbj.classpal.books.client.bo.books.BooksCategoryApiBO;
import com.dbj.classpal.books.client.dto.books.BooksCategoryApiDTO;
import com.dbj.classpal.books.common.bo.books.BooksCategoryMqBO;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.books.common.constant.ExchangeConstant;
import com.dbj.classpal.books.common.constant.RedisKeyConstants;
import com.dbj.classpal.books.common.constant.RoutingKeyConstant;
import com.dbj.classpal.books.service.biz.books.IBooksCategoryBiz;
import com.dbj.classpal.books.service.entity.books.BooksCategory;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.mq.pool.DbjRabbitTemplate;
import com.dbj.classpal.framework.redisson.config.RedissonRedisUtils;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className AdminBooksCategoryApiImpl
 * @description
 * @date 2025-04-17 14:36
 **/
@RestController
public class AdminBooksCategoryApiImpl implements AdminBooksCategoryApi {

    @Resource
    private IBooksCategoryBiz bookCategoryBiz;
    @Resource
    private DbjRabbitTemplate dbjRabbitTemplate;
    @Resource
    private RedissonRedisUtils redisUtils;
    @Override
    public RestResponse<List<BooksCategoryApiDTO>> list(BooksCategoryApiBO bookCategoryApiBO) throws BusinessException {
        List<BooksCategory> bookCategoryList = bookCategoryBiz.list();
        List<BooksCategoryApiDTO> booksCategoryApiDTOList = BeanUtil.copyToList(bookCategoryList,BooksCategoryApiDTO.class);
        return RestResponse.success(booksCategoryApiDTOList);
    }

    @Override
    public RestResponse<Boolean> sync() throws BusinessException {
        String value = redisUtils.getValue(RedisKeyConstants.DBJ_BOOKS_CATEGORY_SYNC);
        if(value != null){
            throw new BusinessException(AppErrorCode.BOOK_CATEGORY_SYNC_CODE,AppErrorCode.BOOK_CATEGORY_SYNC_MSG);
        }
        dbjRabbitTemplate.sendExchangeEntityMessage(new BooksCategoryMqBO(), ExchangeConstant.CLASSPAL_BOOKS_EXCHANGE, RoutingKeyConstant.CLASSPAL_BOOKS_CATEGORY_SERVICE_ROUTING_KEY);
        redisUtils.setValue(RedisKeyConstants.DBJ_BOOKS_CATEGORY_SYNC, "SYNC", 3, TimeUnit.MINUTES);
        return RestResponse.success(true);
    }
}
