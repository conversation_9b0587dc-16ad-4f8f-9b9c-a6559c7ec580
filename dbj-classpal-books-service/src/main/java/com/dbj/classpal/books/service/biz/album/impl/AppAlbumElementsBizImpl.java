package com.dbj.classpal.books.service.biz.album.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsQueryBO;
import com.dbj.classpal.books.common.dto.album.AppAlbumElementsQueryDTO;
import com.dbj.classpal.books.common.enums.IsRootEnum;
import com.dbj.classpal.books.service.biz.album.IAppAlbumElementsBiz;
import com.dbj.classpal.books.service.biz.album.IAppAlbumMenusBiz;
import com.dbj.classpal.books.service.entity.album.AppAlbumElements;
import com.dbj.classpal.books.service.entity.album.AppAlbumMenus;
import com.dbj.classpal.books.service.mapper.album.AppAlbumElementsMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Collection;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppAlbumElementsBizImpl
 * Date:     2025-04-15 10:24:49
 * Description: 表名： ,描述： 表
 */
@Service
public class AppAlbumElementsBizImpl extends ServiceImpl<AppAlbumElementsMapper, AppAlbumElements> implements IAppAlbumElementsBiz{

    @Resource
    private IAppAlbumMenusBiz menusBiz;

    @Override
    public Page<AppAlbumElementsQueryDTO> pageAlbumElements(Page<AppAlbumElementsQueryBO> page, AppAlbumElementsQueryBO bo) {
        AppAlbumMenus byId = menusBiz.getById(bo.getAppAlbumMenuId());
        if (byId.getIsRoot().equals(IsRootEnum.IS_ROOT_YES.getCode())) {
            bo.setAppAlbumMenuId(null);
        }
        return baseMapper.listPage(bo, page);
    }

    @Override
    public Boolean allExistsByIds(Collection<Integer> ids) {
        Long count = lambdaQuery().in(AppAlbumElements::getId, ids).count();
        return count == ids.size();
    }
}
