package com.dbj.classpal.books.service.biz.album.impl;

import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsQueryBO;
import com.dbj.classpal.books.common.enums.IsRootEnum;
import com.dbj.classpal.books.service.biz.album.IAppAlbumElementsBiz;
import com.dbj.classpal.books.service.biz.album.IAppAlbumMenusBiz;
import com.dbj.classpal.books.service.entity.album.AppAlbumElements;
import com.dbj.classpal.books.service.entity.album.AppAlbumMenus;
import com.dbj.classpal.books.service.mapper.album.AppAlbumElementsMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppAlbumElementsBizImpl
 * Date:     2025-04-15 10:24:49
 * Description: 表名： ,描述： 表
 */
@Service
public class AppAlbumElementsBizImpl extends ServiceImpl<AppAlbumElementsMapper, AppAlbumElements> implements IAppAlbumElementsBiz{

    @Resource
    private AppAlbumElementsMapper mapper;
    @Resource
    private IAppAlbumMenusBiz menusBiz;

    @Override
    public Page<AppAlbumElements> pageAlbumElements(Page<AppAlbumElements> page, AppAlbumElementsQueryBO condition) {
        LambdaQueryWrapper<AppAlbumElements> wrapper = new LambdaQueryWrapper<>();
        AppAlbumMenus byId = menusBiz.getById(condition.getAppAlbumMenuId());
        // 构建查询条件
        wrapper.like(StringUtils.hasText(condition.getAlbumTitle()), AppAlbumElements::getAlbumTitle, condition.getAlbumTitle())
                .eq(condition.getAlbumStatus() != null, AppAlbumElements::getAlbumStatus, condition.getAlbumStatus())
                .eq(condition.getAlbumType() != null, AppAlbumElements::getAlbumType, condition.getAlbumType())
                .eq(condition.getAlbumVisible() != null, AppAlbumElements::getAlbumVisible, condition.getAlbumVisible());
        if (!byId.getIsRoot().equals(IsRootEnum.IS_ROOT_YES.getCode())) {
            wrapper.eq(condition.getAppAlbumMenuId() != null, AppAlbumElements::getAppAlbumMenuId, condition.getAppAlbumMenuId());
        }
        wrapper.orderByDesc(AppAlbumElements::getCreateTime,AppAlbumElements::getCreateTime);
        return page(page, wrapper);
    }
}
