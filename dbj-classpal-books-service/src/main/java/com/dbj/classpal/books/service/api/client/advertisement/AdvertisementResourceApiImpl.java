package com.dbj.classpal.books.service.api.client.advertisement;

import com.dbj.classpal.books.client.api.advertisement.AdvertisementResourceApi;
import com.dbj.classpal.books.client.bo.advertisement.AdvertisementResourceBO;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementResourceDTO;
import com.dbj.classpal.books.service.service.advertisement.IAdvertisementResourceService;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 广告页面表 远程Api 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@RestController
public class AdvertisementResourceApiImpl implements AdvertisementResourceApi {

    @Resource
    private IAdvertisementResourceService advertisementResourceService;


    @Override
    public RestResponse<List<AdvertisementResourceDTO>> getAdvertisementResourceList(AdvertisementResourceBO bo) {
        return RestResponse.success(advertisementResourceService.getAdvertisementResourceList(bo));
    }
}
