package com.dbj.classpal.books.service.biz.studycenter;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.common.bo.studycenter.AppStudyModuleResourceRelCreateBO;
import com.dbj.classpal.books.common.bo.studycenter.AppStudyModuleResourceRelUpdateBO;
import com.dbj.classpal.books.common.dto.studycenter.AppStudyModuleResourceRelDetailDTO;
import com.dbj.classpal.books.service.entity.studycenter.AppStudyModuleResourceRel;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;

public interface IAppStudyModuleResourceRelBiz extends IService<AppStudyModuleResourceRel> {
    Boolean create(AppStudyModuleResourceRelCreateBO bo) throws BusinessException;
    Boolean update(AppStudyModuleResourceRelUpdateBO bo) throws BusinessException;
    Boolean delete(List<Integer> ids) throws BusinessException;
    AppStudyModuleResourceRelDetailDTO detail(Integer id) throws BusinessException;
} 