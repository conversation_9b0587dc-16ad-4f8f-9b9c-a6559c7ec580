package com.dbj.classpal.books.service.api.client.books.app;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.books.client.api.books.app.AppBooksApi;
import com.dbj.classpal.books.client.dto.books.app.BooksRankInCodesContentsCountDTO;
import com.dbj.classpal.books.client.dto.books.app.BooksUserApiDTO;
import com.dbj.classpal.books.common.enums.books.ContentsTypeEnum;
import com.dbj.classpal.books.service.biz.books.IBooksInfoBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInCodesContentsBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankStudyTimeLogBiz;
import com.dbj.classpal.books.service.biz.books.IBooksUserRefBiz;
import com.dbj.classpal.books.service.entity.books.BooksInfo;
import com.dbj.classpal.books.service.entity.books.BooksRankInCodesContents;
import com.dbj.classpal.books.service.entity.books.BooksUserRef;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import com.dbj.classpal.framework.utils.util.ContextAppUtil;
import com.dbj.classpal.framework.utils.util.ContextUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApi
 * Date:     2025-04-10 11:40:26
 * Description: 表名： ,描述： 表
 */
@RestController
public class AppBooksApiImpl implements AppBooksApi {


    @Resource
    private IBooksInfoBiz booksInfoBiz;
    @Resource
    private IBooksRankInCodesContentsBiz booksRankInCodesContentsBiz;
    @Resource
    private IBooksUserRefBiz booksUserBiz;

    @Override
    public RestResponse<List<BooksUserApiDTO>> list(String isbn) throws BusinessException {
        //根据isbn查询数据
        List<BooksInfo> booksInfoList =  booksInfoBiz.lambdaQuery().eq(BooksInfo::getIsbn, isbn).list();
        //查询书内码数量
        //查询是否添加
        List<BooksUserApiDTO> booksUserApiDTOList = Collections.emptyList();
        if(CollectionUtils.isNotEmpty(booksInfoList)){
            booksUserApiDTOList = BeanUtil.copyToList(booksInfoList,BooksUserApiDTO.class);

            List<Integer> bookIds = booksInfoList.stream().map(BooksInfo::getId).collect(Collectors.toList());
            List<BooksRankInCodesContentsCountDTO> booksRankInCodesContentsCountDTOS = booksRankInCodesContentsBiz.listCount(bookIds);
            Map<Integer,Long>  booksRankInCodesContentsCountMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(booksRankInCodesContentsCountDTOS)){
                booksRankInCodesContentsCountMap = booksRankInCodesContentsCountDTOS.stream().collect(Collectors.toMap(BooksRankInCodesContentsCountDTO::getBooksId,BooksRankInCodesContentsCountDTO::getContentsNum, (v1, v2) -> v1));
            }


            //查询图书与用户的关系
            List<BooksUserRef> booksUserRefList = booksUserBiz.lambdaQuery().in(BooksUserRef::getBooksId, bookIds)
                    .eq(BooksUserRef::getAppUserId, ContextAppUtil.getAppUserIdInt()).list();
            Map<Integer,BooksUserRef> booksUserRefMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(booksUserRefList)){
                booksUserRefMap = booksUserRefList.stream().collect(Collectors.toMap(BooksUserRef::getBooksId, Function.identity(), (v1, v2) -> v1));
            }

            for(BooksUserApiDTO booksUserApiDTO : booksUserApiDTOList){
                booksUserApiDTO.setContentsNum(booksRankInCodesContentsCountMap.getOrDefault(booksUserApiDTO.getId(),0L));
                BooksUserRef bookUserRef = booksUserRefMap.get(booksUserApiDTO.getId());
                if(bookUserRef != null){
                    booksUserApiDTO.setIsAdd(YesOrNoEnum.YES.getCode());
                }else{
                    booksUserApiDTO.setIsAdd(YesOrNoEnum.NO.getCode());
                }
            }
        }
        return RestResponse.success(booksUserApiDTOList);
    }
}
