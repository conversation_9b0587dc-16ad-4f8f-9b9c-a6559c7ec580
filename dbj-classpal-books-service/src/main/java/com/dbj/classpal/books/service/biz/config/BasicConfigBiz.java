package com.dbj.classpal.books.service.biz.config;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.common.bo.config.BasicConfigBO;
import com.dbj.classpal.books.common.dto.config.BasicConfigDTO;
import com.dbj.classpal.books.service.entity.config.BasicConfig;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;

/**
 * 题库学科业务接口
 */
public interface BasicConfigBiz extends IService<BasicConfig> {

    /**
     * 创建配置
     */
    Integer create(BasicConfigBO bo) throws BusinessException;

    /**
     * 更新配置
     */
    void update(BasicConfigBO bo) throws BusinessException;

    /**
     * 删除配置
     */
    void batchDelete(List<Integer> ids) throws BusinessException;

    /**
     * 获取配置详情
     */
    BasicConfigDTO detail(Integer id);

    /**
     * 获取配置详情
     */
    List<BasicConfigDTO> listByType(String bizType);

    /**
     * 分页查询配置列表
     */
    Page<BasicConfig> pageList(Page<BasicConfig> page, BasicConfig condition);
    /**
     * 检查配置名称是否存在
     */
    boolean existsByName(String name,String bizType,Integer excludeId);

    /**
     * 批量更新排序
     */
    void updateSort(List<BasicConfigBO> boList);
}