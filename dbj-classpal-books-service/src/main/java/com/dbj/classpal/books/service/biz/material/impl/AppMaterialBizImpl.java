package com.dbj.classpal.books.service.biz.material.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.client.dto.material.AppMaterialPathDTO;
import com.dbj.classpal.books.common.bo.material.AppMaterialIOBO;
import com.dbj.classpal.books.common.bo.material.AppMaterialQueryBO;
import com.dbj.classpal.books.common.bo.material.AppMaterialQueryFilterBO;
import com.dbj.classpal.books.common.bo.material.AppMaterialQueryOrderBO;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBiz;
import com.dbj.classpal.books.service.entity.material.AppMaterial;
import com.dbj.classpal.books.service.mapper.material.AppMaterialMapper;
import com.dbj.classpal.framework.commons.request.PageInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppMaterialBusinessImpl
 * Date:     2025-04-08 16:26:39
 * Description: 表名： ,描述： 表
 */
@Service
public class AppMaterialBizImpl extends ServiceImpl<AppMaterialMapper, AppMaterial> implements IAppMaterialBiz {


    @Override
    public Page<AppMaterial> pageMaterials(PageInfo<AppMaterialQueryBO> page) {
        AppMaterialQueryBO condition = page.getData();
        List<AppMaterialQueryFilterBO> filters = condition.getFilters();
        if (CollectionUtils.isNotEmpty(filters)) {
            AppMaterialQueryFilterBO filterBO = new AppMaterialQueryFilterBO();
            filterBO.setMaterialType(1);
            filters.add(filterBO);
            condition.setFilters(filters);
        }
        if (CollectionUtils.isNotEmpty(page.getOrders())) {
            List<AppMaterialQueryOrderBO> orderBOList = BeanUtil.copyToList(page.getOrders(), AppMaterialQueryOrderBO.class);
            condition.setOrders(orderBOList);
        }
        page.setOrders(null);
        return baseMapper.pageTree(page.getPage(),condition);
    }

    @Override
    public List<AppMaterial> getMaterialParentsPath(Integer id) {
        return baseMapper.getMaterialParentsPath(id);
    }

    @Override
    public String getMaterialParentsNames(Integer id) {
        return baseMapper.getMaterialParentsNames(id);
    }

    @Override
    public Integer checkAimIdInChildren(AppMaterialIOBO ioBo) {
        return baseMapper.checkAimIdInChildren(ioBo);
    }

    @Override
    public List<AppMaterial> getChildrenFiles(Integer id) {
        return baseMapper.getChildrenFiles(id);
    }

    @Override
    public List<AppMaterial> getBatchDirChildrenFiles(List<Integer> ids) {
        return baseMapper.getBatchDirChildrenFiles(ids);
    }

    @Override
    public Integer sumDuration(Set<Integer> idSet) {
        return baseMapper.sumDuration(idSet);
    }

    @Override
    public List<AppMaterialPathDTO> getPathList(List<Integer> materialIds) {
        return baseMapper.getPathList(materialIds);
    }
}