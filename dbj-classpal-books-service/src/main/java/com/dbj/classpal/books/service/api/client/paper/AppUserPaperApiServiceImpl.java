package com.dbj.classpal.books.service.api.client.paper;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.dbj.classpal.books.client.api.paper.AppUserPaperApi;
import com.dbj.classpal.books.client.bo.paper.*;
import com.dbj.classpal.books.client.dto.paper.PaperQuestionApiDTO;
import com.dbj.classpal.books.client.dto.paper.PaperResultApiDTO;
import com.dbj.classpal.books.client.dto.paper.UserPaperApiDTO;
import com.dbj.classpal.books.client.dto.paper.UserPaperCheckSubmitApiDTO;
import com.dbj.classpal.books.client.dto.question.BlankCorrectAnswerApiDTO;
import com.dbj.classpal.books.client.dto.question.QuestionCorrectAnswerApiDTO;
import com.dbj.classpal.books.common.bo.paper.*;
import com.dbj.classpal.books.common.bo.question.QueryQuestionsCorrectAnswerBO;
import com.dbj.classpal.books.common.dto.paper.PaperQuestionDTO;
import com.dbj.classpal.books.common.dto.paper.PaperQuestionResultDTO;
import com.dbj.classpal.books.common.dto.paper.UserPaperCheckSubmitDTO;
import com.dbj.classpal.books.common.dto.paper.UserPaperDTO;
import com.dbj.classpal.books.common.dto.question.QuestionCorrectAnswerDTO;
import com.dbj.classpal.books.service.service.paper.IAppUserPaperService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户试卷API服务实现类
 */
@RestController
@RequiredArgsConstructor
@Slf4j
public class AppUserPaperApiServiceImpl implements AppUserPaperApi {

    private final IAppUserPaperService appUserPaperService;

    @Override
    public RestResponse<List<PaperQuestionApiDTO>> getPaperQuestions(QueryPaperQuestionsApiBO apiBO) throws BusinessException {
        log.info("获取试题,请求参数:{}", JSON.toJSONString(apiBO));
        QueryPaperQuestionsBO queryBO = BeanUtil.copyProperties(apiBO, QueryPaperQuestionsBO.class);
        
        List<PaperQuestionDTO> questions = appUserPaperService.getPaperQuestions(queryBO);
        
        List<PaperQuestionApiDTO> apiDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(questions)) {
            apiDTOList = questions.stream()
                .map(question -> BeanUtil.copyProperties(question, PaperQuestionApiDTO.class))
                .collect(Collectors.toList());
        }
        log.info("获取到试题:{}", JSON.toJSONString(apiDTOList));
        return RestResponse.success(apiDTOList);
    }

    @Override
    public RestResponse<UserPaperApiDTO> submitPaper(SubmitPaperApiBO apiBO) throws BusinessException {
        log.info("接收到答题参数:{}", JSON.toJSONString(apiBO));
        SubmitPaperBO submitBO = BeanUtil.copyProperties(apiBO, SubmitPaperBO.class);
        
        if (!CollectionUtils.isEmpty(apiBO.getQuestionResults())) {
            List<SubmitQuestionResultBO> questionResults = apiBO.getQuestionResults().stream()
                .map(result -> BeanUtil.copyProperties(result, SubmitQuestionResultBO.class))
                .collect(Collectors.toList());
            submitBO.setQuestionResults(questionResults);
        }
        
        if (!CollectionUtils.isEmpty(apiBO.getBlankResults())) {
            List<SubmitBlankResultBO> blankResults = apiBO.getBlankResults().stream()
                .map(result -> BeanUtil.copyProperties(result, SubmitBlankResultBO.class))
                .collect(Collectors.toList());
            submitBO.setBlankResults(blankResults);
        }
        
        UserPaperDTO result = appUserPaperService.submitPaper(submitBO);
        
        return RestResponse.success(BeanUtil.copyProperties(result,UserPaperApiDTO.class));
    }

    @Override
    public RestResponse<UserPaperCheckSubmitApiDTO> checkSubmit(CheckSubmitApiBO checkBo) throws BusinessException {
        CheckSubmitBO serviceBO = BeanUtil.copyProperties(checkBo, CheckSubmitBO.class);
        UserPaperCheckSubmitDTO serviceDTO =  appUserPaperService.checkSubmit(serviceBO);
        return RestResponse.success(BeanUtil.copyProperties(serviceDTO,UserPaperCheckSubmitApiDTO.class));
    }

    @Override
    public RestResponse<PaperResultApiDTO> getPaperResult(QueryPaperResultApiBO apiBO) throws BusinessException {

        QueryPaperResultBO queryBO = BeanUtil.copyProperties(apiBO, QueryPaperResultBO.class);
        
        PaperQuestionResultDTO result = appUserPaperService.getPaperQuestionResult(queryBO);
        
        PaperResultApiDTO apiDTO = BeanUtil.copyProperties(result, PaperResultApiDTO.class);
        log.info("获取答题结果:{}", JSON.toJSONString(apiDTO));
        return RestResponse.success(apiDTO);
    }

    @Override
    public RestResponse<Void> retakePaper(RetakePaperApiBO apiBO) throws BusinessException {
        RetakePaperBO retakePaperBO = BeanUtil.copyProperties(apiBO, RetakePaperBO.class);
        
        appUserPaperService.retakePaper(retakePaperBO);
        
        return RestResponse.success(null);
    }

    @Override
    public RestResponse<List<QuestionCorrectAnswerApiDTO>> getAllQuestionsCorrectAnswers(QueryQuestionsCorrectAnswerApiBO apiBO) throws BusinessException {
        QueryQuestionsCorrectAnswerBO bo = BeanUtil.copyProperties(apiBO, QueryQuestionsCorrectAnswerBO.class);
        
        List<QuestionCorrectAnswerDTO> answerList = appUserPaperService.getAllQuestionsCorrectAnswers(bo);
        
        List<QuestionCorrectAnswerApiDTO> apiDTOList = answerList.stream().map(dto -> {
            QuestionCorrectAnswerApiDTO apiDTO = BeanUtil.copyProperties(dto, QuestionCorrectAnswerApiDTO.class);
            
            if (!CollectionUtils.isEmpty(dto.getBlankResults())) {
                List<BlankCorrectAnswerApiDTO> blankApiList = dto.getBlankResults().stream()
                    .map(blank -> BeanUtil.copyProperties(blank, BlankCorrectAnswerApiDTO.class))
                    .collect(Collectors.toList());
                apiDTO.setBlankResults(blankApiList);
            }
            
            return apiDTO;
        }).collect(Collectors.toList());
        
        return RestResponse.success(apiDTOList);
    }

} 