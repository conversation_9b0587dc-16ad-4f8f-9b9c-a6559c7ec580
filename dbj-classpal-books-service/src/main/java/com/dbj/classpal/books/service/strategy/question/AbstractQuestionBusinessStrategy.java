package com.dbj.classpal.books.service.strategy.question;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dbj.classpal.books.common.bo.paper.*;
import com.dbj.classpal.books.common.bo.question.AddWrongQuestionBO;
import com.dbj.classpal.books.common.bo.question.WrongQuestionBlankBO;
import com.dbj.classpal.books.common.dto.paper.BlankResultDTO;
import com.dbj.classpal.books.common.dto.paper.PaperQuestionDTO;
import com.dbj.classpal.books.common.dto.paper.PaperQuestionResultDTO;
import com.dbj.classpal.books.common.dto.paper.QuestionResultDTO;
import com.dbj.classpal.books.common.dto.question.*;
import com.dbj.classpal.books.common.enums.BusinessTypeEnum;
import com.dbj.classpal.books.common.enums.question.OptionTypeEnum;
import com.dbj.classpal.books.common.enums.question.QuestionMethodEnum;
import com.dbj.classpal.books.common.enums.question.QuestionTypeEnum;
import com.dbj.classpal.books.common.enums.question.WrongQuestionSourceTypeEnum;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBusinessRefBiz;
import com.dbj.classpal.books.service.biz.paper.IAppUserPaperQuestionBlankResultBiz;
import com.dbj.classpal.books.service.biz.paper.IAppUserPaperQuestionResultBiz;
import com.dbj.classpal.books.service.biz.question.*;
import com.dbj.classpal.books.service.entity.material.AppMaterial;
import com.dbj.classpal.books.service.entity.material.AppMaterialBusinessRef;
import com.dbj.classpal.books.service.entity.paper.AppUserPaperInfo;
import com.dbj.classpal.books.service.entity.question.*;
import com.dbj.classpal.books.service.mapper.material.AppMaterialMapper;
import com.dbj.classpal.books.service.mapper.paper.AppUserPaperInfoMapper;
import com.dbj.classpal.books.service.service.question.WrongQuestionService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

@Slf4j
public abstract class AbstractQuestionBusinessStrategy implements IQuestionBusinessStrategy {

    @Resource
    protected QuestionAnswerBiz questionAnswerBiz;
    
    @Resource
    protected QuestionBiz questionBiz;
    
    @Resource
    protected QuestionBlankAreaBiz questionBlankAreaBiz;

    @Resource
    private IAppMaterialBusinessRefBiz materialBusinessRefBiz;
    
    @Resource
    private AppMaterialMapper materialMapper;
    
    @Resource
    protected IAppUserPaperQuestionBlankResultBiz blankResultBiz;

    @Resource
    protected IAppUserPaperQuestionResultBiz questionResultBiz;

    @Resource
    private AppUserPaperInfoMapper appUserPaperInfoMapper;

    @Resource
    private WrongQuestionService wrongQuestionService;

    @Resource
    private IQuestionCategoryBusinessRefBiz categoryBusinessRefBiz;

    @Resource
    private QuestionCategoryBiz questionCategoryBiz;
    /**
     * 评分普通题目
     */
    @Override
    public void scoreQuestionResults(List<SubmitQuestionResultBO> results, Map<Integer, Question> questionMap) {
        if (CollectionUtils.isEmpty(results)) {
            return;
        }

        for (SubmitQuestionResultBO resultBO : results) {
            Question question = questionMap.get(resultBO.getQuestionId());
            if (question == null) {
                continue;
            }

            boolean isCorrect = validateQuestionAnswer(question, resultBO.getAnswerIds(), resultBO.getUserAnswer());
            resultBO.setResult(isCorrect ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
        }
    }

    private boolean validateQuestionAnswer(Question question, String userAnswerIds, String userAnswer) {
        if (question == null) {
            return false;
        }
        List<String> correctAnswerIds =null;
        List<String> userAnswerList = null;
        if(StringUtils.isNoneEmpty(question.getAnswer())){
            correctAnswerIds = Arrays.asList(question.getAnswer().split(","));
        }
        if(StringUtils.isNoneEmpty(userAnswerIds)){
            userAnswerList = Arrays.asList(userAnswerIds.split(","));
        }

        if(CollectionUtils.isNotEmpty(correctAnswerIds)
                && CollectionUtils.isNotEmpty(userAnswerList)){
            if (QuestionTypeEnum.SINGLE_CHOICE.getValue().equals(question.getType())
                    || QuestionTypeEnum.TRUE_FALSE.getValue().equals(question.getType())
                    || QuestionTypeEnum.SORT.getValue().equals(question.getType())) {
                return correctAnswerIds.equals(userAnswerList);
            } else if (QuestionTypeEnum.MULTIPLE_CHOICE.getValue().equals(question.getType())) {
                return new LinkedHashSet<>(correctAnswerIds).equals(new LinkedHashSet<>(userAnswerList));
            }
        }
        if (QuestionTypeEnum.FILL_BLANK.getValue().equals(question.getType()) && CollectionUtils.isNotEmpty(correctAnswerIds)) {
            if (userAnswer == null) {
                return false;
            }

            List<QuestionAnswer> questionAnswers = questionAnswerBiz.getAnswersByQuestionId(question.getId());
            if (questionAnswers == null || questionAnswers.isEmpty()) {
                return false;
            }

            Map<String, String> idToContentMap = new HashMap<>();
            for (QuestionAnswer qa : questionAnswers) {
                idToContentMap.put(qa.getId().toString(), qa.getOptionContent());
            }

            String[] userAnswers = userAnswer.split(",");
            String[] answerIdGroups = correctAnswerIds.toArray(new String[0]);

            if (userAnswers.length != answerIdGroups.length) {
                return false;
            }

            for (int i = 0; i < answerIdGroups.length; i++) {
                String userAns = userAnswers[i].trim();

                String answerId = answerIdGroups[i];
                String acceptableAnswersStr = idToContentMap.get(answerId);

                if (acceptableAnswersStr == null) {
                    return false;
                }

                String[] acceptableAnswers = acceptableAnswersStr.split("\\|");

                boolean matchFound = false;
                for (String acceptable : acceptableAnswers) {
                    if (userAns.equals(acceptable.trim())) {
                        matchFound = true;
                        break;
                    }
                }

                if (!matchFound) {
                    return false;
                }
            }

            return true;
        } else {
            return false;
        }
    }

    /**
     * 保存空位作答结果
     */
    @Override
    public void saveBlankResult(SubmitPaperBO submitBO, Integer paperId) throws BusinessException {
        if (!CollectionUtils.isEmpty(submitBO.getBlankResults())) {
            for (SubmitBlankResultBO blankBO : submitBO.getBlankResults()) {
                if(StringUtils.isEmpty((blankBO.getAnswerIds()))){
                    continue;
                }
                SaveBlankResultBO result = new SaveBlankResultBO();
                BeanUtil.copyProperties(blankBO, result);
                result.setPaperId(paperId);
                result.setBusinessId(submitBO.getBusinessId());
                result.setBusinessType(submitBO.getBusinessType());
                result.setAppUserId(submitBO.getAppUserId());
                boolean success = blankResultBiz.saveBlankResult(result);
                if (!success) {
                    throw new BusinessException(APP_SAVE_CLOZE_RESULT_FAIL_CODE,APP_SAVE_CLOZE_RESULT_FAIL_MSG);
                }
            }
        }
    }
    @Override
    public void saveQuestionResult(SubmitPaperBO submitBO, Integer paperId) throws BusinessException {
        if (CollectionUtils.isEmpty(submitBO.getQuestionResults()) && CollectionUtils.isEmpty(submitBO.getBlankResults())) {
            throw new BusinessException(APP_PAPER_NO_ANSWER_CODE,APP_PAPER_NO_ANSWER_MSG);
        }
        if (!CollectionUtils.isEmpty(submitBO.getQuestionResults())) {
            for (SubmitQuestionResultBO resultBO : submitBO.getQuestionResults()) {
                SaveQuestionResultBO result = new SaveQuestionResultBO();
                BeanUtil.copyProperties(resultBO, result);
                result.setBusinessId(submitBO.getBusinessId());
                result.setBusinessType(submitBO.getBusinessType());
                result.setAppUserId(submitBO.getAppUserId());
                result.setPaperId(paperId);
                boolean success = questionResultBiz.saveQuestionResult(result);
                if (!success) {
                    throw new BusinessException(APP_SAVE_QUESTION_RESULT_FAIL_CODE,APP_SAVE_QUESTION_RESULT_FAIL_MSG);
                }
            }
        }
    }

    /**
     * 获取空位作答结果列表
     */
    @Override
    public List<BlankResultDTO> getBlankResults(QueryBlankResultBO queryBO) throws BusinessException {
        try {
            return blankResultBiz.getBlankResults(queryBO);
        } catch (BusinessException e) {
            log.error("获取空位作答结果失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("获取空位作答结果发生异常", e);
            throw new BusinessException(APP_GET_BLANK_RESULT_FAIL_CODE,APP_GET_BLANK_RESULT_FAIL_MSG);
        }
    }
    
    /**
     * 获取题目数据
     */
    @Override
    public Map<Integer, Question> loadQuestions(SubmitPaperBO submitBO) {
        Set<Integer> questionIds = new LinkedHashSet<>();
        
        if (!CollectionUtils.isEmpty(submitBO.getQuestionResults())) {
            submitBO.getQuestionResults().forEach(result -> questionIds.add(result.getQuestionId()));
        }
        if (!CollectionUtils.isEmpty(submitBO.getBlankResults())) {
            submitBO.getBlankResults().forEach(result -> questionIds.add(result.getQuestionId()));
        }
        
        Map<Integer, Question> questionMap = new HashMap<>();
        if (!questionIds.isEmpty()) {
            questionBiz.listByIds(questionIds).forEach(q -> questionMap.put(q.getId(), q));
        }
        
        return questionMap;
    }
    
    /**
     * 获取空位区域数据
     */
    @Override
    public Map<Integer, Map<Integer, QuestionBlankArea>> loadBlankAreas(SubmitPaperBO submitBO) {
        Map<Integer, Map<Integer, QuestionBlankArea>> result = new HashMap<>();
        
        if (CollectionUtils.isEmpty(submitBO.getBlankResults())) {
            return result;
        }
        
        Set<Integer> questionIds = submitBO.getBlankResults().stream()
            .map(SubmitBlankResultBO::getQuestionId)
            .collect(Collectors.toSet());
            
        for (Integer questionId : questionIds) {
            List<QuestionBlankArea> areas = questionBlankAreaBiz.getBlankAreasByQuestionId(questionId);
            if (!CollectionUtils.isEmpty(areas)) {
                Map<Integer, QuestionBlankArea> areaMap = areas.stream()
                    .collect(Collectors.toMap(QuestionBlankArea::getId, area -> area));
                result.put(questionId, areaMap);
            }
        }
        
        return result;
    }
    
    /**
     * 评分填空题
     * 此实现将同一题目下所有空位的答案收集成一个大的集合，然后与正确答案的集合进行一次比较
     */
    private void validateBlankAnswer(List<SubmitBlankResultBO> results, Set<Integer> questionIds) {
        if (CollectionUtils.isEmpty(results)) {
            return;
        }

        Map<Integer, Map<Integer, List<QuestionAnswer>>> blankAreaAnswerMap = questionBiz.getBlankAreaAnswerMap(questionIds);


        // 按题目ID分组
        Map<Integer, List<SubmitBlankResultBO>> resultsByQuestion = results.stream()
            .collect(Collectors.groupingBy(SubmitBlankResultBO::getQuestionId));
            
        for (Map.Entry<Integer, List<SubmitBlankResultBO>> entry : resultsByQuestion.entrySet()) {
            Integer questionId = entry.getKey();
            List<SubmitBlankResultBO> questionResults = entry.getValue();
            

            
            // 按blankIndex排序
            questionResults.sort(Comparator.comparing(SubmitBlankResultBO::getBlankIndex));
            
            // 收集用户所有空位的答案ID
            Set<String> allUserAnswers = new LinkedHashSet<>();
            for (SubmitBlankResultBO blankBO : questionResults) {
                String userAnswerIds = blankBO.getAnswerIds();
                if (StringUtils.isNotEmpty(userAnswerIds)) {
                    Arrays.stream(userAnswerIds.split(","))
                        .map(String::trim)
                        .filter(id -> !id.isEmpty())
                        .forEach(allUserAnswers::add);
                }
            }
            // 获取该题目的所有空位区域
            Map<Integer, List<QuestionAnswer>> questionBlankAreaMap = blankAreaAnswerMap.getOrDefault(questionId, Collections.emptyMap());
            if (questionBlankAreaMap == null || questionBlankAreaMap.isEmpty()) {
                continue;
            }
            // 收集所有空位的正确答案ID
            Set<String> allCorrectAnswers = new LinkedHashSet<>();
            for (SubmitBlankResultBO blankBO : questionResults) {
                List<QuestionAnswer> answers = questionBlankAreaMap.getOrDefault(blankBO.getBlankAreaId(), Collections.emptyList());
                if (!CollectionUtils.isEmpty(answers)) {
                    allCorrectAnswers.addAll(answers.stream()
                            .filter(answer -> YesOrNoEnum.YES.getCode().equals(answer.getIsAnswer()))
                            .map(e -> String.valueOf(e.getId()))
                            .collect(Collectors.toSet()));

                }
            }
            
            // 判断用户答案集合与正确答案集合是否完全匹配
            boolean allCorrect = !allUserAnswers.isEmpty() && !allCorrectAnswers.isEmpty() 
                && allUserAnswers.equals(allCorrectAnswers);
            
            // 设置该题目下所有空位的结果
            Integer result = allCorrect ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode();
            for (SubmitBlankResultBO blankBO : questionResults) {
                blankBO.setResult(result);
            }
        }
    }

    /**
     * 评分填空题
     * 调用validateBlankAnswer中的实现，将同一题目下所有空位的答案收集成一个大的集合，然后与正确答案的集合进行一次比较
     */
    @Override
    public void scoreBlankResults(List<SubmitBlankResultBO> results, Set<Integer> questionIds) {
        validateBlankAnswer(results, questionIds);
    }


    /**
     * 转换普通题目结果
     *
     * @param questionResults 题目作答结果列表
     * @param questionMap 题目信息Map
     * @return 转换后的题目结果列表
     */
    private List<QuestionResultDTO> convertQuestionResults(List<QuestionResultDTO> questionResults,
                                                          Map<Integer, QuestionResultDTO> questionMap) {
        if (CollectionUtils.isEmpty(questionResults)) {
            return Collections.emptyList();
        }

        // 复制并补充题目信息
        List<QuestionResultDTO> result = new ArrayList<>(questionResults.size());
        for (QuestionResultDTO userResult : questionResults) {
            QuestionResultDTO questionInfo = questionMap.get(userResult.getQuestionId());
            if (questionInfo != null) {
                QuestionResultDTO dto = new QuestionResultDTO();
                BeanUtil.copyProperties(userResult, dto);

                // 复制题目属性
                dto.setTitle(questionInfo.getTitle());
                dto.setType(questionInfo.getType());
                dto.setMediaType(questionInfo.getMediaType());
                dto.setOptionType(questionInfo.getOptionType());
                dto.setAnalyzes(questionInfo.getAnalyzes());
                dto.setCorrectAnswerIds(questionInfo.getCorrectAnswerIds());
                
                // 根据用户答题时的选项重建完整选项列表（包含补位逻辑）
                dto.setOptions(buildOptionsWithPadding(userResult, questionInfo));
                result.add(dto);
            }
        }
        return result;
    }

    /**
     * 根据用户答题时的选项重建完整选项列表，包含补位逻辑
     *
     * @param userResult 用户答题结果
     * @param questionInfo 题目信息
     * @return 重建后的选项列表
     */
    private List<QuestionAnswerDTO> buildOptionsWithPadding(QuestionResultDTO userResult, QuestionResultDTO questionInfo) {
        // 获取当前题目的所有选项
        List<QuestionAnswer> currentAnswers = questionAnswerBiz.getAnswersByQuestionId(userResult.getQuestionId());

        if (StringUtils.isEmpty(userResult.getUserAnswerIds())) {
            // 如果用户没有答题记录，直接返回当前选项
            return buildAnswerDTOs(currentAnswers);
        }

        // 解析用户答题时选择的选项ID
        Set<Integer> userAnswerIds = Arrays.stream(userResult.getUserAnswerIds().split(","))
                .map(String::trim)
                .filter(StringUtils::isNotEmpty)
                .map(Integer::parseInt)
                .collect(Collectors.toSet());

        // 查询用户答题时选择的选项信息（包括已删除的）
        List<QuestionAnswer> userTimeAnswers = questionAnswerBiz.getAnswersByIds(userAnswerIds);

        // 确定用户答题时的最大序号
        int maxSerialNo = userTimeAnswers.stream()
                .mapToInt(answer -> answer.getSerialNo() != null ? answer.getSerialNo() : 0)
                .max()
                .orElse(0);

        // 如果当前选项的最大序号更大，使用当前的最大序号
        int currentMaxSerialNo = currentAnswers.stream()
                .mapToInt(answer -> answer.getSerialNo() != null ? answer.getSerialNo() : 0)
                .max()
                .orElse(0);

        maxSerialNo = Math.max(maxSerialNo, currentMaxSerialNo);

        // 构建按序号排列的选项列表
        Map<Integer, QuestionAnswer> answerMap = currentAnswers.stream()
                .filter(answer -> answer.getSerialNo() != null)
                .collect(Collectors.toMap(QuestionAnswer::getSerialNo, Function.identity(), (v1, v2) -> v1));

        // 构建用户答题时选项的序号映射（用于确定正确答案的补位）
        Map<Integer, QuestionAnswer> userTimeAnswerMap = userTimeAnswers.stream()
                .filter(answer -> answer.getSerialNo() != null)
                .collect(Collectors.toMap(QuestionAnswer::getSerialNo, Function.identity(), (v1, v2) -> v1));

        List<QuestionAnswerDTO> result = new ArrayList<>();
        List<String> correctAnswerIds = new ArrayList<>();

        for (int i = 1; i <= maxSerialNo; i++) {
            QuestionAnswer answer = answerMap.get(i);
            if (answer != null) {
                // 存在的选项
                QuestionAnswerDTO answerDTO = new QuestionAnswerDTO();
                BeanUtil.copyProperties(answer, answerDTO);
                List<QuestionMediaDTO> mediaList = getAnswerMediaResources(answer.getId());
                answerDTO.setMediaUrl(mediaList);
                result.add(answerDTO);
            } else {
                // 缺失的选项，用空值补位
                QuestionAnswerDTO emptyOption = new QuestionAnswerDTO();
                emptyOption.setSerialNo(i);
                emptyOption.setQuestionId(userResult.getQuestionId());
                emptyOption.setOptionName("");
                emptyOption.setOptionContent("");
                emptyOption.setIsAnswer(0);
                result.add(emptyOption);
            }

            // 根据序号确定正确答案ID（按位置补位）
            QuestionAnswer currentAnswer = answerMap.get(i);
            QuestionAnswer userTimeAnswer = userTimeAnswerMap.get(i);

            if (currentAnswer != null && YesOrNoEnum.YES.getCode().equals(currentAnswer.getIsAnswer())) {
                // 当前选项存在且是正确答案
                correctAnswerIds.add(String.valueOf(currentAnswer.getId()));
            } else if (userTimeAnswer != null && YesOrNoEnum.YES.getCode().equals(userTimeAnswer.getIsAnswer())) {
                // 用户答题时这个位置是正确答案，但现在选项被删除了，用空字符串补位
                correctAnswerIds.add("");
            } else {
                // 这个位置不是正确答案，用空字符串占位
                correctAnswerIds.add("");
            }
        }

        // 更新正确答案ID列表（按位置补位）
        userResult.setCorrectAnswerIds(String.join(",", correctAnswerIds));

        return result;
    }

    /**
     * 构建答案DTO列表
     */
    private List<QuestionAnswerDTO> buildAnswerDTOs(List<QuestionAnswer> answers) {
        if (CollectionUtils.isEmpty(answers)) {
            return Collections.emptyList();
        }

        return answers.stream().map(answer -> {
            QuestionAnswerDTO answerDTO = new QuestionAnswerDTO();
            BeanUtil.copyProperties(answer, answerDTO);
            List<QuestionMediaDTO> mediaList = getAnswerMediaResources(answer.getId());
            answerDTO.setMediaUrl(mediaList);
            return answerDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 根据用户答题时的选项重建完整选项列表（完形填空题专用），包含补位逻辑
     *
     * @param blankResult 用户填空答题结果
     * @param currentAnswers 当前的选项列表
     * @return 重建后的选项列表
     */
    private List<QuestionAnswerDTO> buildBlankOptionsWithPadding(BlankResultDTO blankResult, List<QuestionAnswer> currentAnswers) {
        if (StringUtils.isEmpty(blankResult.getUserAnswerIds())) {
            // 如果用户没有答题记录，直接返回当前选项
            return buildBlankAnswerDTOs(currentAnswers, blankResult.getBlankAreaId());
        }

        // 解析用户答题时选择的选项ID
        Set<Integer> userAnswerIds = Arrays.stream(blankResult.getUserAnswerIds().split(","))
                .map(String::trim)
                .filter(StringUtils::isNotEmpty)
                .map(Integer::parseInt)
                .collect(Collectors.toSet());

        // 查询用户答题时选择的选项信息（包括已删除的）
        List<QuestionAnswer> userTimeAnswers = questionAnswerBiz.getAnswersByIds(userAnswerIds);

        // 确定用户答题时的最大序号
        int maxSerialNo = userTimeAnswers.stream()
                .mapToInt(answer -> answer.getSerialNo() != null ? answer.getSerialNo() : 0)
                .max()
                .orElse(0);

        // 如果当前选项的最大序号更大，使用当前的最大序号
        int currentMaxSerialNo = currentAnswers.stream()
                .mapToInt(answer -> answer.getSerialNo() != null ? answer.getSerialNo() : 0)
                .max()
                .orElse(0);

        maxSerialNo = Math.max(maxSerialNo, currentMaxSerialNo);

        // 构建按序号排列的选项列表
        Map<Integer, QuestionAnswer> answerMap = currentAnswers.stream()
                .filter(answer -> answer.getSerialNo() != null)
                .collect(Collectors.toMap(QuestionAnswer::getSerialNo, Function.identity(), (v1, v2) -> v1));

        // 构建用户答题时选项的序号映射（用于确定正确答案的补位）
        Map<Integer, QuestionAnswer> userTimeAnswerMap = userTimeAnswers.stream()
                .filter(answer -> answer.getSerialNo() != null)
                .collect(Collectors.toMap(QuestionAnswer::getSerialNo, Function.identity(), (v1, v2) -> v1));

        List<QuestionAnswerDTO> result = new ArrayList<>();
        List<String> correctAnswerIds = new ArrayList<>();

        for (int i = 1; i <= maxSerialNo; i++) {
            QuestionAnswer answer = answerMap.get(i);
            if (answer != null) {
                // 存在的选项
                QuestionAnswerDTO answerDTO = new QuestionAnswerDTO();
                BeanUtil.copyProperties(answer, answerDTO);
                answerDTO.setBlankAreaId(blankResult.getBlankAreaId());
                result.add(answerDTO);
            } else {
                // 缺失的选项，用空值补位
                QuestionAnswerDTO emptyOption = new QuestionAnswerDTO();
                emptyOption.setSerialNo(i);
                emptyOption.setQuestionId(blankResult.getQuestionId());
                emptyOption.setBlankAreaId(blankResult.getBlankAreaId());
                emptyOption.setOptionName("");
                emptyOption.setOptionContent("");
                emptyOption.setIsAnswer(0);
                result.add(emptyOption);
            }

            // 根据序号确定正确答案ID（按位置补位）
            QuestionAnswer currentAnswer = answerMap.get(i);
            QuestionAnswer userTimeAnswer = userTimeAnswerMap.get(i);

            if (currentAnswer != null && YesOrNoEnum.YES.getCode().equals(currentAnswer.getIsAnswer())) {
                // 当前选项存在且是正确答案
                correctAnswerIds.add(String.valueOf(currentAnswer.getId()));
            } else if (userTimeAnswer != null && YesOrNoEnum.YES.getCode().equals(userTimeAnswer.getIsAnswer())) {
                // 用户答题时这个位置是正确答案，但现在选项被删除了，用空字符串补位
                correctAnswerIds.add("");
            } else {
                // 这个位置不是正确答案，用空字符串占位
                correctAnswerIds.add("");
            }
        }

        // 更新正确答案ID列表（按位置补位）
        blankResult.setCorrectAnswerIds(String.join(",", correctAnswerIds));

        return result;
    }

    /**
     * 构建填空题答案DTO列表
     */
    private List<QuestionAnswerDTO> buildBlankAnswerDTOs(List<QuestionAnswer> answers, Integer blankAreaId) {
        if (CollectionUtils.isEmpty(answers)) {
            return Collections.emptyList();
        }

        return answers.stream().map(answer -> {
            QuestionAnswerDTO answerDTO = new QuestionAnswerDTO();
            BeanUtil.copyProperties(answer, answerDTO);
            answerDTO.setBlankAreaId(blankAreaId);
            return answerDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 根据保存的题目顺序重建完整题目列表，包含题目级别的补位逻辑
     *
     * @param questionResults 实际的题目结果列表
     * @param savedOrder 用户答题时保存的题目顺序
     * @return 重建后的题目列表
     */
    private List<QuestionResultDTO> buildQuestionsWithPadding(List<QuestionResultDTO> questionResults, List<Integer> savedOrder) {
        Map<Integer, QuestionResultDTO> resultMap = questionResults.stream()
                .collect(Collectors.toMap(QuestionResultDTO::getQuestionId, Function.identity(), (v1, v2) -> v1));

        List<QuestionResultDTO> orderedResults = new ArrayList<>();

        // 按照保存的顺序重建题目列表
        for (Integer questionId : savedOrder) {
            if (resultMap.containsKey(questionId)) {
                // 存在的题目
                orderedResults.add(resultMap.get(questionId));
            } else {
                // 缺失的题目，用空值补位
                QuestionResultDTO emptyQuestion = new QuestionResultDTO();
                emptyQuestion.setQuestionId(questionId);
                emptyQuestion.setTitle("");
                emptyQuestion.setType(null);
                emptyQuestion.setMediaType(null);
                emptyQuestion.setOptionType(null);
                emptyQuestion.setUserAnswer("");
                emptyQuestion.setUserAnswerIds("");
                emptyQuestion.setCorrectAnswer("");
                emptyQuestion.setCorrectAnswerIds("");
                emptyQuestion.setAnalyzes("");
                emptyQuestion.setResult(0); // 标记为错误
                emptyQuestion.setOptions(Collections.emptyList());
                orderedResults.add(emptyQuestion);
            }
        }

        // 添加可能不在顺序中的题目（这些题目可能是后来添加的）
        questionResults.forEach(result -> {
            if (!savedOrder.contains(result.getQuestionId())) {
                orderedResults.add(result);
            }
        });

        return orderedResults;
    }

    /**
     * 转换填空题结果
     *
     * @param blankResults 填空题作答结果列表
     * @param questionMap 题目信息Map
     * @param blankAreaAnswerMap 空位区域答案Map，按questionId和blankAreaId两级分组
     * @return 按题目ID分组的填空题作答结果
     */
    protected Map<Integer, List<BlankResultDTO>> convertBlankResults(List<BlankResultDTO> blankResults,
                                                                   Map<Integer, QuestionResultDTO> questionMap,
                                                                   Map<Integer, Map<Integer, List<QuestionAnswer>>> blankAreaAnswerMap) throws BusinessException {
        if (CollectionUtils.isEmpty(blankResults)) {
            return Collections.emptyMap();
        }

        // 按题目ID分组
        Map<Integer, List<BlankResultDTO>> resultMap = blankResults.stream()
                .collect(Collectors.groupingBy(BlankResultDTO::getQuestionId));

        // 处理每个题目的空位结果
        for (Map.Entry<Integer, List<BlankResultDTO>> entry : resultMap.entrySet()) {
            Integer questionId = entry.getKey();
            List<BlankResultDTO> questionBlankResults = entry.getValue();

            // 获取题目信息
            QuestionResultDTO questionInfo = questionMap.get(questionId);
            if (questionInfo == null) {
                continue;
            }

            // 获取该题目的所有空白区域答案
            Map<Integer, List<QuestionAnswer>> questionBlankAreaMap = blankAreaAnswerMap.getOrDefault(questionId, Collections.emptyMap());

            // 处理每个空位结果
            for (BlankResultDTO blankResult : questionBlankResults) {
                // 获取空位ID
                Integer blankAreaId = blankResult.getBlankAreaId();

                // 根据用户答题时的选项重建完整选项列表（包含补位逻辑）
                blankResult.setOptions(buildBlankOptionsWithPadding(blankResult, questionBlankAreaMap.getOrDefault(blankAreaId, Collections.emptyList())));
            }
        }

        return resultMap;
    }

    /**
     * 校验查询参数
     */
    protected void validateQueryParams(QueryPaperQuestionsBO queryBO) throws BusinessException {
        if (queryBO == null) {
            throw new BusinessException(APP_QUERY_PARAM_NOT_NULL_CODE,APP_QUERY_PARAM_NOT_NULL_MSG);
        }
        if (queryBO.getBusinessId() == null) {
            throw new BusinessException(APP_BUSINESS_ID_NOT_NULL_CODE,APP_BUSINESS_ID_NOT_NULL_MSG);
        }
        if (queryBO.getBusinessType() == null) {
            throw new BusinessException(APP_BUSINESS_TYPE_NOT_NULL_CODE,APP_BUSINESS_TYPE_NOT_NULL_MSG);
        }
    }



    /**
     * 获取题目信息Map
     *
     * @param questionIds 题目ID列表
     * @return 题目信息Map
     */
    private Map<Integer, QuestionResultDTO> getQuestionMap(Set<Integer> questionIds) {
        if (CollectionUtils.isEmpty(questionIds)) {
            return Collections.emptyMap();
        }

        List<Question> questions = questionBiz.getBaseMapper().selectList(
                new LambdaQueryWrapper<Question>()
                        .in(Question::getId, questionIds)
        );

        if (CollectionUtils.isEmpty(questions)) {
            return Collections.emptyMap();
        }

        Map<Integer, QuestionResultDTO> resultMap = new HashMap<>();
        for (Question question : questions) {
            QuestionResultDTO dto = new QuestionResultDTO();
            dto.setQuestionId(question.getId());
            dto.setTitle(question.getTitle());
            dto.setType(question.getType());
            dto.setMediaType(question.getMediaType());
            dto.setOptionType(question.getOptionType());
            dto.setAnalyzes(question.getAnalyzes());

            resultMap.put(question.getId(), dto);
        }

        return resultMap;
    }

    /**
     * 获取用户试卷普通题目作答结果
     *
     * @param questionResultBO 查询参数
     * @return 普通题目作答结果列表
     */
    private List<QuestionResultDTO> queryPaperQuestionResults(QueryQuestionResultBO questionResultBO) throws BusinessException {
        List<QuestionResultDTO> entities = questionResultBiz.getQuestionResults(questionResultBO);
        if (CollectionUtils.isEmpty(entities)) {
            return Collections.emptyList();
        }
        return entities;
    }



    /**
     * 获取题目列表的具体实现
     * 由子类实现具体的业务逻辑
     *
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @return 题目列表
     * @throws BusinessException 业务异常
     */
    protected abstract List<Question> getQuestionsByBusinessId(Integer businessId, Integer businessType) throws BusinessException;


    @Override
    public void saveWrongQuestion(SubmitPaperBO submitBO,Integer paperId) throws BusinessException {
        List<SubmitQuestionResultBO> normalResults = submitBO.getQuestionResults().stream().filter(e -> e.getResult().equals(YesOrNoEnum.NO.getCode())).toList();
        List<SubmitBlankResultBO> blankResults = submitBO.getBlankResults().stream().filter(e -> e.getResult().equals(YesOrNoEnum.NO.getCode())).collect(Collectors.toList());
        List<AddWrongQuestionBO> addWrongQuestionBOList = new ArrayList<>();
        for (SubmitQuestionResultBO questionResult : normalResults) {
            AddWrongQuestionBO wrongQuestionBO = new AddWrongQuestionBO();

            if(submitBO.getBusinessType() != null && submitBO.getBusinessType().equals(BusinessTypeEnum.EVALUATION_BUSINESS.getCode())){
                wrongQuestionBO.setSourceId(submitBO.getBusinessId());
                wrongQuestionBO.setSourceType(WrongQuestionSourceTypeEnum.USER_EVALUATION.getCode());
            }else if(submitBO.getBusinessType() != null && submitBO.getBusinessType().equals(BusinessTypeEnum.QUESTION_BUSINESS.getCode())){
                wrongQuestionBO.setSourceId(paperId);
                wrongQuestionBO.setSourceType(WrongQuestionSourceTypeEnum.USER_PRACTICE.getCode());
            } else if(submitBO.getBusinessType() != null && submitBO.getBusinessType().equals(BusinessTypeEnum.STUDY_CENTER_QUESTION_BUSINESS.getCode())){
                wrongQuestionBO.setSourceId(paperId);
                wrongQuestionBO.setSourceType(WrongQuestionSourceTypeEnum.STUDY_CENTER.getCode());
            }

            wrongQuestionBO.setAppUserId(submitBO.getAppUserId());
            wrongQuestionBO.setQuestionId(questionResult.getQuestionId());
            wrongQuestionBO.setUserAnswer(questionResult.getUserAnswer());
            wrongQuestionBO.setUserAnswerIds(questionResult.getAnswerIds());

            addWrongQuestionBOList.add(wrongQuestionBO);
        }
        if(CollectionUtils.isNotEmpty(blankResults)){
            AddWrongQuestionBO wrongQuestionBO = new AddWrongQuestionBO();
            wrongQuestionBO.setAppUserId(submitBO.getAppUserId());
            List<WrongQuestionBlankBO> wrongQuestionBlankBOS = new ArrayList<>();
            for (SubmitBlankResultBO blankResult : blankResults) {
                WrongQuestionBlankBO wrongQuestionBlankBO = new WrongQuestionBlankBO();
                wrongQuestionBlankBO.setBlankAreaId(blankResult.getBlankAreaId());
                wrongQuestionBlankBO.setQuestionId(blankResult.getQuestionId());
                wrongQuestionBlankBO.setBlankIndex(blankResult.getBlankIndex());
                wrongQuestionBlankBO.setUserAnswer(blankResult.getAnswerIds());
                wrongQuestionBlankBO.setUserAnswerIds(blankResult.getAnswerIds());
                wrongQuestionBlankBOS.add(wrongQuestionBlankBO);
            }
            wrongQuestionBO.setBlankResults(wrongQuestionBlankBOS);
            addWrongQuestionBOList.add(wrongQuestionBO);
        }
        if(CollectionUtils.isNotEmpty(addWrongQuestionBOList)){
            log.info("开始批量增加错题：{}", JSON.toJSONString(addWrongQuestionBOList));
            wrongQuestionService.batchAddWrongQuestions(addWrongQuestionBOList);
        }
    }

    /**
     * 获取题目正确答案列表的模板方法实现
     *
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @param questionIds 指定题目ID列表，若为空则获取所有题目
     * @return 题目正确答案列表
     * @throws BusinessException 业务异常
     */
    @Override
    public List<QuestionCorrectAnswerDTO> getQuestionsCorrectAnswers(Integer businessId, Integer businessType, List<Integer> questionIds) throws BusinessException {
        List<Question> questions;
        if (CollectionUtils.isNotEmpty(questionIds)) {
            questions = questionBiz.getBaseMapper().selectByIds(questionIds);
        } else {
            questions = getQuestionsByBusinessId(businessId, businessType);
        }
        if (CollectionUtils.isEmpty(questions)) {
            return Collections.emptyList();
        }
        List<QuestionCorrectAnswerDTO> result = new ArrayList<>();
        for (Question question : questions) {
            List<QuestionAnswer> answers = questionAnswerBiz.getAnswersByQuestionId(question.getId());
            result.add(buildCorrectAnswerDTO(question, answers));
        }
        return result;
    }

    /**
     * 钩子方法：由子类实现具体答案内容拼接和分支逻辑
     */
    protected QuestionCorrectAnswerDTO buildCorrectAnswerDTO(Question question, List<QuestionAnswer> answers) {
        QuestionCorrectAnswerDTO dto = new QuestionCorrectAnswerDTO();
        dto.setQuestionId(question.getId());
        dto.setTitle(question.getTitle());
        dto.setType(question.getType());
        dto.setCorrectAnswerIds(question.getAnswer());

        if (QuestionTypeEnum.CLOZE.getValue().equals(question.getType())) {
            List<QuestionBlankArea> blankAreas = questionBlankAreaBiz.getBlankAreasByQuestionId(question.getId());
            if (!CollectionUtils.isEmpty(blankAreas)) {
                List<BlankCorrectAnswerDTO> blankResults = new ArrayList<>();
                Map<Integer, Map<Integer, List<QuestionAnswer>>> blankAnswerMap = questionBiz.getBlankAreaAnswerMap(Set.of(question.getId()));
                for (QuestionBlankArea area : blankAreas) {
                    BlankCorrectAnswerDTO blankDto = new BlankCorrectAnswerDTO();
                    blankDto.setBlankAreaId(area.getId());
                    blankDto.setBlankIndex(area.getBlankIndex());
                    if (area.getAnswerIds() != null) {
                        List<QuestionAnswer> blankAnswers = blankAnswerMap.getOrDefault(question.getId(), Collections.emptyMap())
                                .getOrDefault(area.getId(), Collections.emptyList());
                        if (!CollectionUtils.isEmpty(blankAnswers)) {
                            Set<String> correctIds = Arrays.stream(area.getAnswerIds().split(",")).collect(Collectors.toSet());
                            List<String> correctContents = new ArrayList<>();
                            for (QuestionAnswer answer : blankAnswers) {
                                if (correctIds.contains(String.valueOf(answer.getId()))) {
                                    if (OptionTypeEnum.TEXT.getCode().equals(question.getOptionType())) {
                                        correctContents.add(QuestionTypeEnum.displayOptionContent(question.getOptionType())  ? answer.getOptionContent() : answer.getOptionName());
                                    } else if (Objects.requireNonNull(OptionTypeEnum.getByCode(question.getOptionType())).isMedia()) {
                                        List<QuestionMediaDTO> mediaList = queryAnswerMediaResources(answer.getId());
                                        if (!CollectionUtils.isEmpty(mediaList)) {
                                            mediaList.forEach(media -> correctContents.add(media.getMaterialPath()));
                                        }
                                    }
                                }
                            }
                            blankDto.setCorrectAnswer(String.join(",", correctContents));
                        } else {
                            blankDto.setCorrectAnswer("");
                        }
                    } else {
                        blankDto.setCorrectAnswer("");
                    }
                    blankResults.add(blankDto);
                }
                dto.setBlankResults(blankResults);
            }
            dto.setCorrectAnswer("");
        } else {
            if (!CollectionUtils.isEmpty(answers) && question.getAnswer() != null) {
                Set<String> correctIds = Arrays.stream(question.getAnswer().split(",")).collect(Collectors.toSet());
                List<String> correctContents = new ArrayList<>();
                for (QuestionAnswer answer : answers) {
                    if (correctIds.contains(String.valueOf(answer.getId()))) {
                        if (OptionTypeEnum.TEXT.getCode().equals(question.getOptionType())) {
                            correctContents.add(QuestionTypeEnum.displayOptionContent(question.getOptionType())  ? answer.getOptionContent() : answer.getOptionName());
                        } else if (Objects.requireNonNull(OptionTypeEnum.getByCode(question.getOptionType())).isMedia()) {
                            List<QuestionMediaDTO> mediaList = queryAnswerMediaResources(answer.getId());
                            if (!CollectionUtils.isEmpty(mediaList)) {
                                mediaList.forEach(media -> correctContents.add(media.getMaterialPath()));
                            }
                        }
                    }
                }
                dto.setCorrectAnswer(String.join(",", correctContents));
            } else {
                dto.setCorrectAnswer("");
            }
        }
        return dto;
    }

    /**
     * 查询选项的媒体资源
     *
     * @param answerId 选项ID
     * @return 媒体资源列表
     */
    private List<QuestionMediaDTO> queryAnswerMediaResources(Integer answerId) {
        List<AppMaterialBusinessRef> answerMediaRefs = materialBusinessRefBiz.list(
                new LambdaQueryWrapper<AppMaterialBusinessRef>()
                        .eq(AppMaterialBusinessRef::getBusinessId, answerId)
                        .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.ANSWER_MEDIA_BUSINESS.getCode())
                        .orderByAsc(AppMaterialBusinessRef::getOrderNum)
        );

        if (CollectionUtils.isEmpty(answerMediaRefs)) {
            return Collections.emptyList();
        }

        return answerMediaRefs.stream()
                .map(ref -> {
                    QuestionMediaDTO media = new QuestionMediaDTO();
                    media.setMaterialId(ref.getAppMaterialId());
                    media.setSortNum(ref.getOrderNum());
                    AppMaterial material = materialMapper.selectById(ref.getAppMaterialId());
                    if (material != null) {
                        media.setMaterialPath(material.getMaterialPath());
                    }
                    return media;
                })
                .collect(Collectors.toList());
    }

    /**
     * 通用模板方法：将 List<Question> 转换为 List<PaperQuestionDTO>
     * @param questions 题目列表
     * @param questionOrderMap 题目ID到排序号的映射（可为空）
     * @return 题目DTO列表
     */
    protected List<PaperQuestionDTO> buildPaperQuestionDTOs(List<Question> questions, Map<Integer, Integer> questionOrderMap) throws BusinessException {
        if (questions == null || questions.isEmpty()) {
            throw new BusinessException("题目列表为空");
        }
        Map<Integer, Integer> typeCountMap = new LinkedHashMap<>();
        for (Question question : questions) {
            typeCountMap.merge(question.getType(), 1, Integer::sum);
        }
        Map<Integer, Integer> typeIndexMap = new LinkedHashMap<>();
        List<PaperQuestionDTO> result = new LinkedList<>();
        int sortNum = 1;
        for (Question question : questions) {
            PaperQuestionDTO dto = new PaperQuestionDTO();
            BeanUtil.copyProperties(question, dto);
            dto.setTypeName(QuestionTypeEnum.getByValue(question.getType()).getDesc());
            if (questionOrderMap != null && questionOrderMap.containsKey(question.getId())) {
                dto.setSortNum(questionOrderMap.get(question.getId()));
            } else {
                dto.setSortNum(sortNum++);
            }
            int currentIndex = typeIndexMap.getOrDefault(question.getType(), 0) + 1;
            typeIndexMap.put(question.getType(), currentIndex);
            dto.setTypeCount(typeCountMap.get(question.getType()));
            dto.setTypeIndex(currentIndex);

            List<AppMaterialBusinessRef> mediaRefs = materialBusinessRefBiz.list(
                    new LambdaQueryWrapper<AppMaterialBusinessRef>()
                            .eq(AppMaterialBusinessRef::getBusinessId, question.getId())
                            .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.QUESTION_MEDIA_BUSINESS.getCode())
                            .orderByAsc(AppMaterialBusinessRef::getOrderNum)
            );
            if (!CollectionUtils.isEmpty(mediaRefs)) {
                List<QuestionMediaDTO> mediaList = mediaRefs.stream()
                        .map(ref -> {
                            QuestionMediaDTO media = new QuestionMediaDTO();
                            media.setMaterialId(ref.getAppMaterialId());
                            media.setSortNum(ref.getOrderNum());
                            AppMaterial material = materialMapper.selectById(ref.getAppMaterialId());
                            if(material !=null){
                                media.setMaterialPath(material.getMaterialPath());
                                media.setMaterialName(material.getMaterialName());
                            }
                            return media;
                        })
                        .collect(Collectors.toList());
                dto.setMediaUrl(mediaList);
            }
            List<AppMaterialBusinessRef> recognitionRefs = materialBusinessRefBiz.list(
                    new LambdaQueryWrapper<AppMaterialBusinessRef>()
                            .eq(AppMaterialBusinessRef::getBusinessId, question.getId())
                            .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.QUESTION_RECOGNITION_BUSINESS.getCode())
                            .orderByAsc(AppMaterialBusinessRef::getOrderNum)
            );
            if (!CollectionUtils.isEmpty(recognitionRefs)) {
                List<QuestionRecognitionDTO> recognitionList = recognitionRefs.stream()
                        .map(ref -> {
                            QuestionRecognitionDTO recognition = new QuestionRecognitionDTO();
                            recognition.setMaterialId(ref.getAppMaterialId());
                            recognition.setSortNum(ref.getOrderNum());
                            AppMaterial material = materialMapper.selectById(ref.getAppMaterialId());
                            if(material !=null){
                                recognition.setMaterialPath(material.getMaterialPath());
                                recognition.setMaterialName(material.getMaterialName());
                            }
                            return recognition;
                        })
                        .collect(Collectors.toList());
                dto.setAidedRecognitionUrl(recognitionList);
            }

            List<QuestionAnswer> answers = questionAnswerBiz.getAnswersByQuestionId(question.getId());
            if (!CollectionUtils.isEmpty(answers) && !Objects.equals(question.getType(), QuestionTypeEnum.CLOZE.getValue()) ) {
                List<QuestionAnswerDTO> answerDTOs = answers.stream()
                        .map(answer -> {
                            QuestionAnswerDTO answerDTO = new QuestionAnswerDTO();
                            BeanUtil.copyProperties(answer, answerDTO);
                            List<AppMaterialBusinessRef> answerMediaRefs = materialBusinessRefBiz.list(
                                    new LambdaQueryWrapper<AppMaterialBusinessRef>()
                                            .eq(AppMaterialBusinessRef::getBusinessId, answer.getId())
                                            .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.ANSWER_MEDIA_BUSINESS.getCode())
                                            .orderByAsc(AppMaterialBusinessRef::getOrderNum)
                            );
                            if (!CollectionUtils.isEmpty(answerMediaRefs)) {
                                List<QuestionMediaDTO> mediaList = answerMediaRefs.stream()
                                        .map(ref -> {
                                            QuestionMediaDTO media = new QuestionMediaDTO();
                                            media.setMaterialId(ref.getAppMaterialId());
                                            media.setSortNum(ref.getOrderNum());
                                            AppMaterial material = materialMapper.selectById(ref.getAppMaterialId());
                                            if(material !=null){
                                                media.setMaterialPath(material.getMaterialPath());
                                                media.setMaterialName(material.getMaterialName());
                                            }
                                            return media;
                                        })
                                        .collect(Collectors.toList());
                                answerDTO.setMediaUrl(mediaList);
                            }
                            return answerDTO;
                        })
                        .collect(Collectors.toList());
                if (QuestionTypeEnum.SORT.getValue().equals(question.getType())) {
                    Collections.shuffle(answerDTOs);
                }
                dto.setAnswers(answerDTOs);
            } else if (Objects.equals(question.getType(), QuestionTypeEnum.CLOZE.getValue())) {
                List<QuestionBlankArea> blankAreas = questionBlankAreaBiz.getBlankAreasByQuestionId(question.getId());
                if (!CollectionUtils.isEmpty(blankAreas)) {
                    List<QuestionBlankAreaDTO> blankAreaDTOs = new ArrayList<>();
                    for (QuestionBlankArea area : blankAreas) {
                        QuestionBlankAreaDTO areaDTO = new QuestionBlankAreaDTO();
                        BeanUtil.copyProperties(area, areaDTO);
                        List<QuestionAnswer> blankAnswers = questionAnswerBiz.getAnswersByBlankAreaId(question.getId(), area.getId());
                        if (!CollectionUtils.isEmpty(blankAnswers)) {
                            List<QuestionAnswerDTO> blankAnswerDTOs = blankAnswers.stream()
                                    .map(blankAnswer -> {
                                        QuestionAnswerDTO answerDTO = new QuestionAnswerDTO();
                                        BeanUtil.copyProperties(blankAnswer, answerDTO);
                                        List<AppMaterialBusinessRef> blankAnswerMediaRefs = materialBusinessRefBiz.list(
                                                new LambdaQueryWrapper<AppMaterialBusinessRef>()
                                                        .eq(AppMaterialBusinessRef::getBusinessId, blankAnswer.getId())
                                                        .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.ANSWER_MEDIA_BUSINESS.getCode())
                                                        .orderByAsc(AppMaterialBusinessRef::getOrderNum)
                                        );
                                        if (!CollectionUtils.isEmpty(blankAnswerMediaRefs)) {
                                            List<QuestionMediaDTO> mediaList = blankAnswerMediaRefs.stream()
                                                    .map(ref -> {
                                                        QuestionMediaDTO media = new QuestionMediaDTO();
                                                        media.setMaterialId(ref.getAppMaterialId());
                                                        media.setSortNum(ref.getOrderNum());
                                                        AppMaterial material = materialMapper.selectById(ref.getAppMaterialId());
                                                        if(material !=null){
                                                            media.setMaterialPath(material.getMaterialPath());
                                                            media.setMaterialName(material.getMaterialName());
                                                        }
                                                        return media;
                                                    })
                                                    .collect(Collectors.toList());
                                            answerDTO.setMediaUrl(mediaList);
                                        }
                                        return answerDTO;
                                    })
                                    .collect(Collectors.toList());
                            areaDTO.setAnswers(blankAnswerDTOs);
                        }
                        blankAreaDTOs.add(areaDTO);
                    }
                    dto.setBlankAreas(blankAreaDTOs);
                }
            }
            result.add(dto);
        }
        result.sort(Comparator.comparing(PaperQuestionDTO::getSortNum).thenComparing(PaperQuestionDTO::getId));
        return result;
    }


    @Override
    public PaperQuestionResultDTO getPaperQuestionResult(QueryPaperResultBO queryBO) throws BusinessException {
        log.info("获取试卷结果, 入参:{}",JSON.toJSONString(queryBO));
        validateParam(queryBO);
        try {
            PaperQuestionResultDTO resultDTO = new PaperQuestionResultDTO();

            AppUserPaperInfo paperInfo = appUserPaperInfoMapper.selectOne(
                    new LambdaQueryWrapper<AppUserPaperInfo>()
                            .eq(AppUserPaperInfo::getId, queryBO.getPaperId())
                            .eq(AppUserPaperInfo::getAppUserId, queryBO.getAppUserId())
                            .eq(AppUserPaperInfo::getIsDeleted, YesOrNoEnum.NO.getCode())
            );

            List<Integer> savedOrder;
            if (paperInfo != null && StringUtils.isNotEmpty(paperInfo.getQuestionOrder())) {
                savedOrder = Arrays.stream(paperInfo.getQuestionOrder().split(","))
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
            } else {
                savedOrder = null;
            }
            List<QuestionResultDTO> questionResults = queryPaperQuestionResults(QueryQuestionResultBO.builder()
                    .paperId(queryBO.getPaperId())
                    .appUserId(queryBO.getAppUserId())
                    .build());
            List<BlankResultDTO> blankResults = getBlankResults(QueryBlankResultBO.builder()
                    .paperId(queryBO.getPaperId())
                    .appUserId(queryBO.getAppUserId())
                    .build());

            Set<Integer> questionIds = new LinkedHashSet<>();
            questionResults.forEach(result -> questionIds.add(result.getQuestionId()));
            blankResults.forEach(result -> questionIds.add(result.getQuestionId()));

            if (questionIds.isEmpty()) {
                resultDTO.setQuestionResults(Collections.emptyList());
                resultDTO.setCorrectCount(0);
                resultDTO.setWrongCount(0);
                return resultDTO;
            }

            Map<Integer, QuestionResultDTO> questionMap = getQuestionMap(questionIds);
            List<QuestionResultDTO> normalResults = convertQuestionResults(questionResults, questionMap);
            Map<Integer, Map<Integer, List<QuestionAnswer>>> blankAreaAnswerMap = questionBiz.getBlankAreaAnswerMap(questionIds);
            Map<Integer, List<BlankResultDTO>> blankResultMap = convertBlankResults(blankResults, questionMap, blankAreaAnswerMap);
            
            // 合并普通题和完形填空题结果
            List<QuestionResultDTO> finalResults = new ArrayList<>(normalResults);
            
            // 设置完形填空题的空白区域结果
            for (QuestionResultDTO result : normalResults) {
                if (QuestionTypeEnum.CLOZE.getValue().equals(result.getType())) {
                    List<BlankResultDTO> blankList = blankResultMap.get(result.getQuestionId());
                    if (blankList != null) {
                        result.setBlankResults(blankList);
                    }
                }
            }
            
            // 处理不在normalResults中的完形填空题
            Set<Integer> processedQuestionIds = normalResults.stream()
                    .map(QuestionResultDTO::getQuestionId)
                    .collect(Collectors.toSet());

            for (Map.Entry<Integer, List<BlankResultDTO>> entry : blankResultMap.entrySet()) {
                Integer questionId = entry.getKey();
                if (processedQuestionIds.contains(questionId)) {
                    continue;
                }
                QuestionResultDTO questionInfo = questionMap.get(questionId);
                if (questionInfo != null && QuestionTypeEnum.CLOZE.getValue().equals(questionInfo.getType())) {
                    QuestionResultDTO clozeResult = new QuestionResultDTO();
                    BeanUtil.copyProperties(questionInfo, clozeResult);
                    List<BlankResultDTO> blankList = entry.getValue();
                    clozeResult.setBlankResults(blankList);
                    finalResults.add(clozeResult);
                }
            }
            
            // 统一处理所有题目资源
            processQuestionResources(finalResults);

            // 处理题目顺序和补位逻辑
            if (savedOrder != null && !savedOrder.isEmpty()) {
                finalResults = buildQuestionsWithPadding(finalResults, savedOrder);
            }
            resultDTO.setQuestionResults(finalResults);
            int correctCount = 0;
            int wrongCount = 0;
            for (QuestionResultDTO result : finalResults) {
                if (QuestionTypeEnum.CLOZE.getValue().equals(result.getType())) {
                    List<BlankResultDTO> questionBlankResults = result.getBlankResults();
                    if (!CollectionUtils.isEmpty(questionBlankResults)) {
                        boolean allCorrect = questionBlankResults.stream()
                                .allMatch(blank -> blank.getResult().equals(YesOrNoEnum.YES.getCode()));
                        if (allCorrect) {
                            correctCount++;
                        } else {
                            wrongCount++;
                        }
                    } else {
                        wrongCount++;
                    }
                } else {
                    if (YesOrNoEnum.YES.getCode().equals(result.getResult())) {
                        correctCount++;
                    } else {
                        wrongCount++;
                    }
                }
            }

            resultDTO.setCorrectCount(correctCount);
            resultDTO.setWrongCount(wrongCount);
            return resultDTO;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取试卷结果失败", e);
            throw new BusinessException(APP_GET_PAPER_RESULT_FAIL_CODE, APP_GET_PAPER_RESULT_FAIL_MSG);
        }
    }

    /**
     * 参数校验
     */
    protected void validateParam(Object param) throws BusinessException {
        if (param == null) {
            throw new BusinessException(APP_PARAM_NOT_NULL_CODE, APP_PARAM_NOT_NULL_MSG);
        }
    }

    /**
     * 处理普通题目的答案显示
     */
    protected void processAnswerDisplay(QuestionResultDTO result) {
        if (!CollectionUtils.isEmpty(result.getOptions()) && StringUtils.isNotEmpty(result.getCorrectAnswerIds())) {
            List<String> correctAnswers = new ArrayList<>();
            String[] correctIds = result.getCorrectAnswerIds().split(",");

            // 按位置处理正确答案，与选项列表一一对应
            for (int i = 0; i < correctIds.length; i++) {
                String correctId = correctIds[i];
                if (StringUtils.isEmpty(correctId.trim())) {
                    // 空字符串表示删除的选项，用空字符串补位
                    correctAnswers.add("");
                    continue;
                }

                try {
                    Integer id = Integer.valueOf(correctId.trim());
                    // 查找对应位置的选项
                    if (i < result.getOptions().size()) {
                        QuestionAnswerDTO option = result.getOptions().get(i);
                        if (option.getId() != null && option.getId().equals(id)) {
                            // 选项ID匹配，获取选项内容
                            if(result.getOptionType().equals(OptionTypeEnum.TEXT.getCode())){
                                correctAnswers.add(QuestionTypeEnum.displayOptionContent(result.getType()) ? option.getOptionContent() : option.getOptionName());
                            }else if (Objects.requireNonNull(OptionTypeEnum.getByCode(result.getOptionType())).isMedia()){
                                if (!CollectionUtils.isEmpty(option.getMediaUrl())) {
                                    correctAnswers.add(option.getMediaUrl().get(0).getMaterialPath());
                                } else {
                                    correctAnswers.add("");
                                }
                            }
                        } else {
                            // 选项ID不匹配，说明选项被删除了
                            correctAnswers.add("");
                        }
                    } else {
                        // 超出选项范围
                        correctAnswers.add("");
                    }
                } catch (NumberFormatException ignored) {
                    // 无效的ID，用空字符串补位
                    correctAnswers.add("");
                }
            }
            result.setCorrectAnswer(String.join(",", correctAnswers));
        }
        if (!CollectionUtils.isEmpty(result.getOptions()) && StringUtils.isNotEmpty(result.getUserAnswerIds())) {
            List<String> userAnswers = new ArrayList<>();
            String[] userIds = result.getUserAnswerIds().split(",");
            for (String userId : userIds) {
                if (StringUtils.isEmpty(userId.trim())) {
                    // 空字符串表示删除的选项，用空字符串补位
                    userAnswers.add("");
                    continue;
                }
                try {
                    Integer id = Integer.valueOf(userId.trim());
                    result.getOptions().stream()
                            .filter(option -> option.getId() != null && option.getId().equals(id))
                            .findFirst()
                            .ifPresent(option -> {
                                if(result.getOptionType().equals(OptionTypeEnum.TEXT.getCode())){
                                    userAnswers.add(QuestionTypeEnum.displayOptionContent(result.getType())  ? option.getOptionContent() : option.getOptionName());
                                }else if (Objects.requireNonNull(OptionTypeEnum.getByCode(result.getOptionType())).isMedia()){
                                    for (QuestionMediaDTO mediaDTO : option.getMediaUrl()) {
                                        userAnswers.add(mediaDTO.getMaterialPath());
                                    }
                                }
                            });
                } catch (NumberFormatException ignored) {
                    // 无效的ID，用空字符串补位
                    userAnswers.add("");
                }
            }
            result.setUserAnswer(String.join(",", userAnswers));
        }
    }

    /**
     * 处理填空题答案显示
     */
    protected void processBlankAnswerDisplay(BlankResultDTO blank,Integer optionType) {
        if (!CollectionUtils.isEmpty(blank.getOptions()) && StringUtils.isNotEmpty(blank.getCorrectAnswerIds())) {
            List<String> correctAnswers = new ArrayList<>();
            String[] correctIds = blank.getCorrectAnswerIds().split(",");

            // 按位置处理正确答案，与选项列表一一对应
            for (int i = 0; i < correctIds.length; i++) {
                String correctId = correctIds[i];
                if (StringUtils.isEmpty(correctId.trim())) {
                    // 空字符串表示删除的选项，用空字符串补位
                    correctAnswers.add("");
                    continue;
                }

                try {
                    Integer id = Integer.valueOf(correctId.trim());
                    // 查找对应位置的选项
                    if (i < blank.getOptions().size()) {
                        QuestionAnswerDTO option = blank.getOptions().get(i);
                        if (option.getId() != null && option.getId().equals(id)) {
                            // 选项ID匹配，获取选项内容
                            if(optionType.equals(OptionTypeEnum.TEXT.getCode())){
                                correctAnswers.add(QuestionTypeEnum.displayOptionContent(optionType) ? option.getOptionContent() : option.getOptionName());
                            }else if (Objects.requireNonNull(OptionTypeEnum.getByCode(optionType)).isMedia()){
                                if (!CollectionUtils.isEmpty(option.getMediaUrl())) {
                                    correctAnswers.add(option.getMediaUrl().get(0).getMaterialPath());
                                } else {
                                    correctAnswers.add("");
                                }
                            }
                        } else {
                            // 选项ID不匹配，说明选项被删除了
                            correctAnswers.add("");
                        }
                    } else {
                        // 超出选项范围
                        correctAnswers.add("");
                    }
                } catch (NumberFormatException ignored) {
                    // 无效的ID，用空字符串补位
                    correctAnswers.add("");
                }
            }
            blank.setCorrectAnswer(String.join(",", correctAnswers));
        }
        if (!CollectionUtils.isEmpty(blank.getOptions()) && StringUtils.isNotEmpty(blank.getUserAnswerIds())) {
            List<String> userAnswers = new ArrayList<>();
            String[] userIds = blank.getUserAnswerIds().split(",");
            for (String userId : userIds) {
                if (StringUtils.isEmpty(userId.trim())) {
                    // 空字符串表示删除的选项，用空字符串补位
                    userAnswers.add("");
                    continue;
                }
                try {
                    Integer id = Integer.valueOf(userId.trim());
                    blank.getOptions().stream()
                            .filter(option -> option.getId() != null && option.getId().equals(id))
                            .findFirst()
                            .ifPresent(option -> {
                                if(optionType.equals(OptionTypeEnum.TEXT.getCode())){
                                    userAnswers.add(QuestionTypeEnum.displayOptionContent(optionType)  ? option.getOptionContent() : option.getOptionName());
                                }else if (Objects.requireNonNull(OptionTypeEnum.getByCode(optionType)).isMedia()){
                                    for (QuestionMediaDTO mediaDTO : option.getMediaUrl()) {
                                        userAnswers.add(mediaDTO.getMaterialPath());
                                    }
                                }
                            });
                } catch (NumberFormatException ignored) {
                    // 无效的ID，用空字符串补位
                    userAnswers.add("");
                }
            }
            blank.setUserAnswer(String.join(",", userAnswers));
        }
        if (!CollectionUtils.isEmpty(blank.getOptions()) && blank.getBlankAreaId() != null) {
            for (QuestionAnswerDTO option : blank.getOptions()) {
                if (option.getBlankAreaId() == null) {
                    option.setBlankAreaId(blank.getBlankAreaId());
                }
            }
        }
    }

    /**
     * 聚合空位答案，为主题目汇总所有空位区域的用户答案信息
     */
    protected void aggregateClozeAnswers(QuestionResultDTO result) {
        List<BlankResultDTO> blankResults = result.getBlankResults();
        if (CollectionUtils.isEmpty(blankResults)) {
            return;
        }
        blankResults.sort(Comparator.comparing(BlankResultDTO::getBlankIndex, Comparator.nullsLast(Comparator.naturalOrder())));
        List<String> allUserAnswers = new ArrayList<>();
        List<String> allUserAnswerIds = new ArrayList<>();
        List<String> allCorrectAnswers = new ArrayList<>();
        List<String> allCorrectAnswerIds = new ArrayList<>();
        for (BlankResultDTO blank : blankResults) {
            if (StringUtils.isNotEmpty(blank.getUserAnswer())) {
                allUserAnswers.add(blank.getUserAnswer());
            }
            if (StringUtils.isNotEmpty(blank.getUserAnswerIds())) {
                allUserAnswerIds.add(blank.getUserAnswerIds());
            }
            if (StringUtils.isNotEmpty(blank.getCorrectAnswer())) {
                allCorrectAnswers.add(blank.getCorrectAnswer());
            }
            if (StringUtils.isNotEmpty(blank.getCorrectAnswerIds())) {
                allCorrectAnswerIds.add(blank.getCorrectAnswerIds());
            }
        }
        if (!allUserAnswers.isEmpty()) {
            result.setUserAnswer(String.join(",", allUserAnswers));
        } else {
            result.setUserAnswer("");
        }
        if (!allUserAnswerIds.isEmpty()) {
            result.setUserAnswerIds(String.join(",", allUserAnswerIds));
        } else {
            result.setUserAnswerIds("");
        }

        if (!allCorrectAnswers.isEmpty()) {
            result.setCorrectAnswer(String.join(",", allCorrectAnswers));
        } else {
            result.setCorrectAnswer("");
        }
        if (!allCorrectAnswerIds.isEmpty()) {
            result.setCorrectAnswerIds(String.join(",", allCorrectAnswerIds));
        } else {
            result.setCorrectAnswerIds("");
        }
    }

    /**
     * 获取并设置题目的媒体文件
     * 
     * @param questionId 题目ID
     * @param questionInfo 题目信息
     */
    private void setQuestionMediaResources(Integer questionId, QuestionResultDTO questionInfo) {
        // 如果题目信息中已有媒体文件，不需要再次查询
        if (!CollectionUtils.isEmpty(questionInfo.getMediaUrl())) {
            return;
        }
        
        // 从数据库获取媒体文件
        List<AppMaterialBusinessRef> mediaRefs = materialBusinessRefBiz.list(
                new LambdaQueryWrapper<AppMaterialBusinessRef>()
                        .eq(AppMaterialBusinessRef::getBusinessId, questionId)
                        .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.QUESTION_MEDIA_BUSINESS.getCode())
                        .orderByAsc(AppMaterialBusinessRef::getOrderNum)
        );
        if (!CollectionUtils.isEmpty(mediaRefs)) {
            List<QuestionMediaDTO> mediaList = mediaRefs.stream()
                    .map(ref -> {
                        QuestionMediaDTO media = new QuestionMediaDTO();
                        media.setMaterialId(ref.getAppMaterialId());
                        media.setSortNum(ref.getOrderNum());
                        AppMaterial material = materialMapper.selectById(ref.getAppMaterialId());
                        if (material != null) {
                            media.setMaterialPath(material.getMaterialPath());
                            media.setMaterialName(material.getMaterialName());
                        }
                        return media;
                    })
                    .collect(Collectors.toList());
            questionInfo.setMediaUrl(mediaList);
        }
    }
    
    /**
     * 获取并设置题目的辅助识图
     * 
     * @param questionId 题目ID
     * @param questionInfo 题目信息
     */
    private void setQuestionRecognitionResources(Integer questionId, QuestionResultDTO questionInfo) {
        // 如果题目信息中已有辅助识图，不需要再次查询
        if (!CollectionUtils.isEmpty(questionInfo.getAidedRecognitionUrl())) {
            return;
        }
        
        // 从数据库获取辅助识图
        List<AppMaterialBusinessRef> recognitionRefs = materialBusinessRefBiz.list(
                new LambdaQueryWrapper<AppMaterialBusinessRef>()
                        .eq(AppMaterialBusinessRef::getBusinessId, questionId)
                        .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.QUESTION_RECOGNITION_BUSINESS.getCode())
                        .orderByAsc(AppMaterialBusinessRef::getOrderNum)
        );
        if (!CollectionUtils.isEmpty(recognitionRefs)) {
            List<QuestionRecognitionDTO> recognitionList = recognitionRefs.stream()
                    .map(ref -> {
                        QuestionRecognitionDTO recognition = new QuestionRecognitionDTO();
                        recognition.setMaterialId(ref.getAppMaterialId());
                        recognition.setSortNum(ref.getOrderNum());
                        AppMaterial material = materialMapper.selectById(ref.getAppMaterialId());
                        if (material != null) {
                            recognition.setMaterialPath(material.getMaterialPath());
                            recognition.setMaterialName(material.getMaterialName());
                        }
                        return recognition;
                    })
                    .collect(Collectors.toList());
            questionInfo.setAidedRecognitionUrl(recognitionList);
        }
    }
    
    /**
     * 获取并设置选项的媒体资源
     * 
     * @param answerId 选项ID
     * @return 媒体资源列表
     */
    private List<QuestionMediaDTO> getAnswerMediaResources(Integer answerId) {
        List<AppMaterialBusinessRef> answerMediaRefs = materialBusinessRefBiz.list(
                new LambdaQueryWrapper<AppMaterialBusinessRef>()
                        .eq(AppMaterialBusinessRef::getBusinessId, answerId)
                        .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.ANSWER_MEDIA_BUSINESS.getCode())
                        .orderByAsc(AppMaterialBusinessRef::getOrderNum)
        );
        if (CollectionUtils.isEmpty(answerMediaRefs)) {
            return Collections.emptyList();
        }
        
        return answerMediaRefs.stream()
                .map(ref -> {
                    QuestionMediaDTO media = new QuestionMediaDTO();
                    media.setMaterialId(ref.getAppMaterialId());
                    media.setSortNum(ref.getOrderNum());
                    AppMaterial material = materialMapper.selectById(ref.getAppMaterialId());
                    if (material != null) {
                        media.setMaterialPath(material.getMaterialPath());
                        media.setMaterialName(material.getMaterialName());
                    }
                    return media;
                })
                .collect(Collectors.toList());
    }

    /**
     * 统一处理题目资源，包括媒体文件、辅助识图和选项等
     * 
     * @param questionResultList 题目结果列表
     */
    private void processQuestionResources(List<QuestionResultDTO> questionResultList) {
        if (CollectionUtils.isEmpty(questionResultList)) {
            return;
        }
        
        for (QuestionResultDTO result : questionResultList) {
            if (result == null || result.getQuestionId() == null) {
                continue;
            }
            
            // 设置题目的媒体文件和辅助识图
            setQuestionMediaResources(result.getQuestionId(), result);
            setQuestionRecognitionResources(result.getQuestionId(), result);
            
            // 确保选项数据存在
            if (!QuestionTypeEnum.CLOZE.getValue().equals(result.getType())) {
                List<QuestionAnswer> answers = questionAnswerBiz.getAnswersByQuestionId(result.getQuestionId());
                if (!CollectionUtils.isEmpty(answers)) {
                    List<QuestionAnswerDTO> answerDTOs = answers.stream().map(answer -> {
                        QuestionAnswerDTO answerDTO = new QuestionAnswerDTO();
                        BeanUtil.copyProperties(answer, answerDTO);
                        List<QuestionMediaDTO> mediaList = getAnswerMediaResources(answer.getId());
                        answerDTO.setMediaUrl(mediaList);
                        return answerDTO;
                    }).collect(Collectors.toList());
                    
                    // 如果是排序题，打乱选项顺序
                    if (QuestionTypeEnum.SORT.getValue().equals(result.getType())) {
                        Collections.shuffle(answerDTOs);
                    }
                    result.setOptions(answerDTOs);
                }
            }
            
            // 处理普通题目答案显示
            if (!QuestionTypeEnum.CLOZE.getValue().equals(result.getType())) {
                processAnswerDisplay(result);
            }
            
            // 处理完形填空题
            if (QuestionTypeEnum.CLOZE.getValue().equals(result.getType())) {
                List<BlankResultDTO> blankList = result.getBlankResults();
                if (!CollectionUtils.isEmpty(blankList)) {
                    for (BlankResultDTO blank : blankList) {
                        if (blank == null || blank.getBlankAreaId() == null) {
                            continue;
                        }
                        
                        List<QuestionAnswer> blankAnswers = questionAnswerBiz.getAnswersByBlankAreaId(blank.getQuestionId(), blank.getBlankAreaId());
                        if (!CollectionUtils.isEmpty(blankAnswers)) {
                            List<QuestionAnswerDTO> blankAnswerDTOs = blankAnswers.stream().map(answer -> {
                                QuestionAnswerDTO answerDTO = new QuestionAnswerDTO();
                                BeanUtil.copyProperties(answer, answerDTO);
                                answerDTO.setBlankAreaId(blank.getBlankAreaId());
                                List<QuestionMediaDTO> mediaList = getAnswerMediaResources(answer.getId());
                                answerDTO.setMediaUrl(mediaList);
                                return answerDTO;
                            }).collect(Collectors.toList());
                            blank.setOptions(blankAnswerDTOs);
                        }

                        processBlankAnswerDisplay(blank, result.getOptionType());
                    }
                    aggregateClozeAnswers(result);
                }
            }
        }
    }

    protected List<Question> queryQuestionCategoryBusinessRef(Integer businessId, Integer businessType, int totalNeeded, Integer questionMethod) throws BusinessException {
        List<QuestionCategoryBusinessRef> categoryRefs = categoryBusinessRefBiz.getRefsByBusinessId(
                businessId,
                businessType
        );
        if (CollectionUtils.isEmpty(categoryRefs)) {
            return Collections.emptyList();
        }
        List<Question> selectedQuestions = new LinkedList<>();
        for (QuestionCategoryBusinessRef categoryRef : categoryRefs) {
            if (selectedQuestions.size() >= totalNeeded) {
                break;
            }
            QuestionCategory category = questionCategoryBiz.getBaseMapper().selectById(categoryRef.getQuestionCategoryId());
            if (category == null) {
                continue;
            }
            List<Question> questions = questionBiz.getAllQuestionsByCategory(category.getId());
            if (Objects.equals(questionMethod, QuestionMethodEnum.RANDOM.getValue())) {
                Collections.shuffle(questions);
            }
            int remainingNeeded = totalNeeded - selectedQuestions.size();
            int toTake = Math.min(remainingNeeded, questions.size());
            for (int i = 0; i < toTake; i++) {
                selectedQuestions.add(questions.get(i));
            }
        }
        return selectedQuestions;
    }
}
