package com.dbj.classpal.books.service.biz.album;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsQueryBO;
import com.dbj.classpal.books.common.dto.album.AppAlbumElementsQueryDTO;
import com.dbj.classpal.books.service.entity.album.AppAlbumElements;

import java.util.Collection;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppAlbumElementsBiz
 * Date:     2025-04-15 10:23:35
 * Description: 表名： ,描述： 表
 */
public interface IAppAlbumElementsBiz extends IService<AppAlbumElements> {

    /**
     * 分页查询专辑列表
     */
    Page<AppAlbumElementsQueryDTO> pageAlbumElements(Page<AppAlbumElementsQueryBO> page, AppAlbumElementsQueryBO condition);

    /**
     * 批量判断专辑是否全部存在
     */
    Boolean allExistsByIds(Collection<Integer> ids);
}
