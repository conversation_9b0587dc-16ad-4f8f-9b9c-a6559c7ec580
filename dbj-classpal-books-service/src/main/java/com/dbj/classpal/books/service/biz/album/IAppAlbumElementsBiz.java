package com.dbj.classpal.books.service.biz.album;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.common.bo.album.AppAlbumElementsQueryBO;
import com.dbj.classpal.books.service.entity.album.AppAlbumElements;
import com.dbj.classpal.framework.commons.request.PageInfo;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppAlbumElementsBiz
 * Date:     2025-04-15 10:23:35
 * Description: 表名： ,描述： 表
 */
public interface IAppAlbumElementsBiz extends IService<AppAlbumElements> {

    /**
     * 分页查询专辑列表
     */
    Page<AppAlbumElements> pageAlbumElements(Page<AppAlbumElements> page, AppAlbumElementsQueryBO condition);
}
