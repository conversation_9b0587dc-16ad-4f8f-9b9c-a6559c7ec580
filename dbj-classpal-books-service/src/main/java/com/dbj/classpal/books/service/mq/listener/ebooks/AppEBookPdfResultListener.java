package com.dbj.classpal.books.service.mq.listener.ebooks;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookResourceSaveBO;
import com.dbj.classpal.books.common.bo.watermark.WaterMarkEntity;
import com.dbj.classpal.books.common.enums.FileStatusEnum;
import com.dbj.classpal.books.common.enums.ResourceTypeEnum;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookPdfTaskBiz;
import com.dbj.classpal.books.service.entity.ebooks.AppEBookPdfTask;
import com.dbj.classpal.books.service.entity.ebooks.AppEbooksConfigWatermarkTemplate;
import com.dbj.classpal.books.service.entity.product.AppEBook;
import com.dbj.classpal.books.service.entity.product.AppEBookResource;
import com.dbj.classpal.books.service.mapper.ebooks.AppEBookMapper;
import com.dbj.classpal.books.service.mapper.ebooks.AppEBookResourceMapper;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import com.dbj.classpal.framework.mq.annotation.ExtractHeader;
import com.dbj.classpal.framework.mq.pool.DbjRabbitTemplate;
import com.dbj.classpal.framework.pdf.constant.PdfWaterMarkExChangeConstant;
import com.dbj.classpal.framework.pdf.constant.PdfWaterMarkQueueConstant;
import com.dbj.classpal.framework.pdf.constant.PdfWaterMarkRoutingKeyConstant;
import com.dbj.classpal.framework.pdf.enums.PdfWaterMarkBusinessTypeEnum;
import com.dbj.classpal.framework.utils.util.ContextAppUtil;
import com.dbj.classpal.framework.utils.util.ContextUtil;
import com.rabbitmq.client.Channel;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.*;

@Component
@Slf4j
public class AppEBookPdfResultListener {

    // 静态常量定义
    private static final String CLEAR_ERROR_MSG = "";

    @Resource
    private DbjRabbitTemplate dbjRabbitTemplate;

    @Resource
    private AppEBookMapper eBookMapper;

    @Resource
    private AppEBookResourceMapper eBookResourceMapper;

    @Resource
    private IAppEBookPdfTaskBiz pdfTaskBiz;

    private final ConcurrentHashMap<String, CompletableFuture<WaterMarkEntity>> resultCache = new ConcurrentHashMap<>();

    /**
     * 同步处理PDF文件（添加水印和切片）
     * @param businessId 业务ID
     * @param waterMarkBusinessType 水印类型
     * @param fileUrl 文件URL
     * @param fileName 文件名
     * @param timeoutSeconds 超时时间(秒)
     * @return 处理结果
     */
    public WaterMarkEntity syncProcessPdf(Integer businessId,
                                          String businessKey,
                                          Integer waterMarkBusinessType,
                                          AppEbooksConfigWatermarkTemplate template ,
                                          String fileUrl,
                                          String fileName,
                                          long timeoutSeconds)
            throws TimeoutException, ExecutionException, InterruptedException {
        
        // 创建唯一请求ID
        String requestId = UUID.randomUUID().toString();
        
        // 创建CompletableFuture用于等待结果
        CompletableFuture<WaterMarkEntity> resultFuture = new CompletableFuture<>();
        
        // 将请求ID和Future放入缓存
        resultCache.put(requestId, resultFuture);
        
        try {
            // 创建水印实体对象
            WaterMarkEntity entity = new WaterMarkEntity();
            entity.setBusinessKey(businessKey);
            entity.setRequestId(requestId);
            entity.setWaterMarkBusinessType(waterMarkBusinessType);
            if(template != null) {
                entity.setWaterMarkOssPath(template.getWatermark());
                entity.setWaterMarkName(template.getTemplateName());
                entity.setScale(template.getScale());
                entity.setTransparency(template.getTransparency());
                entity.setRotationAngle(template.getRotationAngle());
                entity.setHorizontalSpacing(template.getHorizontalSpacing());
                entity.setVerticalSpacing(template.getVerticalSpacing());
            }
            entity.setPdfOssPath(fileUrl);
            entity.setPdfName(fileName);
            entity.setAddPageNums(null);
            
            // 发送消息到MQ
            dbjRabbitTemplate.sendExchangeEntityMessage(entity,
                    PdfWaterMarkExChangeConstant.CLASSPAL_FILE_SERVICE_EXCHANGE_PDF_WATERMARK,
                    PdfWaterMarkRoutingKeyConstant.CLASSPAL_FILE_SERVICE_ROUTING_KEY_PDF_DOWNLOAD_FILE);
            
            log.info("启动同步处理PDF文件: {}, 业务ID: {}, 请求ID: {}", fileName, businessId, requestId);
            
            // 等待结果，设置超时时间
            return resultFuture.get(timeoutSeconds, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("处理PDF文件超时: {}, 业务ID: {}, 请求ID: {}", fileName, businessId, requestId);
            resultCache.remove(requestId);
            throw new TimeoutException("处理PDF文件超时，请稍后重试");
        } catch (Exception e) {
            log.error("处理PDF文件异常: {}, 业务ID: {}", fileName, businessId, e);
            resultCache.remove(requestId);
            throw e;
        }
    }

    @ExtractHeader
    @RabbitListener(queues = {PdfWaterMarkQueueConstant.CLASSPAL_FILE_SERVICE_QUEUE_PDF_ADD_WATER_MARK_RESULT})
    public void handlePdfWaterMarkResult(WaterMarkEntity entity, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag, Message message) throws Exception {
        log.info("接收到PDF添加水印结果消息:{}, requestId:{}", JSON.toJSONString(entity), entity.getRequestId());

        if(entity.getIsError()){
            channel.basicAck(tag, false);// 拒绝并重新入队
        }
        cleanupTimeoutTasksIfNeeded();

        boolean processSuccess;
        try {
            processSuccess = processMQResult(entity);

            if (processSuccess) {
                log.info("PDF添加水印结果处理成功，消息已确认，requestId:{}", entity.getRequestId());
            } else {
                log.warn("PDF添加水印结果处理失败，消息已拒绝并重新入队，requestId:{}", entity.getRequestId());
            }
        } catch (Exception e) {
            log.error("PDF添加水印结果处理异常，requestId:{}, 错误:{}", entity.getRequestId(), e.getMessage(), e);
        }finally {
            channel.basicAck(tag, false);
        }
    }

    @ExtractHeader
    @RabbitListener(queues = {PdfWaterMarkQueueConstant.CLASSPAL_FILE_SERVICE_QUEUE_PDF_IMAGE_SLICING_RESULT})
    public void handlePdfImageSlicingResult(WaterMarkEntity entity, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag, Message message) throws Exception {
        log.info("接收到PDF图片切片结果消息:{}, requestId:{}", JSON.toJSONString(entity), entity.getRequestId());
        if(entity.getIsError()){
            channel.basicAck(tag, false);// 拒绝并重新入队
        }
        boolean processSuccess;
        try {
            // 处理结果
            processSuccess = processMQResult(entity);
            if (processSuccess) {
                log.info("PDF图片切片结果处理成功，消息已确认，requestId:{}", entity.getRequestId());
            } else {
                log.warn("PDF图片切片结果处理失败，requestId:{}", entity.getRequestId());
            }
        } catch (Exception e) {
            log.error("PDF图片切片结果处理异常，requestId:{}, 错误:{}", entity.getRequestId(), e.getMessage(), e);
        }finally {
            channel.basicAck(tag, false);
        }
    }


    private boolean processMQResult(WaterMarkEntity entity){
        try {
            // 处理结果
            String requestId = entity.getRequestId();
            if (requestId != null) {
                CompletableFuture<WaterMarkEntity> future = resultCache.remove(requestId);
                if (future != null) {
                    future.complete(entity);
                    log.info("PDF处理结果已返回，requestId: {}", requestId);
                }
            }

            // 处理不同类型的消息
            String taskId = entity.getTaskId();

            if (requestId == null && taskId == null) {
                // 原有的异步处理逻辑（兼容旧版本）
                return processResultAsynchronously(entity);
            } else if (taskId != null) {
                // 新的任务管理异步处理
                return updateTaskStatus(entity);
            }

            return true; // 默认返回成功
        } catch (Exception e) {
            log.error("处理MQ结果异常，requestId: {}, taskId: {}, 错误: {}",
                entity.getRequestId(), entity.getTaskId(), e.getMessage(), e);
            return false;
        }
    }

    // 原有的异步处理逻辑，用于处理没有requestId的消息
    private boolean processResultAsynchronously(WaterMarkEntity entity) {
        if (entity == null || entity.getBusinessId() == null) {
            log.error("PDF图片切片结果消息无效");
            return false;
        }

        // 获取业务ID
        String fileMd5 = entity.getBusinessKey();

        try {
            // 查询单书信息
            AppEBook eBook = eBookMapper.selectOne(new LambdaQueryWrapper<AppEBook>().eq(AppEBook::getFileMd5,fileMd5).last("limit 1"));
            if (eBook == null) {
                log.error("未找到对应的单书信息，fileMd5:{}", fileMd5);
                return false;
            }

            // 处理PDF图片切片结果
            if (entity.getIsError() != null && entity.getIsError()) {
                // 处理失败
                log.error("PDF图片切片处理失败，fileMd5:{}, 错误信息:{}", fileMd5, entity.getErrorMsg());
                eBook.setFileStatus(FileStatusEnum.FAILED.getCode());
            } else {
                // 处理成功
                log.info("PDF图片切片处理成功，fileMd5:{}", fileMd5);
                eBook.setFileStatus(FileStatusEnum.COMPLETED.getCode());
                // 如果有图片列表，取第一张作为封面
                String coverUrl = null;
                List<String> waterMarkList = entity.getWaterMarkOssPathList();
                if (CollectionUtils.isNotEmpty(waterMarkList) && !waterMarkList.isEmpty()) {
                    coverUrl = waterMarkList.get(0);
                    log.info("设置单书封面，fileMd5:{}, 封面URL:{}", fileMd5, coverUrl);
                    eBook.setCoverUrl(coverUrl);
                }
                saveProcessResults(entity,coverUrl);
            }

            // 更新单书信息
            eBookMapper.updateById(eBook);
            return true; // 处理成功
        } catch (Exception e) {
            log.error("处理PDF图片切片结果异常，fileMd5:{}", fileMd5, e);
            return false; // 处理失败
        }
    }

    private void deleteResourceByMd5(String fileMd5, Integer watermarkBusinessType) {
        if (StringUtils.isBlank(fileMd5) || watermarkBusinessType == null) {
            return;
        }
        try {
            LambdaQueryWrapper<AppEBookResource> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AppEBookResource::getResourceKey, fileMd5)
                    .eq(AppEBookResource::getBusinessType, watermarkBusinessType)
                    .eq(AppEBookResource::getCreateBy, ContextUtil.getUserIdInt());
            eBookResourceMapper.delete(queryWrapper);
        } catch (Exception e) {
            log.error("根据文件MD5和水印类型删除封面异常，fileMd5: {}, watermarkType: {}",
                    fileMd5, watermarkBusinessType, e);
        }
    }


    /**
     * 保存处理结果到资源表
     *
     * @param entity 水印实体
     * @param coverUrl 封面URL，用于识别哪个图片是封面
     */
    public void saveProcessResults(WaterMarkEntity entity,String coverUrl) {
        String businessKey = entity.getBusinessKey();
        Integer businessId = entity.getBusinessId();

        Integer businessType = entity.getWaterMarkBusinessType();

        List<AppEBookResourceSaveBO> resourceList = new ArrayList<>();

        deleteResourceByMd5(entity.getBusinessKey(), entity.getWaterMarkBusinessType());

        // 1. 保存加水印的PDF文件（如果有）
        if (StringUtils.isNotBlank(entity.getPdfOutPutOssPath()) &&
                (businessType.equals(PdfWaterMarkBusinessTypeEnum.ONLY_ADD_WATER_MARK.getCode()) ||
                        businessType.equals(PdfWaterMarkBusinessTypeEnum.ONLY_ADD_WATER_MARK_AND_SLICING_IMAGE.getCode()))) {
            AppEBookResourceSaveBO pdfResource = new AppEBookResourceSaveBO();
            pdfResource.setResourceId(businessId);
            pdfResource.setResourceKey(businessKey);
            pdfResource.setResourceType(ResourceTypeEnum.WATERMARKED_PDF.getCode());
            pdfResource.setResourceUrl(entity.getPdfOutPutOssPath());
            pdfResource.setFileName(entity.getPdfName());
            pdfResource.setBusinessType(businessType);
            resourceList.add(pdfResource);
            log.info("添加加水印PDF资源, URL: {}", entity.getPdfOutPutOssPath());
        }

        // 2. 保存切图结果（如果有）
        List<String> imageList = entity.getWaterMarkOssPathList();
        if (CollectionUtils.isNotEmpty(imageList) &&
                (businessType.equals(PdfWaterMarkBusinessTypeEnum.ONLY_SLICING_IMAGE.getCode()) ||
                        businessType.equals(PdfWaterMarkBusinessTypeEnum.ONLY_ADD_WATER_MARK_AND_SLICING_IMAGE.getCode()))) {
            for (int i = 0; i < imageList.size(); i++) {
                String imageUrl = imageList.get(i);
                boolean isCoverImage = StringUtils.isNotBlank(coverUrl) && coverUrl.equals(imageUrl);
                AppEBookResourceSaveBO imageResource = new AppEBookResourceSaveBO();
                imageResource.setResourceId(businessId);
                imageResource.setResourceKey(businessKey);
                if(isCoverImage){
                    imageResource.setResourceType(ResourceTypeEnum.COVER_IMAGE.getCode());
                    log.info("处理封面图片, extraCover=true, URL: {}", imageUrl);
                }else{
                    imageResource.setResourceType(ResourceTypeEnum.PAGE_IMAGE.getCode());
                }
                imageResource.setResourceUrl(imageUrl);
                imageResource.setPageNum(i + 1);
                imageResource.setBusinessType(businessType);
                imageResource.setSortNum(i);

                // 从URL中提取文件名
                if (StringUtils.isNotBlank(imageUrl)) {
                    String fileName = imageUrl.substring(imageUrl.lastIndexOf("/") + 1);
                    imageResource.setFileName(fileName);
                }

                resourceList.add(imageResource);
            }
            log.info("添加切图资源, 总数量: {}, 实际处理数量: {}", imageList.size(), resourceList.size() - (StringUtils.isNotBlank(entity.getPdfOutPutOssPath()) ? 1 : 0));
        }

        // 批量保存资源
        if (!resourceList.isEmpty()) {
            resourceList.forEach(e ->{
                eBookResourceMapper.insert(BeanUtil.copyProperties(e, AppEBookResource.class));
            });
            log.info("批量保存资源结果, 数量: {}", resourceList.size());
        }
    }

    /**
     * 更新任务状态
     */
    private boolean updateTaskStatus(WaterMarkEntity entity) {
        String taskId = entity.getTaskId();

        try {
            if (entity.getIsError() != null && entity.getIsError()) {
                // 处理失败
                pdfTaskBiz.updateTaskFailed(taskId, entity.getErrorMsg());
                updateEBookAfterPdfError(entity.getBusinessId(),entity.getErrorMsg());
                log.info("更新PDF处理任务为失败状态，taskId: {}, errorMsg: {}", taskId, entity.getErrorMsg());
            } else {
                // 处理成功
                String coverUrl = "";
                List<String> waterMarkList = entity.getWaterMarkOssPathList();
                if (CollectionUtils.isNotEmpty(waterMarkList) && !waterMarkList.isEmpty()) {
                    coverUrl = waterMarkList.get(0);
                }

                // 先保存处理结果
                saveProcessResults(entity,coverUrl);

                // 再更新任务状态
                String resultJson = JSON.toJSONString(entity);
                pdfTaskBiz.updateTaskSuccess(taskId, coverUrl, resultJson);
                log.info("更新PDF处理任务为成功状态，taskId: {}, coverUrl: {}", taskId, coverUrl);

                // 回调更新业务数据
                updateBusinessDataAfterSuccess(taskId, coverUrl, entity);
            }

            return true;

        } catch (Exception e) {
            log.error("更新PDF处理任务状态异常，taskId: {}, 错误: {}", taskId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 异步处理PDF文件（添加水印和切片）
     *
     * @param taskId 任务ID
     * @param businessKey 业务key
     * @param waterMarkBusinessType 水印类型
     * @param template 水印模板
     * @param fileUrl 文件URL
     * @param fileName 文件名
     */
    public void asyncProcessPdf(String taskId,
                               Integer businessId,
                               String businessKey,
                               Integer waterMarkBusinessType,
                               AppEbooksConfigWatermarkTemplate template,
                               String fileUrl,
                               String fileName,
                                boolean onlyExtraCover) {
        try {
            // 构建水印实体
            WaterMarkEntity entity = new WaterMarkEntity();
            entity.setTaskId(taskId);
            entity.setBusinessId(businessId);
            entity.setBusinessKey(businessKey);
            entity.setWaterMarkBusinessType(waterMarkBusinessType);
            entity.setOnlyExtraCover(onlyExtraCover);

            // 设置水印模板参数
            if (template != null) {
                entity.setScale(template.getScale());
                entity.setTransparency(template.getTransparency());
                entity.setRotationAngle(template.getRotationAngle());
                entity.setHorizontalSpacing(template.getHorizontalSpacing());
                entity.setVerticalSpacing(template.getVerticalSpacing());
                entity.setWaterMarkOssPath(template.getWatermark());
                entity.setWaterMarkName(template.getWatermarkName());
            }

            entity.setPdfOssPath(fileUrl);
            entity.setPdfName(fileName);
            entity.setAddPageNums(null);

            // 发送消息到MQ进行异步处理
            dbjRabbitTemplate.sendExchangeEntityMessage(entity,
                    PdfWaterMarkExChangeConstant.CLASSPAL_FILE_SERVICE_EXCHANGE_PDF_WATERMARK,
                    PdfWaterMarkRoutingKeyConstant.CLASSPAL_FILE_SERVICE_ROUTING_KEY_PDF_DOWNLOAD_FILE);

            log.info("异步PDF处理消息已发送，taskId: {}, businessId: {}", taskId, businessKey);

        } catch (Exception e) {
            log.error("发送异步PDF处理消息失败，taskId: {}, businessId: {}", taskId, businessKey, e);
            // 更新任务状态为失败
            try {
                pdfTaskBiz.updateTaskFailed(taskId, "发送处理消息失败：" + e.getMessage());
            } catch (Exception ex) {
                log.error("更新任务失败状态异常，taskId: {}", taskId, ex);
            }
        }
    }

    /**
     * 在消费时触发清理超时任务（可选方案，不推荐）
     * 使用频率控制避免每次消费都执行
     */
    private volatile long lastCleanupTime = 0;
    private static final long CLEANUP_INTERVAL = 5 * 60 * 1000;

    private void cleanupTimeoutTasksIfNeeded() {
        long currentTime = System.currentTimeMillis();

        // 频率控制：5分钟内只执行一次
        if (currentTime - lastCleanupTime < CLEANUP_INTERVAL) {
            return;
        }

        // 使用synchronized确保多个消费者不会同时执行
        synchronized (this) {
            // 双重检查
            if (currentTime - lastCleanupTime < CLEANUP_INTERVAL) {
                return;
            }

            try {
                int cleanupCount = pdfTaskBiz.cleanupTimeoutTasks(30);
                if (cleanupCount > 0) {
                    log.info("消费端清理了{}个超时的PDF处理任务", cleanupCount);
                }
                lastCleanupTime = currentTime;
            } catch (Exception e) {
                log.warn("消费端清理超时任务失败", e);
            }
        }
    }

    /**
     * MQ消费成功后，回调更新业务数据
     *
     * @param taskId 任务ID
     * @param coverUrl 封面URL
     * @param entity MQ消息实体
     */
    private void updateBusinessDataAfterSuccess(String taskId, String coverUrl, WaterMarkEntity entity) {
        try {
            // 获取任务信息
            AppEBookPdfTask task = pdfTaskBiz.getTaskByTaskId(taskId);
            String businessKey = task.getBusinessKey();

            log.info("开始回调更新业务数据，taskId: {}, businessKey: {}, coverUrl: {}", taskId, businessKey, coverUrl);

            // 更新单书信息
            updateEBookAfterPdfProcessed(entity.getBusinessId(), coverUrl);

            log.info("回调更新业务数据完成，taskId: {}, businessKey: {}", taskId, businessKey);

        } catch (Exception e) {
            log.error("回调更新业务数据失败，taskId: {}, 错误: {}", taskId, e.getMessage(), e);
        }
    }

    /**
     * 更新单书信息（PDF处理完成后）
     */
    private void updateEBookAfterPdfProcessed(Integer businessId,String coverUrl) {
        try {
            // 查询单书信息
            if(Objects.nonNull(businessId)){
                AppEBook eBook = eBookMapper.selectById(businessId);
                if (eBook == null) {
                    log.warn("未找到对应的单书信息，businessId: {}", businessId);
                    return;
                }
                // 更新单书状态和封面
                eBook.setFileStatus(FileStatusEnum.COMPLETED.getCode());
                eBook.setFileErrMsg(CLEAR_ERROR_MSG);
                eBook.setLaunchStatus(YesOrNoEnum.YES.getCode());
                // 设置封面URL
                if (coverUrl != null && !coverUrl.isEmpty() && StringUtils.isEmpty(eBook.getCoverUrl())) {
                    eBook.setCoverUrl(coverUrl);
                    log.info("更新单书封面，businessKey: {}, coverUrl: {}", businessId, coverUrl);
                }
                if(StringUtils.isEmpty(eBook.getCoverUrlName())){
                    eBook.setCoverUrlName(eBook.getFileName() + ".png");
                }
                // 更新单书信息
                eBookMapper.updateById(eBook);
                log.info("更新单书状态为完成，businessId: {}", businessId);
            }

        } catch (Exception e) {
            log.error("更新单书信息失败，businessId: {}, 错误: {}", businessId, e.getMessage(), e);
        }
    }


    private void updateEBookAfterPdfError(Integer businessId,String errMsg) {
        try {
            // 查询单书信息
            if(Objects.nonNull(businessId)){
                AppEBook eBook = eBookMapper.selectById(businessId);
                if (eBook == null) {
                    log.warn("未找到对应的单书信息，businessKey: {}", businessId);
                    return;
                }
                eBook.setFileStatus(FileStatusEnum.FAILED.getCode());
                eBook.setFileErrMsg(errMsg);
                eBook.setLaunchStatus(YesOrNoEnum.NO.getCode());
                eBookMapper.updateById(eBook);
                log.info("更新单书状态为失败，businessId: {}", businessId);
            }

        } catch (Exception e) {
            log.error("更新单书信息失败，businessId: {}, 错误: {}", businessId, e.getMessage(), e);
        }
    }
}