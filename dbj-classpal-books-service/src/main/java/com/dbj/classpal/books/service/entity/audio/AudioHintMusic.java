package com.dbj.classpal.books.service.entity.audio;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serial;
import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 预置提示音表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("audio_hint_music")
@ApiModel(value="AudioHintMusic对象", description="预置提示音表")
public class AudioHintMusic extends BizEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "提示音名称")
    private String name;

    @ApiModelProperty(value = "提示音类型：1 预置提示音 2 预置背景音")
    private Integer type;

    @ApiModelProperty(value = "素材id")
    private Integer originMaterialId;

    @ApiModelProperty(value = "素材")
    private String materialUrl;

    @ApiModelProperty(value = "素材名称")
    private String materialName;

    @ApiModelProperty(value = "素材icon")
    private String materialIcon;

    @ApiModelProperty(value = "素材时长（单位：秒）")
    private String materialDuration;

    @ApiModelProperty(value = "排序权重")
    private Integer weight;

}
