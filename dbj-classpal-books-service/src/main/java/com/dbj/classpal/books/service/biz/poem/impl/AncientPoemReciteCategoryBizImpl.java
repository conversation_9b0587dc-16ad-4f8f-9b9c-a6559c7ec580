package com.dbj.classpal.books.service.biz.poem.impl;

import com.dbj.classpal.books.service.entity.poem.AncientPoemReciteCategory;
import com.dbj.classpal.books.service.mapper.poem.AncientPoemReciteCategoryMapper;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemReciteCategoryBiz;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 古诗文背诵分类表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Service
public class AncientPoemReciteCategoryBizImpl extends ServiceImpl<AncientPoemReciteCategoryMapper, AncientPoemReciteCategory> implements IAncientPoemReciteCategoryBiz {

}
