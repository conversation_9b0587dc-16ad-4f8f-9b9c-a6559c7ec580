package com.dbj.classpal.books.service.api.client.tree;

import com.dbj.classpal.books.client.api.tree.TreeClassifyApi;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.tree.TreeClassifyAddBO;
import com.dbj.classpal.books.client.bo.tree.TreeClassifyEditBO;
import com.dbj.classpal.books.client.bo.tree.TreeClassifyListBO;
import com.dbj.classpal.books.client.bo.tree.TreeClassifyMoveBO;
import com.dbj.classpal.books.client.dto.tree.TreeClassifyDTO;
import com.dbj.classpal.books.service.service.tree.ITreeClassifyService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 树菜单 远程Api 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@RestController
public class TreeClassifyApiImpl implements TreeClassifyApi {

    @Resource
    private ITreeClassifyService treeClassifyService;

    @Override
    public RestResponse<List<TreeClassifyDTO>> getTree(TreeClassifyListBO bo) {
        return RestResponse.success(treeClassifyService.getTree(bo));
    }

    @Override
    public RestResponse<Boolean> addNode(TreeClassifyAddBO bo) throws BusinessException {
        return RestResponse.success(treeClassifyService.addNode(bo));
    }

    @Override
    public RestResponse<Boolean> renameNode(TreeClassifyEditBO bo) throws BusinessException {
        return RestResponse.success(treeClassifyService.renameNode(bo));
    }

    @Override
    public RestResponse<Boolean> deleteNode(CommonIdApiBO bo) throws BusinessException {
        return RestResponse.success(treeClassifyService.deleteNode(bo));
    }

    @Override
    public RestResponse<Boolean> moveNode(TreeClassifyMoveBO bo) throws BusinessException {
        return RestResponse.success(treeClassifyService.moveNode(bo));
    }
    
}
