package com.dbj.classpal.books.service.api.client.evaluation;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.books.client.api.evaluation.AdminEvaluationNodeApi;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminEvaluationNodeReCoverApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminEvaluationNodeEditApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminEvaluationNodeSaveApiBO;
import com.dbj.classpal.books.client.bo.evaluation.AdminEvaluationNodeSortApiBO;
import com.dbj.classpal.books.common.enums.AlbumMenusOrderEnum;
import com.dbj.classpal.books.common.enums.BusinessTypeEnum;
import com.dbj.classpal.books.service.biz.evaluation.IAppEvaluationNodeBiz;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBiz;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBusinessRefBiz;
import com.dbj.classpal.books.service.biz.question.QuestionBusinessRefBiz;
import com.dbj.classpal.books.service.entity.evaluation.AppEvaluationNode;
import com.dbj.classpal.books.service.entity.material.AppMaterial;
import com.dbj.classpal.books.service.entity.material.AppMaterialBusinessRef;
import com.dbj.classpal.books.service.entity.question.QuestionBusinessRef;
import com.dbj.classpal.books.service.service.evaluation.IAppEvaluationNodeService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import jodd.util.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.ListIterator;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApiImpl
 * Date:     2025-04-10 11:53:30
 * Description: 表名： ,描述： 表
 */
@RestController
public class AdminEvaluationNodeApiImpl implements AdminEvaluationNodeApi {

    @Resource
    private IAppEvaluationNodeService appEvaluationNodeService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<Boolean> saveEvaluationNode(AdminEvaluationNodeSaveApiBO bo) throws BusinessException {
        return RestResponse.success(appEvaluationNodeService.saveEvaluationNode(bo));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<Boolean> editEvaluationNode(AdminEvaluationNodeEditApiBO bo) throws BusinessException {
        return RestResponse.success(appEvaluationNodeService.editEvaluationNode(bo));
    }

    @Override
    public RestResponse<Boolean> reSort(AdminEvaluationNodeSortApiBO bo) throws BusinessException {
       return RestResponse.success(appEvaluationNodeService.reSort(bo));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<Boolean> delete(CommonIdsApiBO bo) throws BusinessException {
        return RestResponse.success(appEvaluationNodeService.delete(bo));
    }
}
