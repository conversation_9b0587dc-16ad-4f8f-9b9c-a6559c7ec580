package com.dbj.classpal.books.service.api.client.audio;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.dbj.classpal.books.client.api.audio.AudioContextHintMusicApi;
import com.dbj.classpal.books.client.bo.audio.AudioBackgroundBO;
import com.dbj.classpal.books.client.bo.audio.AudioContextHintAddBO;
import com.dbj.classpal.books.client.bo.audio.AudioReorderBO;
import com.dbj.classpal.books.client.bo.audio.AudioTypeBO;
import com.dbj.classpal.books.client.dto.audio.AudioBackgroundDTO;
import com.dbj.classpal.books.client.dto.audio.AudioContextHintDTO;
import com.dbj.classpal.books.client.dto.audio.AudioDetailsDTO;
import com.dbj.classpal.books.common.constant.AudioConstants;
import com.dbj.classpal.books.common.enums.audio.AudicBackgroundTypeEnum;
import com.dbj.classpal.books.service.biz.audio.IAudioContextHintMusicBiz;
import com.dbj.classpal.books.service.entity.audio.AudioBackground;
import com.dbj.classpal.books.service.entity.audio.AudioContextHintMusic;
import com.dbj.classpal.books.service.util.audio.AudioDurationUtils;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 音频制作配置
 * <AUTHOR>
 * @since 2025-06-25
 */
@RestController
public class AudioContextHintMusicApiImpl implements AudioContextHintMusicApi {

    @Autowired
    private IAudioContextHintMusicBiz audioContextHintMusicBiz;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<Integer> save(List<AudioContextHintAddBO> bo) throws BusinessException {
        List<AudioContextHintMusic> hintMusicList = audioContextHintMusicBiz.lambdaQuery().eq(AudioContextHintMusic::getType, AudicBackgroundTypeEnum.DEFINITION.getCode())
                .orderByDesc(AudioContextHintMusic::getOrderNum).list();
        List<String> urlList = bo.stream().map(AudioContextHintAddBO::getMaterialPath).toList();
        // 参数校验
        AudioDurationUtils.validateAudioFormat(urlList);

        if (CollectionUtil.isEmpty(hintMusicList)) {
            insertAudio(bo);
            return RestResponse.success(1);
        }

        int max = 20;
        int size = hintMusicList.size() + bo.size();
        if (size > max) {
            // 超出（20条）
            int subSize = size - max;
            List<Integer> outCountIds = new ArrayList<>();
            for (int i = 0; i < subSize; i++) {
                if (i > hintMusicList.size()) {
                    break;
                }
                outCountIds.add(hintMusicList.get(i).getId());
            }
            audioContextHintMusicBiz.deleteByIds(outCountIds);

            insertAudio(bo);
            List<AudioContextHintMusic> updateList = hintMusicList.stream()
                    .filter(v -> !outCountIds.contains(v.getId()))
                    .sorted(Comparator.comparingInt(AudioContextHintMusic::getOrderNum))
                    .toList();
            int orderNum = bo.size();
            for (AudioContextHintMusic hintMusic : updateList) {
                orderNum++;
                hintMusic.setOrderNum(orderNum);
            }
            boolean update = audioContextHintMusicBiz.updateBatchById(updateList);
            Assert.isTrue(update, "更新排序异常！");
        } else {
            // 未超出
            insertAudio(bo);
        }
        return RestResponse.success(1);
    }

    private void insertAudio(List<AudioContextHintAddBO> bo) {
        List<AudioContextHintMusic> saveList = new ArrayList<>();
        // 复制一份素材中心文件
        for (int i = 0; i < bo.size(); i++) {
            AudioContextHintAddBO audioBO = bo.get(i);
            AudioContextHintMusic domain = new AudioContextHintMusic();
            domain.setMaterialName(audioBO.getMaterialName());
            domain.setMaterialPath(audioBO.getMaterialPath());
            domain.setMaterialSize(audioBO.getMaterialSize());
            domain.setMaterialDuration(audioBO.getMaterialDuration());
            domain.setType(AudicBackgroundTypeEnum.DEFINITION.getCode());
            domain.setOrderNum(i+1);
            saveList.add(domain);
        }
        boolean save = audioContextHintMusicBiz.saveBatch(saveList);
        Assert.isTrue(save, "保存失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<Integer> reorder(List<Integer> bo) throws BusinessException {
        List<AudioContextHintMusic> updateList = new ArrayList<>();
        for (int i = 0; i < bo.size(); i++) {
            AudioContextHintMusic hint = new AudioContextHintMusic();
            hint.setId(bo.get(i));
            hint.setOrderNum(i+1);
            updateList.add(hint);
        }
        boolean update = audioContextHintMusicBiz.updateBatchById(updateList);
        Assert.isTrue(update, "重排序异常！");
        return RestResponse.success(1);
    }

    @Override
    public RestResponse<List<AudioContextHintDTO>> getDefinitionHint(AudioTypeBO bo) throws BusinessException {
        List<AudioContextHintMusic> list = audioContextHintMusicBiz.lambdaQuery().eq(AudioContextHintMusic::getType, bo.getType()).orderByAsc(AudioContextHintMusic::getOrderNum).list();
        if (CollectionUtil.isEmpty(list)) {
            return RestResponse.success(new ArrayList<>());
        }

        return RestResponse.success(list.stream().map(item -> {
            AudioContextHintDTO dto = new AudioContextHintDTO();
            BeanUtils.copyProperties(item, dto);
            dto.setName(item.getMaterialName());
            return dto;
        }).collect(Collectors.toList()));
    }
}
