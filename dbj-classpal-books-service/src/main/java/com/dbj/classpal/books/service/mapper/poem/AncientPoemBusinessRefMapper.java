package com.dbj.classpal.books.service.mapper.poem;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.poem.AncientPoemBusinessRefListBO;
import com.dbj.classpal.books.client.bo.poem.app.AncientPoemBusinessRefPageBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemBusinessRefListDTO;
import com.dbj.classpal.books.client.dto.poem.app.AncientPoemBusinessRefPageDTO;
import com.dbj.classpal.books.common.dto.poem.AncientPoemBusinessRefCountDTO;
import com.dbj.classpal.books.service.entity.poem.AncientPoemBusinessRef;
import com.dbj.classpal.framework.commons.request.PageInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 古诗文关联业务关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
public interface AncientPoemBusinessRefMapper extends BaseMapper<AncientPoemBusinessRef> {




    List<AncientPoemBusinessRefCountDTO> getCount(@Param("businessIds") List<Integer> businessIds, @Param("businessType") Integer businessType);

    List<AncientPoemBusinessRefListDTO> listAncientPoemBusinessRef(AncientPoemBusinessRefListBO anomalyPoemBusinessRefListBO);


    Page<AncientPoemBusinessRefPageDTO> pageAncientPoemBusinessRef(Page pageInfo,@Param("bo") AncientPoemBusinessRefPageBO anomalyPoemBusinessRefPage);

}
