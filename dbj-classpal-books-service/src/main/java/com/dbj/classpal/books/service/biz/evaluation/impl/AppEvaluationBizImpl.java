package com.dbj.classpal.books.service.biz.evaluation.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.bo.evaluation.AdminEvaluationQueryBO;
import com.dbj.classpal.books.service.biz.evaluation.IAppEvaluationBiz;
import com.dbj.classpal.books.service.entity.evaluation.AppEvaluation;
import com.dbj.classpal.books.service.mapper.evaluation.AppEvaluationMapper;
import com.dbj.classpal.framework.commons.request.PageInfo;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationBizImpl
 * Date:     2025-04-08 16:26:13
 * Description: 表名： ,描述： 表
 */
@Service
public class AppEvaluationBizImpl extends ServiceImpl<AppEvaluationMapper, AppEvaluation> implements IAppEvaluationBiz {

    @Override
    public Page<AppEvaluation> pageInfo(PageInfo<AdminEvaluationQueryBO> page) {
        return baseMapper.pageInfo(page.getPage(),page.getData());
    }
}
