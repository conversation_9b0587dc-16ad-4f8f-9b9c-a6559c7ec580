package com.dbj.classpal.books.service.biz.advertisement;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementLevelConditionDTO;
import com.dbj.classpal.books.service.entity.advertisement.AdvertisementLevelCondition;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 广告条件层级表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
public interface IAdvertisementLevelConditionBiz extends IService<AdvertisementLevelCondition> {

    /**
     * 根据广告id获取广告条件层级表
     *
     * @param advertisementIds 广告id集合
     * @return 广告条件层级表
     */
    Map<Integer, List<AdvertisementLevelConditionDTO>> getMapByAdvertisementIds(Collection<Integer> advertisementIds);
}
