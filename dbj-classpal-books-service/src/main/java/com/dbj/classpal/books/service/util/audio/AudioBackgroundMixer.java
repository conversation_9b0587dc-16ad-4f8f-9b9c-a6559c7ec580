package com.dbj.classpal.books.service.util.audio;

import cn.hutool.core.lang.Assert;
import com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.handler.codec.EncoderException;
import com.dbj.classpal.books.common.constant.AudioConstants;
import com.dbj.classpal.books.common.enums.audio.AudioBgmModelEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import ws.schild.jave.Encoder;
import ws.schild.jave.MultimediaObject;
import ws.schild.jave.encode.AudioAttributes;
import ws.schild.jave.encode.EncodingAttributes;

import javax.sound.sampled.AudioFileFormat;
import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.UUID;

/**
 * 读取 本地文件 并合成背景音乐(在线音频)
 */
@Component
public class AudioBackgroundMixer {
    private static final Logger log = LoggerFactory.getLogger(AudioBackgroundMixer.class);
    // 调试模式开关
    private static final boolean DEBUG_MODE = false;

    public static void main(String[] args) {
        String downloadDirPath =  AudioConstants.AUDIO_INPUT_DIR;
        try {
            long start = System.currentTimeMillis();
            // 输入文件路径（本地主音频）
            String mainAudioPath = "E:\\audio\\output.wav";
            // 在线背景音乐URL
            String backgroundMusicUrl = "https://dbj-app.oss-cn-hangzhou.aliyuncs.com/classpal/1746778899309tdogzxrs.mp3";
//            String backgroundMusicUrl = "https://cdn.xiaoliuban.com/printer/1750296964807myxgfkgt.wav";
            // 项目指定的下载目录
            // 获取resources目录绝对路径
//            System.out.println(getResourcesPathBySystemProperty());
//            String downloadDirPath = getResourcesPathBySystemProperty();
//            String downloadDirPath =  "src" + File.separator + "main" + File.separator +
//                    "resources" + File.separator +  "audio_temp" ;
            System.out.println(downloadDirPath);
            // 输出文件路径
//            String outputPath = "C:\Users\<USER>\AppData\Local\Temp\audio_temp\output_with_online_single_bgm.wav";
            String outputPath = AudioConstants.AUDIO_OUTPUT_DIR + File.separator + AudioConstants.BGM_OUTPUT_PREFIX + UUID.randomUUID().toString().replace("-", "") + AudioConstants.BGM_FORMAT_WAV;
            mixAudioWithBackground(new File(mainAudioPath), backgroundMusicUrl, downloadDirPath, outputPath, 30, 1);
            System.out.println("音频混合完成，输出文件: " + outputPath);
            long end = System.currentTimeMillis();
            System.out.println("耗时：" + (end - start) + "ms");
        } catch (Exception e) {
            System.err.println("音频混合过程中出错: " + e.getMessage());
            log.error(e.getMessage());
        } finally {
//            DeleteFile.deleteDir(downloadDirPath);
        }
    }

    /**
     * 将主音频与在线背景音乐混合（支持百分比音量控制）
     * @param mainAudioPath 主音频文件
     * @param backgroundMusicUrl 背景音乐URL
     * @param downloadDirPath 下载目录
     * @param outputPath 输出文件路径
     * @param bgVolumePercent 背景音乐音量百分比 (0-100)
     * @throws Exception 处理过程中可能出现的异常
     */
    public static void mixAudioWithBackground(File mainAudioPath, String backgroundMusicUrl,
                                              String downloadDirPath, String outputPath, int bgVolumePercent, Integer model) throws Exception {
        // 音量参数校验（确保在0-100范围内）
        float bgVolumeScale = Math.max(0, Math.min(100, bgVolumePercent)) / 100.0f;
        System.out.println("使用背景音乐音量: " + bgVolumePercent + "% (" + bgVolumeScale + " 缩放因子)");

        // 打开主音频文件
        AudioInputStream mainAudio = AudioSystem.getAudioInputStream(mainAudioPath);
        AudioFormat mainFormat = mainAudio.getFormat();

        // 从URL下载并打开背景音乐
        AudioInputStream background = downloadAndOpenBackgroundMusic(backgroundMusicUrl, downloadDirPath, mainFormat);
        AudioFormat bgFormat = background.getFormat();

        // 格式兼容处理
        if (!mainFormat.matches(bgFormat)) {
            AudioFormat targetFormat = new AudioFormat(
                    mainFormat.getEncoding(),
                    mainFormat.getSampleRate(),
                    mainFormat.getSampleSizeInBits(),
                    mainFormat.getChannels(),
                    mainFormat.getFrameSize(),
                    mainFormat.getFrameRate(),
                    mainFormat.isBigEndian()
            );
            background = AudioSystem.getAudioInputStream(targetFormat, background);
            bgFormat = targetFormat;
        }

        // 混合音频并保存
        AudioFileFormat.Type fileType = AudioFileFormat.Type.WAVE;
        AudioInputStream mixedStream;
        if (model.equals(AudioBgmModelEnum.LOOP.getCode())) {
            // 循环播放
            mixedStream = mixAudioStreamsLoopPlay(mainAudio, background, bgVolumePercent, mainFormat);
        } else {
            // 只播放一次
            mixedStream = mixAudioStreamsSinglePlay(mainAudio, background, bgVolumePercent, mainFormat);
        }

        // 确保输出目录存在
        File outputFile = new File(outputPath);
        File outputDir = new File(outputFile.getParent());
        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }

        AudioSystem.write(mixedStream, fileType, outputFile);

        // 关闭流
        mixedStream.close();
        mainAudio.close();
        background.close();
    }

    /**
     * 下载背景音乐到指定目录并打开为AudioInputStream
     */
//    private static AudioInputStream downloadAndOpenBackgroundMusic(String bgUrl, String downloadDirPath,
//                                                                   AudioFormat targetFormat) throws Exception {
//        // 确保下载目录存在
//        File downloadDir = new File(downloadDirPath);
//        if (!downloadDir.exists()) {
//            if (!downloadDir.mkdirs()) {
//                throw new IOException("无法创建下载目录: " + downloadDirPath);
//            }
//        }
//
//        // 下载背景音乐到指定目录
//        File tempBgFile = downloadFileToSpecifiedDir(bgUrl, downloadDirPath);
//
//        // TODO 2025/7/2 待完善：背景音乐格式转换成.wav格式
//        // 打开音频流
//        AudioInputStream bgStream = AudioSystem.getAudioInputStream(tempBgFile);
//
//        // 格式转换
//        if (!bgStream.getFormat().matches(targetFormat)) {
//            bgStream = AudioSystem.getAudioInputStream(targetFormat, bgStream);
//        }
//
//        return bgStream;
//    }

    private static AudioInputStream downloadAndOpenBackgroundMusic(String bgUrl, String downloadDirPath,
                                                                   AudioFormat targetFormat) throws Exception {
        // 确保下载目录存在
        File downloadDir = new File(downloadDirPath);
        if (!downloadDir.exists()) {
            if (!downloadDir.mkdirs()) {
                throw new IOException("无法创建下载目录: " + downloadDirPath);
            }
        }

        // 下载背景音乐到指定目录
        File tempBgFile = downloadFileToSpecifiedDir(bgUrl, downloadDirPath);

        // 检查文件扩展名，非 WAV 格式则转换
        String fileExtension = getFileExtension(tempBgFile).toLowerCase();
        if (!"wav".equals(fileExtension)) {
            tempBgFile = convertToWav(tempBgFile);
        }

        // 打开音频流（此时应为 WAV 格式）
        Assert.isFalse(tempBgFile == null, "背景音乐文件不存在或无法打开！" + bgUrl);
        AudioInputStream bgStream = AudioSystem.getAudioInputStream(tempBgFile);

        // 格式转换
        if (!bgStream.getFormat().matches(targetFormat)) {
            bgStream = AudioSystem.getAudioInputStream(targetFormat, bgStream);
        }

        return bgStream;
    }

    // 获取文件扩展名
    private static String getFileExtension(File file) {
        String name = file.getName();
        int lastIndexOf = name.lastIndexOf('.');
        if (lastIndexOf == -1) {
            return ""; // 没有扩展名
        }
        return name.substring(lastIndexOf + 1);
    }

    // 使用 JAVE 将音频转换为 WAV 格式
//    private static File convertToWav(File sourceFile) throws EncoderException, IOException {
//        try {
//            File targetDir = sourceFile.getParentFile();
//            File targetFile = new File(targetDir, getFileNameWithoutExtension(sourceFile) + ".wav");
//
//            AudioAttributes audioAttributes = new AudioAttributes();
//            audioAttributes.setCodec("pcm_s16le"); // PCM 16-bit 编码
//            audioAttributes.setBitRate(128000);
//            audioAttributes.setChannels(2);
//            audioAttributes.setSamplingRate(44100);
//
//            EncodingAttributes encodingAttributes = new EncodingAttributes();
//            encodingAttributes.setFormat("wav");
//            encodingAttributes.setAudioAttributes(audioAttributes);
//
//            Encoder encoder = new Encoder();
//            MultimediaObject multimediaObject = new MultimediaObject(sourceFile);
//            encoder.encode(multimediaObject, targetFile, encodingAttributes);
//
//            // 设置 JVM 退出时删除临时文件
//            targetFile.deleteOnExit();
//            return targetFile;
//        } catch (Exception e) {
//            log.error("音频转换失败", e);
//        }
//        return null;
//
//    }

    /**
     * 将非 WAV 格式音频转换 WAV 格式
     * @param sourceFile
     * @return
     * @throws EncoderException
     * @throws IOException
     */
    private static File convertToWav(File sourceFile) throws EncoderException, IOException {
        try {
            File targetDir = sourceFile.getParentFile();
            File targetFile = new File(targetDir, getFileNameWithoutExtension(sourceFile) + AudioConstants.BGM_FORMAT_WAV);

            // 创建音频属性
            AudioAttributes audioAttributes = new AudioAttributes();
            audioAttributes.setCodec("pcm_s16le");
            audioAttributes.setBitRate(128000);
            audioAttributes.setChannels(2);
            audioAttributes.setSamplingRate(44100);

            // 创建编码任务
            EncodingAttributes encodingAttributes = new EncodingAttributes();
            encodingAttributes.setOutputFormat("wav"); // 注意：2.x 版本使用 setOutputFormat
            encodingAttributes.setAudioAttributes(audioAttributes);

            // 执行转换
            Encoder encoder = new Encoder();
            MultimediaObject multimediaObject = new MultimediaObject(sourceFile);
            encoder.encode(multimediaObject, targetFile, encodingAttributes);

            targetFile.deleteOnExit(); // JVM 退出时删除临时文件
            return targetFile;
        } catch (Exception e) {
            log.error("音频转换失败", e);
        }
        return null;
    }

    // 获取不带扩展名的文件名
    private static String getFileNameWithoutExtension(File file) {
        String name = file.getName();
        int lastIndexOf = name.lastIndexOf('.');
        if (lastIndexOf == -1) {
            return name; // 没有扩展名
        }
        return name.substring(0, lastIndexOf);
    }

    /**
     * 下载文件到指定目录
     */
    private static File downloadFileToSpecifiedDir(String fileUrl, String dirPath) throws IOException {
        URL url = new URL(fileUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");

        if (connection.getResponseCode() != HttpURLConnection.HTTP_OK) {
            throw new IOException("下载失败，HTTP响应码: " + connection.getResponseCode());
        }

        // 生成指定目录下的文件名（使用原始文件名或自定义命名）
        String fileName = fileUrl.substring(fileUrl.lastIndexOf('/') + 1);
        if (fileName.isEmpty() || fileName.contains("?")) {
            fileName = AudioConstants.BGM_PREFIX + UUID.randomUUID().toString().replace("-", "") + AudioConstants.BGM_FORMAT_WAV; // 处理无文件名或带参数的URL
        }
        File targetFile = new File(dirPath, AudioConstants.BGM_PREFIX +fileName);

        // 下载文件到指定目录
        try (InputStream in = connection.getInputStream();
             FileOutputStream out = new FileOutputStream(targetFile)) {

            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
        }

        return targetFile;
    }

    /**
     * 混合两个音频流(背景音乐循环播放)
     * @param mainAudio
     * @param background
     * @param bgVolumePercent
     * @param format
     * @return
     * @throws IOException
     */
    private static AudioInputStream mixAudioStreamsLoopPlay(AudioInputStream mainAudio, AudioInputStream background,
                                                            int bgVolumePercent, AudioFormat format) throws IOException {
        int sampleSize = format.getSampleSizeInBits() / 8;
        int frameSize = format.getFrameSize();
        int bufferSize = 4096; // 4KB 缓冲区

        // 缓存完整的背景音乐数据用于循环播放
        ByteArrayOutputStream bgBufferOut = new ByteArrayOutputStream();
        byte[] bgTempBuffer = new byte[bufferSize];
        int bgBytesRead;
        while ((bgBytesRead = background.read(bgTempBuffer)) != -1) {
            bgBufferOut.write(bgTempBuffer, 0, bgBytesRead);
        }
        byte[] fullBgData = bgBufferOut.toByteArray();
        int bgDataLength = fullBgData.length;

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        byte[] mainBuffer = new byte[bufferSize];
        byte[] bgBuffer = new byte[bufferSize];

        int mainBytesRead;
        int bgPosition = 0;

        while ((mainBytesRead = mainAudio.read(mainBuffer)) != -1) {
            for (int i = 0; i < mainBytesRead; i++) {
                bgBuffer[i] = fullBgData[bgPosition % bgDataLength];
                bgPosition++;
            }

            byte[] mixedBuffer = mixAudioBytes(mainBuffer, bgBuffer, mainBytesRead, format, bgVolumePercent);
            out.write(mixedBuffer, 0, mainBytesRead);
        }

        byte[] mixedData = out.toByteArray();
        ByteArrayInputStream bais = new ByteArrayInputStream(mixedData);
        return new AudioInputStream(bais, format, mixedData.length / frameSize);
    }

    /**
     * 混合两个音频流(背景音乐仅播放一次)
     * @param mainAudio 主音频流
     * @param background 背景音频流
     * @param bgVolumePercent 背景音乐音量缩放比例
     * @param format 输出音频格式
     * @return 混合后的音频流
     * @throws IOException 输入/输出异常
     */
    private static AudioInputStream mixAudioStreamsSinglePlay(AudioInputStream mainAudio, AudioInputStream background,
                                                              int bgVolumePercent, AudioFormat format) throws IOException {

        // 计算样本大小和缓冲区大小
        int sampleSize = format.getSampleSizeInBits() / 8;
        int frameSize = format.getFrameSize();
        int bufferSize = 4096; // 4KB 缓冲区

        // 缓存完整的背景音乐数据
        ByteArrayOutputStream bgBufferOut = new ByteArrayOutputStream();
        byte[] bgTempBuffer = new byte[bufferSize];
        int bgBytesRead;
        while ((bgBytesRead = background.read(bgTempBuffer)) != -1) {
            bgBufferOut.write(bgTempBuffer, 0, bgBytesRead);
        }
        byte[] fullBgData = bgBufferOut.toByteArray();
        int bgDataLength = fullBgData.length;

        // 创建混合后的音频流
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        byte[] mainBuffer = new byte[bufferSize];
        byte[] bgBuffer = new byte[bufferSize];

        // 循环读取主音频并混合背景音乐
        int mainBytesRead;
        int bgPosition = 0; // 记录当前背景音乐的位置
        boolean bgFinished = false; // 标记背景音乐是否已播放完

        while ((mainBytesRead = mainAudio.read(mainBuffer)) != -1) {
            // 填充背景音乐缓冲区
            for (int i = 0; i < mainBytesRead; i++) {
                if (bgPosition < bgDataLength) {
                    // 背景音乐未播放完，继续使用
                    bgBuffer[i] = fullBgData[bgPosition];
                    bgPosition++;
                } else {
                    // 背景音乐已播放完，填充0（静音）
                    bgBuffer[i] = 0;
                    bgFinished = true;
                }
            }

            // 混合音频数据
            byte[] mixedBuffer = mixAudioBytes(mainBuffer, bgBuffer, mainBytesRead, format, bgVolumePercent);

            // 写入输出流
            out.write(mixedBuffer, 0, mainBytesRead);

            // 如果背景音乐已播放完，可以提前结束循环（可选优化）
            if (bgFinished && false) {
                // 复制剩余的主音频数据（不混合背景音乐）
                while ((mainBytesRead = mainAudio.read(mainBuffer)) != -1) {
                    out.write(mainBuffer, 0, mainBytesRead);
                }
                break;
            }
        }

        // 创建混合后的音频输入流
        byte[] mixedData = out.toByteArray();
        ByteArrayInputStream bais = new ByteArrayInputStream(mixedData);
        return new AudioInputStream(bais, format, mixedData.length / frameSize);
    }

    /**
     * 混合音频数据并应用音量控制（支持百分比）
     */
    private static byte[] mixAudioBytes(byte[] mainData, byte[] bgData, int bytesToProcess,
                                        AudioFormat format, int bgVolumePercent) {
        byte[] mixed = new byte[bytesToProcess];
        int sampleSize = format.getSampleSizeInBits() / 8;
        boolean isBigEndian = format.isBigEndian();
        int channels = format.getChannels();
        float bgVolumeScale = bgVolumePercent / 100.0f;

        for (int i = 0; i < bytesToProcess; i += sampleSize * channels) {
            for (int c = 0; c < channels; c++) {
                int sampleOffset = i + c * sampleSize;

                // 提取主音频样本
                double mainSample = extractSample(mainData, sampleOffset, sampleSize, isBigEndian);

                // 提取背景音频样本并应用音量缩放
                double bgSample = extractSample(bgData, sampleOffset, sampleSize, isBigEndian);
                bgSample = applyVolumeScale(bgSample, bgVolumeScale);

                // 混合样本（带音量归一化）
                double mixedSample = mixSamplesWithVolume(mainSample, bgSample);

                insertSample(mixed, sampleOffset, sampleSize, isBigEndian, mixedSample);

                // 调试输出音量信息
                if (DEBUG_MODE && i < 100) { // 只打印前100个样本用于调试
                    debugVolume(mainSample, bgSample, mixedSample, bgVolumeScale);
                }
            }
        }

        return mixed;
    }

    /**
     * 应用音量缩放（支持0-100的百分比）
     */
    private static double applyVolumeScale(double sample, float volumeScale) {
        // 确保音量在有效范围并应用缩放
        float scaledVolume = Math.max(0.0f, Math.min(1.0f, volumeScale));
        return sample * scaledVolume;
    }

    /**
     * 优化的音量混合算法（防止音量过载）
     */
    private static double mixSamplesWithVolume(double mainSample, double bgSample) {
        // 混合主音频和背景音频，并进行音量归一化
        double mixed = mainSample + bgSample;

        // 确保混合后的样本值在[-1.0, 1.0]范围内
        if (mixed > 1.0) {
            mixed = 1.0;
        } else if (mixed < -1.0) {
            mixed = -1.0;
        }
        return mixed;
    }

    /**
     * 调试模式下打印音量信息
     */
    private static void debugVolume(double mainSample, double bgSample, double mixedSample, float volumeScale) {
        if (DEBUG_MODE) {
            System.out.printf("主音频: %.4f, 背景音频: %.4f(音量: %.1f%%), 混合后: %.4f\n",
                    mainSample, bgSample, volumeScale * 100, mixedSample);
        }
    }

    private static double extractSample(byte[] data, int offset, int sampleSize, boolean isBigEndian) {
        if (offset >= data.length) {
            return 0.0;
        }

        long value = 0;
        for (int b = 0; b < sampleSize && offset + b < data.length; b++) {
            int shift = (isBigEndian ? (sampleSize - 1 - b) : b) * 8;
            value |= (long) (data[offset + b] & 0xFF) << shift;
        }

        if (sampleSize < 8) {
            long mask = 1L << ((sampleSize * 8) - 1);
            value = (value ^ mask) - mask;
        }

        double maxValue = Math.pow(2, sampleSize * 8 - 1);
        return value / maxValue;
    }

    private static void insertSample(byte[] data, int offset, int sampleSize, boolean isBigEndian, double sample) {
        sample = Math.max(-1.0, Math.min(1.0, sample));
        long maxValue = (long) Math.pow(2, sampleSize * 8 - 1);
        long value = Math.round(sample * maxValue);

        for (int b = 0; b < sampleSize && offset + b < data.length; b++) {
            int shift = (isBigEndian ? (sampleSize - 1 - b) : b) * 8;
            data[offset + b] = (byte) ((value >> shift) & 0xFF);
        }
    }

    private static double mixSamples(double sample1, double sample2) {
        return (sample1 + sample2) / 2.0;
    }

    /**
     * 清理下载目录中的临时文件
     */
//    public static void deleteTempFiles(String downloadDirPath) {
//        File downloadDir = new File(downloadDirPath);
//        if (!downloadDir.exists() || !downloadDir.isDirectory()) {
//            log.error("下载目录不存在或不是目录，无需清理");
//            return;
//        }
//
//        File[] files = downloadDir.listFiles();
//        if (files == null) {
//            log.error("无法列出下载目录中的文件");
//            return;
//        }
//        int deletedCount = 0;
//        int failedCount = 0;
//
//        for (File file : files) {
//            // 只删除以"bgm_temp_"开头的临时文件（根据文件名规则）
//            if (file.getName().startsWith(AudioConstants.bgm_prefix) && file.isFile()) {
//                if (file.delete()) {
//                    deletedCount++;
//                } else {
//                    failedCount++;
//                    log.error("删除失败: " + file.getAbsolutePath());
//                }
//            }
//        }
//
//        log.error("临时文件清理完成 - 已删除: " + deletedCount + " 个，失败: " + failedCount + " 个");
//    }

}