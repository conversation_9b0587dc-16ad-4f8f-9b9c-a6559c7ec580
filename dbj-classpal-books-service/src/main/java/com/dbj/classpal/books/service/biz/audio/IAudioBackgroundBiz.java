package com.dbj.classpal.books.service.biz.audio;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.service.entity.audio.AudioBackground;

import java.util.List;

/**
 * <p>
 * 音频背景音 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
public interface IAudioBackgroundBiz extends IService<AudioBackground> {

    /**
     * 批量删除
     * @param outCountIds
     */
    int deleteByIds(List<Integer> outCountIds);
}
