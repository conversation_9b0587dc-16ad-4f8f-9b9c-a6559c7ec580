package com.dbj.classpal.books.service.biz.poem;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.client.bo.poem.AncientPoemPageBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemDTO;
import com.dbj.classpal.books.service.entity.poem.AncientPoem;

/**
 * <p>
 * 古诗文 业务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
public interface IAncientPoemBiz extends IService<AncientPoem> {
    /**
     * 古诗文分页查询
     * @param page 分页对象
     * @param bo 分页查询参数
     * @return 分页数据
     */
    Page<AncientPoemDTO> getAncientPoemPage(Page<Object> page, AncientPoemPageBO bo);
}
