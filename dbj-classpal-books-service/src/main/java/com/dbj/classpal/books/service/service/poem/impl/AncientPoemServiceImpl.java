package com.dbj.classpal.books.service.service.poem.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemCopyBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemMoveBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemPageBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemUpsertBO;
import com.dbj.classpal.books.client.dto.material.AppCommonMediaDTO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemDTO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemRelateDTO;
import com.dbj.classpal.books.common.enums.BusinessTypeEnum;
import com.dbj.classpal.books.common.enums.poem.PoemBusinessTypeEnum;
import com.dbj.classpal.books.common.enums.tree.TreeClassifyEnum;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBusinessRefBiz;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemBiz;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemBusinessRefBiz;
import com.dbj.classpal.books.service.biz.tree.ITreeClassifyBiz;
import com.dbj.classpal.books.service.entity.material.AppMaterialBusinessRef;
import com.dbj.classpal.books.service.entity.poem.AncientPoem;
import com.dbj.classpal.books.service.entity.poem.AncientPoemBusinessRef;
import com.dbj.classpal.books.service.entity.tree.TreeClassify;
import com.dbj.classpal.books.service.mapper.poem.AncientPoemMapper;
import com.dbj.classpal.books.service.service.poem.IAncientPoemService;
import com.dbj.classpal.books.service.strategy.poem.IAncientPoemReleateStrategy;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.utils.util.SpringUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.ANCIENT_POEM_VERIFICATION_FAIL_CODE;


/**
 * <p>
 * 古诗文 业务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Service
public class AncientPoemServiceImpl extends ServiceImpl<AncientPoemMapper, AncientPoem> implements IAncientPoemService {

    @Resource
    private ITreeClassifyBiz treeClassifyBiz;

    @Resource
    private IAncientPoemBiz ancientPoemBiz;

    @Resource
    private IAncientPoemBusinessRefBiz ancientPoemBusinessRefBiz;

    @Resource
    private IAppMaterialBusinessRefBiz appMaterialBusinessRefBiz;

    @Override
    public Page<AncientPoemDTO> getAncientPoemPage(PageInfo<AncientPoemPageBO> pageInfo) {
        if(pageInfo.getData().getClassifyId() == null) {
            TreeClassify root = treeClassifyBiz.getRootNode(TreeClassifyEnum.ANCIENT_POEM);
            if(root == null) {
                return new Page<>();
            }
            pageInfo.getData().setClassifyId(root.getId());
        }
        List<Integer> subTreeIds = treeClassifyBiz.getSubTreeIds(pageInfo.getData().getClassifyId(), true);
        pageInfo.getData().setSubTreeIds(subTreeIds);
        Page<AncientPoemDTO> ancientPoemPage = ancientPoemBiz.getAncientPoemPage(pageInfo.getPage(), pageInfo.getData());
        setBizOtherInfo(ancientPoemPage.getRecords());
        return ancientPoemPage;
    }


    @Override
    public AncientPoemDTO getAncientPoemInfo(CommonIdApiBO bo) {
        AncientPoem ancientPoem = ancientPoemBiz.getById(bo.getId());
        AncientPoemDTO ancientPoemDTO = BeanUtil.copyProperties(ancientPoem, AncientPoemDTO.class);
        List<AppCommonMediaDTO> originalAudioMedias = appMaterialBusinessRefBiz.getCommonBusinessList(Collections.singletonList(bo.getId()),
                BusinessTypeEnum.ANCIENT_POEM_ORIGINAL_AUDIO_BUSINESS);
        ancientPoemDTO.setOriginalAudioMedias(originalAudioMedias);
        List<AppCommonMediaDTO> explanationAudioMedias = appMaterialBusinessRefBiz.getCommonBusinessList(Collections.singletonList(bo.getId()),
                BusinessTypeEnum.ANCIENT_POEM_EXPLANATION_AUDIO_BUSINESS);
        ancientPoemDTO.setExplanationAudioMedias(explanationAudioMedias);


        return ancientPoemDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveAncientPoem(AncientPoemUpsertBO bo) throws BusinessException {
        checkAncientPoemUpsertParams(bo, false);
        AncientPoem ancientPoem = upsertAncientPoem(bo);
        desertAncientPoemMedias(ancientPoem.getId(), bo);
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateAncientPoem(AncientPoemUpsertBO bo) throws BusinessException {
        checkAncientPoemUpsertParams(bo, true);
        AncientPoem ancientPoem = upsertAncientPoem(bo);
        desertAncientPoemMedias(ancientPoem.getId(), bo);
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteAncientPoem(CommonIdsApiBO bo) throws BusinessException{
        Map<Integer, Long> refMap = getAncientPoemRelateCountMap(bo.getIds());
        for (Integer id : bo.getIds()) {
            if(refMap.containsKey(id)) {
                Assert.isTrue(refMap.get(id) == 0,
                        () -> new BusinessException(ANCIENT_POEM_VERIFICATION_FAIL_CODE, "古诗文被引用，无法删除"));
            }
        }
        ancientPoemBiz.removeByIds(bo.getIds());
        deletedAncientPoemMedias(bo.getIds());
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean copyAncientPoem(AncientPoemCopyBO bo) throws BusinessException {
        List<AncientPoem> ancientPoems = ancientPoemBiz.listByIds(bo.getIds());
        CopyOptions ignoreOptions = CopyOptions.create()
                .setIgnoreProperties("id", "createBy", "createTime", "updateBy", "updateTime", "isDeleted", "tenantId");
        ancientPoems.forEach(poem -> poem.setCopyId(poem.getId()));
        List<AncientPoem> copyAncientPoems = BeanUtil.copyToList(ancientPoems, AncientPoem.class, ignoreOptions);
        copyAncientPoems.forEach(poem -> poem.setClassifyId(bo.getNewClassifyId()));

        //批量保存新古诗文
        boolean save = ancientPoemBiz.saveBatch(copyAncientPoems);
        Assert.isTrue(save, () -> new BusinessException(ANCIENT_POEM_VERIFICATION_FAIL_CODE, "古诗文复制失败"));

        //构建原古诗文与新古诗文ID的映射关系（用于后续媒体资源绑定）
        Map<Integer, Integer> oldIdToNewIdMap = copyAncientPoems.stream().collect(Collectors.toMap(AncientPoem::getCopyId, AncientPoem::getId));

        List<AppMaterialBusinessRef> originalAudios = appMaterialBusinessRefBiz.lambdaQuery()
                .in(AppMaterialBusinessRef::getBusinessId, oldIdToNewIdMap.keySet())
                .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.ANCIENT_POEM_ORIGINAL_AUDIO_BUSINESS.getCode())
                .list();
        List<AppMaterialBusinessRef> explanationAudios = appMaterialBusinessRefBiz.lambdaQuery()
                .in(AppMaterialBusinessRef::getBusinessId, oldIdToNewIdMap.keySet())
                .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.ANCIENT_POEM_EXPLANATION_AUDIO_BUSINESS.getCode())
                .list();
        List<AppMaterialBusinessRef> copiedOriginalAudios = copyAndRebindMaterials(originalAudios, oldIdToNewIdMap, ignoreOptions);
        List<AppMaterialBusinessRef> copiedExplanationAudios = copyAndRebindMaterials(explanationAudios, oldIdToNewIdMap, ignoreOptions);

        //批量保存
        if (!copiedOriginalAudios.isEmpty()) {
            appMaterialBusinessRefBiz.saveBatch(copiedOriginalAudios);
        }
        if (!copiedExplanationAudios.isEmpty()) {
            appMaterialBusinessRefBiz.saveBatch(copiedExplanationAudios);
        }

        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean moveAncientPoem(AncientPoemMoveBO bo) {
        List<AncientPoem> ancientPoems = bo.getIds().stream().map(id ->
                new AncientPoem().setId(id).setClassifyId(bo.getNewClassifyId())).toList();
        return ancientPoemBiz.updateBatchById(ancientPoems);
    }

    @Override
    public List<AncientPoemRelateDTO> getAncientPoemRelate(CommonIdApiBO bo) throws BusinessException {
        AncientPoem ancientPoem = ancientPoemBiz.getById(bo.getId());
        Assert.notNull(ancientPoem, () -> new BusinessException(ANCIENT_POEM_VERIFICATION_FAIL_CODE, "古诗文不存在"));
        List<AncientPoemBusinessRef> businessRefList = ancientPoemBusinessRefBiz.lambdaQuery()
                .eq(AncientPoemBusinessRef::getAncientPoemId, bo.getId())
                .orderByDesc(AncientPoemBusinessRef::getSort).list();
        Map<Integer, List<AncientPoemBusinessRef>> businessTypeGroup = businessRefList.stream()
                .collect(Collectors.groupingBy(AncientPoemBusinessRef::getBusinessType));



        List<AncientPoemRelateDTO> ancientPoemRelates = businessTypeGroup.keySet().stream().map(businessType -> getAncientPoemReleateStrategy(businessType)
                .getAncientPoemRelate(businessType, businessTypeGroup.get(businessType), ancientPoem)).flatMap(List::stream).toList();
        ancientPoemRelates.forEach(relate -> {
            relate.setBusinessName(PoemBusinessTypeEnum.getByCode(relate.getBusinessType()).getName());
        });
        return ancientPoemRelates;
    }

    private void setBizOtherInfo(List<AncientPoemDTO> ancientPoems) {
        if(CollUtil.isEmpty(ancientPoems)) {
            return;
        }
        Set<Integer> ids = ancientPoems.stream().map(AncientPoemDTO::getId).collect(Collectors.toSet());
        List<AppCommonMediaDTO> originalAudioMedias = appMaterialBusinessRefBiz.getCommonBusinessList(ids, BusinessTypeEnum.ANCIENT_POEM_ORIGINAL_AUDIO_BUSINESS);
        List<AppCommonMediaDTO> explanationAudioMedias = appMaterialBusinessRefBiz.getCommonBusinessList(ids, BusinessTypeEnum.ANCIENT_POEM_EXPLANATION_AUDIO_BUSINESS);
        Map<Integer, List<AppCommonMediaDTO>> originalAudioMediaMap = originalAudioMedias.stream().collect(Collectors.groupingBy(AppCommonMediaDTO::getBusinessId));
        Map<Integer, List<AppCommonMediaDTO>> explanationAudioMediaMap = explanationAudioMedias.stream().collect(Collectors.groupingBy(AppCommonMediaDTO::getBusinessId));
        Map<Integer, Long> ancientPoemRelateCountMap = getAncientPoemRelateCountMap(ids);
        ancientPoems.forEach(ancientPoemDTO -> {
            ancientPoemDTO.setOriginalAudioMedias(originalAudioMediaMap.get(ancientPoemDTO.getId()));
            ancientPoemDTO.setExplanationAudioMedias(explanationAudioMediaMap.get(ancientPoemDTO.getId()));
            ancientPoemDTO.setIsRef(ancientPoemRelateCountMap.containsKey(ancientPoemDTO.getId()));
        });
    }

    private void checkAncientPoemUpsertParams(AncientPoemUpsertBO bo, boolean isUpdate) throws BusinessException {
        //标题不能重复
//        LambdaQueryWrapper<AncientPoem> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(AncientPoem::getTitle, bo.getTitle())
//                .eq(AncientPoem::getTitle, bo.getTitle());
        if (isUpdate) {
            Assert.notNull(bo.getId(), () -> new BusinessException(ANCIENT_POEM_VERIFICATION_FAIL_CODE, "ID不能为空"));
//            queryWrapper.ne(AncientPoem::getId, bo.getId());
        } else {
            bo.setId(null);
        }
//        long count = ancientPoemBiz.count(queryWrapper);
//        Assert.isTrue(count == 0, () -> new BusinessException(ANCIENT_POEM_VERIFICATION_FAIL_CODE, "标题已存在"));
    }

    private AncientPoem upsertAncientPoem(AncientPoemUpsertBO bo) throws BusinessException {
        AncientPoem ancientPoem =  BeanUtil.copyProperties(bo, AncientPoem.class);
        boolean upsert = ancientPoemBiz.saveOrUpdate(ancientPoem);
        Assert.isTrue(upsert, () -> new BusinessException(ANCIENT_POEM_VERIFICATION_FAIL_CODE, "信息保存失败"));
        return ancientPoem;
    }

    /**
     * 删除并插入相关媒体文件
     */
    private void desertAncientPoemMedias(Integer businessId, AncientPoemUpsertBO bo) {
        deletedAncientPoemMedias(Collections.singleton(businessId));
        appMaterialBusinessRefBiz.insert(BusinessTypeEnum.ANCIENT_POEM_ORIGINAL_AUDIO_BUSINESS, businessId, bo.getOriginalAudioMedias());
        appMaterialBusinessRefBiz.insert(BusinessTypeEnum.ANCIENT_POEM_EXPLANATION_AUDIO_BUSINESS, businessId, bo.getExplanationAudioMedias());
    }

    /**
     * 删除相关媒体文件
     */
    private void deletedAncientPoemMedias(Collection<Integer> businessIds) {
        appMaterialBusinessRefBiz.delete(businessIds, BusinessTypeEnum.ANCIENT_POEM_ORIGINAL_AUDIO_BUSINESS);
        appMaterialBusinessRefBiz.delete(businessIds, BusinessTypeEnum.ANCIENT_POEM_EXPLANATION_AUDIO_BUSINESS);
    }

    /**
     * 复制并重绑定媒体资源到新 ID
     */
    private List<AppMaterialBusinessRef> copyAndRebindMaterials(List<AppMaterialBusinessRef> materials,
                                                                Map<Integer, Integer> oldIdToNewIdMap, CopyOptions ignoreOptions) {
        if (materials == null || materials.isEmpty()) {
            return Collections.emptyList();
        }
        List<AppMaterialBusinessRef> copyMaterials = BeanUtil.copyToList(materials, AppMaterialBusinessRef.class, ignoreOptions);
        return copyMaterials.stream()
                .filter(material -> oldIdToNewIdMap.containsKey(material.getBusinessId()))
                .map(material -> material.setBusinessId(oldIdToNewIdMap.get(material.getBusinessId()))).toList();
    }

    private static IAncientPoemReleateStrategy getAncientPoemReleateStrategy(Integer businessCode) {
        PoemBusinessTypeEnum typeEnum = PoemBusinessTypeEnum.getByCode(businessCode);
        return SpringUtils.getBean(typeEnum.getStrategy());
    }


    public Map<Integer, Long> getAncientPoemRelateCountMap(Collection<Integer> ancientPoemIds) {
        if(CollUtil.isEmpty(ancientPoemIds)) {
            return Collections.emptyMap();
        }
        String groupByColumn = "ancient_poem_id";
        QueryWrapper<AncientPoemBusinessRef> wrapper =  Wrappers.query();
        List<Map<String, Object>> maps = ancientPoemBusinessRefBiz.listMaps(wrapper.select(groupByColumn, "COUNT(*) AS count")
                .in(groupByColumn, ancientPoemIds)
                .groupBy(groupByColumn));
        return maps.stream()
                .collect(Collectors.toMap(
                        map -> (Integer)map.get(groupByColumn),
                        map -> (Long)map.get("count")));
                
    }
}
