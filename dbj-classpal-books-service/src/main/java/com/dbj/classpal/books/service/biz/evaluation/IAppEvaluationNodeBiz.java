package com.dbj.classpal.books.service.biz.evaluation;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.service.entity.evaluation.AppEvaluationNode;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppEvaluationNodeBiz
 * Date:     2025-04-08 16:26:13
 * Description: 表名： ,描述： 表
 */
public interface IAppEvaluationNodeBiz extends IService<AppEvaluationNode> {

    /**
     * 获取评测表下评测项最大排序
     * @param id
     * @return
     */
    Integer getMaxSortNum(Integer id);

}
