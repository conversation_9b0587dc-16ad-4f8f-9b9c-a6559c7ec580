package com.dbj.classpal.books.service.biz.ebooks.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookshelfBookRefBiz;
import com.dbj.classpal.books.service.entity.ebooks.AppEBookshelfBookRef;
import com.dbj.classpal.books.service.mapper.ebooks.AppEBookshelfBookRefMapper;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 书架-单书关联 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
@Slf4j
@Service
public class AppEBookshelfBookRefBizImpl extends ServiceImpl<AppEBookshelfBookRefMapper, AppEBookshelfBookRef> implements IAppEBookshelfBookRefBiz {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(Integer shelfId, List<Integer> bookIds) throws BusinessException {
        if (shelfId == null || CollectionUtils.isEmpty(bookIds)) {
            throw new BusinessException("参数不能为空");
        }
        
        // 查询已经存在的关联关系，避免重复添加
        List<Integer> existBookIds = listBookIds(List.of(shelfId));
        
        // 过滤出需要添加的ID
        List<Integer> needAddBookIds = bookIds.stream()
                .filter(bookId -> !existBookIds.contains(bookId))
                .collect(Collectors.toList());
        
        if (CollectionUtils.isEmpty(needAddBookIds)) {
            return true;
        }
        
        // 批量添加关联关系
        List<AppEBookshelfBookRef> refList = new ArrayList<>();
        
        // 获取当前最大排序号
        Integer maxSortNum = 0;
        if (!existBookIds.isEmpty()) {
            LambdaQueryWrapper<AppEBookshelfBookRef> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AppEBookshelfBookRef::getShelfId, shelfId)
                    .orderByDesc(AppEBookshelfBookRef::getSortNum)
                    .last("LIMIT 1");
            AppEBookshelfBookRef maxSortRef = this.getOne(queryWrapper);
            if (maxSortRef != null) {
                maxSortNum = maxSortRef.getSortNum();
            }
        }
        
        // 构建关联实体列表
        for (Integer bookId : needAddBookIds) {
            AppEBookshelfBookRef ref = new AppEBookshelfBookRef();
            ref.setShelfId(shelfId);
            ref.setBookId(bookId);
            ref.setSortNum(++maxSortNum);
            refList.add(ref);
        }
        
        return this.saveBatch(refList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeBatch(List<Integer> shelfIds, List<Integer> bookIds) throws BusinessException {
        if (shelfIds == null) {
            throw new BusinessException("书架ID不能为空");
        }
        
        LambdaQueryWrapper<AppEBookshelfBookRef> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AppEBookshelfBookRef::getShelfId, shelfIds);
        
        // 如果指定了书籍ID列表，则只删除这些关联
        if (CollectionUtils.isNotEmpty(bookIds)) {
            queryWrapper.in(AppEBookshelfBookRef::getBookId, bookIds);
        }
        
        return this.remove(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSort(Integer shelfId, Map<Integer, Integer> bookSortMap) throws BusinessException {
        if (shelfId == null || bookSortMap == null || bookSortMap.isEmpty()) {
            throw new BusinessException("参数不能为空");
        }
        
        List<Integer> bookIds = new ArrayList<>(bookSortMap.keySet());
        
        // 查询需要更新的关联记录
        LambdaQueryWrapper<AppEBookshelfBookRef> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppEBookshelfBookRef::getShelfId, shelfId)
                .in(AppEBookshelfBookRef::getBookId, bookIds);
        
        List<AppEBookshelfBookRef> refList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(refList)) {
            return true;
        }
        
        // 更新排序号
        for (AppEBookshelfBookRef ref : refList) {
            Integer sortNum = bookSortMap.get(ref.getBookId());
            if (sortNum != null) {
                ref.setSortNum(sortNum);
            }
        }
        
        return this.updateBatchById(refList);
    }

    @Override
    public List<Integer> listBookIds(List<Integer> shelfIds) {
        if (shelfIds == null) {
            return new ArrayList<>();
        }
        
        LambdaQueryWrapper<AppEBookshelfBookRef> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AppEBookshelfBookRef::getShelfId, shelfIds)
                .orderByAsc(AppEBookshelfBookRef::getSortNum);
        
        List<AppEBookshelfBookRef> refList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(refList)) {
            return new ArrayList<>();
        }
        
        return refList.stream()
                .map(AppEBookshelfBookRef::getBookId)
                .collect(Collectors.toList());
    }

    @Override
    public List<Integer> listShelfIds(Integer bookId) {
        if (bookId == null) {
            return new ArrayList<>();
        }
        
        LambdaQueryWrapper<AppEBookshelfBookRef> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppEBookshelfBookRef::getBookId, bookId);
        
        List<AppEBookshelfBookRef> refList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(refList)) {
            return new ArrayList<>();
        }
        
        return refList.stream()
                .map(AppEBookshelfBookRef::getShelfId)
                .collect(Collectors.toList());
    }

    @Override
    public Map<Integer, List<Integer>> batchListBookIds(List<Integer> shelfIds) {
        if (CollectionUtils.isEmpty(shelfIds)) {
            return new HashMap<>();
        }

        LambdaQueryWrapper<AppEBookshelfBookRef> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AppEBookshelfBookRef::getShelfId, shelfIds)
                .orderByAsc(AppEBookshelfBookRef::getSortNum);

        List<AppEBookshelfBookRef> refList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(refList)) {
            return new HashMap<>();
        }

        // 按书架ID分组
        return refList.stream()
                .collect(Collectors.groupingBy(
                        AppEBookshelfBookRef::getShelfId,
                        Collectors.mapping(
                                AppEBookshelfBookRef::getBookId,
                                Collectors.toList()
                        )
                ));
    }

    @Override
    public int countBooks(Integer shelfId) {
        if (shelfId == null) {
            return 0;
        }

        LambdaQueryWrapper<AppEBookshelfBookRef> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppEBookshelfBookRef::getShelfId, shelfId);

        return (int)this.count(queryWrapper);
    }

    @Override
    public Map<Integer, Integer> batchCountBooks(List<Integer> shelfIds) {
        if (CollectionUtils.isEmpty(shelfIds)) {
            return new HashMap<>();
        }

        // 批量查询所有书架的书籍关联关系
        LambdaQueryWrapper<AppEBookshelfBookRef> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AppEBookshelfBookRef::getShelfId, shelfIds);

        List<AppEBookshelfBookRef> refList = this.list(queryWrapper);

        // 按书架ID分组统计数量
        Map<Integer, Integer> result = new HashMap<>();

        // 初始化所有书架的计数为0
        for (Integer shelfId : shelfIds) {
            result.put(shelfId, 0);
        }

        // 统计每个书架的书籍数量
        if (CollectionUtils.isNotEmpty(refList)) {
            Map<Integer, Long> countMap = refList.stream()
                    .collect(Collectors.groupingBy(
                            AppEBookshelfBookRef::getShelfId,
                            Collectors.counting()
                    ));

            // 转换为Integer类型
            for (Map.Entry<Integer, Long> entry : countMap.entrySet()) {
                result.put(entry.getKey(), entry.getValue().intValue());
            }
        }

        return result;
    }
} 