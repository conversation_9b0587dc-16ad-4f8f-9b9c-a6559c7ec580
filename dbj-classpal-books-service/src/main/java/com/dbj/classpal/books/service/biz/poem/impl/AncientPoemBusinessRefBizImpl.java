package com.dbj.classpal.books.service.biz.poem.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.client.bo.poem.AncientPoemBusinessRefBO;
import com.dbj.classpal.books.client.bo.poem.app.AncientPoemBusinessRefPageBO;
import com.dbj.classpal.books.client.dto.poem.app.AncientPoemBusinessRefPageDTO;
import com.dbj.classpal.books.common.enums.poem.PoemBusinessTypeEnum;
import com.dbj.classpal.books.client.bo.poem.AncientPoemBusinessRefListBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemBusinessRefListDTO;
import com.dbj.classpal.books.common.dto.poem.AncientPoemBusinessRefCountDTO;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemBusinessRefBiz;
import com.dbj.classpal.books.service.entity.poem.AncientPoemBusinessRef;
import com.dbj.classpal.books.service.mapper.poem.AncientPoemBusinessRefMapper;
import com.dbj.classpal.framework.commons.request.PageInfo;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;


/**
 * <p>
 * 古诗文关联业务关系表 业务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Service
public class AncientPoemBusinessRefBizImpl extends ServiceImpl<AncientPoemBusinessRefMapper, AncientPoemBusinessRef> implements IAncientPoemBusinessRefBiz {

    @Override
    public boolean insert(PoemBusinessTypeEnum businessTypeEnum, Integer businessId, List<AncientPoemBusinessRefBO> refs) {
        List<AncientPoemBusinessRef> ancientPoemBusinessRefs = refs.stream().map(media -> new AncientPoemBusinessRef()
                .setAncientPoemId(media.getAncientPoemId())
                .setBusinessId(businessId)
                .setBusinessType(businessTypeEnum.getCode())
                .setBusinessName(businessTypeEnum.getType())
                .setSort(media.getSort())
        ).toList();
        return saveBatch(ancientPoemBusinessRefs);
    }

    @Override
    public boolean delete(Collection<Integer> businessIds, PoemBusinessTypeEnum businessTypeEnum) {
        return remove(new LambdaQueryWrapper<AncientPoemBusinessRef>()
                .in(AncientPoemBusinessRef::getBusinessId, businessIds)
                .eq(AncientPoemBusinessRef::getBusinessType, businessTypeEnum.getCode())
        );
    }
    @Override
    public List<AncientPoemBusinessRefCountDTO> getCount(List<Integer> businessIds, Integer businessType) {
        return baseMapper.getCount(businessIds, businessType);
    }

    @Override
    public List<AncientPoemBusinessRefListDTO> listAncientPoemBusinessRef(AncientPoemBusinessRefListBO anomalyPoemBusinessRefListBO) {
        return baseMapper.listAncientPoemBusinessRef(anomalyPoemBusinessRefListBO);
    }

    @Override
    public Page<AncientPoemBusinessRefPageDTO> pageAncientPoemBusinessRef(PageInfo<AncientPoemBusinessRefPageBO> pageInfo) {
        return baseMapper.pageAncientPoemBusinessRef(pageInfo.getPage(), pageInfo.getData());
    }
}
