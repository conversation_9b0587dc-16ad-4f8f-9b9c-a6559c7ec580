package com.dbj.classpal.books.service.biz.config.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.bo.config.BasicConfigBO;
import com.dbj.classpal.books.common.dto.config.BasicConfigDTO;
import com.dbj.classpal.books.service.biz.config.BasicConfigBiz;
import com.dbj.classpal.books.service.entity.config.BasicConfig;
import com.dbj.classpal.books.service.mapper.config.BasicConfigMapper;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_SUBJECT_NAME_EXIST_CODE;
import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_SUBJECT_NAME_EXIST_MSG;

/**
 * 题库学科业务实现类
 */
@Service
@RequiredArgsConstructor
public class BasicConfigBizImpl extends ServiceImpl<BasicConfigMapper, BasicConfig> implements BasicConfigBiz {

    private final BasicConfigMapper basicConfigMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer create(BasicConfigBO bo) throws BusinessException {
        if (existsByName(bo.getName(),bo.getBizType(), null)) {
            throw new BusinessException(APP_SUBJECT_NAME_EXIST_CODE,APP_SUBJECT_NAME_EXIST_MSG);
        }

        BasicConfig entity = BeanUtil.copyProperties(bo, BasicConfig.class);
        basicConfigMapper.insert(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(BasicConfigBO bo) throws BusinessException {
        if (existsByName(bo.getName(),bo.getBizType(), bo.getId())) {
            throw new BusinessException(APP_SUBJECT_NAME_EXIST_CODE,APP_SUBJECT_NAME_EXIST_MSG);
        }

        BasicConfig entity = BeanUtil.copyProperties(bo, BasicConfig.class);
        basicConfigMapper.updateById(entity);
    }

    @Override
    public void batchDelete(List<Integer> ids) throws BusinessException {
        basicConfigMapper.deleteByIds(ids);
    }

    @Override
    public BasicConfigDTO detail(Integer id) {
        BasicConfig entity = basicConfigMapper.selectById(id);
        return BeanUtil.copyProperties(entity, BasicConfigDTO.class);
    }

    @Override
    public List<BasicConfigDTO> listByType(String bizType) {
        LambdaQueryWrapper<BasicConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BasicConfig::getBizType, bizType)
                .eq(BasicConfig::getIsDeleted, YesOrNoEnum.NO.getCode());
        List<BasicConfig> entityList = basicConfigMapper.selectList(wrapper);
        return BeanUtil.copyToList(entityList, BasicConfigDTO.class);
    }


    @Override
    public Page<BasicConfig> pageList(Page<BasicConfig> page, BasicConfig query) {
        // 1. 构建基础查询条件

        LambdaQueryWrapper<BasicConfig> wrapper = new LambdaQueryWrapper<BasicConfig>()
                .like(StringUtil.isNotBlank(query.getName()), BasicConfig::getName, query.getName())
                .eq(StringUtil.isNotBlank(query.getBizType()), BasicConfig::getBizType, query.getBizType())
                .orderByDesc(BasicConfig::getSortNum,BasicConfig::getCreateTime);
        return this.page(page, wrapper);
    }

    @Override
    public boolean existsByName(String name,String bizType, Integer excludeId) {
        LambdaQueryWrapper<BasicConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BasicConfig::getName, name).eq(BasicConfig::getBizType, bizType)
                .eq(BasicConfig::getIsDeleted, YesOrNoEnum.NO.getCode());
        if (excludeId != null) {
            wrapper.ne(BasicConfig::getId, excludeId);
        }
        return basicConfigMapper.selectCount(wrapper) > YesOrNoEnum.NO.getCode();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSort(List<BasicConfigBO> boList) {
        if (CollectionUtils.isEmpty(boList)) {
            return;
        }

        for (BasicConfigBO bo : boList) {
            if (Objects.isNull(bo.getId()) || Objects.isNull(bo.getSortNum())) {
                continue;
            }
            lambdaUpdate()
                    .eq(BasicConfig::getId, bo.getId())
                    .eq(BasicConfig::getBizType, bo.getBizType())
                    .set(BasicConfig::getSortNum, bo.getSortNum())
                    .update();
        }
    }
} 