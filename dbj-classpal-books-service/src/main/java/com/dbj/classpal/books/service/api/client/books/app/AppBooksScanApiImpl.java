package com.dbj.classpal.books.service.api.client.books.app;

import com.dbj.classpal.books.client.api.books.app.AppBooksScanApi;
import com.dbj.classpal.books.client.bo.books.app.BooksScanInfoBO;
import com.dbj.classpal.books.client.enums.ScanCodeTypeEnum;
import com.dbj.classpal.books.service.biz.books.IBooksRankInfoBiz;
import com.dbj.classpal.books.service.biz.books.IBooksScanInfoBiz;
import com.dbj.classpal.books.service.biz.books.IBooksUserRankInCodeStudyLogBiz;
import com.dbj.classpal.books.service.biz.books.IBooksUserRankStudyLogBiz;
import com.dbj.classpal.books.service.biz.books.IBooksUserRefBiz;
import com.dbj.classpal.books.service.entity.books.BooksRankInfo;
import com.dbj.classpal.books.service.entity.books.BooksScanInfo;
import com.dbj.classpal.books.service.entity.books.BooksUserRankInCodeStudyLog;
import com.dbj.classpal.books.service.entity.books.BooksUserRankStudyLog;
import com.dbj.classpal.books.service.entity.books.BooksUserRef;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.enums.YesOrNoEnum;
import com.dbj.classpal.framework.utils.util.ContextAppUtil;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className BooksScanApiImpl
 * @description
 * @date 2025-04-25 08:38
 **/
@RestController
public class AppBooksScanApiImpl implements AppBooksScanApi {

    @Resource
    private IBooksRankInfoBiz booksRankInfoBiz;
    @Resource
    private IBooksScanInfoBiz booksScanInfoBiz;
    @Resource
    private IBooksUserRankStudyLogBiz booksUserRankStudyLogBiz;
    @Resource
    private IBooksUserRefBiz booksUserRefBiz;
    @Resource
    private IBooksUserRankInCodeStudyLogBiz booksUserRankInCodeStudyLogBiz;
    @Override
    public RestResponse<Boolean> save(BooksScanInfoBO bo) throws BusinessException {
        Integer appUserId = ContextAppUtil.getAppUserIdInt();
        Integer rankId = bo.getRankId();
        Integer booksId = bo.getBooksId();
        Integer rankClassifyId = bo.getRankClassifyId();
        Integer inCodesContentsId = bo.getInCodesContentsId();
        BooksRankInfo bookRankInfo = booksRankInfoBiz.getById(rankId);
        ScanCodeTypeEnum scanCodeTypeEnum = bo.getScanCodeType();
        //添加一次扫码记录
        BooksScanInfo booksScanInfo = new BooksScanInfo();
        booksScanInfo.setBooksId(bookRankInfo.getBookId());
        booksScanInfo.setRankId(bookRankInfo.getId());
        booksScanInfo.setRankClassifyId(rankClassifyId);
        booksScanInfo.setInCodesContentsId(inCodesContentsId);
        booksScanInfo.setAppUserId(appUserId);
        booksScanInfo.setCodeType(scanCodeTypeEnum.getCode());
        booksScanInfoBiz.save(booksScanInfo);
        if(appUserId !=null){

            //判断用户的这本书是否在书架 如果不在需要添加
            List<BooksUserRef> booksUserRefList = booksUserRefBiz.lambdaQuery().eq(BooksUserRef::getAppUserId, appUserId).eq(BooksUserRef::getBooksId, booksId).list();
            if(CollectionUtils.isEmpty(booksUserRefList)){
                BooksUserRef booksUserRef = new BooksUserRef();
                booksUserRef.setAppUserId(appUserId);
                booksUserRef.setBooksId(booksId);
                booksUserRefBiz.save(booksUserRef);
            }
            switch (scanCodeTypeEnum){
                case b1:
                case b2:
                    //修改用户的学习记录
                    BooksUserRankStudyLog booksUserRankStudyLog = booksUserRankStudyLogBiz.lambdaQuery().eq(BooksUserRankStudyLog::getAppUserId,appUserId).eq(BooksUserRankStudyLog::getRankId,rankId).last("LIMIT 1").one();
                    //不等于空进行修改，等于空则需要新增
                    if(booksUserRankStudyLog != null){
                        booksUserRankStudyLogBiz.lambdaUpdate().eq(BooksUserRankStudyLog::getId,booksUserRankStudyLog.getId())
                                .set(BooksUserRankStudyLog::getIsLastStudy, YesOrNoEnum.YES.getCode())
                                .set(BooksUserRankStudyLog::getLastStudyTime, LocalDateTime.now())
                                .update();
                    }else {
                        BooksUserRankStudyLog rankStudyLog = new BooksUserRankStudyLog();
                        rankStudyLog.setAppUserId(appUserId);
                        rankStudyLog.setRankId(rankId);
                        rankStudyLog.setBooksId(bookRankInfo.getBookId());
                        rankStudyLog.setIsLastStudy(YesOrNoEnum.YES.getCode());
                        rankStudyLog.setLastStudyTime(LocalDateTime.now());
                        booksUserRankStudyLogBiz.save(rankStudyLog);
                    }

                    break;
                case b3:
                case b4:
                    //修改用户的学习记录
                    BooksUserRankInCodeStudyLog booksUserRankInCodeStudyLog = booksUserRankInCodeStudyLogBiz.lambdaQuery().eq(BooksUserRankInCodeStudyLog::getAppUserId,appUserId).eq(BooksUserRankInCodeStudyLog::getInCodesContentsId,inCodesContentsId).last("LIMIT 1").one();
                    //不等于空进行修改，等于空则需要新增
                    if(booksUserRankInCodeStudyLog != null){
                        booksUserRankInCodeStudyLogBiz.lambdaUpdate().eq(BooksUserRankInCodeStudyLog::getId,booksUserRankInCodeStudyLog.getId())
                                .set(BooksUserRankInCodeStudyLog::getIsLastStudy, YesOrNoEnum.YES.getCode())
                                .set(BooksUserRankInCodeStudyLog::getLastStudyTime, LocalDateTime.now())
                                .update();
                    }else {
                        BooksUserRankInCodeStudyLog inCodeStudyLog =  new BooksUserRankInCodeStudyLog();
                        inCodeStudyLog.setAppUserId(appUserId);
                        inCodeStudyLog.setRankId(rankId);
                        inCodeStudyLog.setBooksId(bookRankInfo.getBookId());
                        inCodeStudyLog.setInCodesContentsId(inCodesContentsId);
                        inCodeStudyLog.setRankClassifyId(rankClassifyId);
                        inCodeStudyLog.setIsLastStudy(YesOrNoEnum.YES.getCode());
                        inCodeStudyLog.setLastStudyTime(LocalDateTime.now());
                        booksUserRankInCodeStudyLogBiz.save(inCodeStudyLog);
                    }
                    break;
                default:
                    break;
            }
        }
        return RestResponse.success(true);
    }
}
