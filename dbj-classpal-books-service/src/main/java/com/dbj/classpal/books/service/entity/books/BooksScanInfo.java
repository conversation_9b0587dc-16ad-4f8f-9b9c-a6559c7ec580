package com.dbj.classpal.books.service.entity.books;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 图书配置-图书内容分类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("books_scan_info")
@Tag(name="BooksScanInfo对象", description="图书配置-图书内容分类")
public class BooksScanInfo extends BizEntity implements Serializable {




    @Schema(description ="app用户id")
    @TableField("app_user_id")
    private Integer appUserId;
    @Schema(description ="图书id")
    @TableField("books_id")
    private Integer booksId;

    @Schema(description ="册数id")
    @TableField("rank_id")
    private Integer rankId;

    @Schema(description ="册数功能分类id")
    @TableField("rank_classify_id")
    private Integer rankClassifyId;

    @Schema(description ="书内码内容目录id")
    @TableField("in_codes_contents_id")
    private Integer inCodesContentsId;

    @Schema(description ="二维码类型 册数新印码 b2 册数H5码 b1  新印码 b4 H5码 b3")
    @TableField("code_type ")
    private String codeType ;

    @Schema(description ="是否启用 1-是 0-否")
    @TableField("status")
    private Boolean status;



}
