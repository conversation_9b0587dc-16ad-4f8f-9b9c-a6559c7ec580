package com.dbj.classpal.books.service.biz.material.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.client.bo.material.AppCommonMediaBO;
import com.dbj.classpal.books.client.dto.material.AppCommonMediaDTO;
import com.dbj.classpal.books.common.bo.common.CommonIdBO;
import com.dbj.classpal.books.common.bo.material.AppMaterialBusinessRefQueryBO;
import com.dbj.classpal.books.common.bo.material.AppMaterialBusinessRefQueryCommonBO;
import com.dbj.classpal.books.common.dto.material.AppMaterialBusinessRefDTO;
import com.dbj.classpal.books.common.dto.material.AppMaterialBusinessRefTypeCountDTO;
import com.dbj.classpal.books.common.enums.BusinessTypeEnum;
import com.dbj.classpal.books.common.enums.FileTypeEnum;
import com.dbj.classpal.books.common.enums.MaterialTypeEnum;
import com.dbj.classpal.books.service.biz.material.IAppMaterialBusinessRefBiz;
import com.dbj.classpal.books.service.entity.material.AppMaterialBusinessRef;
import com.dbj.classpal.books.service.mapper.material.AppMaterialBusinessRefMapper;
import com.dbj.classpal.framework.commons.request.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppMaterialBusinessImpl
 * Date:     2025-04-08 16:26:39
 * Description: 表名： ,描述： 表
 */
@Service
public class AppMaterialBusinessRefBizImpl extends ServiceImpl<AppMaterialBusinessRefMapper, AppMaterialBusinessRef> implements IAppMaterialBusinessRefBiz {
    @Override
    public List<AppMaterialBusinessRefDTO> refBusinessList(AppMaterialBusinessRefQueryCommonBO bo) {
        return baseMapper.refBusinessList(bo);
    }

    @Override
    public List<AppMaterialBusinessRefDTO> getBusinessList(List<Integer> businessIds, Integer businessType, List<Integer> appMaterialTypes) {
        return baseMapper.getBusinessList(businessIds,businessType,appMaterialTypes);
    }

    @Override
    public List<AppMaterialBusinessRefTypeCountDTO> refBusinessTypeCount(CommonIdBO bo) {
        return baseMapper.refBusinessTypeCount(bo);
    }

    @Override
    public Page<AppMaterialBusinessRefDTO> pageInfo(PageInfo<AppMaterialBusinessRefQueryBO> bo) {
        return baseMapper.pageInfo(bo.getPage(),bo.getData());
    }

    @Override
    public List<AppCommonMediaDTO> getCommonBusinessList(Collection<Integer> businessIds, BusinessTypeEnum typeEnum) {
        List<AppCommonMediaDTO> commonMedias = this.baseMapper.getCommonBusinessList(businessIds, typeEnum.getCode());
        commonMedias.forEach(media -> {
            if(StringUtils.isNotBlank(media.getMaterialExtension())) {
                MaterialTypeEnum materialTypeEnum = FileTypeEnum.getMaterialType(media.getMaterialExtension());
                if (materialTypeEnum != null) {
                    media.setMaterialIcon(materialTypeEnum.getIcon());
                }
            }
        });
        return commonMedias;
    }

    @Override
    public boolean insert(BusinessTypeEnum businessTypeEnum, Integer businessId, List<AppCommonMediaBO> appCommonMedias) {
        if(CollUtil.isEmpty(appCommonMedias)) {
            return false;
        }
        List<AppMaterialBusinessRef> appMaterialBusinessRefs = appCommonMedias.stream().map(media -> new AppMaterialBusinessRef()
                .setAppMaterialId(media.getMaterialId())
                .setBusinessId(businessId)
                .setBusinessType(businessTypeEnum.getCode())
                .setBusinessName(businessTypeEnum.getType())
                .setOrderNum(media.getOrderNum())
        ).toList();
        return saveBatch(appMaterialBusinessRefs);
    }

    @Override
    public boolean delete(Collection<Integer> businessIds, BusinessTypeEnum businessTypeEnum) {
        return remove(new LambdaQueryWrapper<AppMaterialBusinessRef>()
                .in(AppMaterialBusinessRef::getBusinessId, businessIds)
                .eq(AppMaterialBusinessRef::getBusinessType, businessTypeEnum.getCode())
        );
    }
}