package com.dbj.classpal.books.service.api.client.audio;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.audio.AudioHintMusicApi;
import com.dbj.classpal.books.client.bo.audio.AudioHintMusicAddBO;
import com.dbj.classpal.books.client.bo.audio.AudioHintMusicDelBO;
import com.dbj.classpal.books.client.bo.audio.AudioHintMusicPageBO;
import com.dbj.classpal.books.client.bo.audio.AudioHintMusicUpdBO;
import com.dbj.classpal.books.client.dto.audio.AudioHintMusicPageDTO;
import com.dbj.classpal.books.common.dto.audio.AudioHintRefNumDTO;
import com.dbj.classpal.books.common.enums.audio.AudioGlobalConfigAudioType;
import com.dbj.classpal.books.common.enums.audio.AudioGlobalConfigTypeEnum;
import com.dbj.classpal.books.common.enums.audio.AudioHintMusicType;
import com.dbj.classpal.books.service.biz.audio.IAudioContextInfoBiz;
import com.dbj.classpal.books.service.biz.audio.IAudioGlobalConfigBiz;
import com.dbj.classpal.books.service.biz.audio.IAudioHintMusicBiz;
import com.dbj.classpal.books.service.entity.audio.AudioGlobalConfig;
import com.dbj.classpal.books.service.entity.audio.AudioHintMusic;
import com.dbj.classpal.books.service.util.audio.AudioDurationUtils;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import com.dbj.classpal.framework.commons.utils.Assert;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 音频制作配置
 * <AUTHOR>
 * @since 2025-06-25
 */
@RestController
@RequiredArgsConstructor
public class AudioHintMusicApiImpl implements AudioHintMusicApi {

    private final IAudioHintMusicBiz audioHintMusicBiz;
    private final IAudioGlobalConfigBiz audioGlobalConfigBiz;
    private final IAudioContextInfoBiz audioContextInfoBiz;

    @Override
    public RestResponse<Page<AudioHintMusicPageDTO>> pageInfo(PageInfo<AudioHintMusicPageBO> pageInfo) throws BusinessException {
        AudioHintMusicPageBO bo = pageInfo.getData();
        Page<AudioHintMusicPageDTO> page = (Page<AudioHintMusicPageDTO>) audioHintMusicBiz.lambdaQuery()
                .eq(AudioHintMusic::getType, bo.getType())
                .like(StrUtil.isNotEmpty(bo.getName()), AudioHintMusic::getName, bo.getName())
                .orderByDesc(AudioHintMusic::getWeight)
                .page(pageInfo.getPage())
                .convert(e -> BeanUtil.copyProperties(e, AudioHintMusicPageDTO.class));
        List<AudioHintMusicPageDTO> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return RestResponse.success(page);
        }

        List<Integer> ids = records.stream().map(AudioHintMusicPageDTO::getId).toList();
        Map<Integer, Integer> hintRefNumMap = new HashMap<>();
        Map<Integer, Long> backgroundRefNumMap = new HashMap<>();

        switch (AudioHintMusicType.of(bo.getType())) {
            case BACKGROUND -> {
                List<AudioGlobalConfig> agcList = audioGlobalConfigBiz.lambdaQuery()
                        .select(AudioGlobalConfig::getId, AudioGlobalConfig::getAudioBackgroundId)
                        .in(AudioGlobalConfig::getAudioBackgroundId, ids)
                        .eq(AudioGlobalConfig::getType, AudioGlobalConfigTypeEnum.BACKGROUND.getCode())
                        .eq(AudioGlobalConfig::getAudioType, AudioGlobalConfigAudioType.ADVANCED.getCode())
                        .list();
                backgroundRefNumMap = agcList.stream().collect(Collectors.groupingBy(AudioGlobalConfig::getAudioBackgroundId,
                        Collectors.mapping(AudioGlobalConfig::getAudioBackgroundId, Collectors.counting())));
            }
            case HINT -> {
                List<AudioHintRefNumDTO> refNumList = audioContextInfoBiz.statRefNum(ids);
                hintRefNumMap = refNumList.stream().collect(Collectors.toMap(AudioHintRefNumDTO::getAudioHintMusicId, AudioHintRefNumDTO::getCount));
            }
        }

        for (AudioHintMusicPageDTO record : records) {
            switch (AudioHintMusicType.of(record.getType())) {
                case HINT -> record.setRefNum(hintRefNumMap.get(record.getId()));
                case BACKGROUND -> record.setRefNum(backgroundRefNumMap.getOrDefault(record.getId(), 0L).intValue());
            }
        }
        return RestResponse.success(page);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RestResponse<Boolean> save(AudioHintMusicAddBO bo) throws BusinessException {
        if (bo.getType().equals(AudioHintMusicType.HINT.getValue())) {
            // 音频格式校验
            AudioDurationUtils.validateAudioFormat(Collections.singletonList(bo.getMaterialUrl()));
        }

        AudioHintMusicType type = AudioHintMusicType.of(bo.getType());
        Assert.isFalse(audioHintMusicBiz.lambdaQuery()
                .eq(AudioHintMusic::getName, bo.getName())
                .eq(AudioHintMusic::getType, type.getValue())
                .exists(), "{} {}已存在", type.getName(), bo.getName());
        AudioHintMusic audioHintMusic = BeanUtil.copyProperties(bo, AudioHintMusic.class);
        Assert.isTrue(audioHintMusicBiz.save(audioHintMusic));
        return RestResponse.success();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RestResponse<Boolean> update(AudioHintMusicUpdBO bo) throws BusinessException {
        if (bo.getType().equals(AudioHintMusicType.HINT.getValue())) {
            // 音频格式校验
            AudioDurationUtils.validateAudioFormat(Collections.singletonList(bo.getMaterialUrl()));
        }

        AudioHintMusicType type = AudioHintMusicType.of(bo.getType());
        Assert.isFalse(audioHintMusicBiz.lambdaQuery()
                .eq(AudioHintMusic::getName, bo.getName())
                .eq(AudioHintMusic::getType, type.getValue())
                .ne(AudioHintMusic::getId, bo.getId())
                .exists(), "{} {}已存在", type.getName(), bo.getName());
        AudioHintMusic audioHintMusic = BeanUtil.copyProperties(bo, AudioHintMusic.class);
        Assert.isTrue(audioHintMusicBiz.updateById(audioHintMusic));
        return RestResponse.success();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RestResponse<Boolean> delete(AudioHintMusicDelBO bo) throws BusinessException {
        AudioHintMusicType type = AudioHintMusicType.of(bo.getType());
        switch (type) {
            case HINT -> {
                List<AudioHintRefNumDTO> refNumList = audioContextInfoBiz.statRefNum(bo.getIds());
                Assert.isTrue(refNumList.stream().mapToInt(AudioHintRefNumDTO::getCount).sum() <= 0,
                        "该文件被关联，请先取消关联后再重试");
            }
            case BACKGROUND -> Assert.isFalse(audioGlobalConfigBiz.lambdaQuery()
                    .eq(AudioGlobalConfig::getType, AudioGlobalConfigTypeEnum.BACKGROUND.getCode())
                    .eq(AudioGlobalConfig::getAudioType, AudioGlobalConfigAudioType.ADVANCED.getCode())
                    .in(AudioGlobalConfig::getAudioBackgroundId, bo.getIds())
                    .exists(), "该文件被关联，请先取消关联后再重试");
        }
        audioHintMusicBiz.lambdaUpdate()
                .in(AudioHintMusic::getId, bo.getIds())
                .eq(AudioHintMusic::getType, bo.getType())
                .remove();
        return RestResponse.success();
    }


}
