package com.dbj.classpal.books.service.entity.poem;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 古诗文表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ancient_poem")
@Tag(name="AncientPoem对象", description="古诗文表")
public class AncientPoem extends BizEntity implements Serializable {



    @Schema(description = "ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "古诗文分类")
    private Integer classifyId;

    @Schema(description = "封面图名称")
    private String coverName;

    @Schema(description = "封面图路径")
    private String coverUrl;

    @Schema(description = "背景图名称")
    private String backgroundName;

    @Schema(description = "背景图路径")
    private String backgroundUrl;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "标题拼音")
    private String titlePinyin;

    @Schema(description = "作者")
    private String author;

    @Schema(description = "所属朝代")
    private String dynasty;

    @Schema(description = "年级")
    private String grade;

    @Schema(description = "是否课外内容 0-课内 1-课外")
    private Integer isOut;

    @Schema(description = "古诗文简介")
    private String introduction;

    @Schema(description = "古诗文释义")
    private String interpretation;

    @Schema(description = "古诗文赏析")
    private String appreciation;

    @Schema(description = "校验文本")
    private String verifyText;

    @Schema(description = "排序权重")
    private Integer sort;

    @TableField(exist = false)
    private Integer copyId;
}
