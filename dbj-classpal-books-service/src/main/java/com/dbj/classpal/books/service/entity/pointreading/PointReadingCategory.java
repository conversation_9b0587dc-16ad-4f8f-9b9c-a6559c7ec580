package com.dbj.classpal.books.service.entity.pointreading;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 点读书分类表
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("point_reading_category")
@Schema(name = "PointReadingCategory", description = "点读书分类表")
public class PointReadingCategory extends BizEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "分类名称")
    private String name;

    @Schema(description = "父级分类ID")
    private Integer parentId;

    @Schema(description = "是否默认分类：0-否 1-是")
    private Integer isDefault;

    @Schema(description = "排序")
    private Integer sortNum;

    @Schema(description = "版本号")
    private Integer version;

    @Schema(description = "状态：0-停用 1-正常")
    private Integer status;
}
