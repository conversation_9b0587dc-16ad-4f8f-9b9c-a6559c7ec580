package com.dbj.classpal.books.service.entity.tree;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 树形分类菜单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tree_classify")
@Tag(name = "树形分类菜单表", description = "树形分类菜单表")
public class TreeClassify extends BizEntity implements Serializable {



    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "分类名称")
    private String name;

    @Schema(description = "父分类id, null表示顶级分类")
    private Integer parentId;

    @Schema(description = "分类级别，0为顶级分类")
    private Integer level;

    @Schema(description = "业务类型")
    private Integer type;

    @Schema(description = "排序权重")
    private Integer sort;
}
