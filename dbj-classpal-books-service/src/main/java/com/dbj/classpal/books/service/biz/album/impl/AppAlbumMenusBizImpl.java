package com.dbj.classpal.books.service.biz.album.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.bo.album.AppAlbumMenusQueryBO;
import com.dbj.classpal.books.common.dto.album.AppAlbumMenusInfoDTO;
import com.dbj.classpal.books.service.biz.album.IAppAlbumMenusBiz;
import com.dbj.classpal.books.service.entity.album.AppAlbumMenus;
import com.dbj.classpal.books.service.mapper.album.AppAlbumMenusMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IAppAlbumMenusBizImpl
 * Date:     2025-04-14 17:10:16
 * Description: 表名： ,描述： 表
 */
@Service
public class AppAlbumMenusBizImpl extends ServiceImpl<AppAlbumMenusMapper, AppAlbumMenus> implements IAppAlbumMenusBiz {

    @Override
    public List<AppAlbumMenusInfoDTO> getAppAlbumMenusInfoByDeptIdGroup(AppAlbumMenusQueryBO bo) {
        return baseMapper.getAppAlbumMenusInfoByDeptIdGroup(bo);
    }

    @Override
    public Integer getChildrenDepth(Integer id) {
        return baseMapper.getChildrenDepth(id);
    }

    @Override
    public Integer getRootDepth(Integer id) {
        return  baseMapper.getRootDepth(id);
    }

    @Override
    public Integer getMaxOrderNum(Integer id) {
        return baseMapper.getMaxOrderNum(id);
    }
}
