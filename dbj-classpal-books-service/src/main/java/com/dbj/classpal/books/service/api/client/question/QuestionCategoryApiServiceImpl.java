package com.dbj.classpal.books.service.api.client.question;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.books.client.api.question.QuestionCategoryApi;
import com.dbj.classpal.books.client.bo.question.*;
import com.dbj.classpal.books.client.dto.question.QuestionCategoryApiDTO;
import com.dbj.classpal.books.client.dto.question.QuestionCategoryRefApiDTO;
import com.dbj.classpal.books.common.bo.question.*;
import com.dbj.classpal.books.common.dto.question.QuestionCategoryDTO;
import com.dbj.classpal.books.common.dto.question.QuestionCategoryRefDTO;
import com.dbj.classpal.books.service.service.question.QuestionCategoryService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * 题目分类接口实现类
 */
@RestController
@RequiredArgsConstructor
public class QuestionCategoryApiServiceImpl implements QuestionCategoryApi {

    private final QuestionCategoryService questionCategoryService;

    @Override
    public RestResponse<QuestionCategoryApiDTO> getCategory(QuestionCategoryIdApiBO apiBO) {
        if (Objects.isNull(apiBO) || Objects.isNull(apiBO.getId())) {
            return RestResponse.success(null);
        }
        QuestionCategoryIdBO idBO = BeanUtil.copyProperties(apiBO, QuestionCategoryIdBO.class);
        QuestionCategoryDTO category = questionCategoryService.getCategory(idBO);
        return RestResponse.success(BeanUtil.copyProperties(category, QuestionCategoryApiDTO.class));
    }

    @Override
    public RestResponse<List<QuestionCategoryApiDTO>> getCategoryList(QuestionCategoryIdApiBO apiBO) {
        QuestionCategoryQueryBO queryBO = BeanUtil.copyProperties(apiBO, QuestionCategoryQueryBO.class);
        queryBO.setFatherId(apiBO.getFatherId());
        List<QuestionCategoryDTO> categories = questionCategoryService.getCategoryTree(queryBO);
        return RestResponse.success(BeanUtil.copyToList(categories, QuestionCategoryApiDTO.class));
    }

    @Override
    public RestResponse<List<QuestionCategoryRefApiDTO>> getBusinessRefs(QuestionCategoryIdQueryApiBO apiBO) {
        QuestionCategoryIdQueryBO queryBO = BeanUtil.copyProperties(apiBO, QuestionCategoryIdQueryBO.class);
        List<QuestionCategoryRefDTO> refList = questionCategoryService.getBusinessRefs(queryBO);
        return RestResponse.success(BeanUtil.copyToList(refList,QuestionCategoryRefApiDTO.class));
    }

    @Override
    public RestResponse<Integer> createCategory(QuestionCategoryApiBO apiBO) throws BusinessException {
        QuestionCategoryBO categoryBO = BeanUtil.copyProperties(apiBO, QuestionCategoryBO.class);
        Integer id = questionCategoryService.createCategory(categoryBO);
        return RestResponse.success(id);
    }

    @Override
    public RestResponse<Void> updateCategory(QuestionCategoryApiBO apiBO) throws BusinessException {
        QuestionCategoryBO categoryBO = BeanUtil.copyProperties(apiBO, QuestionCategoryBO.class);
        questionCategoryService.updateCategory(categoryBO);
        return RestResponse.success(null);
    }

    @Override
    public RestResponse<Void> batchDeleteCategory(QuestionCategoryIdsApiBO apiBO) throws BusinessException {
        QuestionCategoryIdsBO idsBO = BeanUtil.copyProperties(apiBO, QuestionCategoryIdsBO.class);
        questionCategoryService.batchDeleteCategory(idsBO);
        return RestResponse.success(null);
    }

    @Override
    public RestResponse<Void> updateSort(QuestionCategorySortApiBO apiBO) throws BusinessException {
        QuestionCategorySortBO sortBO = BeanUtil.copyProperties(apiBO,QuestionCategorySortBO.class);
        questionCategoryService.updateSort(sortBO);
        return RestResponse.success(null);
    }

    @Override
    public RestResponse<Boolean> checkNameExists(QuestionCategoryApiBO apiBO) {
        boolean exists = questionCategoryService.checkNameExists(apiBO.getName(), apiBO.getFatherId(), apiBO.getId());
        return RestResponse.success(exists);
    }
}