package com.dbj.classpal.books.service.entity.audio;

import lombok.Data;

/**
 * 任务进度信息类
 */
@Data
public class ProgressInfo {
    private final int completed;
    private final int total;

    public ProgressInfo(int completed, int total) {
        this.completed = completed;
        this.total = total;
    }

    public int getCompleted() {
        return completed;
    }

    public int getTotal() {
        return total;
    }

    public double getPercentage() {
        return total == 0 ? 0 : (double) completed / total * 100;
    }

    @Override
    public String toString() {
        return String.format("进度: %d/%d (%.2f%%)", completed, total, getPercentage());
    }
}
