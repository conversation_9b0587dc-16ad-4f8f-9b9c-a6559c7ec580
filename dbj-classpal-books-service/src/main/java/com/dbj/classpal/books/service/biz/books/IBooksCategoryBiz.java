package com.dbj.classpal.books.service.biz.books;

import com.dbj.classpal.books.client.dto.books.BooksCategoryNameTreeApiDTO;
import com.dbj.classpal.books.service.entity.books.BooksCategory;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.framework.commons.exception.BusinessException;

import java.util.List;

/**
 * <p>
 * 产品分类配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
public interface IBooksCategoryBiz extends IService<BooksCategory> {


    void sync() throws BusinessException;


    List<BooksCategoryNameTreeApiDTO> categoryNameTree(List<Integer> categoryIds);

}
