package com.dbj.classpal.books.service.api.client.ebooks;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.ebooks.AppEBookstoreApi;
import com.dbj.classpal.books.client.bo.ebooks.*;
import com.dbj.classpal.books.client.dto.ebooks.AppEBookstoreApiDTO;
import com.dbj.classpal.books.client.dto.ebooks.AppEBookstoreH5ListApiDTO;
import com.dbj.classpal.books.client.dto.ebooks.AppEBookstoreStatisticsApiDTO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookstoreH5QueryBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookstoreQueryBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookstoreSaveBO;
import com.dbj.classpal.books.common.bo.ebooks.AppEBookstoreUpdateBO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookshelfDTO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookstoreDTO;
import com.dbj.classpal.books.common.dto.ebooks.AppEBookstoreH5DTO;
import com.dbj.classpal.books.service.service.ebooks.IAppEBookstoreService;
import com.dbj.classpal.framework.commons.converter.PageInfoConverter;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 书城API接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
@Slf4j
@RestController
public class AppEBookstoreApiImpl implements AppEBookstoreApi {

    @Resource
    private IAppEBookstoreService eBookstoreService;

    @Override
    public RestResponse<Page<AppEBookstoreApiDTO>> page(PageInfo<AppEBookstoreQueryApiBO> pageRequest) throws BusinessException {
        log.info("分页查询书城列表 入参：{}", JSON.toJSONString(pageRequest));

        PageInfo<AppEBookstoreQueryBO> pageBO = PageInfoConverter.convertPageInfo(pageRequest, AppEBookstoreQueryBO.class);

        Page<AppEBookstoreDTO> page = eBookstoreService.page(pageBO);

        log.info("分页查询书城列表 返回记录：{}", page.getRecords());
        return RestResponse.success((Page<AppEBookstoreApiDTO>) page.convert(vo -> {
            AppEBookstoreApiDTO dto = new AppEBookstoreApiDTO();
            BeanUtil.copyProperties(vo, dto);
            return dto;
        }));
    }

    @Override
    public RestResponse<AppEBookstoreApiDTO> detail(AppEBookstoreIdApiBO idBO) throws BusinessException {
        log.info("查询书城详情 入参：{}", JSON.toJSONString(idBO));
        AppEBookstoreDTO dto = eBookstoreService.detail(idBO.getId());
        AppEBookstoreApiDTO apiDTO = BeanUtil.copyProperties(dto, AppEBookstoreApiDTO.class);
        log.info("查询书城详情 结果：{}", JSON.toJSONString(apiDTO));
        return RestResponse.success(apiDTO);
    }

    @Override
    public RestResponse<Integer> save(AppEBookstoreSaveApiBO saveBO) throws BusinessException {
        log.info("新增书城 入参：{}", JSON.toJSONString(saveBO));
        AppEBookstoreSaveBO serviceSaveBO = BeanUtil.copyProperties(saveBO, AppEBookstoreSaveBO.class);
        Integer id = eBookstoreService.save(serviceSaveBO);
        log.info("新增书城 结果：{}", id);
        return RestResponse.success(id);
    }

    @Override
    public RestResponse<Boolean> update(AppEBookstoreUpdateApiBO updateBO) throws BusinessException {
        log.info("更新书城 入参：{}", JSON.toJSONString(updateBO));
        AppEBookstoreUpdateBO serviceUpdateBO = BeanUtil.copyProperties(updateBO, AppEBookstoreUpdateBO.class);
        boolean result = eBookstoreService.update(serviceUpdateBO);
        log.info("更新书城 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> delete(AppEBookstoreIdApiBO idBO) throws BusinessException {
        log.info("删除书城 入参：{}", JSON.toJSONString(idBO));
        boolean result = eBookstoreService.delete(idBO.getId());
        log.info("删除书城 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> deleteBatch(AppEBookstoreIdsApiBO idsBO) throws BusinessException {
        log.info("批量删除书城 入参：{}", JSON.toJSONString(idsBO));
        boolean result = eBookstoreService.deleteBatch(idsBO.getIds());
        log.info("批量删除书城 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> enableBatch(AppEBookstoreIdsApiBO idsBO) throws BusinessException {
        log.info("批量启用书城 入参：{}", JSON.toJSONString(idsBO));
        boolean result = eBookstoreService.enableBatch(idsBO.getIds());
        log.info("批量启用书城 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> disableBatch(AppEBookstoreIdsApiBO idsBO) throws BusinessException {
        log.info("批量禁用书城 入参：{}", JSON.toJSONString(idsBO));
        boolean result = eBookstoreService.disableBatch(idsBO.getIds());
        log.info("批量禁用书城 结果：{}", result);
        return RestResponse.success(result);
    }


    @Override
    public RestResponse<Boolean> allowDownloadBatch(AppEBookstoreIdsApiBO idsBO) throws BusinessException {
        log.info("批量允许下载书城 入参：{}", JSON.toJSONString(idsBO));
        boolean result = eBookstoreService.allowDownloadBatch(idsBO.getIds());
        log.info("批量允许下载单书 结果：{}", result);
        return RestResponse.success(result);
    }

    @Override
    public RestResponse<Boolean> disableDownloadBatch(AppEBookstoreIdsApiBO idsBO) throws BusinessException {
        log.info("批量关闭下载书城 入参：{}", JSON.toJSONString(idsBO));
        boolean result = eBookstoreService.disableDownloadBatch(idsBO.getIds());
        log.info("批量关闭下载单书 结果：{}", result);
        return RestResponse.success(result);
    }


    @Override
    public RestResponse<AppEBookstoreStatisticsApiDTO> getStatistics(AppEBookstoreIdApiBO idBO) throws BusinessException {
        log.info("获取书城统计信息 入参：{}", JSON.toJSONString(idBO));
        
        AppEBookstoreDTO dto = eBookstoreService.getStatistics(idBO.getId());
        
        // 转换为API DTO
        AppEBookstoreStatisticsApiDTO statsDTO = new AppEBookstoreStatisticsApiDTO();
        statsDTO.setStoreId(dto.getId());
        statsDTO.setStoreTitle(dto.getStoreTitle());
        statsDTO.setTotalShelfCount(dto.getShelfCount());
        statsDTO.setTotalBookCount(dto.getTotalBookCount());
        
        // 构建书架统计信息
        Map<Integer, Integer> shelfBookCountMap = new HashMap<>();
        List<AppEBookstoreStatisticsApiDTO.ShelfStatisticsApiDTO> shelfStatsList = new ArrayList<>();
        
        if (dto.getShelves() != null && !dto.getShelves().isEmpty()) {
            for (AppEBookshelfDTO shelfDTO : dto.getShelves()) {
                // 添加到映射
                shelfBookCountMap.put(shelfDTO.getId(), shelfDTO.getBookCount());
                
                // 添加到统计列表
                AppEBookstoreStatisticsApiDTO.ShelfStatisticsApiDTO stats = new AppEBookstoreStatisticsApiDTO.ShelfStatisticsApiDTO();
                stats.setShelfId(shelfDTO.getId());
                stats.setShelfTitle(shelfDTO.getShelfTitle());
                stats.setBookCount(shelfDTO.getBookCount());
                shelfStatsList.add(stats);
            }
        }
        
        statsDTO.setShelfBookCountMap(shelfBookCountMap);
        statsDTO.setShelfStatistics(shelfStatsList);
        
        log.info("获取书城统计信息 结果：{}", JSON.toJSONString(statsDTO));
        return RestResponse.success(statsDTO);
    }

    @Override
    public RestResponse<Page<AppEBookstoreH5ListApiDTO>> pageBookstoreCollections(PageInfo<AppEBookstoreH5QueryApiBO> pageRequest) throws BusinessException {
        log.info("分页查询书城集 入参：{}", JSON.toJSONString(pageRequest));

        // 转换请求参数
        PageInfo<AppEBookstoreH5QueryBO> pageBO = PageInfoConverter.convertPageInfo(pageRequest, AppEBookstoreH5QueryBO.class);

        // 调用服务层方法
        Page<AppEBookstoreH5DTO> page = eBookstoreService.pageBookstoreCollections(pageBO);

        // 转换返回结果
        Page<AppEBookstoreH5ListApiDTO> resultPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<AppEBookstoreH5ListApiDTO> records = new ArrayList<>();
        
        for (AppEBookstoreH5DTO dto : page.getRecords()) {
            AppEBookstoreH5ListApiDTO apiDTO = new AppEBookstoreH5ListApiDTO();
            BeanUtil.copyProperties(dto, apiDTO);

            // 转换书架统计数据（H5分页查询优化版本 - 不包含书籍列表）
            List<AppEBookstoreH5ListApiDTO.ShelfH5ApiDTO> shelfStats = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(dto.getShelfStatistics())) {
                for (AppEBookstoreH5DTO.ShelfH5DTO shelfDTO : dto.getShelfStatistics()) {
                    AppEBookstoreH5ListApiDTO.ShelfH5ApiDTO shelfApiDTO = new AppEBookstoreH5ListApiDTO.ShelfH5ApiDTO();
                    BeanUtil.copyProperties(shelfDTO, shelfApiDTO);
                    shelfStats.add(shelfApiDTO);
                }
            }
            
            apiDTO.setShelfStatistics(shelfStats);
            records.add(apiDTO);
        }
        
        resultPage.setRecords(records);
        
        log.info("分页查询书城集 返回记录：{}", JSON.toJSONString(resultPage.getRecords()));
        return RestResponse.success(resultPage);
    }
} 