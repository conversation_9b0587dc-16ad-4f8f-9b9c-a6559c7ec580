package com.dbj.classpal.books.service.mapper.ebooks;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dbj.classpal.books.service.entity.ebooks.AppEBook;
import com.dbj.classpal.books.service.entity.ebooks.AppEBookshelfBookRef;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 书架-单书关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Mapper
public interface AppEBookshelfBookRefMapper extends BaseMapper<AppEBookshelfBookRef> {

    /**
     * 根据书架ID查询关联的单书列表
     *
     * @param shelfId 书架ID
     * @return 单书列表
     */
    List<AppEBook> getBooksByShelfId(@Param("shelfId") Long shelfId);
    
    /**
     * 批量插入书架-单书关联
     *
     * @param refs 关联记录列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<AppEBookshelfBookRef> refs);
} 