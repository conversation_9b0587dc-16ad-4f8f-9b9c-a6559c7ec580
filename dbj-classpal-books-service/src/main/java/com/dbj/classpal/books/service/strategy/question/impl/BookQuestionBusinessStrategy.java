package com.dbj.classpal.books.service.strategy.question.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dbj.classpal.books.common.bo.paper.QueryPaperQuestionsBO;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.books.common.dto.paper.PaperQuestionDTO;
import com.dbj.classpal.books.common.enums.BusinessTypeEnum;
import com.dbj.classpal.books.service.biz.question.IQuestionCategoryBusinessRefBiz;
import com.dbj.classpal.books.service.biz.question.QuestionBiz;
import com.dbj.classpal.books.service.entity.books.BooksRankInCodesContentsQuestion;
import com.dbj.classpal.books.service.entity.question.Question;
import com.dbj.classpal.books.service.entity.question.QuestionCategoryBusinessRef;
import com.dbj.classpal.books.service.mapper.books.BooksRankInCodesContentsQuestionMapper;
import com.dbj.classpal.books.service.strategy.question.AbstractQuestionBusinessStrategy;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_QUESTION_SETTING_NOT_EXIST_CODE;
import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_QUESTION_SETTING_NOT_EXIST_MSG;

/**
 * 图书题目业务策略实现
 */
@Slf4j
@Component
public class BookQuestionBusinessStrategy extends AbstractQuestionBusinessStrategy  {

    @Resource
    private QuestionBiz questionBiz;

    @Resource
    private BooksRankInCodesContentsQuestionMapper questionSettingMapper;

    @Resource
    private IQuestionCategoryBusinessRefBiz categoryBusinessRefBiz;


    @Override
    public Integer getBusinessType() {
        return BusinessTypeEnum.QUESTION_BUSINESS.getCode();
    }


    @Override
    public List<PaperQuestionDTO> getPaperQuestions(QueryPaperQuestionsBO queryBO) throws BusinessException {
        super.validateQueryParams(queryBO);
        BooksRankInCodesContentsQuestion questionSetting = questionSettingMapper.selectOne(new LambdaQueryWrapper<BooksRankInCodesContentsQuestion>().eq(BooksRankInCodesContentsQuestion::getInCodesContentsId,queryBO.getBusinessId()));
        if (questionSetting == null) {
            throw new BusinessException(AppErrorCode.APP_QUESTION_SETTING_NOT_EXIST_CODE, AppErrorCode.APP_QUESTION_SETTING_NOT_EXIST_MSG);
        }
        List<Question> selectedQuestions = queryQuestionCategoryBusinessRef(questionSetting.getId(),getBusinessType(), questionSetting.getQuestionNum(), questionSetting.getQuestionMethod());
        Map<Integer, Integer> questionOrderMap = new LinkedHashMap<>();
        int order = 1;
        for (Question question : selectedQuestions) {
            questionOrderMap.put(question.getId(), order++);
        }
        return buildPaperQuestionDTOs(selectedQuestions, questionOrderMap);
    }


    @Override
    protected List<Question> getQuestionsByBusinessId(Integer businessId, Integer businessType) throws BusinessException {
        BooksRankInCodesContentsQuestion questionSetting = questionSettingMapper.selectOne(new LambdaQueryWrapper<BooksRankInCodesContentsQuestion>().eq(BooksRankInCodesContentsQuestion::getInCodesContentsId,businessId));
        if (questionSetting == null) {
            throw new BusinessException(APP_QUESTION_SETTING_NOT_EXIST_CODE,APP_QUESTION_SETTING_NOT_EXIST_MSG);
        }
        List<QuestionCategoryBusinessRef> categoryRefs = categoryBusinessRefBiz.getRefsByBusinessId(
                questionSetting.getId(),
                businessType
        );
        if (CollectionUtils.isEmpty(categoryRefs)) {
            return Collections.emptyList();
        }

        List<Integer> categoryIds = categoryRefs.stream()
                .map(QuestionCategoryBusinessRef::getQuestionCategoryId)
                .toList();

        List<Question> allQuestions = new ArrayList<>();
        for (Integer categoryId : categoryIds) {
            List<Question> questions = questionBiz.getQuestionsByCategory(categoryId, null);
            if (!CollectionUtils.isEmpty(questions)) {
                allQuestions.addAll(questions);
            }
        }
        
        return allQuestions;
    }

}