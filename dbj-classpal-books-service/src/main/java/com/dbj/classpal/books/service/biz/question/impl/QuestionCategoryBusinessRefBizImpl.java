package com.dbj.classpal.books.service.biz.question.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.bo.question.QuestionCategoryBusinessRefBO;
import com.dbj.classpal.books.service.biz.question.IQuestionCategoryBusinessRefBiz;
import com.dbj.classpal.books.service.entity.question.Question;
import com.dbj.classpal.books.service.entity.question.QuestionCategory;
import com.dbj.classpal.books.service.entity.question.QuestionCategoryBusinessRef;
import com.dbj.classpal.books.service.mapper.question.QuestionCategoryBusinessRefMapper;
import com.dbj.classpal.books.service.mapper.question.QuestionCategoryMapper;
import com.dbj.classpal.books.service.mapper.question.QuestionMapper;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * 题库业务关联实现类
 */
@Slf4j
@Service
public class QuestionCategoryBusinessRefBizImpl extends ServiceImpl<QuestionCategoryBusinessRefMapper, QuestionCategoryBusinessRef> 
    implements IQuestionCategoryBusinessRefBiz {


    @Override
    public boolean hasBusinessRefs(List<Integer> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return false;
        }

        Long count = this.baseMapper.selectCount(
                new LambdaQueryWrapper<QuestionCategoryBusinessRef>()
                        .in(QuestionCategoryBusinessRef::getQuestionCategoryId, categoryIds)
        );
        return count != null && count > 0;
    }


    @Override
    public List<QuestionCategoryBusinessRef> getRefsByBusinessId(Integer businessId, Integer businessType) throws BusinessException {
        if (businessId == null) {
            throw new BusinessException(APP_BUSINESS_ID_NOT_NULL_CODE,APP_BUSINESS_ID_NOT_NULL_MSG);
        }
        if (businessType == null) {
            throw new BusinessException(APP_BUSINESS_TYPE_NOT_NULL_CODE,APP_BUSINESS_TYPE_NOT_NULL_MSG);
        }
        List<QuestionCategoryBusinessRef> refs = this.baseMapper.selectList(
                new LambdaQueryWrapper<QuestionCategoryBusinessRef>()
                        .eq(QuestionCategoryBusinessRef::getBusinessId, businessId)
                        .eq(QuestionCategoryBusinessRef::getBusinessType, businessType)
                        .orderByAsc(QuestionCategoryBusinessRef::getSortNum,QuestionCategoryBusinessRef::getId)
        );

        return CollectionUtils.isEmpty(refs) ? Collections.emptyList() : refs;
    }

    @Override
    public List<QuestionCategoryBusinessRef> getBusinessRefs(List<Integer> categoryIds) {
        return this.baseMapper.selectList(
                new LambdaQueryWrapper<QuestionCategoryBusinessRef>()
                        .in(QuestionCategoryBusinessRef::getQuestionCategoryId, categoryIds));
    }
}
