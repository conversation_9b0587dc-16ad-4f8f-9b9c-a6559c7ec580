package com.dbj.classpal.books.service.biz.question.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dbj.classpal.books.common.bo.question.QuestionCategoryBusinessRefBO;
import com.dbj.classpal.books.service.biz.question.IQuestionCategoryBusinessRefBiz;
import com.dbj.classpal.books.service.entity.question.Question;
import com.dbj.classpal.books.service.entity.question.QuestionCategory;
import com.dbj.classpal.books.service.entity.question.QuestionCategoryBusinessRef;
import com.dbj.classpal.books.service.mapper.question.QuestionCategoryBusinessRefMapper;
import com.dbj.classpal.books.service.mapper.question.QuestionCategoryMapper;
import com.dbj.classpal.books.service.mapper.question.QuestionMapper;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;

/**
 * 题库业务关联实现类
 */
@Slf4j
@Service
public class QuestionCategoryBusinessRefBizImpl extends ServiceImpl<QuestionCategoryBusinessRefMapper, QuestionCategoryBusinessRef> 
    implements IQuestionCategoryBusinessRefBiz {

    @Resource
    private QuestionCategoryMapper questionCategoryMapper;

    @Resource
    private QuestionMapper questionMapper;
    @Override
    public boolean hasBusinessRefs(List<Integer> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return false;
        }

        Long count = this.baseMapper.selectCount(
                new LambdaQueryWrapper<QuestionCategoryBusinessRef>()
                        .in(QuestionCategoryBusinessRef::getQuestionCategoryId, categoryIds)
        );
        return count != null && count > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Integer> createBusinessRefs(QuestionCategoryBusinessRefBO refBO) throws BusinessException {
        if (refBO == null) {
            throw new BusinessException(APP_BUSINESS_RELATION_PARAM_NOT_NULL_CODE,APP_BUSINESS_RELATION_PARAM_NOT_NULL_MSG);
        }
        if (refBO.getBusinessId() == null) {
            throw new BusinessException(APP_BUSINESS_ID_NOT_NULL_CODE,APP_BUSINESS_ID_NOT_NULL_MSG);
        }
        if (refBO.getBusinessType() == null) {
            throw new BusinessException(APP_BUSINESS_TYPE_NOT_NULL_CODE,APP_BUSINESS_TYPE_NOT_NULL_MSG);
        }
        if (CollectionUtils.isEmpty(refBO.getCategoryIds())) {
            throw new BusinessException(APP_QUESTION_BANK_ID_LIST_NOT_NULL_CODE,APP_QUESTION_BANK_ID_LIST_NOT_NULL_MSG);
        }

        try {
            List<QuestionCategory> categories = questionCategoryMapper.selectByIds(refBO.getCategoryIds());
            if (categories.size() != refBO.getCategoryIds().size()) {
                Set<Integer> existingIds = categories.stream()
                    .map(QuestionCategory::getId)
                    .collect(Collectors.toSet());
                List<Integer> notExistIds = refBO.getCategoryIds().stream()
                    .filter(id -> !existingIds.contains(id))
                    .toList();
                throw new BusinessException(APP_QUESTION_CATEGORY_NOT_EXIST_CODE,APP_QUESTION_CATEGORY_NOT_EXIST_MSG +":"+ notExistIds);
            }

            for (QuestionCategory category : categories) {
                Long questionCount = questionMapper.selectCount(
                    new LambdaQueryWrapper<Question>()
                        .eq(Question::getQuestionCategoryId, category.getId())
                );
                if (questionCount == null || questionCount == 0) {
                    throw new BusinessException(APP_QUESTION_BANK_CATEGORY_NOT_EXIST_CODE,String.format(APP_QUESTION_BANK_CATEGORY_NOT_EXIST_MSG, category.getName()));
                }
            }

            List<QuestionCategoryBusinessRef> existingRefs = this.baseMapper.selectList(
                new LambdaQueryWrapper<QuestionCategoryBusinessRef>()
                    .eq(QuestionCategoryBusinessRef::getBusinessId, refBO.getBusinessId())
                    .eq(QuestionCategoryBusinessRef::getBusinessType, refBO.getBusinessType())
                    .in(QuestionCategoryBusinessRef::getQuestionCategoryId, refBO.getCategoryIds())
            );

            if (!CollectionUtils.isEmpty(existingRefs)) {
                Set<Integer> existingCategoryIds = existingRefs.stream()
                    .map(QuestionCategoryBusinessRef::getQuestionCategoryId)
                    .collect(Collectors.toSet());
                throw new BusinessException(APP_QUESTION_BANK_HAS_RELATION_CODE,APP_QUESTION_BANK_HAS_RELATION_MSG+"：" + existingCategoryIds);
            }

            List<QuestionCategoryBusinessRef> refs = refBO.getCategoryIds().stream()
                .map(categoryId -> {
                    QuestionCategoryBusinessRef ref = new QuestionCategoryBusinessRef();
                    ref.setBusinessId(refBO.getBusinessId());
                    ref.setBusinessType(refBO.getBusinessType());
                    ref.setQuestionCategoryId(categoryId);
                    return ref;
                })
                .collect(Collectors.toList());

            boolean success = this.saveBatch(refs);
            if (!success) {
                throw new BusinessException(APP_CREATE_BUSINESS_RELATION_FAIL_CODE,APP_CREATE_BUSINESS_RELATION_FAIL_MSG);
            }

            return refs.stream()
                .map(QuestionCategoryBusinessRef::getId)
                .collect(Collectors.toList());

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建业务关联失败", e);
            throw new BusinessException(APP_CREATE_BUSINESS_RELATION_FAIL_CODE,APP_CREATE_BUSINESS_RELATION_FAIL_MSG);
        }
    }

    @Override
    public List<QuestionCategoryBusinessRef> getRefsByBusinessId(Integer businessId, Integer businessType) throws BusinessException {
        if (businessId == null) {
            throw new BusinessException(APP_BUSINESS_ID_NOT_NULL_CODE,APP_BUSINESS_ID_NOT_NULL_MSG);
        }
        if (businessType == null) {
            throw new BusinessException(APP_BUSINESS_TYPE_NOT_NULL_CODE,APP_BUSINESS_TYPE_NOT_NULL_MSG);
        }
        List<QuestionCategoryBusinessRef> refs = this.baseMapper.selectList(
                new LambdaQueryWrapper<QuestionCategoryBusinessRef>()
                        .eq(QuestionCategoryBusinessRef::getBusinessId, businessId)
                        .eq(QuestionCategoryBusinessRef::getBusinessType, businessType)
                        .orderByAsc(QuestionCategoryBusinessRef::getSortNum,QuestionCategoryBusinessRef::getId)
        );

        return CollectionUtils.isEmpty(refs) ? Collections.emptyList() : refs;
    }

    @Override
    public List<QuestionCategoryBusinessRef> getBusinessRefs(List<Integer> categoryIds) {
        return this.baseMapper.selectList(
                new LambdaQueryWrapper<QuestionCategoryBusinessRef>()
                        .in(QuestionCategoryBusinessRef::getQuestionCategoryId, categoryIds));
    }
}
