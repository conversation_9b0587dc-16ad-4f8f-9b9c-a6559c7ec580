package com.dbj.classpal.books.service.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dbj.classpal.books.common.constant.RedisKeyConstants;
import com.dbj.classpal.books.service.biz.config.BasicConfigBiz;
import com.dbj.classpal.books.service.biz.ebooks.IAppEbooksConfigCategoryBiz;
import com.dbj.classpal.books.service.entity.config.BasicConfig;
import com.dbj.classpal.books.service.entity.ebooks.AppEbooksConfigCategory;
import com.dbj.classpal.framework.redisson.config.RedissonRedisUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.dbj.classpal.books.common.constant.Constant.DEFAULT_CACHE_EXPIRE;

/**
 * 统一配置缓存管理器
 * 管理 basic_config 和 app_ebooks_config_category 的统一缓存
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Slf4j
@Component
public class ConfigCacheManager {

    @Resource
    private BasicConfigBiz basicConfigBiz;

    @Resource
    private IAppEbooksConfigCategoryBiz appEbooksConfigCategoryBiz;

    @Resource
    private RedissonRedisUtils redisUtils;

    /**
     * 统一缓存key - 存储所有配置的ID到name映射
     * 使用与 AppEBookHelper 相同的缓存key 保持兼容性
     */
    private static final String UNIFIED_CONFIG_CACHE_KEY = RedisKeyConstants.DBJ_CLASSPAL_BASIC_CONFIG_ALL_CACHE_KEY;

    /**
     * 获取所有配置名称的映射（包含 basic_config 和 app_ebooks_config_category）
     * 优先从缓存获取，缓存未命中时查询数据库并缓存
     *
     * @return 配置ID到名称的完整映射
     */
    public Map<Integer, String> getAllConfigNamesMap() {
        try {
            // 尝试从缓存获取
            if (redisUtils.hasKey(UNIFIED_CONFIG_CACHE_KEY)) {
                String cachedData = redisUtils.getValue(UNIFIED_CONFIG_CACHE_KEY);
                if (cachedData != null) {
                    Map<Integer, String> configMap = JSON.parseObject(cachedData, new TypeReference<Map<Integer, String>>() {});
                    log.debug("从缓存获取统一配置映射，数量: {}", configMap.size());
                    return configMap;
                }
            }

            // 缓存未命中，重新构建缓存
            return rebuildCache();

        } catch (Exception e) {
            log.error("获取统一配置映射异常", e);
            return new HashMap<>();
        }
    }

    /**
     * 获取配置映射（不使用锁，内部方法）
     * 用于增量更新时获取当前缓存状态
     *
     * @return 配置ID到名称的映射
     */
    private Map<Integer, String> getAllConfigNamesMapWithoutLock() {
        try {
            if (redisUtils.hasKey(UNIFIED_CONFIG_CACHE_KEY)) {
                String cachedData = redisUtils.getValue(UNIFIED_CONFIG_CACHE_KEY);
                if (cachedData != null) {
                    return JSON.parseObject(cachedData, new TypeReference<Map<Integer, String>>() {});
                }
            }
            // 缓存不存在时返回空Map，由调用方决定是否重建
            return new HashMap<>();
        } catch (Exception e) {
            log.error("获取缓存配置映射失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 重新构建统一配置缓存
     * 查询两个表的数据并合并到一个Map中
     *
     * @return 配置ID到名称的完整映射
     */
    public Map<Integer, String> rebuildCache() {
        try {
            log.info("开始重建统一配置缓存...");

            Map<Integer, String> configMap = new HashMap<>();

            // 查询基础配置数据
            List<BasicConfig> basicConfigs = basicConfigBiz.list();
            int basicConfigCount = 0;
            for (BasicConfig config : basicConfigs) {
                if (config.getName() != null && !config.getName().isEmpty()) {
                    configMap.put(config.getId(), config.getName());
                    basicConfigCount++;
                }
            }

            // 查询分类配置数据
            List<AppEbooksConfigCategory> categories = appEbooksConfigCategoryBiz.list();
            int categoryCount = 0;
            for (AppEbooksConfigCategory category : categories) {
                if (category.getName() != null && !category.getName().isEmpty()) {
                    configMap.put(category.getId(), category.getName());
                    categoryCount++;
                }
            }

            // 存入缓存
            redisUtils.setValue(UNIFIED_CONFIG_CACHE_KEY, JSON.toJSONString(configMap), DEFAULT_CACHE_EXPIRE, TimeUnit.DAYS);

            log.info("统一配置缓存重建完成 - basic_config: {}, category: {}, 总计: {}", 
                    basicConfigCount, categoryCount, configMap.size());

            return configMap;

        } catch (Exception e) {
            log.error("重建统一配置缓存失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 清除统一配置缓存
     * 在任何配置数据发生变更时调用
     */
    public void clearCache() {
        try {
            redisUtils.delKey(UNIFIED_CONFIG_CACHE_KEY);
            log.info("统一配置缓存已清除");
        } catch (Exception e) {
            log.error("清除统一配置缓存异常", e);
        }
    }

    /**
     * 清除缓存并重新构建
     * 适用于需要立即更新缓存的场景
     *
     * @return 新的配置映射
     */
    public Map<Integer, String> refreshCache() {
        clearCache();
        return rebuildCache();
    }

    /**
     * 增量更新基础配置到缓存
     * 在 BasicConfigServiceImpl.list() 查询时调用
     * 使用简单的重试机制避免并发覆盖问题
     *
     * @param basicConfigs 基础配置列表
     */
    public void updateBasicConfigsToCache(List<BasicConfig> basicConfigs) {
        if (CollectionUtils.isEmpty(basicConfigs)) {
            return;
        }

        try {
            // 获取当前缓存
            Map<Integer, String> currentCache = getAllConfigNamesMapWithoutLock();

            // 增量更新基础配置数据
            int updateCount = 0;
            boolean hasUpdate = false;
            for (BasicConfig config : basicConfigs) {
                if (config.getName() != null && !config.getName().isEmpty()) {
                    String existingName = currentCache.get(config.getId());
                    if (!config.getName().equals(existingName)) {
                        //todo 不同表的ID 会重复，
                        currentCache.put(config.getId(), config.getName());
                        updateCount++;
                        hasUpdate = true;
                    }
                }
            }

            // 只有在有实际更新时才写入缓存
            if (hasUpdate) {
                redisUtils.setValue(UNIFIED_CONFIG_CACHE_KEY, JSON.toJSONString(currentCache), DEFAULT_CACHE_EXPIRE, TimeUnit.DAYS);
                log.debug("增量更新基础配置到缓存，更新数量: {}, 缓存总数: {}", updateCount, currentCache.size());
            }

        } catch (Exception e) {
            log.error("增量更新基础配置到缓存失败", e);
        }
    }

    /**
     * 增量更新分类配置到缓存
     * 在 AppEbooksConfigCategoryServiceImpl.getAllCategory() 查询时调用
     * 使用简单的重试机制避免并发覆盖问题
     *
     * @param categories 分类配置列表
     */
    public void updateCategoriesToCache(List<AppEbooksConfigCategory> categories) {
        if (CollectionUtils.isEmpty(categories)) {
            return;
        }

        try {
            // 获取当前缓存
            Map<Integer, String> currentCache = getAllConfigNamesMapWithoutLock();

            // 增量更新分类配置数据
            int updateCount = 0;
            boolean hasUpdate = false;
            for (AppEbooksConfigCategory category : categories) {
                if (category.getName() != null && !category.getName().isEmpty()) {
                    String existingName = currentCache.get(category.getId());
                    if (!category.getName().equals(existingName)) {
                        currentCache.put(category.getId(), category.getName());
                        updateCount++;
                        hasUpdate = true;
                    }
                }
            }

            // 只有在有实际更新时才写入缓存
            if (hasUpdate) {
                redisUtils.setValue(UNIFIED_CONFIG_CACHE_KEY, JSON.toJSONString(currentCache), DEFAULT_CACHE_EXPIRE, TimeUnit.DAYS);
                log.debug("增量更新分类配置到缓存，更新数量: {}, 缓存总数: {}", updateCount, currentCache.size());
            }

        } catch (Exception e) {
            log.error("增量更新分类配置到缓存失败", e);
        }
    }

    /**
     * 获取单个配置名称
     * 优先从统一缓存中获取
     *
     * @param configId 配置ID
     * @return 配置名称，不存在时返回空字符串
     */
    public String getConfigName(Integer configId) {
        if (configId == null) {
            return "";
        }

        Map<Integer, String> allConfigMap = getAllConfigNamesMap();
        return allConfigMap.getOrDefault(configId, "");
    }

    /**
     * 批量获取配置名称
     *
     * @param configIds 配置ID集合
     * @return 配置ID到名称的映射
     */
    public Map<Integer, String> batchGetConfigNames(List<Integer> configIds) {
        if (configIds == null || configIds.isEmpty()) {
            return new HashMap<>();
        }

        Map<Integer, String> allConfigMap = getAllConfigNamesMap();
        Map<Integer, String> result = new HashMap<>();

        for (Integer configId : configIds) {
            String name = allConfigMap.get(configId);
            if (name != null && !name.isEmpty()) {
                result.put(configId, name);
            }
        }

        return result;
    }
}
