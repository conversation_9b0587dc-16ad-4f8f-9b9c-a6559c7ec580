package com.dbj.classpal.books.service.api.client.poem;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.poem.AncientPoemApi;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemCopyBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemMoveBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemPageBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemUpsertBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemDTO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemRelateDTO;
import com.dbj.classpal.books.service.service.poem.IAncientPoemService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 古诗文 远程Api 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@RestController
public class AncientPoemApiImpl implements AncientPoemApi {

    @Resource
    private IAncientPoemService ancientPoemService;

    @Override
    public RestResponse<Page<AncientPoemDTO>> getAncientPoemPage(PageInfo<AncientPoemPageBO> pageInfo) {
        return RestResponse.success(ancientPoemService.getAncientPoemPage(pageInfo));
    }

    @Override
    public RestResponse<AncientPoemDTO> getAncientPoemInfo(CommonIdApiBO bo) {
        return RestResponse.success(ancientPoemService.getAncientPoemInfo(bo));
    }

    @Override
    public RestResponse<Boolean> saveAncientPoem(AncientPoemUpsertBO bo) throws BusinessException {
        return RestResponse.success(ancientPoemService.saveAncientPoem(bo));
    }

    @Override
    public RestResponse<Boolean> updateAncientPoem(AncientPoemUpsertBO bo) throws BusinessException {
        return RestResponse.success(ancientPoemService.updateAncientPoem(bo));
    }

    @Override
    public RestResponse<Boolean> deleteAncientPoem(CommonIdsApiBO bo) throws BusinessException {
        return RestResponse.success(ancientPoemService.deleteAncientPoem(bo));
    }

    @Override
    public RestResponse<Boolean> copyAncientPoem(AncientPoemCopyBO bo) throws BusinessException {
        return RestResponse.success(ancientPoemService.copyAncientPoem(bo));
    }

    @Override
    public RestResponse<Boolean> moveAncientPoem(AncientPoemMoveBO bo) {
        return RestResponse.success(ancientPoemService.moveAncientPoem(bo));
    }

    @Override
    public RestResponse<List<AncientPoemRelateDTO>> getAncientPoemRelate(CommonIdApiBO bo) throws BusinessException {
        return RestResponse.success(ancientPoemService.getAncientPoemRelate(bo));
    }
}
