package com.dbj.classpal.books.service.mapper.poem;

import com.dbj.classpal.books.service.entity.poem.AncientPoemReciteStandard;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * <p>
 * 古诗背诵评分标准表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
public interface AncientPoemReciteStandardMapper extends BaseMapper<AncientPoemReciteStandard> {



    AncientPoemReciteStandard getByScore(@Param( "score") BigDecimal score,@Param( "type")Integer type);
}
