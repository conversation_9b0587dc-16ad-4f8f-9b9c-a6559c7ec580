package com.dbj.classpal.books.service.mapper.poem;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.bo.poem.AncientPoemPageBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemDTO;
import com.dbj.classpal.books.service.entity.poem.AncientPoem;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 古诗文表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
public interface AncientPoemMapper extends BaseMapper<AncientPoem> {

    /**
     * 古诗文分页列表
     *
     * @param page 分页参数
     * @param bo   查询参数
     * @return Page<AncientPoemDTO>
     */
    Page<AncientPoemDTO> getAncientPoemPage(Page<Object> page, @Param("bo") AncientPoemPageBO bo);
}
