package com.dbj.classpal.books.service.strategy.basicConfig.common.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dbj.classpal.books.service.biz.ebooks.IAppEBookBiz;
import com.dbj.classpal.books.service.entity.ebooks.AppEBook;
import com.dbj.classpal.books.service.strategy.basicConfig.handler.IBasicConfigCommonBusinessStrategyHandler;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_EBOOKS_CONFIG_SUBJECT_EXIST_REF_FAILED_CODE;
import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_EBOOKS_CONFIG_SUBJECT_EXIST_REF_FAILED_MSG;

@Service("basicConfigEbookSubjectRefBizStrategy")
public class BasicConfigEbookSubjectRefBizStrategy extends IBasicConfigCommonBusinessStrategyHandler {

    @Resource
    private IAppEBookBiz appEBookBiz;

    @Override
    public Map<Integer, Long> getRefMap(List<Integer> ids) {
        Map<Integer, Long> refMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(ids)) {
            List<AppEBook> subject = appEBookBiz.getBaseMapper().selectList(new LambdaQueryWrapper<AppEBook>().in(AppEBook::getSubjectId, ids));
            Map<Integer, Long> subjectIdToCountMap = subject.stream()
                    .collect(Collectors.groupingBy(
                            AppEBook::getSubjectId,
                            Collectors.counting()
                    ));
            refMap = ids.stream()
                    .collect(Collectors.toMap(
                            id -> id,
                            id -> subjectIdToCountMap.getOrDefault(id, 0L)
                    ));
        }
        return refMap;
    }

    @Override
    public void throwException(String type) throws BusinessException {
        throw new BusinessException(APP_EBOOKS_CONFIG_SUBJECT_EXIST_REF_FAILED_CODE,APP_EBOOKS_CONFIG_SUBJECT_EXIST_REF_FAILED_MSG);
    }
}
