package com.dbj.classpal.books.service.api.client.evaluation;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dbj.classpal.books.client.api.evaluation.AdminEvaluationApi;
import com.dbj.classpal.books.client.bo.common.CommonIdApiBO;
import com.dbj.classpal.books.client.bo.common.CommonIdsApiBO;
import com.dbj.classpal.books.client.bo.evaluation.*;
import com.dbj.classpal.books.client.dto.evaluation.AdminEvaluationDetailQueryApiDTO;
import com.dbj.classpal.books.client.dto.evaluation.AdminEvaluationQueryApiDTO;
import com.dbj.classpal.books.common.bo.common.CommonIdBO;
import com.dbj.classpal.books.common.bo.evaluation.AdminEvaluationQueryBO;
import com.dbj.classpal.books.common.dto.evaluation.AdminEvaluationDetailQueryDTO;
import com.dbj.classpal.books.common.dto.evaluation.AdminEvaluationQueryDTO;
import com.dbj.classpal.books.service.biz.evaluation.IAppEvaluationBiz;
import com.dbj.classpal.books.service.biz.evaluation.IAppUserPaperEvaluationBiz;
import com.dbj.classpal.books.service.entity.evaluation.AppEvaluation;
import com.dbj.classpal.books.service.entity.evaluation.AppUserPaperEvaluation;
import com.dbj.classpal.books.service.service.evaluation.IAppEvaluationService;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.request.PageInfo;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_EVALUATION_DELETE_FAIL_REF_CODE;
import static com.dbj.classpal.books.common.constant.AppErrorCode.APP_EVALUATION_DELETE_FAIL_REF_MSG;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialApiImpl
 * Date:     2025-04-10 11:53:30
 * Description: 表名： ,描述： 表
 */
@RestController
public class AdminEvaluationApiImpl implements AdminEvaluationApi {
    @Resource
    private IAppEvaluationService evaluationService;
    @Resource
    private IAppEvaluationBiz evaluationBiz;
    @Resource
    private IAppUserPaperEvaluationBiz appUserPaperEvaluationBiz;

    @Override
    public RestResponse<Page<AdminEvaluationQueryApiDTO>> pageInfo(PageInfo<AdminEvaluationQueryApiBO> pageRequest) {
        // 1. 转换查询条件
        AdminEvaluationQueryBO queryBO = new AdminEvaluationQueryBO();
        BeanUtil.copyProperties(pageRequest.getData(), queryBO);

        PageInfo<AdminEvaluationQueryBO> servicePageRequest = new PageInfo<>();
        servicePageRequest.setPageNum(pageRequest.getPageNum());
        servicePageRequest.setPageSize(pageRequest.getPageSize());
        servicePageRequest.setData(queryBO);

        // 2. 调用Service
        Page<AdminEvaluationQueryDTO> page = evaluationService.pageInfo(servicePageRequest);
        return RestResponse.success((Page<AdminEvaluationQueryApiDTO>) page.convert(vo -> {
            AdminEvaluationQueryApiDTO dto = new AdminEvaluationQueryApiDTO();
            BeanUtil.copyProperties(vo, dto);
            return dto;
        }));
    }

    @Override
    public RestResponse<List<AdminEvaluationQueryApiDTO>> list() throws BusinessException {
        return  RestResponse.success(evaluationBiz.list().stream().map(d -> {
            AdminEvaluationQueryApiDTO adminEvaluationQueryApiDTO = new AdminEvaluationQueryApiDTO();
            BeanUtil.copyProperties(d, adminEvaluationQueryApiDTO);
            return adminEvaluationQueryApiDTO;
        }).collect(Collectors.toList()));
    }

    @Override
    public RestResponse<Boolean> saveAppEvaluation(AdminEvaluationSaveApiBO bo) {
        AppEvaluation evaluation = new AppEvaluation();
        BeanUtil.copyProperties(bo, evaluation);
        return RestResponse.success(evaluationBiz.save(evaluation));
    }

    @Override
    public RestResponse<Boolean> deleteAppEvaluation(CommonIdsApiBO bo) throws BusinessException {
        int refCount = appUserPaperEvaluationBiz.lambdaQuery().in(AppUserPaperEvaluation::getAppEvaluationId, bo.getIds()).count().intValue();
        if (refCount > 0) {
            throw new BusinessException(APP_EVALUATION_DELETE_FAIL_REF_CODE,APP_EVALUATION_DELETE_FAIL_REF_MSG);
        }
        return  RestResponse.success(evaluationBiz.removeBatchByIds(bo.getIds()));
    }

    @Override
    public RestResponse<AdminEvaluationDetailQueryApiDTO> getDetail(CommonIdApiBO bo) {
        CommonIdBO commonIdBO = new CommonIdBO();
        BeanUtil.copyProperties(bo, commonIdBO);
        AdminEvaluationDetailQueryDTO detail = evaluationService.getDetail(commonIdBO);
        AdminEvaluationDetailQueryApiDTO detailQueryApiDTO = new AdminEvaluationDetailQueryApiDTO();
        BeanUtil.copyProperties(detail, detailQueryApiDTO);
        return RestResponse.success(detailQueryApiDTO);
    }

    @Override
    public RestResponse<Boolean> reName(AdminEvaluationReNameApiBO bo) {
        return RestResponse.success(evaluationBiz.lambdaUpdate().eq(AppEvaluation::getId,bo.getId()).set(AppEvaluation::getEvaluationName,bo.getEvaluationName()).update());
    }

    @Override
    public RestResponse<Boolean> reCover(AdminEvaluationReCoverApiBO bo) {
        return RestResponse.success(evaluationBiz.lambdaUpdate().eq(AppEvaluation::getId,bo.getId()).set(AppEvaluation::getEvaluationCover,bo.getEvaluationCover()).update());
    }

    @Override
    public RestResponse<Boolean> reRemark(AdminEvaluationReRemarkApiBO bo) {
        return RestResponse.success(evaluationBiz.lambdaUpdate().eq(AppEvaluation::getId,bo.getId()).set(AppEvaluation::getEvaluationRemark,bo.getEvaluationRemark()).update());
    }

    @Override
    public RestResponse<Boolean> updateStatus(AdminEvaluationUpdateStatusApiBO bo) {
        return RestResponse.success(evaluationBiz.lambdaUpdate().in(AppEvaluation::getId,bo.getIds()).set(AppEvaluation::getEvaluationStatus,bo.getStatus()).update());
    }

    @Override
    public RestResponse<Boolean> updateVisible(AdminEvaluationUpdateVisibleApiBO bo) {
        return RestResponse.success(evaluationBiz.lambdaUpdate().in(AppEvaluation::getId,bo.getIds()).set(AppEvaluation::getEvaluationVisible,bo.getVisible()).update());
    }

    @Override
    public RestResponse<Boolean> updateOpen(AdminEvaluationUpdateOpenApiBO bo) throws BusinessException {
        return RestResponse.success(evaluationService.updateOpen(bo));
    }

}
