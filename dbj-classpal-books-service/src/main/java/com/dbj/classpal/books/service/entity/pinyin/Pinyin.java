package com.dbj.classpal.books.service.entity.pinyin;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 拼音信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pinyin")
@Tag(name="Pinyin对象", description="拼音信息表")
public class Pinyin extends BizEntity implements Serializable {



    @Schema(description = "ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "拼音分类id")
    private Integer classifyId;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "启用状态")
    private Integer status;

    @Schema(description = "排序权重")
    private Integer sort;
}
