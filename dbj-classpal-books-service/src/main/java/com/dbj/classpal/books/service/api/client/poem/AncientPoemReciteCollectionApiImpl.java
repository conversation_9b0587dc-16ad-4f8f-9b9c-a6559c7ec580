package com.dbj.classpal.books.service.api.client.poem;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.books.client.api.poem.AncientPoemReciteCollectionApi;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionSaveBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionSortBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionUpdateBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionUpdateCoverUrlBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionUpdateDescBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionUpdateStatusBO;
import com.dbj.classpal.books.client.bo.poem.AncientPoemReciteCollectionUpdateTitleBO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemReciteCollectionDetailDTO;
import com.dbj.classpal.books.client.dto.poem.AncientPoemReciteCollectionDTO;
import com.dbj.classpal.books.common.dto.poem.AncientPoemBusinessRefCountDTO;
import com.dbj.classpal.books.common.enums.poem.PoemBusinessTypeEnum;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemBusinessRefBiz;
import com.dbj.classpal.books.service.biz.poem.IAncientPoemReciteCollectionBiz;
import com.dbj.classpal.books.service.entity.poem.AncientPoemBusinessRef;
import com.dbj.classpal.books.service.entity.poem.AncientPoemReciteCollection;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.dbj.classpal.books.common.constant.AppErrorCode.ANCIENT_POEM_RECITE_COLLECTION_EXIST_POEM_CODE;
import static com.dbj.classpal.books.common.constant.AppErrorCode.ANCIENT_POEM_RECITE_COLLECTION_EXIST_POEM_MSG;
import static com.dbj.classpal.books.common.constant.AppErrorCode.ANCIENT_POEM_RECITE_COLLECTION_NOT_EXIST_CODE;
import static com.dbj.classpal.books.common.constant.AppErrorCode.ANCIENT_POEM_RECITE_COLLECTION_NOT_EXIST_MSG;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className AncientPoemReciteCollectionApiImpl
 * @description
 * @date 2025-05-26 14:42
 **/
@RestController
public class AncientPoemReciteCollectionApiImpl implements AncientPoemReciteCollectionApi {

    @Resource
    private IAncientPoemReciteCollectionBiz ancientPoemReciteCollectionBiz;
    @Resource
    private IAncientPoemBusinessRefBiz ancientBusinessRefBiz;
    @Override
    public RestResponse<List<AncientPoemReciteCollectionDTO>> listAncientPoemReciteCollection(AncientPoemReciteCollectionBO ancientPoemReciteCollectionBO) {
        List<AncientPoemReciteCollectionDTO> ancientPoemReciteCollectionDTOList = ancientPoemReciteCollectionBiz.listAncientPoemReciteCollection(ancientPoemReciteCollectionBO);
        if(CollectionUtils.isNotEmpty(ancientPoemReciteCollectionDTOList)){
            List<Integer> ids = ancientPoemReciteCollectionDTOList.stream().map(AncientPoemReciteCollectionDTO::getId).collect(Collectors.toList());
            List<AncientPoemBusinessRefCountDTO> ancientPoemBusinessRefCountDTOList = ancientBusinessRefBiz.getCount(ids,PoemBusinessTypeEnum.ANCIENT_POEM_RECITE_BUSINESS.getCode());
            if(CollectionUtils.isNotEmpty(ancientPoemBusinessRefCountDTOList)){
                Map<Integer,Integer> poemNumMap = ancientPoemBusinessRefCountDTOList.stream().collect(Collectors.toMap(AncientPoemBusinessRefCountDTO::getBusinessId,AncientPoemBusinessRefCountDTO::getPoemNum));
                ancientPoemReciteCollectionDTOList.forEach(
                        ancientPoemReciteCollectionPageDTO -> ancientPoemReciteCollectionPageDTO.setPoemNum(poemNumMap.getOrDefault(ancientPoemReciteCollectionPageDTO.getId(),0))
                );

            }
        }
        return RestResponse.success(ancientPoemReciteCollectionDTOList);
    }

    @Override
    public RestResponse<AncientPoemReciteCollectionDetailDTO> getAncientPoemReciteCollection(Integer id) throws BusinessException {
        AncientPoemReciteCollection abnormalPoemReciteCollection = ancientPoemReciteCollectionBiz.getById(id);
        if(abnormalPoemReciteCollection == null){
            throw new BusinessException(ANCIENT_POEM_RECITE_COLLECTION_NOT_EXIST_CODE,ANCIENT_POEM_RECITE_COLLECTION_NOT_EXIST_MSG);
        }
        AncientPoemReciteCollectionDetailDTO ancientPoemReciteCollectionDetailDTO = BeanUtil.copyProperties(abnormalPoemReciteCollection, AncientPoemReciteCollectionDetailDTO.class);

        return RestResponse.success(ancientPoemReciteCollectionDetailDTO);
    }

    @Override
    public RestResponse<Boolean> deleteAncientPoemReciteCollection(List<Integer> ids) throws BusinessException {
        List<AncientPoemBusinessRef> ancientPoemBusinessRefList = ancientBusinessRefBiz.lambdaQuery().in(AncientPoemBusinessRef::getBusinessId,ids).eq(AncientPoemBusinessRef::getBusinessType, PoemBusinessTypeEnum.ANCIENT_POEM_RECITE_BUSINESS.getCode()).list();
        if(CollectionUtils.isNotEmpty(ancientPoemBusinessRefList)){
            throw new BusinessException(ANCIENT_POEM_RECITE_COLLECTION_EXIST_POEM_CODE,ANCIENT_POEM_RECITE_COLLECTION_EXIST_POEM_MSG);
        }
        ancientPoemReciteCollectionBiz.removeBatchByIds(ids);
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> saveAncientPoemReciteCollection(AncientPoemReciteCollectionSaveBO ancientPoemReciteCollectionSaveBO) throws BusinessException {

        AncientPoemReciteCollection abnormalPoemReciteCollection = BeanUtil.copyProperties(ancientPoemReciteCollectionSaveBO, AncientPoemReciteCollection.class);

        ancientPoemReciteCollectionBiz.save(abnormalPoemReciteCollection);

        return RestResponse.success(true);
    }


    @Override
    public RestResponse<Boolean> updateTitle(AncientPoemReciteCollectionUpdateTitleBO ancientPoemReciteCollectionUpdateTitleBO) throws BusinessException {
        AncientPoemReciteCollection abnormalPoemReciteCollection = ancientPoemReciteCollectionBiz.getById(ancientPoemReciteCollectionUpdateTitleBO.getId());
        if(abnormalPoemReciteCollection == null){
            throw new BusinessException(ANCIENT_POEM_RECITE_COLLECTION_NOT_EXIST_CODE,ANCIENT_POEM_RECITE_COLLECTION_NOT_EXIST_MSG);
        }
        abnormalPoemReciteCollection = BeanUtil.copyProperties(ancientPoemReciteCollectionUpdateTitleBO, AncientPoemReciteCollection.class);
        ancientPoemReciteCollectionBiz.updateById(abnormalPoemReciteCollection);
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> updateDescription(AncientPoemReciteCollectionUpdateDescBO ancientPoemReciteCollectionUpdateDescBO) throws BusinessException {
        AncientPoemReciteCollection abnormalPoemReciteCollection = ancientPoemReciteCollectionBiz.getById(ancientPoemReciteCollectionUpdateDescBO.getId());
        if(abnormalPoemReciteCollection == null){
            throw new BusinessException(ANCIENT_POEM_RECITE_COLLECTION_NOT_EXIST_CODE,ANCIENT_POEM_RECITE_COLLECTION_NOT_EXIST_MSG);
        }
        abnormalPoemReciteCollection = BeanUtil.copyProperties(ancientPoemReciteCollectionUpdateDescBO, AncientPoemReciteCollection.class);
        ancientPoemReciteCollectionBiz.updateById(abnormalPoemReciteCollection);
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> updateStatus(AncientPoemReciteCollectionUpdateStatusBO anAncientPoemReciteCollection) throws BusinessException {
        AncientPoemReciteCollection abnormalPoemReciteCollection = ancientPoemReciteCollectionBiz.getById(anAncientPoemReciteCollection.getId());
        if(abnormalPoemReciteCollection == null){
            throw new BusinessException(ANCIENT_POEM_RECITE_COLLECTION_NOT_EXIST_CODE,ANCIENT_POEM_RECITE_COLLECTION_NOT_EXIST_MSG);
        }
        abnormalPoemReciteCollection = BeanUtil.copyProperties(anAncientPoemReciteCollection, AncientPoemReciteCollection.class);
        ancientPoemReciteCollectionBiz.updateById(abnormalPoemReciteCollection);
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> updateCoverUrl(AncientPoemReciteCollectionUpdateCoverUrlBO anotherCollectionUpdateCoverUrlBO) throws BusinessException {
        AncientPoemReciteCollection abnormalPoemReciteCollection = ancientPoemReciteCollectionBiz.getById(anotherCollectionUpdateCoverUrlBO.getId());
        if(abnormalPoemReciteCollection == null){
            throw new BusinessException(ANCIENT_POEM_RECITE_COLLECTION_NOT_EXIST_CODE,ANCIENT_POEM_RECITE_COLLECTION_NOT_EXIST_MSG);
        }
        abnormalPoemReciteCollection = BeanUtil.copyProperties(anotherCollectionUpdateCoverUrlBO, AncientPoemReciteCollection.class);
        ancientPoemReciteCollectionBiz.updateById(abnormalPoemReciteCollection);
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> updateAncientPoemReciteCollection(AncientPoemReciteCollectionUpdateBO ancientPoemReciteCollectionUpdateBO) throws BusinessException {

        AncientPoemReciteCollection abnormalPoemReciteCollection = ancientPoemReciteCollectionBiz.getById(ancientPoemReciteCollectionUpdateBO.getId());
        if(abnormalPoemReciteCollection == null){
            throw new BusinessException(ANCIENT_POEM_RECITE_COLLECTION_NOT_EXIST_CODE,ANCIENT_POEM_RECITE_COLLECTION_NOT_EXIST_MSG);
        }
        abnormalPoemReciteCollection = BeanUtil.copyProperties(ancientPoemReciteCollectionUpdateBO, AncientPoemReciteCollection.class);
        ancientPoemReciteCollectionBiz.updateById(abnormalPoemReciteCollection);
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> sort(List<AncientPoemReciteCollectionSortBO> sortBOList) throws BusinessException {
        List<AncientPoemReciteCollection> ancientPoemReciteCategoryList = BeanUtil.copyToList(sortBOList,AncientPoemReciteCollection.class);
        ancientPoemReciteCollectionBiz.updateBatchById(ancientPoemReciteCategoryList);
        return RestResponse.success(true);
    }


}
