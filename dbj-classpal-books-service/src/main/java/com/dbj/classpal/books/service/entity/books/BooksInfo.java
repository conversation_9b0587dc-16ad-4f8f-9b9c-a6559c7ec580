package com.dbj.classpal.books.service.entity.books;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 图书表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("books_info")
@Tag(name="BooksInfo对象", description="图书表")
public class BooksInfo extends BizEntity implements Serializable {


    @Schema(description = "产品名称")
    private String bookName;

    @Schema(description = "封面url")
    private String picUrl;
    @Schema(description = "简介")
    private String blurb;

    @Schema(description = "编者")
    private String editorName;

    @Schema(description = "ISBN")
    private String isbn;

    @Schema(description = "出版单位")
    private String publisher;

    @Schema(description = "物流编码")
    private String logisticsCode;

    @Schema(description = "唯一编码")
    private String code;

    @Schema(description = "卷册数")
    private Integer volumeNum;

    @Schema(description = "是否隐藏")
    private Integer isHide;

    @Schema(description = "上下架状态  上架 下架")
    private Integer launchStatus;

    @Schema(description = "是否启用 1-是 0-否")
    private Integer status;
    @Schema(description = "印匠图书id")
    private Integer printBooksId;

}
