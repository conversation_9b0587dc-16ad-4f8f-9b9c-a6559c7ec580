package com.dbj.classpal.books.service.util.audio;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.sound.sampled.*;
import java.io.File;
import java.io.IOException;
import java.io.SequenceInputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * 多个（本地）音频合成一个音频
 */
@Slf4j
public class AudioMerger {

    public static void main(String[] args) {
        List<File> audioFiles = new ArrayList<>();
        // 添加要合并的音频文件路径
        audioFiles.add(new File("C:\\Users\\<USER>\\Downloads\\wav\\SSML-break1.wav"));
        audioFiles.add(new File("C:\\Users\\<USER>\\Downloads\\wav\\SSML-phoneme2.wav"));
        audioFiles.add(new File("C:\\Users\\<USER>\\Downloads\\wav\\SSML-s3.wav"));
        audioFiles.add(new File("C:\\Users\\<USER>\\Downloads\\wav\\SSML-sound-event4.wav"));
        audioFiles.add(new File("C:\\Users\\<USER>\\Downloads\\wav\\SSML-w5.wav"));
        // 可以继续添加更多文件

        File outputFile = new File("C:\\Users\\<USER>\\Downloads\\output.wav");

        try {
            mergeAudioFiles(audioFiles, outputFile);
            System.out.println("音频文件合并完成！");
        } catch (IOException | UnsupportedAudioFileException | LineUnavailableException e) {
            log.error(e.getMessage());
        }
    }

    public static void mergeAudioFiles(List<File> inputFiles, File outputFile) throws IOException, UnsupportedAudioFileException, LineUnavailableException {
        AudioInputStream baseStream = null;
        for (File file : inputFiles) {
            AudioInputStream tempStream = AudioSystem.getAudioInputStream(file);

            if (baseStream == null) {
                // 初始化第一个流作为基准
                baseStream = tempStream;
            } else {
                // 合并流
                baseStream = new AudioInputStream(
                        new SequenceInputStream(baseStream, tempStream),
                        baseStream.getFormat(),
                        baseStream.getFrameLength() + tempStream.getFrameLength());
            }
        }

        // 将合并后的音频写入输出文件
        AudioSystem.write(baseStream, AudioFileFormat.Type.WAVE, outputFile);
    }
}