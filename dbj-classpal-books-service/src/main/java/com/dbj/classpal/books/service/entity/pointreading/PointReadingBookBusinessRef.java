package com.dbj.classpal.books.service.entity.pointreading;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 点读书业务关联表
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("point_reading_book_business_ref")
@Schema(name = "PointReadingBookBusinessRef", description = "点读书业务关联表")
public class PointReadingBookBusinessRef extends BizEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "点读书ID")
    private Integer bookId;

    @Schema(description = "业务类型：BOOK_IN_CODES-图书书内码 BOOK_RANK_CLASSIFY-图书资源")
    private String businessType;

    @Schema(description = "业务ID")
    private Integer businessId;

    @Schema(description = "排序")
    private Integer sortNum;

    @Schema(description = "状态：0-正常 1-停用")
    private Integer status;

    @Schema(description = "版本号")
    private Integer version;
}
