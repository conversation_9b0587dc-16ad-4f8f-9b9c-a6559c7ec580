package com.dbj.classpal.books.service.mq.listener.audio;

import com.alibaba.fastjson.JSON;
import com.dbj.classpal.books.client.enums.material.MaterialTransCodeEnum;
import com.dbj.classpal.books.client.mq.entity.AppMaterialMqEntity;
import com.dbj.classpal.books.common.bo.audio.AudioSyncMaterialBO;
import com.dbj.classpal.books.common.constant.RedisKeyConstants;
import com.dbj.classpal.books.common.enums.audio.AudioIntroStatus;
import com.dbj.classpal.books.common.enums.audio.AudioUploadMaterialStatusEnum;
import com.dbj.classpal.books.service.biz.audio.IAudioIntroBiz;
import com.dbj.classpal.books.service.biz.audio.IAudioIntroHistoryBiz;
import com.dbj.classpal.books.service.entity.audio.AudioIntro;
import com.dbj.classpal.books.service.entity.audio.AudioIntroHistory;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.redisson.config.RedissonRedisUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;

import java.io.IOException;
import java.text.MessageFormat;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

/**
 * 同步素材中心后监听MQ获取同步结果
 * <AUTHOR>
 * @date 2025-06-30
 **/
//@Component
@Slf4j
public class AudioSyncMaterialListener {
    @Autowired
    private RedissonRedisUtils redissonRedisUtils;
    @Autowired
    private IAudioIntroBiz audioIntroBiz;
    @Autowired
    private IAudioIntroHistoryBiz audioIntroHistoryBiz;

//    @ExtractHeader
//    @RabbitListener(queues = {QueueConstant.AUDIO_SYNC_MATERIAL_QUEUE})
    public void audioSyncMaterialHandler(AppMaterialMqEntity bo, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag, Message message) throws IOException, BusinessException {
        log.info("【同步素材中心后监听MQ获取同步结果】 - MQ参数: {}", JSON.toJSONString(bo));
        if (bo == null || StringUtils.isBlank(bo.getParamJson())) {
            throw new BusinessException("【同步素材中心后监听MQ获取同步结果】 - MQ接收参数为空");
        }
        AudioSyncMaterialBO syncMaterialBO = JSON.parseObject(bo.getParamJson(), AudioSyncMaterialBO.class);
        if (syncMaterialBO == null) {
            throw new BusinessException("【同步素材中心后监听MQ获取同步结果】 - json转对象失败: " + JSON.toJSONString(bo.getParamJson()));
        }
        // 分布式锁
        String lockKey = MessageFormat.format(RedisKeyConstants.DBJ_AUDIO_RECEIVE_MATERIAL_MQ_LOCK_KEY, syncMaterialBO.getAudioIntroId());
        Lock lock = redissonRedisUtils.getLock(lockKey);
        try {
            if (lock.tryLock(RedisKeyConstants.LOCK_TIME_OUT, TimeUnit.SECONDS)) {

                AudioIntro audioIntro = audioIntroBiz.lambdaQuery().eq(AudioIntro::getId, syncMaterialBO.getAudioIntroId()).one();
                if (audioIntro == null) {
                    throw new BusinessException("【同步素材中心后监听MQ获取同步结果】 - 音频简介不存在: " + syncMaterialBO.getAudioIntroId());
                }

                Integer frequency = audioIntro.getFrequency();
                if (!bo.getCode().equals(MaterialTransCodeEnum.TRANS_CODE_SUCCESS.getCode())) {
                    log.error("【同步素材中心后监听MQ获取同步结果】 - 上传素材中心返回异常，返回结果：{}", JSON.toJSONString(bo));
                    updateAudioStatus(syncMaterialBO.getAudioIntroId(), AudioUploadMaterialStatusEnum.FAIL.getCode(), StringUtils.isNotBlank(bo.getMsg()) ? bo.getMsg() : "上传素材中心返回异常", null, AudioIntroStatus.FAILED.getValue());
//                    log.info("【同步素材中心后监听MQ获取同步结果】 - 新增音频简介合成历史记录");
//                    insertAudioHistory(syncMaterialBO.getAudioIntroId(), ossUrl, "阿里云合成成功", frequency);
                    return;
                }
                // 更新上传状态
                updateAudioStatus(syncMaterialBO.getAudioIntroId(), AudioUploadMaterialStatusEnum.SUCCESS.getCode(), "上传素材中心成功", frequency == null ?  1 : frequency + 1,  AudioIntroStatus.COMPLETE.getValue());

//                AppMaterialBusinessRef businessRef = appMaterialBusinessRefBiz.lambdaQuery()
//                        .eq(AppMaterialBusinessRef::getBusinessType, BusinessTypeEnum.AUDIO_PRODUCTION_BUSINESS.getCode())
//                        .eq(AppMaterialBusinessRef::getBusinessId, syncMaterialBO.getAudioIntroId()).one();
//                if (businessRef != null) {
//                    businessRef.setAppMaterialId(bo.getFinalMaterialId());
//                    appMaterialBusinessRefBiz.updateById(businessRef);
//                } else {
//                    // 添加素材中心业务关联记录
//                    AppMaterialBusinessRef ref = new AppMaterialBusinessRef();
//                    ref.setAppMaterialId(bo.getFinalMaterialId());
//                    ref.setBusinessId(syncMaterialBO.getAudioIntroId());
//                    ref.setBusinessType(BusinessTypeEnum.AUDIO_PRODUCTION_BUSINESS.getCode());
//                    ref.setBusinessName(BusinessTypeEnum.AUDIO_PRODUCTION_BUSINESS.getType());
//                    appMaterialBusinessRefBiz.save(ref);
//                }
            }

        } catch (Exception e) {
            log.error(e.getMessage());
            log.error("【同步素材中心后监听MQ获取同步结果】 - 异常：{}", e.getMessage());

        } finally {
            try {
                log.info("【同步素材中心后监听MQ获取同步结果】释放锁");
                lock.unlock();
            } catch (Exception e) {
                log.error(e.getMessage());
                log.error("【同步素材中心后监听MQ获取同步结果】 - 释放锁异常：{}", e.getMessage());
            }
            channel.basicAck(tag, false);  // 拒绝并重新入队

        }

    }

    /**
     * 更新音频状态
     * @param audioIntroId
     * @param status
     */
    private void updateAudioStatus(Integer audioIntroId, Integer status, String reason, Integer frequency, Integer frequencyStatus) {
        AudioIntro audioIntro = new AudioIntro();
        audioIntro.setId(audioIntroId);
        audioIntro.setUploadStatus(status);
        audioIntro.setStatus(frequencyStatus);
        audioIntro.setMaterialFailReason(reason);
        if (frequency != null) {
            audioIntro.setFrequency(frequency);
        }
        audioIntro.setFailReason(reason);
        audioIntroBiz.updateById(audioIntro);
    }

    /**
     * 插入合成历史记录
     * @param audioIntroId
     * @param audioUrl
     * @param reason
     * @param version
     */
    private void insertAudioHistory(Integer audioIntroId, String audioUrl, String reason,Integer version) {
        AudioIntroHistory history = new AudioIntroHistory();
        history.setAudioIntroId(audioIntroId);
        history.setVersion(version);
        if (StringUtils.isNotEmpty(audioUrl)) {
            history.setAudioUrl(audioUrl);
        }
        if (StringUtils.isNotEmpty(reason)) {
            history.setFailReason(reason);
        }
        audioIntroHistoryBiz.save(history);
    }

}
