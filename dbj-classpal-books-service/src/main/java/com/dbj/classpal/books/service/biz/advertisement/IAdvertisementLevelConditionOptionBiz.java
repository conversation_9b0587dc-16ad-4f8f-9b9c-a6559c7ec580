package com.dbj.classpal.books.service.biz.advertisement;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dbj.classpal.books.client.dto.advertisement.AdvertisementLevelConditionOptionDTO;
import com.dbj.classpal.books.service.entity.advertisement.AdvertisementLevelConditionOption;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 广告层级条件选项关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
public interface IAdvertisementLevelConditionOptionBiz extends IService<AdvertisementLevelConditionOption> {

    /**
     * 根据广告层级条件id查询广告层级条件选项关联表
     *
     * @param ids 广告层级条件id集合
     * @return 广告层级条件选项关联表集合
     */
    List<AdvertisementLevelConditionOptionDTO> getByAdvertisementLevelConditionIds(Collection<Integer> ids);
}
