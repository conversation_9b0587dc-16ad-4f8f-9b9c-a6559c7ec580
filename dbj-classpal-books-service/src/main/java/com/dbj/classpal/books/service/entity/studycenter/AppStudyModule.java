package com.dbj.classpal.books.service.entity.studycenter;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dbj.classpal.framework.mybatisplus.domain.BizEntity;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_study_module")
@Tag(name = "学习模块", description = "学习中心-模块主表")
@AllArgsConstructor
@NoArgsConstructor
public class AppStudyModule extends BizEntity implements Serializable {


    /** 模块标题 */
    private String title;

    /** 模块简介 */
    private String description;
    /** logoUrl */
    private String logoUrl;
    /** logoName */
    private String logoName;
    /** 背景 */
    private String background;
    /** 背景图片名称 */
    private String backgroundName;

    /**栅格数**/
    private Integer gridCount;

    /** 模块类型1-评测，2-音频专辑 3 -视频专辑 4-功能 5- 题库 **/
    private Integer moduleType;

    /** 所属模块 */
    private Integer belongCategoryId;

    /** 排序权重 */
    private Integer sortNum;

    /** 是否显示 1-显示 0-隐藏 */
    private Integer isVisible;

    /** 上架状态 0-下架 1-上架 */
    private Integer publishStatus;

    /** 适用年级，ALL表示全部，部分年级用逗号分隔ID，如1,2,3 */
    private String applicableGrades;

    /** 状态 0-禁用 1-启用 */
    private Integer status;
}