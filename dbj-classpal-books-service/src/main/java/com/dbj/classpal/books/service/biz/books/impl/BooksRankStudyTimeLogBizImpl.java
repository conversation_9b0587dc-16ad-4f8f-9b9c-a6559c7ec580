package com.dbj.classpal.books.service.biz.books.impl;

import com.dbj.classpal.books.service.entity.books.BooksRankStudyTimeLog;
import com.dbj.classpal.books.service.mapper.books.BooksRankStudyTimeLogMapper;
import com.dbj.classpal.books.service.biz.books.IBooksRankStudyTimeLogBiz;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 图书卷册赠册用户学习时长表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Service
public class BooksRankStudyTimeLogBizImpl extends ServiceImpl<BooksRankStudyTimeLogMapper, BooksRankStudyTimeLog> implements IBooksRankStudyTimeLogBiz {

    @Override
    public Long getLastStudyTime(Integer rankId,Integer appUserId) {
        return baseMapper.getLastStudyTime(rankId,appUserId);
    }
}
