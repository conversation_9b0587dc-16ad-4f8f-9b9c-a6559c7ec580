package com.dbj.classpal.books.service.strategy.question.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dbj.classpal.books.common.bo.paper.RetakePaperBO;
import com.dbj.classpal.books.common.bo.paper.SubmitPaperBO;
import com.dbj.classpal.books.common.dto.paper.UserPaperDTO;
import com.dbj.classpal.books.common.enums.BusinessTypeEnum;
import com.dbj.classpal.books.service.biz.evaluation.IAppEvaluationBiz;
import com.dbj.classpal.books.service.biz.evaluation.IAppEvaluationNodeBiz;
import com.dbj.classpal.books.service.biz.evaluation.IAppUserPaperEvaluationAnalysisBiz;
import com.dbj.classpal.books.service.biz.evaluation.IAppUserPaperEvaluationBiz;
import com.dbj.classpal.books.service.biz.paper.IAppUserPaperInfoBiz;
import com.dbj.classpal.books.service.entity.evaluation.AppEvaluation;
import com.dbj.classpal.books.service.entity.evaluation.AppEvaluationNode;
import com.dbj.classpal.books.service.entity.evaluation.AppUserPaperEvaluation;
import com.dbj.classpal.books.service.entity.evaluation.AppUserPaperEvaluationAnalysis;
import com.dbj.classpal.books.service.strategy.question.AbstractPaperBusinessStrategy;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;

import static com.dbj.classpal.books.common.constant.AppErrorCode.*;
import static com.dbj.classpal.books.common.enums.evaluation.PaperEnvaluationStatusEnum.EVALUATION_GENERATED_YES;

/**
 * 评估试卷业务策略实现
 */
@Slf4j
@Component
public class EvaluationPaperBusinessStrategy extends AbstractPaperBusinessStrategy {

    @Resource
    private IAppEvaluationNodeBiz appEvaluationNodeBiz;
    @Resource
    private IAppEvaluationBiz appEvaluationBiz;

    @Resource
    private IAppUserPaperEvaluationAnalysisBiz appUserPaperEvaluationAnalysisBiz;
    @Resource
    private IAppUserPaperEvaluationBiz appUserPaperEvaluationBiz;

    @Override
    public Integer getBusinessType() {
        return BusinessTypeEnum.EVALUATION_BUSINESS.getCode();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void retakePaper(RetakePaperBO retakePaperBO) throws BusinessException {
        try {
            List<AppUserPaperEvaluationAnalysis> list = appUserPaperEvaluationAnalysisBiz.lambdaQuery().eq(AppUserPaperEvaluationAnalysis::getAppUserPaperInfoId, retakePaperBO.getPaperId()).orderByDesc(AppUserPaperEvaluationAnalysis::getCreateTime).list();
            if (!CollectionUtils.isEmpty(list)) {
                AppUserPaperEvaluation appUserPaperEvaluation = appUserPaperEvaluationBiz.getById(list.get(0).getAppUserPaperEvaluationId());
                if (!ObjectUtils.isEmpty(appUserPaperEvaluation) && appUserPaperEvaluation.getIsGenerated().equals(EVALUATION_GENERATED_YES.getCode())) {
                    throw new BusinessException(APP_EVALUATION_GENERATED_NOT_RETAKE_CODE, APP_EVALUATION_GENERATED_NOT_RETAKE_MSG);
                }
            }
            // 先调用父类的重考方法
            doRetakePaper(retakePaperBO);
            
            // 然后删除评估分析记录
            appUserPaperEvaluationAnalysisBiz.getBaseMapper().delete(
                    new LambdaQueryWrapper<AppUserPaperEvaluationAnalysis>()
                            .eq(AppUserPaperEvaluationAnalysis::getAppUserPaperInfoId, retakePaperBO.getPaperId())
            );
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("重新考试失败", e);
            throw new BusinessException(APP_RE_EXAM_FAIL_CODE,APP_RE_EXAM_FAIL_MSG);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserPaperDTO submitPaper(SubmitPaperBO submitBO) throws BusinessException {
        try {
            AppEvaluation evaluation = appEvaluationBiz.getById(submitBO.getAppEvaluationId());
            if (ObjectUtils.isEmpty(evaluation)){
                throw new BusinessException(APP_EVALUATION_NOT_EXIST_CODE,APP_EVALUATION_NOT_EXIST_MSG);
            }
            AppEvaluationNode evaluationNode = appEvaluationNodeBiz.getById(submitBO.getBusinessId());
            if (ObjectUtils.isEmpty(evaluationNode)){
                throw new BusinessException(APP_EVALUATION_NODE_SORT_FAIL_CODE,APP_EVALUATION_NODE_SORT_FAIL_MSG);
            }
            if (submitBO.getAppEvaluationId() == null) {
                throw new BusinessException(APP_EVALUATION_NOT_EXIST_CODE,APP_EVALUATION_NOT_EXIST_MSG);
            }
            return doSubmitParer(submitBO,evaluationNode.getAppEvaluationNodeName());
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("保存试卷结果失败", e);
            throw new BusinessException(APP_SAVE_PAPER_RESULT_FAIL_CODE,APP_SAVE_PAPER_RESULT_FAIL_MSG);
        }
    }
} 