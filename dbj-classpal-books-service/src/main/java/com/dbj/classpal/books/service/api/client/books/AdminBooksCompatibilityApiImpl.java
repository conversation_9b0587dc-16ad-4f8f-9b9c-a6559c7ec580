package com.dbj.classpal.books.service.api.client.books;

import cn.hutool.core.bean.BeanUtil;
import com.dbj.classpal.books.client.api.books.AdminBooksCompatibilityApi;
import com.dbj.classpal.books.client.bo.books.BooksCompatibilityBO;
import com.dbj.classpal.books.client.bo.books.BooksCompatibilitySaveBO;
import com.dbj.classpal.books.client.bo.books.BooksCompatibilityUpdBO;
import com.dbj.classpal.books.client.dto.books.BooksCompatibilityApiDTO;
import com.dbj.classpal.books.client.dto.books.app.BooksCompatibilityApiDetailDTO;
import com.dbj.classpal.books.common.constant.AppErrorCode;
import com.dbj.classpal.books.common.enums.books.ConnectionTypeEnum;
import com.dbj.classpal.books.service.biz.books.IBooksCompatibilityBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInCodesContentsBiz;
import com.dbj.classpal.books.service.biz.books.IBooksRankInfoBiz;
import com.dbj.classpal.books.service.entity.books.BooksCompatibility;
import com.dbj.classpal.books.service.entity.books.BooksRankInCodesContents;
import com.dbj.classpal.books.service.entity.books.BooksRankInfo;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.commons.response.RestResponse;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className AdminBooksCompatibilityApiImpl
 * @description
 * @date 2025-04-15 10:06
 **/
@RestController
public class AdminBooksCompatibilityApiImpl implements AdminBooksCompatibilityApi {

    @Resource
    private IBooksCompatibilityBiz bookCompatibilityBiz;
    @Resource
    private IBooksRankInfoBiz booksRankInfoBiz;
    @Resource
    private IBooksRankInCodesContentsBiz booksRankInCodesContentsBiz;

    @Override
    public RestResponse<Boolean> save(BooksCompatibilitySaveBO booksCompatibilitySaveBO) throws BusinessException {
        List<BooksCompatibility> booksCompatibilityList = bookCompatibilityBiz.lambdaQuery().eq(BooksCompatibility::getInternalCode,booksCompatibilitySaveBO.getInternalCode())
                .eq(BooksCompatibility::getConnectionType,booksCompatibilitySaveBO.getConnectionType()).list();
        if(CollectionUtils.isNotEmpty(booksCompatibilityList)){
            throw new BusinessException(AppErrorCode.BOOK_COMPATIBILITY_EXIST_CODE,AppErrorCode.BOOK_COMPATIBILITY_EXIST_MSG);
        }
        bookCompatibilityBiz.save(BeanUtil.copyProperties(booksCompatibilitySaveBO, BooksCompatibility.class));
        return RestResponse.success(true);
    }

    @Override
    public RestResponse<BooksCompatibilityApiDetailDTO> detail(String internalCode) throws BusinessException {
        List<BooksCompatibility> booksCompatibilityList = bookCompatibilityBiz.lambdaQuery().eq(BooksCompatibility::getInternalCode,internalCode).list();
        if(CollectionUtils.isEmpty(booksCompatibilityList)){
            throw new BusinessException(AppErrorCode.BOOK_COMPATIBILITY_INVALID_CODE,AppErrorCode.BOOK_COMPATIBILITY_INVALID_CODE);
        }
        BooksCompatibility booksCompatibility = booksCompatibilityList.get(0);
        booksCompatibility.getConnectionType();
        ConnectionTypeEnum connectionTypeEnum = ConnectionTypeEnum.getByCode(booksCompatibility.getConnectionType());
        BooksCompatibilityApiDetailDTO booksCompatibilityApiDetailDTO = BeanUtil.copyProperties(booksCompatibility,BooksCompatibilityApiDetailDTO.class);
        switch (connectionTypeEnum){
            case rank:
                //通过rankId查询相关信息
                BooksRankInfo booksRankInfo =  booksRankInfoBiz.getById(booksCompatibility.getInternalId());
                BooksCompatibilityApiDetailDTO.Rank rank = new BooksCompatibilityApiDetailDTO.Rank();
                rank.setBooksId(booksRankInfo.getBookId());
                rank.setRankId(booksRankInfo.getId());
                booksCompatibilityApiDetailDTO.setRank(rank);
                break;
            case contents:
                BooksRankInCodesContents booksRankInCodesContents = booksRankInCodesContentsBiz.getById(booksCompatibility.getInternalId());
                BooksCompatibilityApiDetailDTO.InCodesContents inCodesContents = new BooksCompatibilityApiDetailDTO.InCodesContents();
                inCodesContents.setBooksId(booksRankInCodesContents.getBooksId());
                inCodesContents.setContentsId(booksRankInCodesContents.getId());
                inCodesContents.setRankId(booksRankInCodesContents.getRankId());
                inCodesContents.setRankClassifyId(booksRankInCodesContents.getRankClassifyId());
                booksCompatibilityApiDetailDTO.setInCodesContents(inCodesContents);
                break;
        }
        return RestResponse.success(booksCompatibilityApiDetailDTO);
    }

    @Override
    public RestResponse<List<BooksCompatibilityApiDTO>> list(BooksCompatibilityBO booksCompatibilityBO) throws BusinessException {
        List<BooksCompatibility> booksCompatibilityList =  bookCompatibilityBiz.lambdaQuery().eq(BooksCompatibility::getInternalId,booksCompatibilityBO.getInternalId())
                .eq(BooksCompatibility::getConnectionType,booksCompatibilityBO.getConnectionType()).list();
        List<BooksCompatibilityApiDTO> booksCompatibilityApiDTOList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(booksCompatibilityList)){
            booksCompatibilityApiDTOList = BeanUtil.copyToList(booksCompatibilityList,BooksCompatibilityApiDTO.class);
        }
        return RestResponse.success(booksCompatibilityApiDTOList);
    }

    @Override
    public RestResponse<Boolean> update(BooksCompatibilityUpdBO booksCompatibilityUpdBO) throws BusinessException {
        BooksCompatibility booksCompatibility = bookCompatibilityBiz.getById(booksCompatibilityUpdBO.getId());
        if (booksCompatibility == null){
            throw new BusinessException(AppErrorCode.BOOK_COMPATIBILITY_NOT_EXIST_CODE,AppErrorCode.BOOK_COMPATIBILITY_NOT_EXIST_MSG);
        }
        if(!StringUtils.equals(booksCompatibilityUpdBO.getInternalCode(),booksCompatibility.getInternalCode())){
            List<BooksCompatibility> booksCompatibilityList = bookCompatibilityBiz.lambdaQuery().eq(BooksCompatibility::getInternalCode,booksCompatibilityUpdBO.getInternalCode())
                    .eq(BooksCompatibility::getConnectionType,booksCompatibility.getConnectionType()).list();
            if(CollectionUtils.isNotEmpty(booksCompatibilityList)){
                throw new BusinessException(AppErrorCode.BOOK_COMPATIBILITY_EXIST_CODE,AppErrorCode.BOOK_COMPATIBILITY_EXIST_MSG);
            }
            bookCompatibilityBiz.updateById(BeanUtil.copyProperties(booksCompatibilityUpdBO, BooksCompatibility.class));


        }

        return RestResponse.success(true);
    }

    @Override
    public RestResponse<Boolean> delete(Integer id) throws BusinessException {
        BooksCompatibility booksCompatibility = bookCompatibilityBiz.getById(id);
        if (booksCompatibility == null){
            throw new BusinessException(AppErrorCode.BOOK_COMPATIBILITY_NOT_EXIST_CODE,AppErrorCode.BOOK_COMPATIBILITY_NOT_EXIST_MSG);
        }
        bookCompatibilityBiz.removeById(id);
        return RestResponse.success(true);
    }
}
