package com.dbj.classpal.books.service.util;

import com.dbj.classpal.books.common.config.books.BookCodeUrlConfig;
import com.dbj.classpal.books.common.dto.ebooks.ShareUrlParamsDTO;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.utils.util.ShareUrlBuilder;
import com.dbj.classpal.framework.utils.util.enums.ShareUrlTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 分享URL构建助手
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Slf4j
@Component
public class ShareUrlBuilderHelper {

    /**
     * 构建H5分享URL
     */
    public String buildH5ShareUrl(BookCodeUrlConfig config, ShareUrlParamsDTO params) throws BusinessException {
        String urlTemplate = getH5PageUrlTemplate(config, params.getBusinessType());

        // 使用框架提供的 ShareUrlBuilder 进行URL构建
        return ShareUrlBuilder.buildH5ShareUrl(urlTemplate, params.getBusinessType(),params.getBusinessId());
    }
    
    /**
     * 根据业务类型获取H5页面URL模板
     */
    private String getH5PageUrlTemplate(BookCodeUrlConfig config, String businessType) throws BusinessException {
        ShareUrlTypeEnum shareUrlTypeEnum = ShareUrlTypeEnum.getByCode(businessType);
        return switch (shareUrlTypeEnum) {
            case BOOK -> config.getEBookShare().getH5PageUrl();
            case BOOKSHELF -> config.getEBookshelfShare().getH5PageUrl();
            case BOOKSTORE -> config.getEBookstoreShare().getH5PageUrl();
            default -> throw new BusinessException("不支持的业务类型: " + businessType);
        };
    }
}
