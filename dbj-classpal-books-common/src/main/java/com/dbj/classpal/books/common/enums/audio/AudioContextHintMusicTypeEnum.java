package com.dbj.classpal.books.common.enums.audio;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 预置音乐类型枚举
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum AudioContextHintMusicTypeEnum {
    HINT(1,"预置提示音"),
    DEFINITION(2,"自定义");

    private Integer code;
    private String name;

    public static AudioContextHintMusicTypeEnum getByCode(Integer code) {
        for (AudioContextHintMusicTypeEnum value : AudioContextHintMusicTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
