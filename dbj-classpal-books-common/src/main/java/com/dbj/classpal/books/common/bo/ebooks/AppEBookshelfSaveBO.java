package com.dbj.classpal.books.common.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 书架保存业务对象
 */
@Data
@Schema(description = "书架保存参数BO")
public class AppEBookshelfSaveBO implements Serializable {

    @Schema(description = "书架名称")
    private String shelfTitle;

    @Schema(description = "封面URL")
    private String coverUrl;

    @Schema(description = "封面URL名称")
    private String coverUrlName;

    @Schema(description = "简介")
    private String blurb;

    @Schema(description = "是否隐藏：0-否，1-是")
    private Integer isHide;

    @Schema(description = "启用状态：0-禁用，1-启用")
    private Integer launchStatus;

    @Schema(description = "排序序号")
    private Integer sortNum;
    
    @Schema(description = "单书ID列表")
    private List<Integer> bookIds;
} 