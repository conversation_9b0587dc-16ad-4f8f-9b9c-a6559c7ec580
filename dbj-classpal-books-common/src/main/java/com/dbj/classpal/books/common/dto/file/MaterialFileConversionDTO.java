package com.dbj.classpal.books.common.dto.file;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: MaterialfileConversionDTO
 * Date:     2025-04-09 17:05:27
 * Description: 表名： ,描述： 表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name = "上传单个文件-回调")
public class MaterialFileConversionDTO implements Serializable {

    @Schema(description = "路径Id")
    private Integer materialId;

    @Schema(description = "文件名")
    private String fileName;

    @Schema(description = "文件大小(kb)")
    private Double size;

    @Schema(description = "文件夹路径（上传文件不填，上传文件夹时必填）")
    private String dirPath;

    @Schema(name = "文件md5值")
    private String md5;

    @Schema(name = "文件所在父节点Id")
    private Integer parentId;
}
