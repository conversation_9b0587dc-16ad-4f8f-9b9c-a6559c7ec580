package com.dbj.classpal.books.common.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum SortEnum {

    SORT_ASC(1,"asc","升序"),
    SORT_DESC(2,"desc","降序");

    /**
     * 类型code
     */
    private  Integer code;

    /**
     * 类型值
     */
    private  String value;

    /**
     * 类型描述
     */
    private  String desc;

    SortEnum() {
    }

    SortEnum(Integer code, String value, String desc) {
        this.code = code;
        this.value = value;
        this.desc = desc;
    }
    /**
     * 根据类型值获取枚举
     *
     * @param code 类型值
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static SortEnum getByCode(String code) {
        for (SortEnum typeEnum : SortEnum.values()) {
            if (Objects.equals(typeEnum.getCode(), code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
