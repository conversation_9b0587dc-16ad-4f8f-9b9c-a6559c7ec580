package com.dbj.classpal.books.common.bo.evaluation;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationUpdateStatusApiBO
 * Date:     2025-04-15 13:35:46
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class AdminEvaluationUpdateOpenBO implements Serializable {

    @Schema(description = "主键id列表",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "主键id列表不能为空")
    private List<Integer> ids;

    @Schema(description = "启用状态  0禁用 1启用",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "启用状态不能为空")
    private Integer isOpen;
}
