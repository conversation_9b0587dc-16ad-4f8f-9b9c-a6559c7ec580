package com.dbj.classpal.books.common.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema
@Data
public class QuestionAnswerDTO implements Serializable {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Integer id;

    /**
     * 题目id
     */
    @Schema(description = "题目id")
    private Integer questionId;


    /**
     * 完形填空区域id
     */
    @Schema(description = "完形填空区域id")
    private Integer blankAreaId;

    /**
     * 序号
     */
    @Schema(description = "序号")
    private Integer serialNo;

    /**
     * 选项名称
     */
    @Schema(description = "选项名称")
    private String optionName;

    /**
     * 选项内容
     */
    @Schema(description = "选项内容")
    private String optionContent;


    /**
     * 媒体文件url
     */
    @Schema(description = "媒体文件url")
    private List<QuestionMediaDTO> mediaUrl;


    /**
     * 是否答案（0-否 1-是）
     */
    @Schema(description = "是否答案（0-否 1-是）")
    private Integer isAnswer;

} 