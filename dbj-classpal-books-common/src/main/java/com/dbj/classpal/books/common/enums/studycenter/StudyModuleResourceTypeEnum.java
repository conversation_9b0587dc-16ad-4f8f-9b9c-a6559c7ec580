package com.dbj.classpal.books.common.enums.studycenter;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum StudyModuleResourceTypeEnum {
    EVALUATION("EVALUATION", "评测",1),
    AUDIO_ALBUM("AUDIO_ALBUM", "音频专辑",2),
    VIDEO_ALBUM("VIDEO_ALBUM", "视频专辑",3),
    FUNCTION("FUNCTION", "功能",4),
    QUESTION_BANK("QUESTION_BANK", "题库",5);

    private final String code;
    private final String desc;
    private final Integer id;
}