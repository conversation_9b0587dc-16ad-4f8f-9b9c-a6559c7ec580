package com.dbj.classpal.books.common.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "错题完形填空空位信息")
@Data
public class WrongQuestionBlankBO implements Serializable {

    /**
     * 空位区域ID
     */
    @Schema(description = "空位区域ID")
    private Integer blankAreaId;

    /**
     * 题目ID
     */
    @Schema(description = "题目ID")
    private Integer questionId;

    /**
     * 空位序号
     */
    @Schema(description = "空位序号")
    private Integer blankIndex;

    /**
     * 用户答案
     */
    @Schema(description = "用户答案")
    private String userAnswer;

    /**
     * 用户选择的答案ID
     */
    @Schema(description = "用户选择的答案ID")
    private String userAnswerIds;
}