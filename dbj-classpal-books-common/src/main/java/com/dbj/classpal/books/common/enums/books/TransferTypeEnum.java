package com.dbj.classpal.books.common.enums.books;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: BusinessTypeEnum
 * Date:     2025-04-14 08:48:01
 * Description: 表名： ,描述： 表
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum TransferTypeEnum {

    H5("H5","H5");

    private String code;
    private String name;

    public static TransferTypeEnum getByCode(String code) {
        for (TransferTypeEnum value : TransferTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
