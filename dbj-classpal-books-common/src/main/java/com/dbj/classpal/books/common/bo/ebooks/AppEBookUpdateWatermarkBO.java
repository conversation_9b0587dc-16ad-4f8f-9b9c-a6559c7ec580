package com.dbj.classpal.books.common.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 单书更新水印业务对象
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Data
@Schema(description = "单书更新水印业务对象")
public class AppEBookUpdateWatermarkBO implements Serializable {

    @Schema(description = "单书ID")
    private Integer id;

    @Schema(description = "水印模板ID")
    private Integer watermarkId;
} 