package com.dbj.classpal.books.common.bo.material;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialBatchCommonIdBO
 * Date:     2025-04-14 10:22:40
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class AppMaterialBatchCommonIdBO implements Serializable {
    @Schema(description = "id列表",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "id列表不能为空")
    private List<Integer>ids;
}
