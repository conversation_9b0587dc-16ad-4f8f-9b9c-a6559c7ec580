package com.dbj.classpal.books.common.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Schema
@Data
public class QuestionCategoryRefDTO implements Serializable {

    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "业务id")
    private Integer businessId;

    @Schema(description = "业务类型 1音频专辑 2视频专辑 3图书")
    private Integer businessType;

    @Schema(description = "业务类型文本")
    private String businessTypeStr;

    @Schema(description = "引用对象")
    private String businessName;

    @Schema(description = "对象类型 1文件夹 2图片  3音频 4视频 5文档 6压缩包 7 答题")
    private Integer appMaterialType;

    @Schema(description = "对象类型文本")
    private String appMaterialTypeStr;

    @Schema(description = "图书id")
    private Integer booksId;

    @Schema(description = "图书名称")
    private String bookName;

    @Schema(description = "册书id")
    private Integer rankId;

    @Schema(description = "书内码id")
    private Integer contentsId;

    @Schema(description = "册数功能分类id")
    private Integer rankClassifyId;
} 