package com.dbj.classpal.books.common.bo.evaluation;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationQueryBO
 * Date:     2025-04-15 13:35:46
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class AdminEvaluationNodeSaveBO implements Serializable {

    @Schema(description = "评测表id")
    private Integer appEvaluationId;

    @Schema(description = "评测项名称",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "评测项名称不能为空")
    private String appEvaluationNodeName;

    @Schema(description = "素材id",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "素材id不能为空")
    private Integer appMaterialId;
}
