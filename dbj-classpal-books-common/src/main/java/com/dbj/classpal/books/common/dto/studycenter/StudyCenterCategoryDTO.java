package com.dbj.classpal.books.common.dto.studycenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "学习中心分类模块")
public class StudyCenterCategoryDTO {
    /**
     * 所属分类ID
     */
    private Integer beLongCategoryId;
    
    /**
     * 所属分类名称
     */
    private String beLongCategoryName;

    /**
     * 所属分类icon
     */
    private String beLongCategoryIcon;

    /**
     * 所属分类背景图片
     */
    private String beLongCategoryBackGround;

    private String beLongCategoryBackGroundName;
    
    /**
     * 模块列表
     */
    private List<StudyCenterModuleListDTO> moduleList;
    
}