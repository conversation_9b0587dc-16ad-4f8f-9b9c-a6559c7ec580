package com.dbj.classpal.books.common.bo.studycenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "答题设置-新增BO")
public class AppStudyModuleQuestionExtCreateBO {
    @Schema(description = "extid , 更新的时候必填")
    private Integer questionExtId;

    @Schema(description = "题库集合")
    private List<AppStudyModuleQuestionExtBO> question;

    @Schema(description = "出题方式")
    private Integer questionMethod;

    @Schema(description = "题目数量")
    private Integer questionNum;
} 