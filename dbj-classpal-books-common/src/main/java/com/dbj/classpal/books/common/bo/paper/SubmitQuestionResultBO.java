package com.dbj.classpal.books.common.bo.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "提交题目作答结果")
public class SubmitQuestionResultBO implements Serializable {

    @Schema(description = "题目ID")
    private Integer questionId;

    @Schema(description = "用户选择的答案ID列表，逗号分隔")
    private String answerIds;

    /**
     * 用户答案)
     */
    @Schema(description = "用户答案")
    private String userAnswer;

    @Schema(description = "用户作答结果")
    private Integer result;
}
