package com.dbj.classpal.books.common.bo.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

/**
 * 保存试卷题目结果参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "保存试卷题目结果参数")
public class SaveQuestionResultBO extends BasicPaperBO implements Serializable {


    /**
     * 试卷ID
     */
    @Schema(description = "试卷ID")
    private Integer paperId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Integer appUserId;

    /**
     * 题目ID
     */
    @Schema(description = "题目ID")
    private Integer questionId;

    /**
     * 用户选择的答案ID（多个英文逗号隔开）
     */
    @Schema(description = "用户选择的答案ID（多个英文逗号隔开）")
    private String answerIds;


    /**
     * 用户答案
     */
    @Schema(description = "用户答案")
    private String userAnswer;

    /**
     * 结果(0:错误,1:正确)
     */
    @Schema(description = "结果(0:错误,1:正确)")
    private Integer result;

    /**
     * 题目序号
     */
    @Schema(description = "题目序号")
    private Integer questionSort;
} 