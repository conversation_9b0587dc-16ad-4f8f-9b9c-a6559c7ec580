package com.dbj.classpal.books.common.enums.audio;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 发音人类型枚举
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum AudioSpeakerTypeEnum {
    MAN(1,"男声"),
    WOMEN(2,"女声"),
    CHILD(3,"童声"),
    EMOTION(4,"多情感");

    private Integer code;
    private String name;

    public static AudioSpeakerTypeEnum getByCode(Integer code) {
        for (AudioSpeakerTypeEnum value : AudioSpeakerTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
