package com.dbj.classpal.books.common.dto.common;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 业务关联信息DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Tag(name="业务关联信息DTO", description="业务关联信息DTO")
public class BusinessInfoDTO implements Serializable {

    @Schema(description = "业务名称")
    private String businessName;

    @Schema(description = "其他信息")
    private Map<String, Object> other;
}
