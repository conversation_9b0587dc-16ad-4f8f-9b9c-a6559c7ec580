package com.dbj.classpal.books.common.bo.material;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialMoveBO
 * Date:     2025-04-10 10:28:06
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class AppMaterialBatchIOBO implements Serializable {

    @Schema(description = "资源列表",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "资源列表不能为空")
    private List<AppMaterialIOBO> ioApiBOList;
}
