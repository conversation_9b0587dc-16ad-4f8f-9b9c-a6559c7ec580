package com.dbj.classpal.books.common.bo.books;

import com.dbj.classpal.framework.mq.entity.RabbitmqEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 产品分类配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Data
public class BooksCategoryMqBO extends RabbitmqEntity implements Serializable {


    @Schema(description = "名称")
    private String name;


}
