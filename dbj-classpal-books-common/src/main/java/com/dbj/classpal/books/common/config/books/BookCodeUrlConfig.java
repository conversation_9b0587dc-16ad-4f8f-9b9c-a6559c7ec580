package com.dbj.classpal.books.common.config.books;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @program dbj-classpal-books-bus
 * @className BookCodeUrlConfig
 * @description
 * @date 2025-04-17 10:20
 **/
@Component
@ConfigurationProperties(prefix = "books.code")
@Data
public class BookCodeUrlConfig {

    /**
     * 默认缓存配置
     */
    private Rank rank;

    /**
     *  样书分享
     */
    private EBookShare eBookShare;

    /**
     * 书架分享
     */
    private EBookshelfShare eBookshelfShare;

    /**
     * 书城分享
     */
    private EBookstoreShare eBookstoreShare;

    /**
     * 默认缓存配置
     */
    private InCode incode;
    /**
     * 默认缓存配置
     */
    private String shortUrl;

    @Data
    public static class Rank {
        private String h5PageUrl;
        private String newPrintCodeUrl;
    }

    @Data
    public static class EBookShare {
        private String h5PageUrl;
    }

    @Data
    public static class EBookshelfShare {
        private String h5PageUrl;
    }

    @Data
    public static class EBookstoreShare {
        private String h5PageUrl;
    }

    @Data
    public static class InCode {
        private String h5PageUrl;
        private String newPrintCodeUrl;
    }

}
