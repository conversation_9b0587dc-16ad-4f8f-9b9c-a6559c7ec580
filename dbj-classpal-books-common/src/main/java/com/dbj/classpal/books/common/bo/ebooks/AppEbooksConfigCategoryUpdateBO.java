package com.dbj.classpal.books.common.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="电子样书-样书配置-图书分类-修改BO", description="电子样书-样书配置-图书分类-修改BO")
public class AppEbooksConfigCategoryUpdateBO {

    @Schema(description = "主键id")
    @NotNull(message = "主键id不能为空")
    private Integer id;

    @Schema(description = "父级id")
    @NotNull(message = "父级id为空")
    private Integer parentId;

    @Schema(description = "名称")
    @NotEmpty(message = "名称不能为空")
    private String name;

    @Schema(description = "权重")
    private Integer sort;
}
