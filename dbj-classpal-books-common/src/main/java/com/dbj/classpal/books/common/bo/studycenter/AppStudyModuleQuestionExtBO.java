package com.dbj.classpal.books.common.bo.studycenter;

import com.dbj.classpal.books.common.dto.studycenter.AppStudyModuleQuestionRefDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 题目分类业务关联 BO
 */
@Data
@Schema(description = "题目分类业务关联")
public class AppStudyModuleQuestionExtBO implements Serializable {

    /**
     * 题目分类ID
     */
    @Schema(description = "题目分类ID")
    private Integer id;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sortNum;

} 