package com.dbj.classpal.books.common.enums.poem;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 古诗文业务关联类型枚举
 * <AUTHOR>
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum PoemBusinessTypeEnum {

    ANCIENT_POEM_RECITE_BUSINESS(1,"古文背诵","古文背诵", "ancientPoemReciteCollectionStrategy"),
    ;

    private int code;
    private String type;
    private String name;
    private String strategy;

    public static PoemBusinessTypeEnum getByCode(Integer code) {
        for (PoemBusinessTypeEnum businessTypeEnum : PoemBusinessTypeEnum.values()) {
            if (businessTypeEnum.getCode() == code) {
                return businessTypeEnum;
            }
        }
        return null;
    }
}
