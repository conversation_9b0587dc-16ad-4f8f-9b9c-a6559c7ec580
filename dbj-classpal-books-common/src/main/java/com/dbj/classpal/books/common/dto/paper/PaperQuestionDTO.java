package com.dbj.classpal.books.common.dto.paper;

import com.dbj.classpal.books.common.dto.question.QuestionAnswerDTO;
import com.dbj.classpal.books.common.dto.question.QuestionBlankAreaDTO;
import com.dbj.classpal.books.common.dto.question.QuestionMediaDTO;
import com.dbj.classpal.books.common.dto.question.QuestionRecognitionDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 试卷题目DTO
 */
@Data
@Schema(description = "试卷题目DTO")
public class PaperQuestionDTO implements Serializable {

    @Schema(description = "题目ID")
    private Integer id;

    @Schema(description = "题目标题")
    private String title;

    @Schema(description = "题目类型")
    private Integer type;

    @Schema(description = "题目类型名称")
    private String typeName;

    @Schema(description = "媒体文件类型 1-文本 2-图片,3-音频,4-视频")
    private Integer mediaType;

    @Schema(description = "选项类型 1-文本 2-图片,3-音频,4-视频")
    private Integer optionType;
    /**
     * 媒体文件url
     */
    @Schema(description = "媒体文件url")
    private List<QuestionMediaDTO> mediaUrl;

    @Schema(description = "辅助识图")
    private List<QuestionRecognitionDTO> aidedRecognitionUrl;


    @Schema(description = "题目权重")
    private Integer weight;

    @Schema(description = "题目答案")
    private String answer;

    @Schema(description = "题目解析")
    private String analyzes;

    @Schema(description = "题目选项列表")
    private List<QuestionAnswerDTO> answers;

    @Schema(description = "题目空位列表")
    private List<QuestionBlankAreaDTO> blankAreas;

    @Schema(description = "作答结果")
    private Integer result;

    @Schema(description = "排序号")
    private Integer sortNum;

    @Schema(description = "题目类型总数")
    private Integer typeCount;

    @Schema(description = "当前题目在该类型中的序号")
    private Integer typeIndex;


    @Schema(description = "状态")
    private Integer status;
} 