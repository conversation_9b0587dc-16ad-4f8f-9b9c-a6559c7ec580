package com.dbj.classpal.books.common.enums.advertisement;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 广告选项类型枚举
 * <AUTHOR>
 * @since 2025-04-19 10:52
 */
@Getter
@AllArgsConstructor
public enum AdvertiseOptionTypeEnum {

    GRADE(1, "年级"),
//    GENDER(2, "性别"),
    RELATION(3, "关系"),
    BOOK_CATEGORY(4, "图书类别"),
//    BOOK_QTY(5, "图书数量区间"),
    BOOK_DESIGNATED(6, "指定图书"),
    AREA_PROVINCE(11, "属地省级"),
    AREA_CITY(12, "属地市级"),
    AREA_LEVEL(13, "属地分级标签"),
    ;

    private final Integer type;
    private final String name;

    public static AdvertiseOptionTypeEnum of(Integer type) {
        return Arrays.stream(values()).filter(e -> e.getType().equals(type))
                .findFirst().orElse(null);
    }
}
