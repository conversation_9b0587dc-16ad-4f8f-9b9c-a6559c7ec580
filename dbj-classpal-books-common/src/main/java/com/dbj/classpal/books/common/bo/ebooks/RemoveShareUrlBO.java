package com.dbj.classpal.books.common.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 删除分享链接业务参数BO
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@Schema(description = "删除分享链接请求参数")
public class RemoveShareUrlBO implements Serializable {

    @Schema(description = "业务类型：b5-单书，b6-书架，b7-书城")
    private String businessType;

    @Schema(description = "业务ID")
    private Integer businessId;
}
