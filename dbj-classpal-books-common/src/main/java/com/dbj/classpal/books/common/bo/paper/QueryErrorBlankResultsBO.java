package com.dbj.classpal.books.common.bo.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 查询用户错误空位答题记录参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "查询用户错误空位答题记录参数")
public class QueryErrorBlankResultsBO implements Serializable {

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Integer appUserId;

    /**
     * 题目ID列表
     */
    @Schema(description = "题目ID列表")
    private List<Integer> questionIds;

    /**
     * 来源类型
     */
    @Schema(description = "来源类型 1:用户答题 2:用户评测")
    private Integer sourceType;

    /**
     * 来源ID
     */
    @Schema(description = "来源ID")
    private Integer sourceId;
} 