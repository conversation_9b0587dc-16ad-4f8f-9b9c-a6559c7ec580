package com.dbj.classpal.books.common.dto.material;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialBusinessRefDTO
 * Date:     2025-04-14 08:54:29
 * Description: 表名： ,描述： 表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@Tag(name = "素材与关联业务关系表DTO")
public class AppMaterialBusinessRefDTO implements Serializable {
    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "业务id")
    private Integer businessId;

    @Schema(description = "业务类型 1内容管理-音频专辑 2内容管理-视频专辑 3图书管理-图书资源 4图书管理-题库 5图书管理-音频专辑 6图书管理-视频专辑 7题目-媒体文件 8题目-辅助识图 9题目-答案")
    private Integer businessType;

    @Schema(description = "业务类型文本")
    private String businessTypeStr;

    @Schema(description = "素材库id")
    private Integer appMaterialId;

    @Schema(description = "原始资源名称")
    private String appMaterialName;

    @Schema(description = "原始资源字幕")
    private String appMaterialCaption;

    @Schema(description = "引用对象")
    private String businessName;

    @Schema(description = "对象类型 1文件夹 2图片  3音频 4视频 5文档 6压缩包")
    private Integer appMaterialType;

    @Schema(description = "对象类型文本")
    private String appMaterialTypeStr;

    @Schema(description = "对象文件大小")
    private Double appMaterialSize;

    @Schema(description = "资源路径")
    private String materialPath;

    @Schema(description = "源文件路径")
    private String materialOriginUrl;

    @Schema(description = "资源图标")
    private String materialIcon;

    @Schema(description = "资源后缀名")
    private String materialExtension;

    @Schema(description = "资源时长(s)")
    private Integer materialDuration;

}
