package com.dbj.classpal.books.common.dto.studycenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "资源关联-分页列表DTO")
public class AppStudyModuleResourceRelListDTO {
    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "学习模块ID")
    private Integer moduleId;

    @Schema(description = "资源ID")
    private Integer resourceId;

    @Schema(description = "资源类型")
    private String resourceType;

    @Schema(description = "排序权重")
    private Integer sortNum;

    @Schema(description = "状态 0-禁用 1-启用")
    private Integer status;
} 