package com.dbj.classpal.books.common.dto.evaluation.app;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppUserPaperEvaluationDTO
 * Date:     2025-05-19 14:26:00
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
@Tag(name = "评测报告评测项分析",description = "评测报告评测项分析")
public class AppUserPaperEvaluationAiParamsDTO implements Serializable {

    @Schema(description = "评测报告id")
    private Integer id;

    @Schema(description = "评测表名称")
    private String appEvaluationName;

    @Schema(description = "评测项分析参数列表")
    List<AppUserPaperEvaluationAnalysisAiParamsDTO> analysisAiParamsDTOList;

}
