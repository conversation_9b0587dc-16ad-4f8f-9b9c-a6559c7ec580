package com.dbj.classpal.books.common.bo.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 提交填空题作答结果BO
 */
@Data
@Schema(description = "提交填空题作答结果BO")
public class SubmitBlankResultBO implements Serializable {


    /**
     * 题目ID
     */
    @Schema(description = "题目ID")
    private Integer questionId;

    @Schema(description = "空位id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "空位id")
    private Integer blankAreaId;

    /**
     * 填空序号
     */
    @Schema(description = "填空序号")
    private Integer blankIndex;

    /**
     * 用户选择的答案ID列表
     */
    @Schema(description = "用户选择的答案ID列表，逗号分隔")
    private String answerIds;


    /**
     * 用户作答结果
     */
    @Schema(description = "用户作答结果")
    private Integer result;
} 