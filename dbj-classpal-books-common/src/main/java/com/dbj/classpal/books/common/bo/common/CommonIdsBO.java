package com.dbj.classpal.books.common.bo.common;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: CommonIdBO
 * Date:     2025-04-15 10:43:02
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class CommonIdsBO implements Serializable {
    @Schema(description = "主键ID列表",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "主键ID列表不能为空")
    private List<Integer> ids;
}
