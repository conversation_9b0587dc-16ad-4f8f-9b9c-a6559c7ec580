package com.dbj.classpal.books.common.enums.tree;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 树形分类业务枚举
 * <AUTHOR>
 * @since 2025-04-25 14:36
 */
@Getter
@AllArgsConstructor
public enum TreeClassifyEnum {

    ANCIENT_POEM(1, "古诗文","ancientPoemRefStrategy");

    private final Integer type;
    private final String desc;
    private final String strategy;
    public static TreeClassifyEnum getByType(Integer type) {
        for (TreeClassifyEnum classifyEnum : TreeClassifyEnum.values()) {
            if (classifyEnum.getType().equals(type)) {
                return classifyEnum;
            }
        }
        return null;
    }
}
