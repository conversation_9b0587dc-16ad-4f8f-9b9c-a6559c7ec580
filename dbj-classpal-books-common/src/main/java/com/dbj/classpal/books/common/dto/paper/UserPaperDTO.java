package com.dbj.classpal.books.common.dto.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户试卷信息
 */
@Data
@Schema(description = "用户试卷信息")
public class UserPaperDTO implements Serializable {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 业务类型
     */
    @Schema(description = "业务类型 1内容管理-音频专辑 2内容管理-视频专辑 3图书管理-图书资源 4图书管理-题库 5图书管理-音频专辑 6图书管理-视频专辑",requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer businessType;

    /**
     * 业务ID
     */
    @Schema(description = "业务ID")
    private Integer businessId;

    /**
     * 试卷名称
     */
    @Schema(description = "试卷名称")
    private String paperName;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Integer appUserId;

    /**
     * 是否提交
     */
    @Schema(description = "是否提交 0-否 1-是")
    private Integer isSubmit;

    /**
     * 状态
     */
    @Schema(description = "状态 0-禁用 1-启用")
    private Integer status;
}