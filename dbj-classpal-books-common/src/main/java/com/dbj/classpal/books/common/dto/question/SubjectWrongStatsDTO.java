package com.dbj.classpal.books.common.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 学科错题统计DTO
 */
@Data
@Schema(description = "学科错题统计DTO")
public class SubjectWrongStatsDTO implements Serializable {

    /**
     * 学科ID
     */
    @Schema(description = "学科ID")
    private Integer subjectId;

    /**
     * 学科名称
     */
    @Schema(description = "学科名称")
    private String subjectName;

    /**
     * 错题数量
     */
    @Schema(description = "错题数量")
    private Integer count;
} 