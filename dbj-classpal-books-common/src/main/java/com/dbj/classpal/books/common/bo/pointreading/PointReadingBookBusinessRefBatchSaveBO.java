package com.dbj.classpal.books.common.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 点读书业务关联批量保存BO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingBookBusinessRefBatchSaveBO", description = "点读书业务关联批量保存BO")
public class PointReadingBookBusinessRefBatchSaveBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "业务关联列表")
    @NotEmpty(message = "业务关联列表不能为空")
    private List<PointReadingBookBusinessRefSaveBO> refs;
}
