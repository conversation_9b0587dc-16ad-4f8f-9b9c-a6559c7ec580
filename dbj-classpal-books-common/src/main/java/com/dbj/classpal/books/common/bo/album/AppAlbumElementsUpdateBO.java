package com.dbj.classpal.books.common.bo.album;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumElementsSaveBO
 * Date:     2025-04-15 13:35:46
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class AppAlbumElementsUpdateBO implements Serializable {
    @Schema(description = "主键ID",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键ID不能为空")
    private Integer id;

    @Schema(description = "专辑菜单ID",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "专辑菜单ID不能为空")
    private Integer appAlbumMenuId;

    @Schema(description = "专辑类型 1音频 2视频",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "专辑类型不能为空")
    private Integer albumType;

    @Schema(description = "专辑封面")
    private String albumCover;

    @Schema(description = "专辑标题",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "专辑标题不能为空")
    @Size(max = 64,message = "专辑标题不能超过64字符")
    private String albumTitle;

    @Schema(description = "专辑简介")
    @Size(max = 256,message = "专辑简介不能超过256字符")
    private String albumRemark;

    @Schema(description = "是否隐藏 0显示 1隐藏",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "专辑隐藏状态不能为空")
    private Integer albumVisible;

    @Schema(description = "上架状态 0下架 1上架",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "专辑上架状态不能为空")
    private Integer albumStatus;
}
