package com.dbj.classpal.books.common.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 单书资源保存业务对象
 */
@Data
@Schema(description = "单书资源保存参数BO")
public class AppEBookResourceSaveBO implements Serializable {

    @Schema(description = "关联的id")
    private Integer resourceId;

    @Schema(description = "关联的单书文件md5")
    private String resourceKey;

    @Schema(description = "资源类型：1-加水印PDF, 2-切图图片")
    private Integer resourceType;

    @Schema(description = "资源URL")
    private String resourceUrl;

    @Schema(description = "页码，适用于切图")
    private Integer pageNum;

    @Schema(description = "文件大小(字节)")
    private Integer fileSize;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "排序序号")
    private Integer sortNum;

    @Schema(description = "处理类型：1-仅添加水印, 2-仅切图, 3-加水印并切图")
    private Integer businessType;
} 