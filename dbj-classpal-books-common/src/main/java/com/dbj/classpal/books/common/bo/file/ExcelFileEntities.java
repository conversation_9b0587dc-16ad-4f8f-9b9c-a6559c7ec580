package com.dbj.classpal.books.common.bo.file;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: ExcelFileEntities
 * Date:     2025-04-08 18:04:31
 * Description: 表名： ,描述： 表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExcelFileEntities {
    /**
     * file 表文件的id
     */
    @Schema(name = "file 表文件的ids")
    private Set<Integer> ids;
}
