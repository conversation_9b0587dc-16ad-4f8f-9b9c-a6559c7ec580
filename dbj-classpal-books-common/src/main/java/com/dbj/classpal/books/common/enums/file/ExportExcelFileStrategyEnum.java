package com.dbj.classpal.books.common.enums.file;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ExcelFileStrategyEnum
 * @date 2023-10-27 11:02
 **/
@AllArgsConstructor
@Getter
public enum ExportExcelFileStrategyEnum {
    /**
     * 菜单导入
     */
    SYS_MENU_EXCEL_FILE("booksInCodeExportExcelFileStrategy", "链接导出");

    private String handler;

    private String value;


    public static ExportExcelFileStrategyEnum getEnum(String code){

        if(StringUtils.isEmpty(code)){
            return null;
        }
        for(ExportExcelFileStrategyEnum strategyEnum : ExportExcelFileStrategyEnum.values()){
            if(StringUtils.equals(strategyEnum.getHandler(),code)){
                return strategyEnum;
            }
        }
        return null;
    }

}
