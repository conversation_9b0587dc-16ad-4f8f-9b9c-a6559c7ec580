package com.dbj.classpal.books.common.bo.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;

/**
 * 查询试卷题目结果参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "查询试卷题目结果参数")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryQuestionResultBO extends BasicPaperBO implements Serializable {


    @Schema(description = "试卷ID")
    private Integer paperId;

    @Schema(description = "用户ID")
    private Integer appUserId;
}