package com.dbj.classpal.books.common.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 点读书页面查询BO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingPageQueryBO", description = "点读书页面查询BO")
public class PointReadingChapterQueryBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "页面名称")
    private String name;

    @Schema(description = "所属目录ID")
    private Integer menuId;

    @Schema(description = "状态：0-停用 1-正常")
    private Integer status;
}
