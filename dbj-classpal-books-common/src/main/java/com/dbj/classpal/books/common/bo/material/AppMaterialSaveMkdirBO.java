package com.dbj.classpal.books.common.bo.material;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialQueryBO
 * Date:     2025-04-10 09:37:13
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class AppMaterialSaveMkdirBO implements Serializable {
    @Schema(description = "父节点ID",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "所在文件夹不能为空")
    private Integer parentId;

    @Schema(description = "文件夹名称",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "文件夹名称不能为空")
    @Size(min = 1, max = 100,message = "文件夹名称范围在1~100字符内")
    private String materialName;
}
