package com.dbj.classpal.books.common.bo.album;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumReNameBO
 * Date:     2025-04-11 16:46:18
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class AppAlbumMenusReNameBO implements Serializable {

    @Schema(description = "主键id",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键id不能为空")
    private Integer id;

    @Schema(description = "专辑类型 1音频 2视频",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "专辑类型不能为空")
    private Integer albumType;

    @Schema(description = "分类名称",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "分类名称不能为空")
    @Size(min = 1, max = 100,message = "分类名称范围在1~100字符内")
    private String albumMenuName;
}
