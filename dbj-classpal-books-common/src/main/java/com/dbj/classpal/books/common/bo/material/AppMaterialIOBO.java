package com.dbj.classpal.books.common.bo.material;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialMoveBO
 * Date:     2025-04-10 10:28:06
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class AppMaterialIOBO implements Serializable {

    @Schema(description = "资源id",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "资源id不能为空")
    private Integer id;

    @Schema(description = "所在文件夹",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "所在文件夹不能为空")
    private Integer parentId;

    @Schema(description = "目标文件夹",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "目标文件夹不能为空")
    private Integer aimParentId;
}
