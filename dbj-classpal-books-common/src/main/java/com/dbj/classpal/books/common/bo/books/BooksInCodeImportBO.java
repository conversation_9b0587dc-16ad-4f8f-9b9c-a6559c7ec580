package com.dbj.classpal.books.common.bo.books;

import com.dbj.classpal.framework.utils.annotation.ExportExcel;
import com.dbj.classpal.framework.utils.annotation.ExportExcelColumn;
import com.dbj.classpal.framework.utils.annotation.ImportExcelColumn;
import com.dbj.classpal.framework.utils.dto.CommonExcelBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/24 08:45:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
@ExportExcel(sheetName = "书内码")
public class BooksInCodeImportBO extends CommonExcelBO implements Serializable {

    @Schema(description ="册数标题")
    @ImportExcelColumn(value = "册数标题",required = true)
    @ExportExcelColumn(name = "册数标题")
    public String randName;
    @Schema(description ="册数ID")
    @ImportExcelColumn(value = "册数ID",required = true)
    @ExportExcelColumn(name = "册数ID")
    public Integer randId;

    @Schema(description ="页面标题")
    @ImportExcelColumn(value = "页面标题",required = true)
    @ExportExcelColumn(name = "页面标题")
    public String contentName;

    @Schema(description ="页面ID")
    @ImportExcelColumn(value = "页面ID",required = true)
    @ExportExcelColumn(name = "页面ID")
    public Integer contentId;

    @Schema(description ="强调连接")
    @ImportExcelColumn(value = "强调连接")
    @ExportExcelColumn(name = "强调连接")
    public String forcePromotionUrl;

    @Schema(description ="兼容连接1")
    @ImportExcelColumn(value = "兼容连接1")
    @ExportExcelColumn(name = "兼容连接1")
    public String compatibilityUrl1;

    @Schema(description ="兼容连接2")
    @ImportExcelColumn(value = "兼容连接2")
    @ExportExcelColumn(name = "兼容连接2")
    public String compatibilityUrl2;

    @Schema(description ="兼容连接3")
    @ImportExcelColumn(value = "兼容连接3")
    @ExportExcelColumn(name = "兼容连接3")
    public String compatibilityUrl3;

}
