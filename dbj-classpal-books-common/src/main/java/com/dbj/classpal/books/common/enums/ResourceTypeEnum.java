package com.dbj.classpal.books.common.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 单书资源类型枚举
 */
@Getter
@AllArgsConstructor
public enum ResourceTypeEnum {

    WATERMARKED_PDF(1, "加水印PDF"),
    PAGE_IMAGE(2, "切图图片"),
    COVER_IMAGE(3, "封面图片");

    private final Integer code;
    private final String desc;

    @JsonCreator
    public static ResourceTypeEnum fromValue(Integer code) {
        for (ResourceTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的ResourceTypeEnum代码: " + code);
    }

    public static ResourceTypeEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ResourceTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 