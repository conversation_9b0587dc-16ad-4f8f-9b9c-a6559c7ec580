package com.dbj.classpal.books.common.bo.evaluation;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppUserPaperEvaluationQueryBO
 * Date:     2025-05-19 15:19:02
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class AdminUserPaperEvaluationQueryBO implements Serializable {

    @Schema(name = "评测表id")
    private Integer id;

    @Schema(name = "学员")
    private Integer userId;

    @Schema(name = "学员UID")
    private String uid;

    @Schema(name = "学员年级ids")
    private List<String> gradeIds;

    @Schema(name = "地区")
    private String region;

}
