package com.dbj.classpal.books.common.constant;

/**
 * Redis Key 常量
 */
public interface RedisKeyConstants {

    /**
     * 功能灰度发布 key
     * 参数: featureCode
     */
    String FEATURE_GRAY_KEY = "app:feature:gray:%s";


    public final static String OSS_TOKEN = "dbj:classpal:ossToken";



    public final static String SHORT_URL = "dbj:classpal:shortUrl:{0}";

    /**
     * 用户广告展示时间
     * 参数: appUserId, relationType, advertisementId
     */
    String DBJ_CLASSPAL_ADVERTISEMENT_SHOW_TIME = "dbj:classpal:advertisement:appUserId:{0}:relationType:{1}:advertisementId:{2}";

    /**
     * 广告数据缓存
     */
    String DBJ_CLASSPAL_ADVERTISEMENT_TYPE_CACHE_KEY = "dbj:classpal:advertisement:advertisementType:{0}";

    public final static String DBJ_BOOKS_CATEGORY_SYNC = "dbj:classpal:books:category:sync";

    public final static String DBJ_ANCIENT_POEM_RECITE_STANDARD_TYPE_CACHE_KEY = "dbj:classpal:books:ANCIENT:POEM:RECITE:STANDARD:TYPE:{0}:TOTALSCORE:{1}";

    /**
     * 汉语拼音分类数据缓存
     */
    String DBJ_CLASSPAL_PINYIN_CLASSIFY_CACHE_KEY = "dbj:classpal:pinyin:classify";

    /**
     * 汉语拼音数据缓存
     */
    String DBJ_CLASSPAL_PINYIN_CLASSIFY_DETAIL_CACHE_KEY = "dbj:classpal:pinyin:classify:{0}";

    /**
     * 基础配置名称缓存
     * 参数: configId
     */
    String DBJ_CLASSPAL_BASIC_CONFIG_NAME_CACHE_KEY = "dbj:classpal:books:config:name:{0}";


    /**
     * 基础配置全量缓存
     * 存储所有basic_config数据的Map
     */
    String DBJ_CLASSPAL_BASIC_CONFIG_ALL_CACHE_KEY = "dbj:classpal:books:config:all";

}