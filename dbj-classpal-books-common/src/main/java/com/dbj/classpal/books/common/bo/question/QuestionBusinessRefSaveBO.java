package com.dbj.classpal.books.common.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: QuestionBusinessRefBO
 * Date:     2025-05-19 11:12:52
 * Description: 表名： ,描述： 表
 */
@Data
@Schema(description = "查询业务下关联题目列表BO")
public class QuestionBusinessRefSaveBO implements Serializable {

    @Schema(description = "题目id列表",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "题目列表不能为空")
    private List<Integer> questionIdList;

    @Schema(description = "业务id",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "业务id不能为空")
    private Integer businessId;

    @Schema(description = "业务类型 10内容管理-评测-评测项",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;

}
