package com.dbj.classpal.books.common.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 查询用户错题列表参数
 */
@Data
@Schema(description = "查询用户错题列表参数")
public class QueryUserWrongQuestionsBO implements Serializable {


    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private Integer appUserId;

    /**
     * 学科ID（可选）
     */
    @Schema(description = "学科ID（可选）")
    private Integer subjectId;
} 