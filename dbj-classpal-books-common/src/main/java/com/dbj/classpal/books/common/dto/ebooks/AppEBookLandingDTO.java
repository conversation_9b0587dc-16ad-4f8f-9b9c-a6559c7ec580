package com.dbj.classpal.books.common.dto.ebooks;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 单书数据传输对象
 */
@Data
@Schema(description = "单书DTO")
public class AppEBookLandingDTO implements Serializable {

    @Schema(description = "单书ID")
    private Integer id;

    @Schema(description = "样书标题")
    private String bookTitle;

    @Schema(description = "书籍编码")
    private String bookCode;

    @Schema(description = "分类名称集合，用逗号分隔")
    private String categoryNames;

    @Schema(description = "分类ID集合")
    private List<Integer> categoryIds;

    @Schema(description = "适用年级")
    private List<Integer> applicableGrades;

    @Schema(description = "适用年级,部分年级用逗号分隔")
    private String applicableGradeNames;

    @Schema(description = "封面URL")
    private String coverUrl;

    @Schema(description = "封面URLName")
    private String coverUrlName;

    @Schema(description = "简介")
    private String blurb;

    @Schema(description = "文件URL")
    private String fileUrl;

    @Schema(description = "加水印后的文件URL")
    private String watermarkedFileUrl;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "文件大小")
    private BigDecimal fileSize;

    @Schema(description = "文件md5")
    private String fileMd5;

    @Schema(description = "文件状态：0-待处理，1-处理中，2-已完成，3-处理失败")
    private Integer fileStatus;
    
    @Schema(description = "文件状态描述")
    private String fileStatusDesc;

    @Schema(description = "错误信息")
    private String fileErrMsg;

    @Schema(description = "水印模板ID")
    private Integer watermarkId;

    @Schema(description = "学科ID")
    private Integer subjectId;
    
    @Schema(description = "学科名称")
    private String subjectName;
    
    @Schema(description = "阶段ID")
    private Integer stageId;
    
    @Schema(description = "阶段名称")
    private String stageName;
    
    @Schema(description = "教材版本ID")
    private Integer textbookVersionId;
    
    @Schema(description = "教材版本名称")
    private String textbookVersionName;

    @Schema(description = "加水印PDF资源")
    private AppEBookResourceDTO watermarkedPdf;

    @Schema(description = "启用状态：0-禁用，1-启用")
    private Integer launchStatus;

    @Schema(description = "允许下载：0-否，1-是")
    private Integer allowDownload;

    @Schema(description = "排序序号")
    private Integer sortNum;

    @Schema(description = "分享链接")
    private String shareUrl;

    @Schema(description = "分享二维码")
    private String shareQrCode;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
} 