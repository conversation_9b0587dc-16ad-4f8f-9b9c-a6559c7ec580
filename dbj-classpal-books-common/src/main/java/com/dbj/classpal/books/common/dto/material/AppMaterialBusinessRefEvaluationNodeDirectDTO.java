package com.dbj.classpal.books.common.dto.material;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialBusinessRefEvaluationNodeDirectDTO
 * Date:     2025-04-14 08:54:29
 * Description: 表名： ,描述： 表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@Tag(name = "内容管理-评测-评测项引用素材跳转DTO")
public class AppMaterialBusinessRefEvaluationNodeDirectDTO implements Serializable {
    @Schema(description = "评测表id")
    private Integer evaluationId;

    @Schema(description = "评测项id")
    private Integer businessId;

    @Schema(description = "业务类型 16内容管理-评测-评测项")
    private Integer businessType;

    @Schema(description = "评测表名称")
    private String evaluationName;
}
