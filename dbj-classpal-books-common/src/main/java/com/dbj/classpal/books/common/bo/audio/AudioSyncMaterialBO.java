package com.dbj.classpal.books.common.bo.audio;

import com.dbj.classpal.framework.mq.bo.MaterialTransParamsBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 接收素材中心同步后的结果
 * <AUTHOR>
 * @date 2025-06-30
 **/
@Data
public class AudioSyncMaterialBO extends MaterialTransParamsBO {


    @Schema(description = "音频简介id")
    private Integer audioIntroId;

    @Schema(description = "素材id")
    private Integer appMaterialId;

    @Schema(description = "版本")
    private Integer version;
}
