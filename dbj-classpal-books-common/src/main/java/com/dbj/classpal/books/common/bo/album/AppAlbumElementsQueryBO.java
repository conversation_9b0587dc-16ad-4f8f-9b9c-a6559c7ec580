package com.dbj.classpal.books.common.bo.album;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumElementsQueryBO
 * Date:     2025-04-15 13:35:46
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class AppAlbumElementsQueryBO implements Serializable {
    @Schema(description = "专辑分类ID",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "专辑分类ID不能为空")
    private Integer appAlbumMenuId;

    @Schema(description = "专辑类型 1音频 2视频",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "专辑类型不能为空")
    private Integer albumType;

    @Schema(description = "专辑标题")
    private String albumTitle;

    @Schema(description = "上架状态  0下架 1上架")
    private Integer albumStatus;

    @Schema(description = "是否隐藏  0隐藏 1显示 ")
    private Integer albumVisible;

    @Schema(description = "权益标签")
    private Set<Integer> vipRightsTagIds;

    @Schema(description = "售卖方式")
    private Integer salesMode;

    @Schema(hidden = true)
    private String productType;

    @Schema(hidden = true)
    private Set<Integer> ids;
}
