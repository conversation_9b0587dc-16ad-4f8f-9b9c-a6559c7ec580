package com.dbj.classpal.books.common.bo.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;

/**
 * 查询试卷题目空位作答结果参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "查询试卷题目空位作答结果参数")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryBlankResultBO extends BasicPaperBO implements Serializable {

    @Schema(description = "试卷ID")
    private Integer paperId;

    @Schema(description = "用户ID")
    private Integer appUserId;

    @Schema(description = "题目ID")
    private Integer questionId;

}