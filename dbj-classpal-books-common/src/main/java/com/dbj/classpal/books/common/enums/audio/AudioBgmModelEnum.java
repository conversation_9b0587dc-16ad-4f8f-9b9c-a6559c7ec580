package com.dbj.classpal.books.common.enums.audio;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 预置音乐类型枚举
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum AudioBgmModelEnum {
    LOOP(1,"循环播放"),
    ONCE(2,"播放一次");

    private Integer code;
    private String name;

    public static AudioBgmModelEnum getByCode(Integer code) {
        for (AudioBgmModelEnum value : AudioBgmModelEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
