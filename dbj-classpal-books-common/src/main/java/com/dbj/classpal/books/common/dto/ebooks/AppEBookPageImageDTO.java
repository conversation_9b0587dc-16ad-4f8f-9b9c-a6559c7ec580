package com.dbj.classpal.books.common.dto.ebooks;

import lombok.Data;

import java.io.Serializable;

/**
 * 单书图片资源DTO
 */
@Data
public class AppEBookPageImageDTO implements Serializable {
    
    /**
     * 资源ID
     */
    private Integer id;
    
    /**
     * 单书ID
     */
    private Integer bookId;
    
    /**
     * 页码
     */
    private Integer pageNum;
    
    /**
     * 资源URL
     */
    private String resourceUrl;
    
    /**
     * 文件名称
     */
    private String fileName;
    
    /**
     * 上一页页码，如果不存在则为null
     */
    private Integer prevPageNum;
    
    /**
     * 下一页页码，如果不存在则为null
     */
    private Integer nextPageNum;
    
    /**
     * 总页数
     */
    private Integer totalPages;
} 