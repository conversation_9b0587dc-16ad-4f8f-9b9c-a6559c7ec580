package com.dbj.classpal.books.common.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 完形填空错题详情DTO
 */
@Data
@Schema(description = "完形填空错题详情DTO")
public class BlankWrongDetailDTO implements Serializable {

    /**
     * ID
     */
    @Schema(description = "ID")
    private Integer id;

    /**
     * 填空题空位序号
     */
    @Schema(description = "填空题空位序号")
    private Integer blankIndex;

    /**
     * 空位区域ID
     */
    @Schema(description = "空位区域ID")
    private Integer blankAreaId;

    /**
     * 用户错误答案
     */
    @Schema(description = "用户错误答案")
    private String userAnswer;

    /**
     * 用户选择的答案ID（多个英文逗号隔开）
     */
    @Schema(description = "用户选择的答案ID（多个英文逗号隔开）")
    private String answerIds;
} 