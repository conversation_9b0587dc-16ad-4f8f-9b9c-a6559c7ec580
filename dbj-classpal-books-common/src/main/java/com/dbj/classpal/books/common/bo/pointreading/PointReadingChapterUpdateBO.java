package com.dbj.classpal.books.common.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Data;

/**
 * 点读书页面更新BO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingPageUpdateBO", description = "点读书页面更新BO")
public class PointReadingChapterUpdateBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "页面ID")
    @NotNull(message = "页面ID不能为空")
    private Integer id;

    @Schema(description = "所属目录ID")
    @NotNull(message = "所属目录ID不能为空")
    private Integer menuId;

    @Schema(description = "页面名称")
    @NotBlank(message = "页面名称不能为空")
    private String name;

    @Schema(description = "页面图片ID")
    private String imageId;

    @Schema(description = "页面图片URL")
    private String imageUrl;

    @Schema(description = "页面图片名称")
    private String imageName;

    @Schema(description = "图片来源：1-点读书 2-素材中心")
    private Integer imageSourceType;

    @Schema(description = "热点数量")
    private Integer hotspotCount;

    @Schema(description = "关联媒体数量")
    private Integer mediaCount;

    @Schema(description = "关联媒体类型：10-音频")
    private Integer mediaType;

    @Schema(description = "排序")
    private Integer sortNum;

    @Schema(description = "状态：0-停用 1-正常")
    private Integer status;
}
