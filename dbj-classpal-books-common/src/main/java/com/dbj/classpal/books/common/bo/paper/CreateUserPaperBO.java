package com.dbj.classpal.books.common.bo.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 创建用户试卷参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "创建用户试卷参数")
public class CreateUserPaperBO extends BasicPaperBO implements Serializable {

    @Schema(description = "用户ID")
    private Integer appUserId;

    @Schema(description = "试卷名称")
    private String paperName;

    @Schema(description = "是否提交 0-未提交 1-已提交")
    private Integer isSubmit;

    /**
     * 题目顺序，逗号分隔的题目ID
     */
    @Schema(description = "题目顺序，逗号分隔的题目ID")
    private String questionOrder;
} 