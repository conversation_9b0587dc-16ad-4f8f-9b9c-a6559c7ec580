package com.dbj.classpal.books.common.enums.file;

import com.dbj.classpal.books.common.bo.books.BooksInCodeImportBO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ExcelFileStrategyEnum
 * @date 2023-10-27 11:02
 **/
@AllArgsConstructor
@Getter
public enum ExcelFileStrategyEnum {
    /**
     * 菜单导入
     */
    SYS_MENU_EXCEL_FILE("booksInCodeExcelFileStrategy", "链接导入", null, BooksInCodeImportBO.class);

    private String handler;

    private String value;

    private String routingKey;

    private Class<?> importClass;

    public static ExcelFileStrategyEnum getEnum(String code){

        if(StringUtils.isEmpty(code)){
            return null;
        }
        for(ExcelFileStrategyEnum strategyEnum : ExcelFileStrategyEnum.values()){
            if(StringUtils.equals(strategyEnum.getHandler(),code)){
                return strategyEnum;
            }
        }
        return null;
    }

}
