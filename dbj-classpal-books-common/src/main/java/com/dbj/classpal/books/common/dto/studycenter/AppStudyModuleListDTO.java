package com.dbj.classpal.books.common.dto.studycenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "学习模块")
public class AppStudyModuleListDTO {
    @Schema(description = "主键ID")
    private Integer id;
    @Schema(description = "模块标题")
    private String title;
    @Schema(description = "模块简介")
    private String description;
    @Schema(description = "Logo图片URL")
    private String logoUrl;
    @Schema(description = "Logo图片name")
    private String logoName;
    @Schema(description = "背景图片")
    private String background;
    @Schema(description = "背景图片名称")
    private String backgroundName;
    @Schema(description = "栅格数")
    private Integer gridCount;
    @Schema(description = "所属分类")
    private String belongCategory;
    @Schema(description = "模块类型1-评测，2-音频专辑 3 -视频专辑 4-功能 5- 题库")
    private Integer moduleType;
    @Schema(description = "排序权重")
    private Integer sortNum;
    @Schema(description = "是否显示 1-显示 0-隐藏")
    private Integer isVisible;
    @Schema(description = "上架状态 0-下架 1-上架")
    private Integer publishStatus;
    @Schema(description = "适用年级")
    private String applicableGrades;
    @Schema(description = "状态 0-禁用 1-启用")
    private Integer status;
} 