package com.dbj.classpal.books.common.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 书城保存业务对象
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
@Data
@Schema(description = "书城保存参数BO")
public class AppEBookstoreUpdateBO implements Serializable {

    @Schema(description = "书城ID，更新时必传")
    private Integer id;

    @Schema(description = "书城名称")
    private String storeTitle;

    @Schema(description = "封面URL")
    private String coverUrl;

    @Schema(description = "封面URL名称")
    private String coverUrlName;

    @Schema(description = "简介")
    private String blurb;

    @Schema(description = "是否隐藏：0-否，1-是")
    private Integer isHide;

    @Schema(description = "启用状态：0-禁用，1-启用")
    private Integer launchStatus;

    @Schema(description = "允许下载：0-否，1-是")
    @NotNull(message = "允许下载不能为空")
    private Integer allowDownload;

    @Schema(description = "排序序号")
    private Integer sortNum;
    
    @Schema(description = "书架ID列表")
    private List<Integer> shelfIds;
} 