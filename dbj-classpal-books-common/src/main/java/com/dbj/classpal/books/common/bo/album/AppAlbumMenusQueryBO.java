package com.dbj.classpal.books.common.bo.album;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumMenusQueryBO
 * Date:     2025-04-10 09:37:13
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class AppAlbumMenusQueryBO implements Serializable {

    @Schema(description = "专辑类型 1音频 2视频")
    private Integer albumType;
}
