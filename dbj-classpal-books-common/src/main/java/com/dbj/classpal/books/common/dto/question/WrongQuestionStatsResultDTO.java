package com.dbj.classpal.books.common.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema(description = "错题统计结果")
@Data
public class WrongQuestionStatsResultDTO implements Serializable {


    /**
     * 总错题数量
     */
    @Schema(description = "总错题数量")
    private Integer totalCount;

    /**
     * 按题目类型统计
     */
    //@Schema(description = "按题目类型统计")
    //private List<TypeWrongStatsDTO> typeStats;

    /**
     * 按学科统计
     */
    @Schema(description = "按学科统计")
    private List<SubjectWrongStatsDTO> subjectStats;

    /**
     * 按来源类型统计
     */
    //@Schema(description = "按来源类型统计")
    //private List<SourceWrongStatsDTO> sourceStats;
}