package com.dbj.classpal.books.common.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 错题详情DTO
 */
@Data
@Schema(description = "错题详情DTO")
public class WrongQuestionDetailDTO implements Serializable {

    /**
     * 问题id
     */
    @Schema(description = "问题id")
    private Integer questionId;

    /**
     * 题目内容
     */
    @Schema(description = "题目内容")
    private String questionContent;

    /**
     * 题目类型
     */
    @Schema(description = "题目类型")
    private String questionType;

    /**
     * 学科ID
     */
    @Schema(description = "学科ID")
    private Integer subjectId;

    /**
     * 学科名称
     */
    @Schema(description = "学科名称")
    private String subjectName;

    /**
     * 来源类型 1:用户答题 2:用户评测
     */
    @Schema(description = "来源类型 1:用户答题 2:用户评测")
    private Integer sourceType;

    /**
     * 来源ID
     */
    @Schema(description = "来源ID")
    private Integer sourceId;

    /**
     * 用户错误答案（仅普通题使用）
     */
    @Schema(description = "用户错误答案（仅普通题使用）")
    private String userAnswer;

    /**
     * 用户选择的答案ID（仅普通题使用，多个英文逗号隔开）
     */
    @Schema(description = "用户选择的答案ID（仅普通题使用，多个英文逗号隔开）")
    private String answerIds;

    /**
     * 完形填空空位详情列表（仅完形填空题使用）
     */
    @Schema(description = "完形填空空位详情列表（仅完形填空题使用）")
    private List<BlankWrongDetailDTO> blankDetails;
}