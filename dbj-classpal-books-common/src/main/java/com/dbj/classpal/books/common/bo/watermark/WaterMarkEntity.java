package com.dbj.classpal.books.common.bo.watermark;

import com.dbj.classpal.framework.mq.entity.RabbitmqEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WaterMarkEntity extends RabbitmqEntity implements Serializable {

    private String requestId;

    private String taskId;

    private boolean success;

    private boolean onlyExtraCover;

    @Schema(name = "业务id")
    private Integer businessId;

    @Schema(name = "业务key")
    private String businessKey;

    @Schema(name = "水印处理业务类型 1仅添加水印 2仅进行切图 3添加水印后进行切图导出")
    private Integer waterMarkBusinessType;


    //下载参数
    @Schema(name = "pdf文件Oss路径")
    private String pdfOssPath;
    @Schema(name = "水印Oss路径")
    private String waterMarkOssPath;


    @Schema(name = "pdf文件名称")
    private String pdfName;
    @Schema(name = "水印名称")
    private String waterMarkName;


    //异常参数
    @Schema(name = "是否异常")
    private Boolean isError;
    @Schema(name = "异常信息")
    private String errorMsg;


    //输入参数
    @Schema(name = "pdf临时下载路径")
    private String pdfLocalPath;
    @Schema(name = "水印临时下载路径")
    private String waterMarkLocalPath;


    //添加水印输入参数
    @Schema(name = "水印大小倍率")
    private Float scale;
    @Schema(name = "水印透明度")
    private Float transparency;
    @Schema(name = "水印旋转角度")
    private Float rotationAngle;
    @Schema(name = "水印横向间距")
    private Float horizontalSpacing;
    @Schema(name = "水印纵向间距")
    private Float verticalSpacing;
    @Schema(name = "需要添加水印的下标 从1开始")
    private List<Integer>addPageNums;
    @Schema(name = "0不忽略首尾水印 1忽略首尾水印")
    private Integer ignoreType;

    //输出参数
    @Schema(name = "pdf的本地添加水印路径")
    private String pdfLocalWatermarkPath;
    @Schema(name = "pdf的oss上传路径")
    private String pdfOutPutOssPath;
    @Schema(name = "临时存储本地的图片输出列表")
    private List<String> waterMarkLocalPathList;
    @Schema(name = "上传到oss的图片输出列表")
    private List<String> waterMarkOssPathList;


}
