package com.dbj.classpal.books.common.dto.paper;

import com.dbj.classpal.books.common.dto.question.QuestionAnswerDTO;
import com.dbj.classpal.books.common.dto.question.QuestionMediaDTO;
import com.dbj.classpal.books.common.dto.question.QuestionRecognitionDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 题目结果DTO
 */
@Data
@Schema(description = "题目结果")
public class QuestionResultDTO implements Serializable {

    /**
     * 题目ID
     */
    @Schema(description = "题目ID")
    private Integer questionId;

    /**
     * 题目标题
     */
    @Schema(description = "题目标题")
    private String title;

    /**
     * 题目类型
     */
    @Schema(description = "题目类型")
    private Integer type;

    /**
     * 题目类型名称
     */
    @Schema(description = "题目类型名称")
    private String typeName;

    /**
     * 媒体类型
     */
    @Schema(description = "媒体文件类型 1-文本 2-图片,3-音频,4-视频")
    private Integer mediaType;

    /**
     * 选项类型
     */
    @Schema(description = "选项类型 1-文本 2-图片,3-音频,4-视频")
    private Integer optionType;
    /**
     * 媒体文件url
     */
    @Schema(description = "媒体文件url")
    private List<QuestionMediaDTO> mediaUrl;

    @Schema(description = "辅助识图")
    private List<QuestionRecognitionDTO> aidedRecognitionUrl;

    /**
     * 用户答案
     */
    @Schema(description = "用户答案")
    private String userAnswer;

    /**
     * 用户选择的答案ID
     */
    @Schema(description = "用户选择的答案ID")
    private String userAnswerIds;

    /**
     * 正确答案
     */
    @Schema(description = "正确答案")
    private String correctAnswer;

    /**
     * 正确答案ID
     */
    @Schema(description = "正确答案ID")
    private String correctAnswerIds;

    /**
     * 答案选项列表
     */
    @Schema(description = "答案选项列表")
    private List<QuestionAnswerDTO> options;

    /**
     * 解析
     */
    @Schema(description = "解析")
    private String analyzes;

    /**
     * 结果 1 正确 0 错误
     */
    @Schema(description = "结果 1 正确 0 错误")
    private Integer result;

    /**
     * 题目排序
     */
    @Schema(description = "题目排序")
    private Integer questionSort;

    /**
     * 完形填空结果列表
     */
    @Schema(description = "完形填空结果列表")
    private List<BlankResultDTO> blankResults;
} 