package com.dbj.classpal.books.common.bo.product;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/7/31 17:08
 */
@Data
@Accessors(chain = true)
public class ProductSalesConfigSaveBO {

    private Integer productId;
    private String productType;
    private Integer trialCount;
    private Boolean salesModeStatus;
    private Integer salesMode;
    private BigDecimal salesPrice;
    private BigDecimal originalPrice;
    private Boolean listingStatus;
    private Boolean isHide;
}
