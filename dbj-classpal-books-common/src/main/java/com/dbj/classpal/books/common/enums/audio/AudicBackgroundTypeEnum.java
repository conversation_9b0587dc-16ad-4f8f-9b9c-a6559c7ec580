package com.dbj.classpal.books.common.enums.audio;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 预置音乐类型枚举
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum AudicBackgroundTypeEnum {
    BACKGROUND(1,"预置背景音"),
    DEFINITION(2,"自定义");

    private Integer code;
    private String name;

    public static AudicBackgroundTypeEnum getByCode(Integer code) {
        for (AudicBackgroundTypeEnum value : AudicBackgroundTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
