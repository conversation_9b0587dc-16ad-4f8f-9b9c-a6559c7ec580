package com.dbj.classpal.books.common.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@Schema(description = "题目DTO")
public class QuestionBusinessRefDTO implements Serializable {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Integer id;

    /**
     * 题目
     */
    @Schema(description = "题目id")
    private Integer questionId;

    /**
     * 题目
     */
    @Schema(description = "题目")
    private String title;

    /**
     * 题目类型 0-单选题 1-多选题 2-判断题 3-填空题 4-排序题，5-完形填空
     */
    @Schema(description = "题目类型 0-单选题 1-多选题 2-判断题 3-填空题 4-排序题，5-完形填空")
    private Integer type;

    /**
     * 题目类型名称
     */
    @Schema(description = "题目类型名称")
    private String typeName;

    /**
     * 学科ID
     */
    @Schema(description = "学科ID")
    private Integer subjectId;

    /**
     * 选项类型
     */
    @Schema(description = "1-文本 2-图片 3-音频 4-视频")
    private Integer mediaType;

    /**
     * 学科名称
     */
    @Schema(description = "学科名称")
    private String subjectName;

    @Schema(description = "题目分类id")
    private Integer questionCategoryId;

    @Schema(description = "排序")
    private Integer orderNum;
}