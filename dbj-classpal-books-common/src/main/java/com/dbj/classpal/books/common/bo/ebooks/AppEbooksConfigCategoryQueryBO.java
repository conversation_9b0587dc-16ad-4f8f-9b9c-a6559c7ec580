package com.dbj.classpal.books.common.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="电子样书-样书配置-图书分类-查询BO", description="电子样书-样书配置-图书分类-查询BO")
public class AppEbooksConfigCategoryQueryBO {

    @Schema(description = "分类名称")
    private String name;

}
