package com.dbj.classpal.books.common.bo.album;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumElementsBusinessRefQueryBO
 * Date:     2025-04-15 13:35:46
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
@Tag(name = "专辑查询BO")
public class AppAlbumElementsBusinessRefSaveBO {
    @Schema(description = "素材库id")
    private Integer appAlbumId;

    @Schema(description = "业务id")
    private Integer businessId;

    @Schema(description = "业务类型 1内容管理-音频专辑 2内容管理-视频专辑 3图书管理-图书资源 4图书管理-题库 5图书管理-音频专辑 6图书管理-视频专辑")
    private Integer businessType;

    @Schema(description = "排序")
    private Integer orderNum;
}
