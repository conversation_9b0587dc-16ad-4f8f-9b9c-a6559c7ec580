package com.dbj.classpal.books.common.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 获取分享信息业务参数BO
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@Schema(description = "获取分享信息请求参数")
public class GetShareInfoBO implements Serializable {

    @Schema(description = "业务类型：b5-单书，b6-书架，b7-书城")
    private String businessType;

    @Schema(description = "业务ID")
    private Integer businessId;

    @Schema(description = "是否强制重新生成（覆盖已有的分享链接）")
    private Boolean forceRegenerate = false;
}
