package com.dbj.classpal.books.common.bo.album;



import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialCommonIdBO
 * Date:     2025-04-14 10:22:40
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class AppAlbumElementsBusinessRefQueryCommonBO implements Serializable {

    @Schema(description = "专辑id")
    private Integer albumId;

    @Schema(description = "业务id")
    private Integer businessId;

    @Schema(description = "业务id集合")
    private Set<Integer> businessIds;

    @Schema(description = "业务类型 1内容管理-音频专辑 2内容管理-视频专辑 3图书管理-图书资源 4图书管理-题库 5图书管理-音频专辑 6图书管理-视频专辑 7题目-媒体文件 8题目-辅助识图 9题目-答案")
    private Integer businessType;

    @Schema(description = "业务名称")
    private String businessName;

    @Schema(description = "专辑类型 1音频专辑 2视频专辑")
    private Integer albumType;
}
