package com.dbj.classpal.books.common.bo.studycenter;

import com.dbj.classpal.books.common.dto.studycenter.AppStudyModuleQuestionResourceBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "学习模块")
public class AppStudyModuleCreateBO {
    @Schema(description = "模块标题")
    private String title;
    @Schema(description = "模块简介")
    private String description;
    @Schema(description = "Logo图片URL")
    private String logoUrl;
    @Schema(description = "Logo图片名称")
    private String logoName;
    @Schema(description = "背景图片")
    private String background;
    @Schema(description = "背景图片名称")
    private String backgroundName;
    @Schema(description = "栅格数")
    private Integer gridCount;
    @Schema(description = "所属分类")
    private Integer belongCategoryId;
    @Schema(description = "模块类型")
    private Integer moduleType;
    @Schema(description = "排序权重")
    private Integer sortNum;
    @Schema(description = "是否显示 1-显示 0-隐藏")
    private Integer isVisible;
    @Schema(description = "上架状态 0-下架 1-上架")
    private Integer publishStatus;
    @Schema(description = "适用年级，ALL表示全部，部分年级用逗号分隔ID")
    private String applicableGrades;
    @Schema(description = "资源ID列表（功能/音频/视频/评测等）")
    private List<AppStudyModuleQuestionResourceBO> resourceList;
    @Schema(description = "答题设置，仅题目类型时使用")
    private AppStudyModuleQuestionExtCreateBO questionExt;
} 