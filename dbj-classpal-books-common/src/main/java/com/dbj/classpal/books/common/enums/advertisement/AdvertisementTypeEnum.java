package com.dbj.classpal.books.common.enums.advertisement;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 广告类型字典项的值
 * <AUTHOR>
 * @since 2025-04-19 10:52
 */
@Getter
@AllArgsConstructor
public enum AdvertisementTypeEnum {
    // 瓷片区Banner、瓷片区右上、瓷片区右下、瓷片列表、首页弹窗
    CERAMIC_BANNER(1, "瓷片区Banner"),
    ceramic_top_right(2, "瓷片区右上"),
    ceramic_bottom_right(3, "瓷片区右下"),
    ceramic_list(4, "瓷片列表"),
    HOME_POPUP(5, "首页弹窗"),
    ;

    private final Integer type;
    private final String name;
}
