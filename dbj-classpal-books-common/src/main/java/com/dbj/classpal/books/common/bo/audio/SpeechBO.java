package com.dbj.classpal.books.common.bo.audio;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SpeechBO {

    /**
     * 音频简介id
     */
    private Integer audioIntroId;
    /**
     * 音频文本id
     */
    private Integer audioContextInfoId;
    /**
     * 语调，取值范围：-500~500
     */
    private Integer pitchRate;
    /**
     * 语速，取值范围：-500~500
     */
    private Integer speechRate;
    /**
     * 发音人
     */
    private String voice;
    /**
     * 音频长文本
     */
    private String longText;
    /**
     * 音量，取值范围：0~100
     */
    private Integer volume;
}
