package com.dbj.classpal.books.common.bo.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 保存试卷题目空位作答结果参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "保存试卷题目空位作答结果参数")
public class SaveBlankResultBO extends BasicPaperBO implements Serializable {

    @Schema(description = "试卷ID")
    private Integer paperId;

    @Schema(description = "用户ID")
    private Integer appUserId;

    @Schema(description = "题目ID")
    private Integer questionId;

    @Schema(description = "空位序号")
    private Integer blankIndex;
    /**
     * 用户选择的答案ID列表
     */
    @Schema(description = "用户选择的答案ID列表")
    private String answerIds;
    @Schema(description = "空位id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "空位id")
    private Integer blankAreaId;
//    /**
//     * 用户答案
//     */
//    @Schema(description = "用户答案")
//    private String userAnswer;

    @Schema(description = "作答结果")
    private Integer result;
} 