package com.dbj.classpal.books.common.constant;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: QueueConstant
 * Date:     2025-04-08 10:57:27
 * Description: 表名： ,描述： 表
 */
public class QueueConstant {

    /**
     * 素材中心文件上传消息队列(文件)
     */
    public final static String CLASSPAL_FILE_SERVICE_QUEUE_BOOKS_MATERIAL = "classpal_file_service_queue_books_material";

    /**
     * 素材中心文件上传消息队列(文件夹)
     */
    public final static String CLASSPAL_FILE_SERVICE_QUEUE_BOOKS_MATERIAL_DIRECTORY = "classpal_file_service_queue_books_material_directory";


    /**
     * books的导入文件处理
     *
     */
    public final static String CLASSPAL_FILE_SERVICE_QUEUE_BOOKS = "classpal_file_service_queue_books";

    /**
     * books导出的文件处理
     *
     */
    public final static String CLASSPAL_EXPORT_FILE_SERVICE_QUEUE_BOOKS = "classpal_export_file_service_queue_books";



    /**
     * 统计接口的监听
     */
    public final static String BOOK_CATEGORY_SYNC_QUEUE = "book_category_sync_queue";

    /**
     * 音频同步到素材中心队列
     */
    public final static String AUDIO_SYNC_MATERIAL_QUEUE = "audio_sync_material_queue";
    /**
     * 音频合成队列
     */
    public final static String AUDIO_PRODUCTION_QUEUE = "audio_production_queue";
}
