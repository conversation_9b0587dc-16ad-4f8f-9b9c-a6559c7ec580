package com.dbj.classpal.books.common.enums.audio;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description 多情感枚举
 */
@Getter
@AllArgsConstructor
public enum AudioEmotionEnum {
    ZHIFENG_MO("zhifeng_emo", Arrays.asList("angry", "fear", "happy", "neutral", "sad", "surprise")),
    <PERSON>HIBING_<PERSON><PERSON>("zhibing_emo", Arrays.asList("angry", "fear", "happy", "neutral", "sad", "surprise")),
    <PERSON>HIMIA<PERSON>_<PERSON>O("zhimiao_emo", Arrays.asList("serious", "sad", "disgust", "jealousy", "embarrassed", "happy", "fear", "surprise", "neutral", "frustrated", "affectionate", "gentle", "angry", "newscast", "customer-service", "story", "living")),
    ZHIMI_MO("zhimi_emo", Arrays.asList("angry", "fear", "happy", "hate", "neutral", "sad", "surprise")),
    <PERSON>HIYAN_MO("zhiyan_emo", Arrays.asList("neutral", "happy", "angry", "sad", "fear", "hate", "surprise", "arousal")),
    ZHIBEI_MO("zhibei_emo", Arrays.asList("neutral", "happy", "angry", "sad", "fear", "hate", "surprise")),
    ZHITIAN_MO("zhitian_emo", Arrays.asList("neutral", "happy", "angry", "sad", "fear", "hate", "surprise"));

    private final String name;
    private final List<String> emotions;

    public static AudioEmotionEnum getByCode(String name) {
        for (AudioEmotionEnum value : AudioEmotionEnum.values()) {
            if (value.getName().equals(name)) {
                return value;
            }
        }
        return null;
    }
}
