package com.dbj.classpal.books.common.dto.studycenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "学习进度")
public class AppStudyModuleProgressListDTO {
    @Schema(description = "主键ID")
    private Integer id;
    @Schema(description = "学习模块ID")
    private Integer moduleId;
    @Schema(description = "用户ID")
    private Integer userId;
    @Schema(description = "模块类型1-评测，2-音频专辑 3 -视频专辑 4-功能 5- 题库")
    private Integer moduleType;
    @Schema(description = "模块标题")
    private String title;
    @Schema(description = "模块简介")
    private String description;
    @Schema(description = "Logo图片URL")
    private String logoUrl;
    @Schema(description = "Logo图片name")
    private String logoName;
    @Schema(description = "背景图片")
    private String background;
    @Schema(description = "背景图片名称")
    private String backgroundName;
    @Schema(description = "栅格数")
    private Integer gridCount;
    @Schema(description = "最后学习时间")
    private LocalDateTime lastLearnTime;
    @Schema(description = "状态 0-禁用 1-启用")
    private Integer status;
    @Schema(description = "资源关联")
    private List<AppStudyModuleResourceRelDetailDTO> resourceList;
} 