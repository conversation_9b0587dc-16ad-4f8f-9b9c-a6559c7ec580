package com.dbj.classpal.books.common.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文件处理状态枚举
 */
@Getter
@AllArgsConstructor
public enum FileStatusEnum {

    PENDING(0, "待处理"),
    PROCESSING(1, "处理中"),
    COMPLETED(2, "已完成"),
    FAILED(3, "处理失败");

    private final Integer code;
    private final String desc;

    @JsonCreator
    public static FileStatusEnum fromValue(Integer code) {
        for (FileStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("无效的FileStatusEnum代码: " + code);
    }

    public static FileStatusEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (FileStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 