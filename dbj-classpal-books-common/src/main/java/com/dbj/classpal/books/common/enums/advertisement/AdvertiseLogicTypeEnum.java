package com.dbj.classpal.books.common.enums.advertisement;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 广告逻辑类型枚举
 * <AUTHOR>
 * @since 2025-04-25 14:36
 */
@Getter
@AllArgsConstructor
public enum AdvertiseLogicTypeEnum {

    AND(1, "与"),
    OR(2, "或"),
    NOT(3, "非");

    private final Integer type;
    private final String name;

    public static AdvertiseLogicTypeEnum of(Integer logicType) {
        return Arrays.stream(values()).filter(e -> e.getType().equals(logicType)).findFirst().orElse(null);
    }
}
