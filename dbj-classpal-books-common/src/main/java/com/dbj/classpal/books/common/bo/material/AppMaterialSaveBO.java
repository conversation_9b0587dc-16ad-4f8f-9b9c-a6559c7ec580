package com.dbj.classpal.books.common.bo.material;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialQueryBO
 * Date:     2025-04-10 09:37:13
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class AppMaterialSaveBO implements Serializable {

    @Schema(description = "父节点ID")
    @NotNull(message = "父节点ID不能为空")
    private Integer parentId;

    @Schema(description = "资源名称")
    @NotEmpty(message = "资源名称不能为空")
    private String materialName;

    @Schema(description = "资源类型 1文件夹 2图片  3音频 4视频 5文档 6压缩包")
    @NotNull(message = "资源类型不能为空")
    private Integer materialType;

    @Schema(description = "资源路径")
    @NotEmpty(message = "资源路径不能为空")
    private String materialPath;

    @Schema(description = "原始资源oss路径")
    @NotEmpty(message = "原始资源oss路径不能为空")
    private String materialOriginUrl;

    @Schema(description = "资源大小")
    @NotNull(message = "资源大小不能为空")
    private Double materialSize;

    @Schema(description = "资源时长(s)")
    @NotNull(message = "资源时长不能为空")
    private Integer materialDuration;
}
