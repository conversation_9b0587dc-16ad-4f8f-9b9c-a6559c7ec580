package com.dbj.classpal.books.common.enums.advertisement;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024-06-12 14:32
 */
@Getter
@AllArgsConstructor
public enum AdvertisementConditionCodeEnum {

    AREA("area", "用户地区"),
    RELATION("relation", "与小朋友的关系"),
    BOOKS("books", "拥有图书"),
    GRADE("grade", "小朋友年级"),
    GENDER("gender", "小朋友性别"),
    ;

    private final String code;
    private final String name;
}
