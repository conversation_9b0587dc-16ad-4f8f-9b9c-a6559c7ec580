package com.dbj.classpal.books.common.dto.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 批改结果DTO
 */
@Data
@Schema(description = "批改结果DTO")
public class MarkingResultDTO implements Serializable {

    @Schema(description = "试卷ID")
    private Integer paperId;

    @Schema(description = "用户ID")
    private Integer appUserId;

    @Schema(description = "题目ID")
    private Integer questionId;

    @Schema(description = "题目类型")
    private Integer questionType;

    @Schema(description = "题目权重")
    private Integer weight;

    @Schema(description = "得分")
    private Integer score;

    @Schema(description = "总分")
    private Integer totalScore;

    @Schema(description = "正确答案")
    private String correctAnswer;

    @Schema(description = "用户答案")
    private String userAnswer;

    @Schema(description = "批改结果")
    private Integer result;

    @Schema(description = "批改状态")
    private Integer status;

    @Schema(description = "批改备注")
    private String remark;
} 