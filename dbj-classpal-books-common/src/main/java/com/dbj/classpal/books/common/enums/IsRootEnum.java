package com.dbj.classpal.books.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: IsRootEnum
 * Date:     2025-04-09 15:46:40
 * Description: 表名： ,描述： 表
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum IsRootEnum {

    IS_ROOT_NO(0,"否"),
    IS_ROOT_YES(1,"是");

    private Integer code;
    private String desc;
}
