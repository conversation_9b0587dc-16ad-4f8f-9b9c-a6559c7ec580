package com.dbj.classpal.books.common.bo.studycenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "学习进度-分页查询BO")
public class AppStudyModuleProgressQueryPageBO {
    @Schema(description = "学习模块ID")
    private Integer moduleId;

    @Schema(description = "用户ID")
    private Integer userId;

    @Schema(description = "状态 0-禁用 1-启用")
    private Integer status;
} 