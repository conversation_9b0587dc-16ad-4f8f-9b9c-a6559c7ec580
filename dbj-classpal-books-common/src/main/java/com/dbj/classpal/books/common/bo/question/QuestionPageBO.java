package com.dbj.classpal.books.common.bo.question;

import lombok.Data;

import java.io.Serializable;

/**
 * 题目分页查询参数
 */
@Data
public class QuestionPageBO implements Serializable {


    /**
     * 学科ID
     */
    private Integer subjectId;

    /**
     * 分类ID
     */
    private Integer categoryId;

    /**
     * 题目类型
     */
    private Integer type;

    /**
     * 题目标题
     */
    private String title;

} 