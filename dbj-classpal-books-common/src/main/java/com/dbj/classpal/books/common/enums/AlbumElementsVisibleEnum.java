package com.dbj.classpal.books.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.NoSuchElementException;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AlbumElementsVisibleEnum
 * Date:     2025-04-16 09:20:41
 * Description: 表名： ,描述： 表
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum AlbumElementsVisibleEnum {
    ALBUM_VISIBLE_HIDE(0,"隐藏"),
    ALBUM_VISIBLE_SHOW(1,"显示");

    private int code;
    private String type;

    public static AlbumElementsVisibleEnum getByCode(Integer code) {
        for (AlbumElementsVisibleEnum statusEnum : AlbumElementsVisibleEnum.values()) {
            if (statusEnum.getCode() == code) {
                return statusEnum;
            }
        }
        throw new NoSuchElementException("未知枚举值：" + code);
    }
}
