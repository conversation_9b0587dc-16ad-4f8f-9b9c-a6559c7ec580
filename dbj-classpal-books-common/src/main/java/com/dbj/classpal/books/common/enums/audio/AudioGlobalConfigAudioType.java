package com.dbj.classpal.books.common.enums.audio;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum AudioGlobalConfigAudioType {
    ADVANCED(1,"预置"),
    CUSTOM(2,"自定义");

    private Integer code;
    private String name;

    public static AudioGlobalConfigAudioType getByCode(Integer code) {
        for (AudioGlobalConfigAudioType value : AudioGlobalConfigAudioType.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
