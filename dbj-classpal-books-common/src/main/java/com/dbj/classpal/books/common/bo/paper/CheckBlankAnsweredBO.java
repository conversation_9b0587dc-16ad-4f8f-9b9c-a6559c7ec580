package com.dbj.classpal.books.common.bo.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 检查空位作答完成参数BO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "检查空位作答完成参数BO")
public class CheckBlankAnsweredBO extends BasicPaperBO implements Serializable {

    @Schema(description = "题目ID")
    private Integer questionId;

    @Schema(description = "用户ID")
    private Integer appUserId;

    @Schema(description = "空位总数")
    private Integer totalBlanks;
} 