package com.dbj.classpal.books.common.dto.evaluation;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationQueryBO
 * Date:     2025-04-15 13:35:46
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
@Tag(name = "评测表详情",description = "评测表详情")
public class AdminEvaluationDetailQueryDTO implements Serializable {

    @Schema(name = "主键id")
    private Integer id;

    @Schema(name = "评测封面")
    private String evaluationCover;

    @Schema(name = "评测名称")
    private String evaluationName;

    @Schema(name = "评测介绍")
    private String evaluationRemark;

    @Schema(name = "上架状态 0下架 1上架")
    private Integer evaluationStatus;

    @Schema(name = "是否隐藏  0隐藏 1显示")
    private Integer evaluationVisible;

    @Schema(description = "是否启用 0否 1是")
    private Integer evaluationOpen;

    @Schema(name = "评测项列表")
    private List<AdminEvaluationNodeQueryDTO> evaluationNodeList;
}
