package com.dbj.classpal.books.common.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 题库业务关联BO
 */
@Data
@Schema(description = "题库业务关联BO")
public class QuestionCategoryBusinessRefBO implements Serializable {

    /**
     * 题目分类ID
     */
    @Schema(description = "题目分类ID")
    private Integer questionCategoryId;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sortNum;
} 