package com.dbj.classpal.books.common.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 题库业务关联BO
 */
@Data
@Schema(description = "题库业务关联BO")
public class QuestionCategoryBusinessRefBO implements Serializable {

    @Schema(description = "业务ID")
    private Integer businessId;

    @Schema(description = "业务类型")
    private Integer businessType;

    @Schema(description = "题库ID列表")
    private List<Integer> categoryIds;
} 