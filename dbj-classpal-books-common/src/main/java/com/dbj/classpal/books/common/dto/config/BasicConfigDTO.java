package com.dbj.classpal.books.common.dto.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Schema
@Data
public class BasicConfigDTO implements Serializable {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;


    @Schema(description = "配置分类类型")
    private String bizType;

    /**
     * 配置名称
     */
    @Schema(description = "配置名称")
    private String name;

    /**
     * 封面
     */
    @Schema(description = "封面")
    private String icon;

    /**
     * 封面名称
     */
    @Schema(description = "封面名称")
    private String iconName;


    /**
     * 背景图片
     */
    @Schema(description = "背景图片")
    private String background;

    /**
     * 背景图片名称
     */
    @Schema(description = "背景图片名称")
    private String backgroundName;

    /**
     * 排序权重
     */
    @Schema(description = "排序权重")
    private Integer sortNum;

    /**
     * 关联业务数量
     */
    @Schema(description = "关联业务数量")
    private Integer bizCount;

    @Schema(description = "创建时间")
    protected LocalDateTime createTime;

    /**
     * 状态0-禁用1-启用
     */
    @Schema(description = "状态0-禁用1-启用")
    private Integer status;
} 