package com.dbj.classpal.books.common.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 单书保存业务对象
 */
@Data
@Schema(description = "单书保存参数BO")
public class AppEBookSaveBO implements Serializable {

    @Schema(description = "样书标题")
    private String bookTitle;

    @Schema(description = "书籍编码")
    private String bookCode;

    @Schema(description = "分类ID")
    private List<Integer> categoryIds;

    @Schema(description = "适用年级，ALL表示全部，部分年级用逗号分隔")
    private List<Integer> applicableGrades;

    @Schema(description = "封面URLId")
    private Integer coverUrlId;

    @Schema(description = "封面URL")
    private String coverUrl;

    @Schema(description = "封面URLName")
    private String coverUrlName;

    @Schema(description = "简介")
    private String blurb;

    @Schema(description = "文件Id")
    private Integer fileId;

    @Schema(description = "文件URL")
    private String fileUrl;

    @Schema(description = "文件MD5")
    private String fileMd5;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "文件大小")
    private BigDecimal fileSize;

    @Schema(description = "水印模板ID")
    private Integer watermarkId;

    @Schema(description = "学科ID")
    private Integer subjectId;
    
    @Schema(description = "阶段ID")
    private Integer stageId;
    
    @Schema(description = "教材版本ID")
    private Integer textbookVersionId;

    @Schema(description = "启用状态：0-禁用，1-启用")
    private Integer launchStatus;

    @Schema(description = "允许下载：0-否，1-是")
    private Integer allowDownload;

    @Schema(description = "排序序号")
    private Integer sortNum;
} 