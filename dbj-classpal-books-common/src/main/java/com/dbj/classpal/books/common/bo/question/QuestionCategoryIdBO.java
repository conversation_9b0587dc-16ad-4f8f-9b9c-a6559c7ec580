package com.dbj.classpal.books.common.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Schema
@Data
public class QuestionCategoryIdBO implements Serializable {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Integer id;
    /**
     * 父节点
     */
    @Schema(description = "父节点")
    private Integer fatherId;
} 