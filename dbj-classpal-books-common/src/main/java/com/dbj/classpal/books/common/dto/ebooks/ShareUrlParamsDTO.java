package com.dbj.classpal.books.common.dto.ebooks;

import com.dbj.classpal.framework.utils.util.enums.ShareUrlTypeEnum;
import lombok.Data;

/**
 * 分享URL参数DTO
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Data
public class ShareUrlParamsDTO {

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 业务ID (根据业务类型确定具体含义)
     * BOOK: ebookId
     * BOOKSHELF: shelfId
     * BOOKSTORE: storeId
     */
    private Integer businessId;

    /**
     * 构造单书分享参数
     */
    public static ShareUrlParamsDTO forEBook(Integer ebookId) {
        ShareUrlParamsDTO params = new ShareUrlParamsDTO();
        params.setBusinessType(ShareUrlTypeEnum.BOOK.getCode());
        params.setBusinessId(ebookId);
        return params;
    }

    /**
     * 构造书架分享参数
     */
    public static ShareUrlParamsDTO forEBookshelf(Integer shelfId) {
        ShareUrlParamsDTO params = new ShareUrlParamsDTO();
        params.setBusinessType(ShareUrlTypeEnum.BOOKSHELF.getCode());
        params.setBusinessId(shelfId);
        return params;
    }

    /**
     * 构造书城分享参数
     */
    public static ShareUrlParamsDTO forEBookstore(Integer storeId) {
        ShareUrlParamsDTO params = new ShareUrlParamsDTO();
        params.setBusinessType(ShareUrlTypeEnum.BOOKSTORE.getCode());
        params.setBusinessId(storeId);
        return params;
    }
}
