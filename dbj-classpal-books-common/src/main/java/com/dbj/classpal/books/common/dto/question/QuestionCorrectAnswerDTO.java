package com.dbj.classpal.books.common.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QuestionCorrectAnswerDTO implements Serializable {
    @Schema(description = "题目ID")
    private Integer questionId;
    @Schema(description = "题目标题")
    private String title;
    @Schema(description = "题目类型")
    private Integer type;
    @Schema(description = "学科名称")
    private String subjectName;
    @Schema(description = "正确答案ID")
    private String correctAnswerIds;
    @Schema(description = "正确答案-填空题")
    private String correctAnswer;
    @Schema(description = "完形填空题答案")
    private List<BlankCorrectAnswerDTO> blankResults;
} 