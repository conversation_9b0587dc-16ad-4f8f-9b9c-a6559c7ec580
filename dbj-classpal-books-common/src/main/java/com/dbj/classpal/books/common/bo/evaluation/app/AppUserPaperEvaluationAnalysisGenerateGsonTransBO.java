package com.dbj.classpal.books.common.bo.evaluation.app;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppUserPaperEvaluationDTO
 * Date:     2025-05-19 14:26:00
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
@Tag(name = "评测报告",description = "评测报告")
public class AppUserPaperEvaluationAnalysisGenerateGsonTransBO implements Serializable {

    @Schema(description = "评测报告id")
    private Integer id;

    @Schema(description = "评测项名称")
    private String evaluationName;

    @Schema(description = "能力得分")
    private String abilityScore;

    @Schema(description = "能力分析")
    private String description;

    @Schema(description = "得分")
    private String score;

    @Schema(description = "分析")
    private String analysis;

    @Schema(description = "建议")
    private String suggest;

}
