package com.dbj.classpal.books.common.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="电子样书-样书配置-水印模板-新增BO", description="电子样书-样书配置-水印模板-新增BO")
public class AppEbooksConfigWatermarkTemplateSaveBO implements Serializable {

    @Schema(description = "水印图片")
    @NotEmpty(message = "水印图片不能为空")
    private String watermark;

    @Schema(name = "水印图片名称")
    private String watermarkName;

    @Schema(description = "模板名称")
    @NotEmpty(message = "模板名称不能为空")
    @Size(max = 64,message = "模板名称最大支持64个字符")
    private String templateName;

    @Schema(description = "水印大小（倍率0.1~1）")
    @NotNull(message = "水印大小不能为空")
    @DecimalMin(value = "0.1", message = "水印大小不能小于0.1")
    @DecimalMax(value = "1", message = "水印大小不能大于1")
    private Float scale;

    @Schema(description = "透明度（倍率0.1~1）")
    @DecimalMin(value = "0.1", message = "透明度不能小于0.1")
    @DecimalMax(value = "1", message = "透明度不能大于1")
    private Float transparency;

    @Schema(description = "旋转角度（0°~360°)")
    @DecimalMin(value = "0", message = "旋转角度不能小于0°")
    @DecimalMax(value = "360", message = "旋转角度不能大于360°")
    private Float rotationAngle;

    @Schema(description = "横向间距（倍率0.1~2）")
    @DecimalMin(value = "0.1", message = "横向间距不能小于0.1")
    @DecimalMax(value = "2", message = "横向间距不能大于2")
    private Float horizontalSpacing;

    @Schema(description = "纵向间距（倍率0.1~2）")
    @DecimalMin(value = "0.1", message = "纵向间距不能小于0.1")
    @DecimalMax(value = "2", message = "纵向间距不能大于2")
    private Float verticalSpacing;

    @Schema(description = "权重")
    private Integer sort;
}
