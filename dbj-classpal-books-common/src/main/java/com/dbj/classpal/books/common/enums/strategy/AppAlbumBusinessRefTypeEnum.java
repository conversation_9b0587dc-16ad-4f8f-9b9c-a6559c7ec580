package com.dbj.classpal.books.common.enums.strategy;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumBusinessRefTypeEnum
 * Date:     2025-05-09 16:47:00
 * Description: 表名： ,描述： 表
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum AppAlbumBusinessRefTypeEnum {
    AUDIO_ALBUM_REF_BOOKS_IN_CODE(5,"音频专辑","appBooksInCodeRefAlbumStrategy"),
    VIDEO_APP_ALBUM_REF_BOOKS_IN_CODE(6,"视频专辑","appBooksInCodeRefAlbumStrategy"),
    STUDY_CENTER_AUDIO_ALBUM_CODE(17,"学习模块-音频专辑","studyModelAudioStrategy"),
    STUDY_CENTER_VIDEO_ALBUM_CODE(18,"学习模块-视频专辑","studyModelVideoStrategy"),
    SYNC_COURSE_VIDEO_ALBUM_CODE(21,"同步课程-视频专辑","syncCourseVideoAlbumStrategy"),
    ;
    private Integer type;
    private String typeName;
    private String strategy;

    public static AppAlbumBusinessRefTypeEnum getByCode(Integer type) {
        for (AppAlbumBusinessRefTypeEnum typeEnum : AppAlbumBusinessRefTypeEnum.values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }
        return null;
    }
}
