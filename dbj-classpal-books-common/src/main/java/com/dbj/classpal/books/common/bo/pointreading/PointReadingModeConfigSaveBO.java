package com.dbj.classpal.books.common.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Data;

/**
 * 点读书模式配置保存BO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingModeConfigSaveBO", description = "点读书模式配置保存BO")
public class PointReadingModeConfigSaveBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "模式配置字典ID")
    @NotNull(message = "模式配置字典ID不能为空")
    private Integer configDictId;

    @Schema(description = "点读书ID")
    @NotNull(message = "点读书ID不能为空")
    private Integer bookId;

    @Schema(description = "显示名称")
    @NotBlank(message = "显示名称不能为空")
    private String displayName;

    @Schema(description = "处理状态：10-处理中 20-处理完成 30-处理失败")
    private Integer processStatus;

    @Schema(description = "原始文件ID")
    private String originalFileId;

    @Schema(description = "原始文件URL")
    private String originalUrl;

    @Schema(description = "原始文件名")
    private String originalName;

    @Schema(description = "原始文件MD5")
    private String originalMd5;

    @Schema(description = "排序")
    private Integer sortNum;

    @Schema(description = "状态：0-停用 1-正常")
    private Integer status;
}
