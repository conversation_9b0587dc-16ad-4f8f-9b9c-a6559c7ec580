package com.dbj.classpal.books.common.enums.evaluation;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: PaperEnvaluationStatusEnum
 * Date:     2025-04-09 10:22:57
 * Description: 表名： ,描述： 表
 */
@Getter
@AllArgsConstructor
public enum PaperEnvaluationStatusEnum {
    //是否生成评测报告
    EVALUATION_GENERATED_NO(0,"未生成"),
    EVALUATION_GENERATED_YES(1,"已生成"),
    EVALUATION_GENERATED_FAIL(2,"生成失败");
    private Integer code;
    private String desc;


    public static PaperEnvaluationStatusEnum getByCode(Integer code) {
        for (PaperEnvaluationStatusEnum statusEnum : PaperEnvaluationStatusEnum.values()) {
            if (Objects.equals(statusEnum.getCode(), code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
