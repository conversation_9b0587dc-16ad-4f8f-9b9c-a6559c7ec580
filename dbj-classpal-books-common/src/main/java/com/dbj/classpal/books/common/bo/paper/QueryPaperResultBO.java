package com.dbj.classpal.books.common.bo.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 查询试卷结果参数BO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "查询试卷结果参数")
public class QueryPaperResultBO extends BasicPaperBO implements Serializable {

    /**
     * 试卷ID
     */
    @Schema(description = "试卷ID")
    private Integer paperId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Integer appUserId;
} 