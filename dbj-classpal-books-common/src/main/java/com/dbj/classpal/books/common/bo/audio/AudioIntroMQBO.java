package com.dbj.classpal.books.common.bo.audio;

import com.dbj.classpal.framework.mq.entity.RabbitmqEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * 预置提示音表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AudioIntroMQBO extends RabbitmqEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "音频简介ID")
    private Integer audioIntroId;
}
