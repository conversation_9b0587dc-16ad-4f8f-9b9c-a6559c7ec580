package com.dbj.classpal.books.common.bo.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 提交试卷参数BO
 */
@Data
@Schema(description = "重新考试试卷BO")
public class RetakePaperBO extends BasicPaperBO implements Serializable {


    /**
     * 试卷ID
     */
    @Schema(description = "试卷ID")
    private Integer paperId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Integer appUserId;


}


