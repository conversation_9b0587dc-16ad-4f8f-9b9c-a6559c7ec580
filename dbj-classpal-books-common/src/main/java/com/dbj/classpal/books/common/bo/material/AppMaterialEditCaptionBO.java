package com.dbj.classpal.books.common.bo.material;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialReNameBO
 * Date:     2025-04-11 16:46:18
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class AppMaterialEditCaptionBO implements Serializable {

    @Schema(description = "主键id",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键id不能为空")
    private Integer id;

    @Schema(description = "字幕")
    @Size(max = 10000,message = "字幕最大长度为10000字符")
    private String materialCaption;
}
