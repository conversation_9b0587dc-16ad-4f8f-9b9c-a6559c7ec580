package com.dbj.classpal.books.common.dto.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * PDF处理任务状态DTO
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@Schema(description = "PDF处理任务状态")
public class PdfTaskStatusDTO implements Serializable {

    @Schema(description = "任务ID")
    private String taskId;

    @Schema(description = "业务ID")
    private Integer businessId;

    @Schema(description = "业务key")
    private String businessKey;

    @Schema(description = "任务状态：0-处理中，1-成功，2-失败")
    private Integer status;

    @Schema(description = "任务状态描述")
    private String statusDesc;

    @Schema(description = "封面URL")
    private String coverUrl;

    @Schema(description = "错误信息")
    private String errorMsg;

    @Schema(description = "处理进度百分比（0-100）")
    private Integer progress;
}
