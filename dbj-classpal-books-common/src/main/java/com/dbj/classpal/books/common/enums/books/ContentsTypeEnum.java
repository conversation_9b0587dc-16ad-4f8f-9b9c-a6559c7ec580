package com.dbj.classpal.books.common.enums.books;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: BusinessTypeEnum
 * Date:     2025-04-14 08:48:01
 * Description: 表名： ,描述： 表
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ContentsTypeEnum {

    DIRECTORY("directory","目录"),
    RESOURCE("resource","图书资源"),
    QUESTION("question","答题"),
    AUDIO("audio","音频专辑"),
    VIDEO("video","视频专辑"),;

    private String code;
    private String name;

    public static ContentsTypeEnum getByCode(String code) {
        for (ContentsTypeEnum value : ContentsTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
