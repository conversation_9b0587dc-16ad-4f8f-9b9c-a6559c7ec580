package com.dbj.classpal.books.common.dto.material;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialBusinessRefDTO
 * Date:     2025-04-14 08:54:29
 * Description: 表名： ,描述： 表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@Tag(name = "专辑引用素材跳转DTO")
public class AppMaterialBusinessRefAlbumDirectDTO implements Serializable {
    @Schema(description = "专辑分类id")
    private Integer menuId;

    @Schema(description = "专辑id")
    private Integer businessId;

    @Schema(description = "业务类型 1内容管理-音频专辑 2内容管理-视频专辑")
    private Integer businessType;

    @Schema(description = "专辑标题")
    private String albumTitle;
}
