package com.dbj.classpal.books.common.dto.evaluation.app;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationReportQueryDTO
 * Date:     2025-05-20 08:55:36
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
@Tag(name = "评测报告(详情)",description = "评测报告(详情)")
public class AppEvaluationReportQueryDTO implements Serializable {
    @Schema(name = "评测报告主键id")
    private Integer id;

    @Schema(name = "学员")
    private Integer userId;

    @Schema(name = "学员名称")
    private String userName;

    @Schema(name = "学员UID")
    private String uid;

    @Schema(name = "年级")
    private String gradeName;

    @Schema(name = "头像")
    private String avatar;

    @Schema(name = "评测名称")
    private String evaluationName;

    @Schema(name = "评测时间")
    private LocalDateTime generatedTime;

    @Schema(name = "综合评价")
    private String evaluation;

    @Schema(name = "各模块答题情况")
    List<AppUserPaperEvaluationAnalysisQueryDTO>analysisQueryDTOList;

}
