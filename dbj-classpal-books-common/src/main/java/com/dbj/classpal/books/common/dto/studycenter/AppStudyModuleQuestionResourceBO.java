package com.dbj.classpal.books.common.dto.studycenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "资源")
public class AppStudyModuleQuestionResourceBO {
    @Schema(description = "资源ID")
    private Integer resourceId;

    @Schema(description = "资源名称")
    private String resourceName;

    @Schema(description = "资源icon")
    private String resourceIcon;
} 