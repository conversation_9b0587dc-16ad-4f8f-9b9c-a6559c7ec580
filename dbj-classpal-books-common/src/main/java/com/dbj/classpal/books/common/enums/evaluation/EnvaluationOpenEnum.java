package com.dbj.classpal.books.common.enums.evaluation;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: EnvaluationOpenEnum
 * Date:     2025-04-09 10:22:57
 * Description: 表名： ,描述： 表
 */
@Getter
@AllArgsConstructor
public enum EnvaluationOpenEnum {
    //是否启用
    EVALUATION_OPEN_NO(0,"否","禁用"),
    EVALUATION_OPEN_YES(1,"是","启用");
    private Integer code;
    private String value;
    private String desc;


    public static EnvaluationOpenEnum getByCode(Integer code) {
        for (EnvaluationOpenEnum openEnum : EnvaluationOpenEnum.values()) {
            if (Objects.equals(openEnum.getCode(), code)) {
                return openEnum;
            }
        }
        return null;
    }
}
