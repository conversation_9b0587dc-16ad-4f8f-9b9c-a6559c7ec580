package com.dbj.classpal.books.common.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: QuestionBusinessRefBO
 * Date:     2025-05-19 11:12:52
 * Description: 表名： ,描述： 表
 */
@Data
@Schema(description = "查询业务下关联题目列表BO")
public class QuestionBusinessRefPageBO implements Serializable {
    @Schema(name = "业务id")
    private Integer businessId;

    @Schema(name = "业务类型 1内容管理-评测-评测项")
    private Integer businessType;

}
