package com.dbj.classpal.books.common.dto.album;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialQueryDTO
 * Date:     2025-04-10 09:31:28
 * Description: 表名： ,描述： 表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@Tag(name = "专辑目录查询DTO")
public class AppAlbumMenusTreeDTO implements Serializable {

    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "是否根节点 0否 1是")
    private Integer isRoot;

    @Schema(description = "父节点ID")
    private Integer parentId;

    @Schema(description = "默认分类 0否 1是")
    private Integer defaultType;

    @Schema(description = "分类名称")
    private String albumMenuName;

    @Schema(description = "专辑类型 1音频 2视频")
    private Integer albumType;

    @Schema(description = "专辑分类状态 0禁用 1启用 ")
    private String albumMenuStatus;

    @Schema(description = "排序")
    private Integer orderNum;

    @Schema(description = "专辑数量")
    private Integer albumCount;

    @Schema(description = "子节点")
    private List<AppAlbumMenusTreeDTO>children;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
