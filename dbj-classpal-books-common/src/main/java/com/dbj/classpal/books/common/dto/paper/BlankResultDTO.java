package com.dbj.classpal.books.common.dto.paper;

import com.dbj.classpal.books.common.dto.question.QuestionAnswerDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 完形填空结果DTO
 */
@Data
@Schema(description = "完形填空结果")
public class BlankResultDTO implements Serializable {
    /**
     * 题目ID
     */
    @Schema(description = "题目ID")
    private Integer questionId;


    /**
     * 空位区域ID
     */
    @Schema(description = "空位区域ID")
    private Integer blankAreaId;

    /**
     * 空位序号
     */
    @Schema(description = "空位序号")
    private Integer blankIndex;

    /**
     * 用户答案
     */
    @Schema(description = "用户答案")
    private String userAnswer;

    /**
     * 用户选择的答案ID
     */
    @Schema(description = "用户选择的答案ID")
    private String userAnswerIds;

    /**
     * 正确答案
     */
    @Schema(description = "正确答案")
    private String correctAnswer;

    /**
     * 正确答案ID
     */
    @Schema(description = "正确答案ID")
    private String correctAnswerIds;

    /**
     * 答案选项列表
     */
    @Schema(description = "答案选项列表")
    private List<QuestionAnswerDTO> options;

    /**
     * 结果 1 正确 0 错误
     */
    @Schema(description = "结果 1 正确 0 错误")
    private Integer result;
}