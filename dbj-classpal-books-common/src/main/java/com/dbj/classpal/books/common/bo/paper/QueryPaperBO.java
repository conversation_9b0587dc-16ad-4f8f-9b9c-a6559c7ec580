package com.dbj.classpal.books.common.bo.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 查询试卷题目参数BO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "查询试卷参数BO")
public class QueryPaperBO extends BasicPaperBO implements Serializable {

    @Schema(description = "试卷ID")
    private Integer paperId;

    @Schema(description = "用户ID")
    private Integer appUserId;
} 