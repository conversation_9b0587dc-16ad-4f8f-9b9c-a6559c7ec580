package com.dbj.classpal.books.common.dto.material;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialBusinessRefDTO
 * Date:     2025-04-14 08:54:29
 * Description: 表名： ,描述： 表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@Tag(name = "图书管理-题库引用素材跳转DTO")
public class AppMaterialBusinessRefQuestionDirectDTO implements Serializable {
    @Schema(description = "题目分类id")
    private Integer questionCategoryId;

    @Schema(description = "题目id")
    private Integer businessId;

    @Schema(description = "业务类型 4图书管理-题库")
    private Integer businessType;
    @Schema(description = "题目标题")
    private String title;
    @Schema(description = "题目ID")
    private Integer questionId ;
}
