package com.dbj.classpal.books.common.dto.material;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialBusinessRefDTO
 * Date:     2025-04-14 08:54:29
 * Description: 表名： ,描述： 表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@Tag(name = "素材与关联业务关系表DTO")
public class AppMaterialBusinessRefTypeCountDTO implements Serializable {

    @Schema(description = "对象类型 1文件夹 2图片  3音频 4视频 5文档 6压缩包")
    private Integer appMaterialType;

    @Schema(description = "对象类型文本")
    private String appMaterialTypeStr;

    @Schema(description = "对象类型关联数量")
    private Integer appMaterialTypeRefCount;

}
