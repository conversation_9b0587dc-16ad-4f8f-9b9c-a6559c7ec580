package com.dbj.classpal.books.common.dto.album;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumMenusInfoDTO
 * Date:     2025-04-15 16:04:25
 * Description: 表名： ,描述： 表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
public class AppAlbumMenusInfoDTO implements Serializable {


    /**
     * 数量
     */
    @Schema(description ="数量")
    private Integer num;
    /**
     * 专辑分类Id
     */
    @Schema(description ="专辑分类Id")
    private Integer menusId;
}
