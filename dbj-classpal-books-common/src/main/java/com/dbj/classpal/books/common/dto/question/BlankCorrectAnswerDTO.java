package com.dbj.classpal.books.common.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
public class BlankCorrectAnswerDTO implements Serializable {
    @Schema(description = "题目ID")
    private Integer questionId;
    @Schema(description = "完形填空ID")
    private Integer blankAreaId;
    @Schema(description = "完形填空题空位")
    private Integer blankIndex;
    @Schema(description = "完形填空题正确答案")
    private String correctAnswer;
    @Schema(description = "完形填空题答案ID")
    private String correctAnswerIds;
} 