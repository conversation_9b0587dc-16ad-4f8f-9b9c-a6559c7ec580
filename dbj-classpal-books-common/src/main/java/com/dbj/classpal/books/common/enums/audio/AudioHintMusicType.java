package com.dbj.classpal.books.common.enums.audio;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2025/7/1 8:41
 */
@Getter
@AllArgsConstructor
public enum AudioHintMusicType {

    HINT(1, "预置提示音"),
    BACKGROUND(2, "预置背景音");

    private final Integer value;
    private final String name;

    public static AudioHintMusicType of(Integer value) {
        return Arrays.stream(values()).filter(e -> e.getValue().equals(value)).findFirst().orElseThrow();
    }
}
