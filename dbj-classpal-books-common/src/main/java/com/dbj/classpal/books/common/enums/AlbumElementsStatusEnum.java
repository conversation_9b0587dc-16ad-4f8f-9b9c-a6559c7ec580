package com.dbj.classpal.books.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AlbumElementsStatusEnum
 * Date:     2025-04-16 09:20:41
 * Description: 表名： ,描述： 表
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum AlbumElementsStatusEnum {
    ALBUM_STATUS_NO(0,"下架"),
    ALBUM_STATUS_YES(1,"上架");

    private int code;
    private String type;

    public static AlbumElementsStatusEnum getByCode(Integer code) {
        for (AlbumElementsStatusEnum statusEnum : AlbumElementsStatusEnum.values()) {
            if (statusEnum.getCode() == code) {
                return statusEnum;
            }
        }
        return null;
    }
}
