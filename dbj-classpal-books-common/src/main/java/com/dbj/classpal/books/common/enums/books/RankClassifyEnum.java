package com.dbj.classpal.books.common.enums.books;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: BusinessTypeEnum
 * Date:     2025-04-14 08:48:01
 * Description: 表名： ,描述： 表
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum RankClassifyEnum {

    BOOK_IN_CODES("BOOK_IN_CODES","图书资源"),
    POINT_READING("POINT_READING","点读"),
    AUDIO_ALBUM("AUDIO_ALBUM","音频专辑"),
    VIDEO_ALBUM("VIDEO_ALBUM","视频专辑"),
    ;

    private String code;
    private String name;
}
