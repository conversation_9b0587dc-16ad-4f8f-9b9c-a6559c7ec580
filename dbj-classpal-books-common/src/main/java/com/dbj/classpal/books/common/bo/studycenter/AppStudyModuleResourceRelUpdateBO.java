package com.dbj.classpal.books.common.bo.studycenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "资源关联-编辑BO")
public class AppStudyModuleResourceRelUpdateBO extends AppStudyModuleResourceRelCreateBO  {
    @Schema(description = "主键ID")
    private Integer id;
} 