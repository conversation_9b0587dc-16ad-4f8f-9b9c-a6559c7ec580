package com.dbj.classpal.books.common.bo.studycenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "学习中心模块-最新学习")
public class StudyCenterModuleStudyBO {
    @Schema(description = "用户ID")
    private Integer userId;

    @Schema(description = "模块ID")
    private Integer moduleId;
} 