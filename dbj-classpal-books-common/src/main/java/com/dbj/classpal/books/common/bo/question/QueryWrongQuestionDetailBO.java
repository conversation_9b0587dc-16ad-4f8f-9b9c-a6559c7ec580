package com.dbj.classpal.books.common.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 查询错题详情参数
 */
@Data
@Schema(description = "查询错题详情参数")
public class QueryWrongQuestionDetailBO implements Serializable {

    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private Integer appUserId;

    /**
     * 问题id
     */
    @Schema(description = "问题id")
    private Integer questionId;

    /**
     * 题目类型
     */
    @Schema(description = "题目类型")
    private String questionType;
} 