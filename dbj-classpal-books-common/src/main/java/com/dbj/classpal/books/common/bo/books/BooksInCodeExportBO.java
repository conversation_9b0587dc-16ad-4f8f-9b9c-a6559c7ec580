package com.dbj.classpal.books.common.bo.books;

import com.dbj.classpal.framework.utils.annotation.ExportExcel;
import com.dbj.classpal.framework.utils.annotation.ExportExcelColumn;
import com.dbj.classpal.framework.utils.annotation.ImportExcelColumn;
import com.dbj.classpal.framework.utils.dto.CommonExportExcelBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/24 08:45:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
@ExportExcel(sheetName = "字典")
public class BooksInCodeExportBO extends CommonExportExcelBO implements Serializable {


    @Schema(description ="册数标题")
    @ExportExcelColumn(name = "册数标题")
    public String productItemName;
    @Schema(description ="册数ID")
    @ExportExcelColumn(name = "册数ID")
    public Integer rankId;

    @Schema(description ="页面标题")
    @ExportExcelColumn(name = "页面标题")
    public String contentName;

    @Schema(description ="页面ID")
    @ExportExcelColumn(name = "页面ID")
    public Integer contentId;

    @Schema(description ="强调连接")
    @ExportExcelColumn(name = "强调连接")
    public String forcePromotionUrl;

    @Schema(description ="兼容连接1")
    @ExportExcelColumn(name = "兼容连接1")
    public String compatibilityUrl1;

    @Schema(description ="兼容连接2")
    @ExportExcelColumn(name = "兼容连接2")
    public String compatibilityUrl2;

    @Schema(description ="兼容连接3")
    @ExportExcelColumn(name = "兼容连接3")
    public String compatibilityUrl3;

    private int rowNum;
}
