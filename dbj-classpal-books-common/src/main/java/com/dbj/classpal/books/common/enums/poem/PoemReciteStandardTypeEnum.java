package com.dbj.classpal.books.common.enums.poem;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 古诗文业务关联类型枚举
 * <AUTHOR>
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum PoemReciteStandardTypeEnum {

    ANCIENT_POEM_RECITE_TOTAL_SCORE(1,"古诗背诵得分超越标准"),
    ANCIENT_POEM_RECITE_TOTAL_NUM(2,"古诗背诵总数超越标准"),
    ;

    private int code;
    private String value;

    public static PoemReciteStandardTypeEnum getByCode(Integer code) {
        for (PoemReciteStandardTypeEnum businessTypeEnum : PoemReciteStandardTypeEnum.values()) {
            if (businessTypeEnum.getCode() == code) {
                return businessTypeEnum;
            }
        }
        return null;
    }
}
