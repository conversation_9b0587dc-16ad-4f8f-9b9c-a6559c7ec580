package com.dbj.classpal.books.common.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 点读书分类查询BO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Schema(name = "PointReadingCategoryQueryBO", description = "点读书分类查询BO")
public class PointReadingCategoryQueryBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "分类名称")
    private String name;

    @Schema(description = "父级分类ID")
    private Integer parentId;

    @Schema(description = "是否默认分类：0-否 1-是")
    private Integer isDefault;

    @Schema(description = "状态：0-停用 1-正常")
    private Integer status;

    @Schema(description = "租户ID")
    private Integer tenantId;
}
