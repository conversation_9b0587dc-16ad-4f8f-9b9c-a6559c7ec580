package com.dbj.classpal.books.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AlbumMenusOrderEnum
 * Date:     2025-04-16 10:00:42
 * Description: 表名： ,描述： 表
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum AlbumMenusOrderEnum {
    ORDER_BEFORE(-1,"前面"),
    ORDER_AFTER(1,"后面");

    private int code;
    private String type;

    public static AlbumMenusOrderEnum getByCode(Integer code) {
        for (AlbumMenusOrderEnum statusEnum : AlbumMenusOrderEnum.values()) {
            if (statusEnum.getCode() == code) {
                return statusEnum;
            }
        }
        return null;
    }
}
