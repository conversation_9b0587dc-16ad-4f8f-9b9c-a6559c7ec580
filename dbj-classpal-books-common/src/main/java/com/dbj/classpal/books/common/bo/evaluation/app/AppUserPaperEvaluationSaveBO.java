package com.dbj.classpal.books.common.bo.evaluation.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppUserPaperEvaluationSaveBO
 * Date:     2025-05-19 15:19:02
 * Description: 表名： ,描述： 表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AppUserPaperEvaluationSaveBO implements Serializable {
    @Schema(name = "主键id")
    private Integer id;

    @Schema(name = "app用户id")
    private Integer appUserId;

    @Schema(name = "评测表id")
    private Integer appEvaluationId;

    @Schema(name = "学员年级id")
    private String gradeId;

    @Schema(name = "学员年级")
    private String gradeName;

    @Schema(name = "地区")
    private String region;

    @Schema(name = "综合评价")
    private String evaluation;

    @Schema(name = "是否生成评测报告 0否 1是")
    private Integer isGenerated;

}
