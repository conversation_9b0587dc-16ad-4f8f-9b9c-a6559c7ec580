package com.dbj.classpal.books.common.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class QuestionCategoryBO implements Serializable {
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Integer id;

    /**
     * 类型名称
     */
    @Schema(description = "类型名称")
    private String name;

    /**
     * 父级id
     */
    @Schema(description = "父级id")
    private Integer fatherId;

}