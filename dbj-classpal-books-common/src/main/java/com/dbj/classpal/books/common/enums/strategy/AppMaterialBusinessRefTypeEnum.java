package com.dbj.classpal.books.common.enums.strategy;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppMaterialBusinessRefTypeEnum
 * Date:     2025-05-09 14:38:07
 * Description: 表名： ,描述： 表
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum AppMaterialBusinessRefTypeEnum {
    AUDIO_APP_ALBUM_REF_MATERIAL(1,"音频专辑","appAlbumRefMaterialStrategy"),
    VIDEO_APP_ALBUM_REF_MATERIAL(2,"视频专辑","appAlbumRefMaterialStrategy"),
    BOOKS_IN_CONTENT_REF_MATERIAL(3,"图书资源","appBooksInCodeRefMaterialStrategy"),
    QUESTION_REF_MATERIAL_MEDIA(7,"题目-媒体文件","appQuestionRefMaterialStrategy"),
    QUESTION_REF_MATERIAL_PIC(8,"题目-辅助识图","appQuestionRefMaterialStrategy"),
    QUESTION_REF_MATERIAL_ANSWER(9,"题目-答案","appQuestionRefMaterialStrategy"),
    PINYIN_ORAL_ANIMATION_MATERIAL(11,"拼音-口型动画","pinyinMaterialStrategy"),
    PINYIN_PRONOUNCE_BUSINESS_MATERIAL(12,"拼音-发音","pinyinMaterialStrategy"),
    PINYIN_FOUR_TONE_PRONOUNCE_MATERIAL(13,"拼音-四声发音","pinyinMaterialStrategy"),
    ANCIENT_POEM_ORIGINAL_AUDIO_MATERIAL(14,"古诗文-原文音频","ancientPoemMaterialStrategy"),
    ANCIENT_POEM_EXPLANATION_AUDIO_MATERIAL(15,"古诗文-解析音频","ancientPoemMaterialStrategy"),
    ;

    private Integer type;
    private String typeName;
    private String strategy;

    public static AppMaterialBusinessRefTypeEnum getByCode(Integer type) {
        for (AppMaterialBusinessRefTypeEnum typeEnum : AppMaterialBusinessRefTypeEnum.values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }
        return null;
    }
}
