package com.dbj.classpal.books.common.dto.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name="电子样书-样书配置-图书分类-查询DTO", description="电子样书-样书配置-图书分类-查询DTO")
public class AppEbooksConfigCategoryQueryDTO {
    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "父级id")
    private Integer parentId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "权重")
    private Integer sort;

    @Schema(description = "是否启用 1-是 0-否")
    private Integer status;

    @Schema(description = "关联数量")
    private Integer refCount;
}
