package com.dbj.classpal.books.common.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 题目分类排序BO
 */
@Data
@Schema(description = "题目分类排序BO")
public class QuestionCategorySortBO implements Serializable {

    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "目标父节点ID")
    private Integer fatherId;

    @Schema(description = "目标子节点id(如果没有则传-1)")
    private Integer aimId;

    @Schema(description = "相对目标子节点位置 -1前面 1后面")
    private Integer order;
}