package com.dbj.classpal.books.common.dto.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 试卷结果DTO
 */
@Data
@Schema(description = "试卷结果")
public class PaperQuestionResultDTO implements Serializable {

    /**
     * 题目结果列表
     */
    @Schema(description = "题目结果列表")
    private List<QuestionResultDTO> questionResults;

    /**
     * 正确题目数量
     */
    @Schema(description = "正确题目数量")
    private Integer correctCount;

    /**
     * 错误题目数量
     */
    @Schema(description = "错误题目数量")
    private Integer wrongCount;
} 