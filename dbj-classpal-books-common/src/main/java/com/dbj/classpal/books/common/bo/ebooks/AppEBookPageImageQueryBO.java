package com.dbj.classpal.books.common.bo.ebooks;

import lombok.Data;

import java.io.Serializable;

/**
 * 单书图片资源查询参数
 */
@Data
public class AppEBookPageImageQueryBO implements Serializable {
    
    /**
     * 单书ID
     */
    private Integer bookId;
    
    /**
     * 当前页码
     */
    private Integer pageNum;
    
    /**
     * 是否需要查询上一页和下一页信息
     */
    private Boolean needPrevNext = false;
} 