package com.dbj.classpal.books.common.dto.studycenter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "学习中心模块")
public class StudyCenterModuleListDTO {
    @Schema(description = "模块ID")
    private Integer id;
    @Schema(description = "模块标题")
    private String title;
    @Schema(description = "模块简介")
    private String description;
    @Schema(description = "Logo图片URL")
    private String logoUrl;
    @Schema(description = "背景图片")
    private String background;
    @Schema(description = "模块类型1-评测，2-音频专辑 3 -视频专辑 4-功能 5- 题库")
    private Integer moduleType;
    @Schema(description = "栅格数")
    private Integer gridCount;
    @Schema(description = "答题设置，仅题目类型时使用")
    private AppStudyModuleQuestionExtDetailDTO questionExt;
    @Schema(description = "资源关联")
    private List<AppStudyModuleResourceRelDetailDTO> resourceList;
} 