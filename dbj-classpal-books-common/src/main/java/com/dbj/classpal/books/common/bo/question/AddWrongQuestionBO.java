package com.dbj.classpal.books.common.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema(description = "添加错题参数")
@Data
public class AddWrongQuestionBO implements Serializable {

    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private Integer appUserId;

    /**
     * 题目id
     */
    @Schema(description = "题目id")
    private Integer questionId;


    /**
     * 1:用户答题练习,2:用户评测答题
     */
    @Schema(description = "来源类型（1:用户答题练习,2:用户评测答题）")
    @NotNull(message = "来源类型不能为空")
    private Integer sourceType;

    /**
     * 来源ID
     */
    @Schema(description = "来源ID")
    @NotNull(message = "来源ID不能为空")
    private Integer sourceId;

    /**
     * 用户错误答案
     */
    @Schema(description = "用户错误答案")
    private String userAnswer;

    /**
     * 用户选择的答案ID（多个英文逗号隔开）
     */
    @Schema(description = "用户选择的答案ID（多个英文逗号隔开）")
    private String userAnswerIds;

    /**
     * 完形填空空位信息（仅完形填空题使用）
     */
    @Schema(description = "完形填空空位信息（仅完形填空题使用）")
    private List<WrongQuestionBlankBO> blankResults;
}