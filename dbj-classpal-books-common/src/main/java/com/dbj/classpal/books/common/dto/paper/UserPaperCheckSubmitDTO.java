package com.dbj.classpal.books.common.dto.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户试卷信息
 */
@Data
@Schema(description = "答题卡是否提交")
@Builder
public class UserPaperCheckSubmitDTO implements Serializable {


    /**
     * 答题卡ID
     */
    @Schema(description = "答题卡ID")
    private Integer paperId;

    /**
     * 是否提交
     */
    @Schema(description = "是否提交 true ｜ false")
    private Boolean isSubmit;

}