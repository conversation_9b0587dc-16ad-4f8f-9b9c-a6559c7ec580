package com.dbj.classpal.books.common.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 从答题结果添加错题参数
 */
@Data
@Schema(description = "从答题结果添加错题参数")
public class AddWrongQuestionsFromResultBO implements Serializable {


    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private Integer appUserId;

    /**
     * 试卷id
     */
    @Schema(description = "试卷id")
    private Integer paperId;

    /**
     * 学科ID
     */
    @Schema(description = "学科ID")
    private Integer subjectId;

    /**
     * 学科名称
     */
    @Schema(description = "学科名称")
    private String subjectName;

    /**
     * 来源类型 1:用户答题 2:用户评测
     */
    @Schema(description = "来源类型 1:用户答题 2:用户评测")
    private Integer sourceType;

    /**
     * 来源ID
     */
    @Schema(description = "来源ID")
    private Integer sourceId;
} 