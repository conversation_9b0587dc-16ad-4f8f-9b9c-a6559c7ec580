package com.dbj.classpal.books.common.dto.evaluation;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppEvaluationQueryBO
 * Date:     2025-04-15 13:35:46
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class AdminEvaluationNodeQueryDTO implements Serializable {
    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "评测表id")
    private Integer appEvaluationId;

    @Schema(description = "评测项名称")
    private String appEvaluationNodeName;

    @Schema(description = "评测项封面")
    private String appEvaluationNodeCover;

    @Schema(description = "评测项封面名称")
    private String appEvaluationNodeCoverName;

    @Schema(description = "评测项排序")
    private Integer appEvaluationOrder;
}
