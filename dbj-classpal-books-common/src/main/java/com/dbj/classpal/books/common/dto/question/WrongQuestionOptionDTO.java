package com.dbj.classpal.books.common.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema(description = "错题选项信息")
@Data
public class WrongQuestionOptionDTO implements Serializable {


    /**
     * ID
     */
    @Schema(description = "ID")
    private Integer id;


    /**
     * 题目id
     */
    @Schema(description = "题目ID")
    private Integer questionId;

    /**
     * 空位区域ID（仅完形填空题使用）
     */
    @Schema(description = "空位区域ID")
    private Integer blankAreaId;

    /**
     * 序号
     */
    @Schema(description = "序号")
    private Integer serialNo;

    /**
     * 选项名称
     */
    @Schema(description = "选项名称")
    private String optionName;

    /**
     * 选项内容
     */
    @Schema(description = "选项内容")
    private String optionContent;

    /**
     * 是否为答案
     */
    @Schema(description = "是否为答案")
    private Integer isAnswer;

    /**
     * 媒体文件URL
     */
    @Schema(description = "媒体文件URL")
    private List<QuestionMediaDTO> mediaUrl;
}