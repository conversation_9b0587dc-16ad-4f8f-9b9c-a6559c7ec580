package com.dbj.classpal.books.common.enums.audio;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025/6/30 10:38
 */
@Getter
@AllArgsConstructor
public enum AudioIntroStatus {

    WAIT_COMPOUND(0, "待合成"),
    COMPOUNDING(1, "合成中"),
    COMPLETE(2, "完成"),
    FAILED(3, "失败"),
    CANCEL(4, "取消"),
    ;

    private final Integer value;
    private final String name;
}
