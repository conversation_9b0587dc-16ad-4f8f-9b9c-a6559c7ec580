package com.dbj.classpal.books.common.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema
@Data
public class QuestionBO implements Serializable {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Integer id;

    /**
     * 分类id
     */
    @Schema(description = "分类id")
    private Integer questionCategoryId;

    @Schema(description = "学科")
    private Integer subjectId;

    /**
     * 题目
     */
    @Schema(description = "题目")
    private String title;

    /**
     * 媒体文件类型 2-图片 3-音频 4-视频 1- 文本
     * @see com.dbj.classpal.books.common.enums.question.OptionTypeEnum
     */
    @Schema(description = "媒体文件类型 1-文本 2-图片,3-音频,4-视频")
    private Integer mediaType;

    /**
     * 媒体文件url
     */
    @Schema(description = "媒体文件url")
    private List<QuestionMediaBO> mediaUrl;

    @Schema(description = "辅助识图")
    private List<QuestionRecognitionBO> aidedRecognitionUrl;
    /**
     * 题目类型
     */
    @Schema(description = "题目类型")
    private Integer type;

    /**
     * 选项类型 1-文本 2-图片,3-音频,4-视频
     */
    @Schema(description = "选项类型 1-文本 2-图片,3-音频,4-视频")
    private Integer optionType;

    /**
     * 权重
     */
    @Schema(description = "权重")
    private Integer weight;

    /**
     * 选择题答案id（多个英文逗号隔开）
     */
    @Schema(description = "选择题答案id（多个英文逗号隔开）")
    private String answer;

    /**
     * 解析
     */
    @Schema(description = "解析")
    private String analyzes;


    /**
     * 答案列表
     */
    @Schema(description = "答案列表")
    private List<QuestionAnswerBO> answers;

    /**
     * 完形填空区域列表
     */
    @Schema(description = "完形填空区域列表")
    private List<QuestionBlankAreaBO> blankAreas;
} 