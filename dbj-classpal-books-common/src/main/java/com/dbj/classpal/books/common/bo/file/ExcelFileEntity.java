package com.dbj.classpal.books.common.bo.file;

import com.dbj.classpal.framework.mq.entity.RabbitmqEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ExcelFileEntity
 * @description
 * @date 2023-10-27 10:38
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExcelFileEntity extends RabbitmqEntity implements Serializable {

    /**
     * file 表文件的id
     */
    @Schema(name = "file 表文件的id")
    private Integer id;

    /**
     * 文件名称
     */
    @Schema(name = "文件名称")
    private String fileName;

    /**
     * 文件md5值
     */
    @Schema(name = "文件md5值")
    private String md5;

    @Schema(name = "父文件夹id")
    private Integer parentId;

    /**
     * 分析任务的jobId
     */
    @Schema(name = "分析任务的jobId")
    private String analysisJobId;

    /**
     * 分析结果的最佳模板id
     */
    @Schema(name = "分析任务的jobId")
    private String templateId;

    /**
     * 转码任务的jobId
     */
    @Schema(name = "转码任务的jobId")
    private String transCodeJobId;


    @Schema(name = "额外参数")
    private String paramJson;
}
