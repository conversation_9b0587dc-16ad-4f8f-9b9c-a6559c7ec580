package com.dbj.classpal.books.common.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 删除错题参数
 */
@Data
@Schema(description = "错题ids参数")
public class WrongQuestionPkIdsBO implements Serializable {


    /**
     * 用户id
     */
    @Schema(description = "用户id",hidden = true)
    private Integer appUserId;

    /**
     * 错误问题主键ID列表
     */
    @Schema(description = "错误问题主键ID列表")
    private List<Integer> ids;

} 