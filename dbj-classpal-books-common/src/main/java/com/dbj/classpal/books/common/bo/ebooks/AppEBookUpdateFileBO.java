package com.dbj.classpal.books.common.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 单书更新文件业务对象
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Data
@Schema(description = "单书更新文件业务对象")
public class AppEBookUpdateFileBO implements Serializable {

    @Schema(description = "单书ID")
    private Integer id;

    @Schema(description = "文件Id")
    private Integer fileId;

    @Schema(description = "文件URL")
    private String fileUrl;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "文件md5")
    private String fileMd5;

    @Schema(description = "文件大小")
    private Double fileSize;

} 