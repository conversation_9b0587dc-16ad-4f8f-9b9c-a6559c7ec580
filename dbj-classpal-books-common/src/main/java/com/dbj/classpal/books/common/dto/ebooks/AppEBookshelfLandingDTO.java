package com.dbj.classpal.books.common.dto.ebooks;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 书架数据传输对象
 */
@Data
@Schema(description = "书架DTO")
public class AppEBookshelfLandingDTO implements Serializable {

    @Schema(description = "书架ID")
    private Integer id;

    @Schema(description = "书架名称")
    private String shelfTitle;

    @Schema(description = "封面URL")
    private String coverUrl;

    @Schema(description = "封面URL")
    private String coverUrlName;

    @Schema(description = "简介")
    private String blurb;

    @Schema(description = "启用状态：0-禁用，1-启用")
    private Integer launchStatus;

    @Schema(description = "允许下载：0-否，1-是")
    private Integer allowDownload;

    @Schema(description = "排序权重")
    private Integer sortNum;
    
    @Schema(description = "包含的单书数量")
    private Integer bookCount;

    @Schema(description = "分享链接")
    private String shareUrl;

    @Schema(description = "分享二维码")
    private String shareQrCode;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
} 