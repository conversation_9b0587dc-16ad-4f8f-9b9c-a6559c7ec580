package com.dbj.classpal.books.common.bo.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * description: description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/04/21 14:06:59
 */
@Data
public class BasicPaperBO {
    /**
     * @see com.dbj.classpal.books.client.enums.BusinessTypeEnum
     */
    @Schema(description = "业务类型 1内容管理-音频专辑 2内容管理-视频专辑 3图书管理-图书资源 4图书管理-题库 5图书管理-音频专辑 6图书管理-视频专辑",requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer businessType;

    @Schema(description = "业务ID")
    private Integer businessId;
}
