package com.dbj.classpal.books.common.bo.pointreading;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Data;

/**
 * 点读书业务关联保存BO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(name = "PointReadingBookBusinessRefSaveBO", description = "点读书业务关联保存BO")
public class PointReadingBookBusinessRefSaveBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "点读书ID")
    @NotNull(message = "点读书ID不能为空")
    private Integer bookId;

    @Schema(description = "业务类型：BOOK_IN_CODES-图书书内码 BOOK_RANK_CLASSIFY-图书资源")
    @NotBlank(message = "业务类型不能为空")
    private String businessType;

    @Schema(description = "业务ID")
    @NotNull(message = "业务ID不能为空")
    private Integer businessId;

    @Schema(description = "排序")
    private Integer sortNum;

    @Schema(description = "状态：0-正常 1-停用")
    private Integer status;
}
