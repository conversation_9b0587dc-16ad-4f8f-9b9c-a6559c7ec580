package com.dbj.classpal.books.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * PDF处理任务状态枚举
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Getter
@AllArgsConstructor
public enum PdfTaskStatusEnum {

    PROCESSING(0, "处理中"),
    SUCCESS(1, "成功"),
    FAILED(2, "失败");

    private final Integer code;
    private final String desc;

    public static PdfTaskStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PdfTaskStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
