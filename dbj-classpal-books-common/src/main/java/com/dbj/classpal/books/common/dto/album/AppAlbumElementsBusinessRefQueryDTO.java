package com.dbj.classpal.books.common.dto.album;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: AppAlbumElementsBusinessRefQueryDTO
 * Date:     2025-04-10 09:31:28
 * Description: 表名： ,描述： 表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@Tag(name = "专辑关联业务查询查询DTO")
public class AppAlbumElementsBusinessRefQueryDTO implements Serializable {
    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "业务id")
    private Integer businessId;

    @Schema(description = "业务类型 1内容管理-音频专辑 2内容管理-视频专辑 3图书管理-图书资源 4图书管理-题库 5图书管理-音频专辑 6图书管理-视频专辑 7题目-媒体文件 8题目-辅助识图 9题目-答案")
    private Integer businessType;

    @Schema(description = "业务类型文本")
    private String businessTypeStr;

    @Schema(description = "专辑id")
    private Integer albumId;

    @Schema(description = "专辑标题")
    private String albumTitle;

    @Schema(description = "引用对象")
    private String businessName;

    @Schema(description = "对象类型 1音频专辑 2视频专辑")
    private Integer appMaterialType;

    @Schema(description = "对象类型文本")
    private String appMaterialTypeStr;
}
