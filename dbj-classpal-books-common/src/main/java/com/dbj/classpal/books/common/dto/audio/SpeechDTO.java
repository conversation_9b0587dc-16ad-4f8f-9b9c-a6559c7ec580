package com.dbj.classpal.books.common.dto.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.File;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SpeechDTO {


    @Schema(description = "音频文本id")
    private Integer audioContextInfoId;

    @Schema(description = "合成的音频文件")
    private File file;

    @Schema(description = "合成的音频文件名")
    private String fileName;

    @Schema(description = "合成音频文件路径")
    private String filePath;

    @Schema(description = "阿里云合成任务id")
    private String taskId;

    @Schema(description = "阿里云合成失败错误信息")
    private TaskErrorDTO errorMsg;

    private AudioTrialUseGlobalConfigDTO globalConfig;
}
