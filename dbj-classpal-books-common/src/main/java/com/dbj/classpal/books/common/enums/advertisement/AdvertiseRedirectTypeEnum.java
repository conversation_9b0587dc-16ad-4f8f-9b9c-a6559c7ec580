package com.dbj.classpal.books.common.enums.advertisement;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 广告跳转类型枚举
 * <AUTHOR>
 * @since 2025-04-19 10:52
 */
@Getter
@AllArgsConstructor
public enum AdvertiseRedirectTypeEnum {
    NOTHING(0, "无跳转"),
    EXTERNAL_LINK(1, "链接"),
    INTERNAL_MP(2, "小程序"),
    EXTERNAL_MP(3, "基础图文"),
    ;

    private final Integer type;
    private final String name;

    public static AdvertiseRedirectTypeEnum of(Integer type) {
        return Arrays.stream(values()).filter(e -> e.getType().equals(type))
                .findFirst().orElse(null);
    }
}
