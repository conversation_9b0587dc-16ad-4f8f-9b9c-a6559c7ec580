package com.dbj.classpal.books.common.enums.audio;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 预置音乐类型枚举
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum AudioGlobalConfigTypeEnum {
    BACKGROUND(1,"背景音"),
    EFFECTS(2,"特效");

    private Integer code;
    private String name;

    public static AudioGlobalConfigTypeEnum getByCode(Integer code) {
        for (AudioGlobalConfigTypeEnum value : AudioGlobalConfigTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
