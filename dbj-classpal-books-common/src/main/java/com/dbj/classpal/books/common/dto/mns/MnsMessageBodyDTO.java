package com.dbj.classpal.books.common.dto.mns;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: MnsMessageBodyDTO
 * Date:     2025-04-29 17:13:09
 * Description: 表名： ,描述： 表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Tag(name = "Mns消息队列消息结构")
public class MnsMessageBodyDTO {
    @Schema(description = "requestId")
    private String requestId;
    @Schema(description = "任务类型 Analysis文件分析 Transcode文件转码")
    private String type;
    @Schema(description = "jobId")
    private String jobId;
    @Schema(description = "状态")
    private String state;
}
