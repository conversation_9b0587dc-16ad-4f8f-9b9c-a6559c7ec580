package com.dbj.classpal.books.common.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 单书异步处理业务参数BO
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
@Schema(description = "单书异步处理请求参数")
public class AppEBookAsyncProcessBO implements Serializable {

    @Schema(description = "业务ID（单书ID）")
    private Integer businessId;

    @Schema(description = "文件Id")
    private Integer fileId;

    @Schema(description = "PDF文件URL")
    private String fileUrl;

    @Schema(description = "PDF文件名")
    private String fileName;

    @Schema(description = "文件md5")
    private String fileMd5;

    @Schema(description = "PDF大小")
    private BigDecimal fileSize;

    @Schema(description = "水印模板ID")
    private Integer watermarkTemplateId;
}
