package com.dbj.classpal.books.common.bo.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Schema
@Data
public class QuestionMediaBO implements Serializable {
    /**
     * 素材库ID
     */
    @Schema(description = "素材库ID")
    private Integer materialId;
    /**
     * 素材库地址
     */
    @Schema(description = "素材库地址")
    private String materialPath;

    @Schema(description = "排序")
    private Integer sortNum;
}
