package com.dbj.classpal.books.common.bo.paper;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 提交试卷参数BO
 */
@Data
@Schema(description = "提交试卷BO")
public class SubmitPaperBO extends BasicPaperBO implements Serializable {

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Integer appUserId;

    /**
     * 评测项ID
     */
    @Schema(description = "评测项ID")
    private Integer appEvaluationId;

    /**
     * 普通题目作答结果列表
     */
    @Schema(description = "题目作答结果列表")
    private List<SubmitQuestionResultBO> questionResults;

    /**
     * 完形填空作答结果列表
     */
    @Schema(description = "填空题作答结果列表")
    private List<SubmitBlankResultBO> blankResults;

}


