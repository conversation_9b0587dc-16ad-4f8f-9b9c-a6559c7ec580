package com.dbj.classpal.books.common.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 错题统计DTO
 */
@Data
@Schema(description = "错题统计DTO")
public class WrongQuestionStatsDTO implements Serializable {


    /**
     * 总错题数量
     */
    @Schema(description = "总错题数量")
    private Integer totalCount;

    /**
     * 普通题错题数量
     */
    @Schema(description = "普通题错题数量")
    private Integer normalCount;

    /**
     * 完形填空题错题数量
     */
    @Schema(description = "完形填空题错题数量")
    private Integer clozeCount;

    /**
     * 学科错题统计列表
     */
    @Schema(description = "学科错题统计列表")
    private List<SubjectWrongStatsDTO> subjectStats;
} 