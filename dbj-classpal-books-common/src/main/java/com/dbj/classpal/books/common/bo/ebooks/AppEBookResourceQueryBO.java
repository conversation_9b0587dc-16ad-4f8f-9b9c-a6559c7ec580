package com.dbj.classpal.books.common.bo.ebooks;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 单书资源查询业务对象
 */
@Data
@Schema(description = "单书资源查询参数BO")
public class AppEBookResourceQueryBO implements Serializable {

    @Schema(description = "关联的单书ID")
    private Integer bookId;

    @Schema(description = "资源类型：1-加水印PDF, 2-切图图片")
    private Integer resourceType;

    @Schema(description = "处理类型：1-仅添加水印, 2-仅切图, 3-加水印并切图")
    private Integer businessType;
} 