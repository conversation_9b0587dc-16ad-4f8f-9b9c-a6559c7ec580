package com.dbj.classpal.books.common.bo.album;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * Copyright (C), 2025, com.dbj
 * FileName: CommonIdBO
 * Date:     2025-04-15 10:43:02
 * Description: 表名： ,描述： 表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
public class AppAlbumMenusBatchMoveBO implements Serializable {
    @Schema(description = "主键ID",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键ID不能为空")
    private Integer id;

    @Schema(description = "目标父节点ID",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "目标父节点ID不能为空")
    private Integer parentId;

    @Schema(description = "目标子节点id(如果没有则传-1)")
    private Integer aimId;

    @Schema(description = "相对目标子节点位置 -1前面 1后面")
    private Integer order;
}
