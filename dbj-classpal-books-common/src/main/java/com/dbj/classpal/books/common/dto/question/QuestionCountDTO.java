package com.dbj.classpal.books.common.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema
@Data
public class QuestionCountDTO implements Serializable {
    /**
     * 分类id
     */
    @Schema(description = "分类id")
    private Integer questionCategoryId;

    /**
     * 题目
     */
    @Schema(description = "题目数量")
    private Integer num;

}