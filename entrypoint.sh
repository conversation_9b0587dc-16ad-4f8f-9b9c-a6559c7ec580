PARAMS="--server.port=${Server_Port:-61004}"

# JVM堆内存的相关选项
JAVA_OPTS="-Xms${XMS_OPTS:-512m} -Xmx${XMX_OPTS:-1G}"

# NACOS相关的选项
NACOS_OPTS="-Djava.security.egd=file:/dev/./urandom \
	-Dfile.encoding=utf8 \
	-Dspring.profiles.active=${profiles_active:-dev} \
	-Dspring.cloud.nacos.discovery.namespace.=${Nacos_Namespace:-k8s-printer-dev} \
	-Dspring.cloud.nacos.config.namespace=${Nacos_Namespace:-k8s-printer-dev} \
	-Dspring.cloud.nacos.config.file-extension=yaml \
	-Dspring.cloud.nacos.server-addr=${Nacos_Server_Addr:-192.168.110.209:18848} "

# 启动命令
java -jar ${JAVA_OPTS} ${NACOS_OPTS}  /dbj-business-hr-api.jar  ${PARAMS}
