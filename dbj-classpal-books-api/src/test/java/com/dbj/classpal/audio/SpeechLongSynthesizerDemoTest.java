package com.dbj.classpal.audio;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.dbj.classpal.books.BaseApplication;
import com.dbj.classpal.books.common.bo.audio.SpeechBO;
import com.dbj.classpal.books.common.bo.material.AppMaterialSaveBO;
import com.dbj.classpal.books.common.dto.audio.SpeechDTO;
import com.dbj.classpal.books.service.entity.material.AppMaterial;
import com.dbj.classpal.books.service.job.audio.AudioCleanTemporaryFileScheduled;
import com.dbj.classpal.books.service.service.material.IAppMaterialService;
import com.dbj.classpal.books.service.util.audio.AudioSpeakerTemplateGenerateUtil;
import com.dbj.classpal.books.service.util.audio.AudioUploadOSS;
import com.dbj.classpal.books.service.util.audio.BatchSpeechLongSynthesizerService;
import com.dbj.classpal.books.service.util.audio.SpeechLongSynthesizer;
import com.dbj.classpal.framework.commons.exception.BusinessException;
import com.dbj.classpal.framework.tts.audio.GetTTSToken;
import com.dbj.classpal.framework.utils.util.ContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@SpringBootTest(classes = BaseApplication.class)
@RunWith(SpringRunner.class)
public class SpeechLongSynthesizerDemoTest {

    @Autowired
    private BatchSpeechLongSynthesizerService synthesizerService;
    @Autowired
    private AudioUploadOSS audioUploadOSS;
    @Autowired
    private AudioSpeakerTemplateGenerateUtil audioSpeakerTemplateGenerateUtil;
    @Autowired
    private AudioCleanTemporaryFileScheduled audioCleanTemporaryFileScheduled;
    @Autowired
    private IAppMaterialService appMaterialService;
    @Autowired
    private SpeechLongSynthesizer speechLongSynthesizer;

    @Test
    public void testProcessBatchSuccess() {
        // 准备测试文本
        List<SpeechBO> list = new ArrayList<>();
        SpeechBO b1 = new SpeechBO();
        b1.setVoice("emily");
        b1.setLongText("<speak pitch=\"0\" rate=\"0\" volume=\"50\" voice=\"emily\" ><s>Bending the neck, singing to the sky; white hair floats on the green water; red palms stir the clear waves</s></speak>");
        b1.setVolume(100);
        list.add(b1);

        List<SpeechDTO> audioFileList = synthesizerService.processBatch(list);
        if (CollectionUtil.isNotEmpty(audioFileList)) {
            for (SpeechDTO speechDTO : audioFileList) {
                System.out.println("音频上传oss地址：" + audioUploadOSS.upload(speechDTO.getFileName(), speechDTO.getFile(), null));
            }
        }
    }

    @Test
    public void getToken() {
        String accessKeyId = "LTAI5tQtbbAbqdFUa4hz6cYq";
        String accessKeySecret = "******************************";
        System.out.println(JSON.toJSONString(GetTTSToken.createToken(accessKeyId, accessKeySecret)));
    }

    @Test
    public void generateSpeakerTemplateTest() {
        ContextHolder.set("X-tenant-Id",1);
        audioSpeakerTemplateGenerateUtil.generateTemplate();
    }

    @Test
    public void cleanTemporaryFileTest() throws BusinessException {
        audioCleanTemporaryFileScheduled.audioCleanTemporaryFileScheduled();
    }

    @Test
    public void saveMaterialTest() throws BusinessException {
        ContextHolder.set("X-tenant-Id",1);
        AppMaterialSaveBO bo = new AppMaterialSaveBO();
        bo.setParentId(648);
        bo.setMaterialName("wav_2_mp3_4bd3f4a3298f4571bed9fa2ae1408c99");
        bo.setMaterialType(3);
        bo.setMaterialDuration(96);
        bo.setMaterialSize(1501.6748046875);
        bo.setMaterialOriginUrl("https://dbj-app-test.oss-cn-hangzhou.aliyuncs.com/audio_output/wav_2_mp3_4bd3f4a3298f4571bed9fa2ae1408c99.mp3");
        bo.setMaterialPath("https://dbj-app-test.oss-cn-hangzhou.aliyuncs.com/audio_output/wav_2_mp3_4bd3f4a3298f4571bed9fa2ae1408c99.mp3");
        AppMaterial appMaterial = appMaterialService.saveMaterial(bo);
        System.out.println(JSON.toJSONString(appMaterial));
    }

    @Test
    public void testProcessSingleBatchSuccess() {
        String ttsTextLong = "<speak pitch=\\\"0\\\" rate=\\\"0\\\" volume=\\\"50\\\" voice=\\\"zhixiaobai\\\"><s>山<phoneme alphabet=\\\"py\\\" ph=\\\"you4\\\">有</phoneme><phoneme alphabet=\\\"py\\\" ph=\\\"se4\\\">色</phoneme></s></speak>";
        List<SpeechBO> speechList = new ArrayList<>();
        speechList.add(new SpeechBO(100, 643, null, null, "zhixiaobai", ttsTextLong, null));
        List<SpeechDTO> dtoList = new ArrayList<>();
        for (SpeechBO bo : speechList) {
            SpeechDTO speechDTO = speechLongSynthesizer.process(bo);
            if (speechDTO != null) {
                dtoList.add(speechDTO);
            }
        }
        System.out.println(JSON.toJSONString(dtoList));
    }

    @Test
    public void upload() {
        ContextHolder.set("X-tenant-Id",1);
        System.out.println(audioUploadOSS.upload("blow.wav", new File("C:\\Users\\<USER>\\Downloads\\blow.wav"), null));
    }
}
