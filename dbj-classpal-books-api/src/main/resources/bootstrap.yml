spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: dbj-classpal-books-api-${version:v1}
    version: ${version:v1}
  cloud:
    nacos:
      username: ${Nacos_Username:nacos}
      password: ${Nacos_Password:nacos}
      discovery:
        server-addr: ${Nacos_Server_Addr:192.168.110.209:18848}  #nacos服务地址
        namespace: ${Nacos_Namespace:k8s-classpal-test}
        group: DEFAULT_GROUP
        service: ${spring.application.name}
      config:
        server-addr: 192.168.110.209:18848
        file-extension: yaml
        namespace: ${Nacos_Namespace:k8s-classpal-test}
        shared-configs:
          - data-id: common.yaml
            refresh: true
          - data-id: dbj-classpal-books-api.yaml
            refresh: true
          - data-id: dbj-classpal-openapi.yaml
            refresh: true
  logging:
  level:
    com.example.feignclient: DEBUG
