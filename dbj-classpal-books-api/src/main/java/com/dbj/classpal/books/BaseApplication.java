/*
 * BaseApplication.java Copyright PrinceEgg Tech Co. Ltd. All Rights Reserved.
 */
package com.dbj.classpal.books;

import com.aliyun.mns.client.MNSClient;
import com.dbj.classpal.books.service.util.MnsUtil;
import com.dbj.classpal.framework.oss.config.MnsConfig;
import com.dbj.classpal.framework.oss.enums.MnsEnvEnum;
import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import jakarta.annotation.Resource;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;

/**
 * 应用启动基类
 *
 * <AUTHOR>
 * @version 0.1
 * @since 2018/07/04
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.dbj.classpal.**.client.api"})
@MapperScan("com.dbj.classpal.books.service.mapper")
@ComponentScan("com.dbj.*")
@ConfigurationPropertiesScan("com.dbj.classpal.*")
@EnableKnife4j
public class BaseApplication{
    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplication(BaseApplication.class);
//        springApplication.setApplicationContextClass(CoreEmbeddedWebApplicationContext.class);
//        springApplication.setAddCommandLineProperties(true);
        springApplication.run(args);

    }
}
