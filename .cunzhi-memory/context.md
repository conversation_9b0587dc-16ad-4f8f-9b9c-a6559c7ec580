# 项目上下文信息

- 书城数据规模：一个书城关联30-100个书架，每个书架关联30-100个单书，每个单书拥有30-100个资源。这种大数据量场景下需要重点优化数据库索引和查询策略。
- 用户已在PdfWaterMarkBusinessTypeEnum中新增了枚举值，可以直接使用新的业务类型来处理单独提取封面的场景
- 用户需要扩展PointReadingHotspotSaveApiBO支持答题功能，新增question_category_business_settings表统一管理题库关联，使用business_id和business_type统一处理app_study_module_question_ext、books_rank_in_codes_contents_question和点读答题设置三种场景
- 用户反馈getPaperQuestionResult方法存在逻辑漏洞：当用户答题完成后，后台修改了题目选项（删除选项或修改正确答案），显示结果时需要按照用户答题时的选项数量显示，缺失的选项用空字符串或null补位
